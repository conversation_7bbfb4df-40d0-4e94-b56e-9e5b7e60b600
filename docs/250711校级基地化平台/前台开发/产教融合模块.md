# 产教融合模块

## 模块概述

产教融合模块展示基地与企业合作信息、优秀学生案例、企业招聘信息等内容，是连接学校教育与企业需求的重要桥梁。

## 功能需求

### 主要功能
- 产教融合主页展示
- 轮播图管理
- 合作企业展示
- 优秀学生展示
- 企业招聘信息
- 产教融合项目详情

## 页面结构

### 1. 产教融合主页 (`/industry`)

#### 1.1 路由配置
```javascript
// router/index.js
{
  path: '/industry',
  name: 'Industry',
  component: () => import('@/views/industry/index.vue'),
  meta: {
    title: '产教融合',
    requiresAuth: false
  }
},
{
  path: '/industry/enterprise',
  name: 'EnterpriseList',
  component: () => import('@/views/industry/enterprise.vue'),
  meta: {
    title: '合作企业',
    requiresAuth: false
  }
},
{
  path: '/industry/recruitment',
  name: 'RecruitmentList',
  component: () => import('@/views/industry/recruitment.vue'),
  meta: {
    title: '企业招聘',
    requiresAuth: false
  }
},
{
  path: '/industry/students',
  name: 'ExcellentStudents',
  component: () => import('@/views/industry/students.vue'),
  meta: {
    title: '优秀学生',
    requiresAuth: false
  }
}
```

#### 1.2 主页布局
```vue
<template>
  <div class="industry-page">
    <div class="content-container">
      <!-- 轮播图区域 -->
      <div class="banner-section">
        <el-carousel :interval="4000" type="card" height="350px">
          <el-carousel-item v-for="banner in bannerList" :key="banner.id">
            <div class="banner-item" @click="goToBannerLink(banner.link)">
              <img :src="banner.image" :alt="banner.title" />
              <div class="banner-overlay">
                <h3 class="banner-title">{{ banner.title }}</h3>
                <p class="banner-description">{{ banner.description }}</p>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>

      <!-- 数据统计区域 -->
      <div class="stats-section">
        <div class="stats-container">
          <div class="stat-item">
            <div class="stat-number">{{ stats.enterpriseCount }}</div>
            <div class="stat-label">合作企业</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.studentCount }}</div>
            <div class="stat-label">优秀学生</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.jobCount }}</div>
            <div class="stat-label">招聘岗位</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">{{ stats.projectCount }}</div>
            <div class="stat-label">合作项目</div>
          </div>
        </div>
      </div>

      <!-- 合作企业展示区域 -->
      <div class="enterprise-section">
        <div class="section-header">
          <h2 class="section-title">合作企业</h2>
          <el-button type="text" @click="goToEnterpriseList">查看更多 >></el-button>
        </div>
        
        <div class="enterprise-grid">
          <div class="enterprise-card" v-for="enterprise in displayEnterprises" :key="enterprise.id" @click="goToEnterpriseDetail(enterprise.id)">
            <div class="enterprise-logo">
              <img :src="enterprise.logo" :alt="enterprise.name" />
            </div>
            <div class="enterprise-info">
              <h3 class="enterprise-name">{{ enterprise.name }}</h3>
              <p class="enterprise-type">{{ enterprise.type }}</p>
              <div class="enterprise-tags">
                <el-tag v-for="tag in enterprise.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 优秀学生展示区域 -->
      <div class="students-section">
        <div class="section-header">
          <h2 class="section-title">优秀学生</h2>
          <el-button type="text" @click="goToStudentsList">查看更多 >></el-button>
        </div>
        
        <div class="students-grid">
          <div class="student-card" v-for="student in displayStudents" :key="student.id">
            <div class="student-photo">
              <img :src="student.photo" :alt="student.name" />
            </div>
            <div class="student-info">
              <h3 class="student-name">{{ student.name }}</h3>
              <p class="student-major">{{ student.major }}</p>
              <p class="student-company">就职于：{{ student.company }}</p>
              <div class="student-achievements">
                <el-tag v-for="achievement in student.achievements" :key="achievement" type="success" size="small">
                  {{ achievement }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 招聘信息区域 -->
      <div class="recruitment-section">
        <div class="section-header">
          <h2 class="section-title">最新招聘</h2>
          <el-button type="text" @click="goToRecruitmentList">查看更多 >></el-button>
        </div>
        
        <div class="recruitment-list">
          <div class="recruitment-item" v-for="job in displayJobs" :key="job.id" @click="goToJobDetail(job.id)">
            <div class="company-logo">
              <img :src="job.companyLogo" :alt="job.companyName" />
            </div>
            <div class="job-info">
              <h3 class="job-title">{{ job.title }}</h3>
              <p class="job-company">{{ job.companyName }}</p>
              <div class="job-details">
                <span class="job-salary">{{ job.salary }}</span>
                <span class="job-location">{{ job.location }}</span>
                <span class="job-experience">{{ job.experience }}</span>
              </div>
            </div>
            <div class="job-status">
              <el-tag :type="job.urgent ? 'danger' : 'primary'" size="small">
                {{ job.urgent ? '急招' : '在招' }}
              </el-tag>
              <div class="job-date">{{ formatDate(job.publishTime) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 1.3 样式设计
```less
<style lang="less" scoped>
.industry-page {
  background-color: #f2f2f2;
  
  .banner-section {
    margin-bottom: 40px;
    
    .banner-item {
      position: relative;
      height: 100%;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      
      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        padding: 30px;
        color: white;
        
        .banner-title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }
        
        .banner-description {
          font-size: 16px;
          opacity: 0.9;
        }
      }
    }
  }
  
  .stats-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 30px;
      
      .stat-item {
        text-align: center;
        
        .stat-number {
          font-size: 42px;
          font-weight: bold;
          color: #4093f9;
          margin-bottom: 8px;
        }
        
        .stat-label {
          font-size: 16px;
          color: #666;
        }
      }
    }
  }
  
  .enterprise-section, .students-section, .recruitment-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
    }
  }
  
  .enterprise-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    
    .enterprise-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
      
      .enterprise-logo {
        text-align: center;
        margin-bottom: 15px;
        
        img {
          width: 80px;
          height: 80px;
          object-fit: contain;
          border-radius: 8px;
        }
      }
      
      .enterprise-info {
        text-align: center;
        
        .enterprise-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }
        
        .enterprise-type {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
        }
        
        .enterprise-tags {
          .el-tag {
            margin: 2px;
          }
        }
      }
    }
  }
  
  .students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    
    .student-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }
      
      .student-photo {
        text-align: center;
        margin-bottom: 15px;
        
        img {
          width: 100px;
          height: 100px;
          object-fit: cover;
          border-radius: 50%;
        }
      }
      
      .student-info {
        text-align: center;
        
        .student-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }
        
        .student-major {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
        
        .student-company {
          font-size: 14px;
          color: #4093f9;
          margin-bottom: 12px;
        }
        
        .student-achievements {
          .el-tag {
            margin: 2px;
          }
        }
      }
    }
  }
  
  .recruitment-list {
    .recruitment-item {
      display: flex;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f8f9fa;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .company-logo {
        width: 60px;
        height: 60px;
        margin-right: 20px;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 8px;
        }
      }
      
      .job-info {
        flex: 1;
        
        .job-title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }
        
        .job-company {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }
        
        .job-details {
          display: flex;
          gap: 20px;
          font-size: 14px;
          
          .job-salary {
            color: #f56c6c;
            font-weight: bold;
          }
          
          .job-location, .job-experience {
            color: #666;
          }
        }
      }
      
      .job-status {
        text-align: center;
        
        .job-date {
          font-size: 12px;
          color: #999;
          margin-top: 8px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .industry-page {
    .banner-section {
      /deep/ .el-carousel {
        height: 250px !important;
      }
    }
    
    .stats-section {
      .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
        
        .stat-item {
          .stat-number {
            font-size: 32px;
          }
        }
      }
    }
    
    .enterprise-grid, .students-grid {
      grid-template-columns: 1fr;
    }
    
    .recruitment-list {
      .recruitment-item {
        flex-direction: column;
        text-align: center;
        
        .company-logo {
          margin-right: 0;
          margin-bottom: 15px;
        }
        
        .job-info {
          margin-bottom: 15px;
          
          .job-details {
            justify-content: center;
          }
        }
      }
    }
  }
}
</style>
```

### 2. 合作企业列表页 (`/industry/enterprise`)

#### 2.1 页面布局
```vue
<template>
  <div class="enterprise-list-page">
    <div class="content-container">
      <div class="page-header">
        <h1 class="page-title">合作企业</h1>
        <div class="page-subtitle">深度合作，共同发展</div>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索企业名称或关键词"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-area">
          <el-select v-model="selectedType" placeholder="企业类型" @change="handleTypeChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="互联网" value="internet"></el-option>
            <el-option label="金融" value="finance"></el-option>
            <el-option label="制造业" value="manufacturing"></el-option>
            <el-option label="教育" value="education"></el-option>
            <el-option label="医疗" value="healthcare"></el-option>
          </el-select>
          
          <el-select v-model="selectedScale" placeholder="企业规模" @change="handleScaleChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="大型企业" value="large"></el-option>
            <el-option label="中型企业" value="medium"></el-option>
            <el-option label="小型企业" value="small"></el-option>
          </el-select>
          
          <el-select v-model="selectedLocation" placeholder="所在地区" @change="handleLocationChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="北京" value="beijing"></el-option>
            <el-option label="上海" value="shanghai"></el-option>
            <el-option label="深圳" value="shenzhen"></el-option>
            <el-option label="杭州" value="hangzhou"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 企业列表 -->
      <div class="enterprise-list-section">
        <div class="enterprise-grid">
          <div class="enterprise-card" v-for="enterprise in enterpriseList" :key="enterprise.id" @click="goToDetail(enterprise.id)">
            <div class="enterprise-header">
              <div class="enterprise-logo">
                <img :src="enterprise.logo" :alt="enterprise.name" />
              </div>
              <div class="enterprise-basic">
                <h3 class="enterprise-name">{{ enterprise.name }}</h3>
                <p class="enterprise-type">{{ enterprise.typeName }}</p>
                <div class="enterprise-location">
                  <i class="el-icon-location"></i>
                  {{ enterprise.location }}
                </div>
              </div>
            </div>
            
            <div class="enterprise-content">
              <p class="enterprise-description">{{ enterprise.description }}</p>
              
              <div class="enterprise-info">
                <div class="info-item">
                  <span class="info-label">成立时间：</span>
                  <span class="info-value">{{ enterprise.foundYear }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">企业规模：</span>
                  <span class="info-value">{{ enterprise.scaleName }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">合作项目：</span>
                  <span class="info-value">{{ enterprise.projectCount }}个</span>
                </div>
              </div>
              
              <div class="enterprise-tags">
                <el-tag v-for="tag in enterprise.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
            </div>
            
            <div class="enterprise-footer">
              <div class="cooperation-info">
                <span class="cooperation-years">合作 {{ enterprise.cooperationYears }} 年</span>
                <span class="student-count">培养学生 {{ enterprise.studentCount }} 人</span>
              </div>
              <el-button type="primary" size="small">查看详情</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
```

### 3. 企业招聘列表页 (`/industry/recruitment`)

#### 3.1 页面布局
```vue
<template>
  <div class="recruitment-list-page">
    <div class="content-container">
      <div class="page-header">
        <h1 class="page-title">企业招聘</h1>
        <div class="page-subtitle">优质岗位，助力职业发展</div>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索职位名称或公司名称"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-area">
          <el-select v-model="selectedCategory" placeholder="职位类别" @change="handleCategoryChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="技术开发" value="development"></el-option>
            <el-option label="产品设计" value="design"></el-option>
            <el-option label="市场营销" value="marketing"></el-option>
            <el-option label="运营管理" value="operation"></el-option>
            <el-option label="人力资源" value="hr"></el-option>
          </el-select>
          
          <el-select v-model="selectedExperience" placeholder="经验要求" @change="handleExperienceChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="应届生" value="fresh"></el-option>
            <el-option label="1-3年" value="1-3"></el-option>
            <el-option label="3-5年" value="3-5"></el-option>
            <el-option label="5年以上" value="5+"></el-option>
          </el-select>
          
          <el-select v-model="selectedLocation" placeholder="工作地点" @change="handleLocationChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="北京" value="beijing"></el-option>
            <el-option label="上海" value="shanghai"></el-option>
            <el-option label="深圳" value="shenzhen"></el-option>
            <el-option label="杭州" value="hangzhou"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 招聘列表 -->
      <div class="recruitment-list-section">
        <div class="job-list">
          <div class="job-item" v-for="job in jobList" :key="job.id" @click="goToJobDetail(job.id)">
            <div class="job-header">
              <div class="company-logo">
                <img :src="job.companyLogo" :alt="job.companyName" />
              </div>
              <div class="job-basic">
                <h3 class="job-title">
                  {{ job.title }}
                  <el-tag v-if="job.urgent" type="danger" size="mini">急招</el-tag>
                  <el-tag v-if="job.hot" type="warning" size="mini">热门</el-tag>
                </h3>
                <p class="job-company">{{ job.companyName }}</p>
                <div class="job-location">
                  <i class="el-icon-location"></i>
                  {{ job.location }}
                </div>
              </div>
              <div class="job-salary">{{ job.salary }}</div>
            </div>
            
            <div class="job-content">
              <div class="job-requirements">
                <div class="requirement-item">
                  <span class="requirement-label">经验要求：</span>
                  <span class="requirement-value">{{ job.experience }}</span>
                </div>
                <div class="requirement-item">
                  <span class="requirement-label">学历要求：</span>
                  <span class="requirement-value">{{ job.education }}</span>
                </div>
                <div class="requirement-item">
                  <span class="requirement-label">工作性质：</span>
                  <span class="requirement-value">{{ job.jobType }}</span>
                </div>
              </div>
              
              <div class="job-description">
                <p>{{ job.description }}</p>
              </div>
              
              <div class="job-skills">
                <span class="skills-label">技能要求：</span>
                <el-tag v-for="skill in job.skills" :key="skill" size="small">{{ skill }}</el-tag>
              </div>
            </div>
            
            <div class="job-footer">
              <div class="job-meta">
                <span class="publish-time">发布时间：{{ formatDate(job.publishTime) }}</span>
                <span class="apply-count">已有{{ job.applyCount }}人投递</span>
              </div>
              <el-button type="primary" size="small" @click.stop="applyJob(job.id)">立即申请</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
```

### 4. 优秀学生展示页 (`/industry/students`)

#### 4.1 页面布局
```vue
<template>
  <div class="students-page">
    <div class="content-container">
      <div class="page-header">
        <h1 class="page-title">优秀学生</h1>
        <div class="page-subtitle">榜样力量，激励前行</div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="filter-tabs">
          <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="outstanding">优秀毕业生</el-radio-button>
            <el-radio-button label="scholarship">奖学金获得者</el-radio-button>
            <el-radio-button label="competition">竞赛获奖者</el-radio-button>
            <el-radio-button label="innovation">创新创业</el-radio-button>
          </el-radio-group>
        </div>
        
        <div class="filter-options">
          <el-select v-model="selectedMajor" placeholder="专业筛选" @change="handleMajorChange">
            <el-option label="全部专业" value=""></el-option>
            <el-option label="计算机科学与技术" value="computer"></el-option>
            <el-option label="软件工程" value="software"></el-option>
            <el-option label="电子信息工程" value="electronic"></el-option>
            <el-option label="机械工程" value="mechanical"></el-option>
          </el-select>
          
          <el-select v-model="selectedYear" placeholder="毕业年份" @change="handleYearChange">
            <el-option label="全部年份" value=""></el-option>
            <el-option label="2024届" value="2024"></el-option>
            <el-option label="2023届" value="2023"></el-option>
            <el-option label="2022届" value="2022"></el-option>
            <el-option label="2021届" value="2021"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 学生列表 -->
      <div class="students-list-section">
        <div class="students-grid">
          <div class="student-card" v-for="student in studentList" :key="student.id" @click="goToStudentDetail(student.id)">
            <div class="student-photo">
              <img :src="student.photo" :alt="student.name" />
              <div class="student-category">{{ student.categoryName }}</div>
            </div>
            
            <div class="student-info">
              <h3 class="student-name">{{ student.name }}</h3>
              <p class="student-major">{{ student.major }} · {{ student.graduationYear }}届</p>
              <p class="student-company">
                <i class="el-icon-office-building"></i>
                {{ student.company }}
              </p>
              <p class="student-position">{{ student.position }}</p>
              
              <div class="student-achievements">
                <div class="achievement-item" v-for="achievement in student.achievements" :key="achievement.id">
                  <el-tag :type="getAchievementType(achievement.type)" size="small">
                    {{ achievement.title }}
                  </el-tag>
                </div>
              </div>
              
              <div class="student-stats">
                <div class="stat-item">
                  <span class="stat-label">GPA:</span>
                  <span class="stat-value">{{ student.gpa }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">项目经验:</span>
                  <span class="stat-value">{{ student.projectCount }}个</span>
                </div>
              </div>
            </div>
            
            <div class="student-footer">
              <div class="student-quote">
                <i class="el-icon-s-comment"></i>
                <span>"{{ student.quote }}"</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
```

## 模拟数据结构

### 轮播图数据
```javascript
const bannerData = {
  id: 1,
  title: '产教融合新突破',
  description: '与知名企业深度合作，打造实践教学新模式',
  image: 'https://via.placeholder.com/800x350',
  link: '/industry/cooperation/1',
  status: 'active',
  sort: 1
}
```

### 企业数据
```javascript
const enterpriseData = {
  id: 1,
  name: '华为技术有限公司',
  logo: 'https://via.placeholder.com/120x120',
  type: 'internet',
  typeName: '互联网',
  scale: 'large',
  scaleName: '大型企业',
  location: '深圳',
  foundYear: '1987',
  description: '全球领先的ICT（信息与通信）基础设施和智能终端提供商',
  tags: ['5G', '人工智能', '云计算'],
  cooperationYears: 5,
  studentCount: 128,
  projectCount: 15,
  website: 'https://www.huawei.com'
}
```

### 招聘数据
```javascript
const jobData = {
  id: 1,
  title: '前端开发工程师',
  companyId: 1,
  companyName: '华为技术有限公司',
  companyLogo: 'https://via.placeholder.com/60x60',
  category: 'development',
  categoryName: '技术开发',
  location: '深圳',
  salary: '15-25K',
  experience: '1-3年',
  education: '本科',
  jobType: '全职',
  description: '负责前端页面开发和用户体验优化',
  skills: ['Vue.js', 'React', 'JavaScript', 'CSS3'],
  urgent: true,
  hot: false,
  publishTime: '2024-01-15 10:00:00',
  applyCount: 25,
  requirements: [
    '计算机相关专业本科以上学历',
    '熟练掌握Vue.js、React等前端框架',
    '具有良好的代码规范和团队协作能力'
  ]
}
```

### 学生数据
```javascript
const studentData = {
  id: 1,
  name: '张三',
  photo: 'https://via.placeholder.com/120x120',
  major: '计算机科学与技术',
  graduationYear: '2023',
  company: '华为技术有限公司',
  position: '高级软件工程师',
  category: 'outstanding',
  categoryName: '优秀毕业生',
  gpa: '3.8',
  projectCount: 8,
  quote: '持续学习，追求卓越',
  achievements: [
    { id: 1, title: '国家奖学金', type: 'scholarship' },
    { id: 2, title: 'ACM金奖', type: 'competition' },
    { id: 3, title: '优秀毕业生', type: 'honor' }
  ],
  story: '在校期间积极参与各类竞赛和项目实践...'
}
```

## 开发任务清单

### 基础设施
- [ ] 创建产教融合模块路由配置
- [ ] 设置页面基础结构和样式
- [ ] 创建模拟数据文件

### 产教融合主页
- [ ] 轮播图组件开发
  - [ ] 轮播图展示
  - [ ] 点击跳转功能
  - [ ] 响应式适配
- [ ] 数据统计区域
  - [ ] 统计数字展示
  - [ ] 动画效果
  - [ ] 实时数据更新
- [ ] 合作企业展示
  - [ ] 企业卡片设计
  - [ ] 网格布局
  - [ ] 悬停效果
- [ ] 优秀学生展示
  - [ ] 学生卡片设计
  - [ ] 成就标签展示
  - [ ] 头像展示
- [ ] 招聘信息展示
  - [ ] 招聘列表设计
  - [ ] 职位信息展示
  - [ ] 状态标识

### 企业列表页
- [ ] 企业搜索功能
  - [ ] 关键词搜索
  - [ ] 企业类型筛选
  - [ ] 企业规模筛选
  - [ ] 地区筛选
- [ ] 企业列表展示
  - [ ] 企业卡片设计
  - [ ] 企业信息展示
  - [ ] 合作信息展示
- [ ] 企业详情页
  - [ ] 企业详细信息
  - [ ] 合作项目展示
  - [ ] 招聘信息展示

### 招聘列表页
- [ ] 职位搜索功能
  - [ ] 关键词搜索
  - [ ] 职位类别筛选
  - [ ] 经验要求筛选
  - [ ] 工作地点筛选
- [ ] 职位列表展示
  - [ ] 职位卡片设计
  - [ ] 职位信息展示
  - [ ] 申请状态管理
- [ ] 职位详情页
  - [ ] 职位详细描述
  - [ ] 公司信息展示
  - [ ] 申请功能

### 优秀学生页
- [ ] 学生筛选功能
  - [ ] 类别筛选
  - [ ] 专业筛选
  - [ ] 年份筛选
- [ ] 学生列表展示
  - [ ] 学生卡片设计
  - [ ] 成就展示
  - [ ] 就业信息展示
- [ ] 学生详情页
  - [ ] 学生详细信息
  - [ ] 成长故事展示
  - [ ] 项目作品展示

### 公共组件
- [ ] 统计数字组件
- [ ] 轮播图组件
- [ ] 企业卡片组件
- [ ] 学生卡片组件
- [ ] 职位卡片组件
- [ ] 筛选组件

### 功能完善
- [ ] 分享功能
- [ ] 收藏功能
- [ ] 申请功能
- [ ] 联系功能
- [ ] 导出功能

### 响应式和优化
- [ ] 移动端适配
- [ ] 图片懒加载
- [ ] 性能优化
- [ ] SEO优化

## 交互细节

### 动画效果
- 卡片悬停时的上升动画
- 轮播图的平滑过渡
- 筛选时的加载动画
- 数字统计的递增动画

### 用户体验
- 合理的加载状态提示
- 友好的错误处理
- 直观的筛选反馈
- 流畅的页面切换

### 视觉设计
- 统一的色彩搭配
- 合理的信息层级
- 清晰的视觉焦点
- 良好的可读性