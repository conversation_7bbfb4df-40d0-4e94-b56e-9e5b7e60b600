# 数据展示模块

## 模块概述

数据展示模块是基地运营数据的统一展示平台，通过各种图表和统计指标，直观地展示基地各个模块的运营情况和发展趋势。

## 功能需求

### 核心功能
- 实训基地整体数据概览
- 实训课程统计数据
- 产教融合数据展示
- 资源中心使用统计
- 数据图表可视化
- 实时数据更新

### 统计维度
- 数量统计（课程数、用户数、资源数等）
- 活跃度统计（浏览量、使用率等）
- 时间趋势分析
- 分类统计分析

## 页面结构

### 1. 数据展示主页 (`/statistics`)

#### 1.1 路由配置
```javascript
// router/index.js
{
  path: '/statistics',
  name: 'Statistics',
  component: () => import('@/views/statistics/index.vue'),
  meta: {
    title: '数据展示',
    requiresAuth: false
  }
}
```

#### 1.2 主页布局
```vue
<template>
  <div class="statistics-page">
    <div class="content-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">实训基地数据中心</h1>
        <div class="page-subtitle">全面展示基地运营数据和发展趋势</div>
        <div class="data-update-info">
          <span>数据更新时间：{{ lastUpdateTime }}</span>
          <el-button @click="refreshData" size="small" icon="el-icon-refresh">刷新数据</el-button>
        </div>
      </div>

      <!-- 总体数据概览 -->
      <div class="overview-section">
        <div class="overview-grid">
          <div class="overview-card" v-for="item in overviewData" :key="item.key">
            <div class="card-icon" :style="{ background: item.color }">
              <i :class="item.icon"></i>
            </div>
            <div class="card-content">
              <div class="card-title">{{ item.title }}</div>
              <div class="card-value">
                <span class="value">{{ item.value }}</span>
                <span class="unit">{{ item.unit }}</span>
              </div>
              <div class="card-trend" :class="item.trend.type">
                <i :class="getTrendIcon(item.trend.type)"></i>
                <span>{{ item.trend.value }}%</span>
                <span class="trend-text">{{ item.trend.text }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实训课程数据 -->
      <div class="course-section">
        <div class="section-header">
          <h2 class="section-title">实训课程数据</h2>
          <div class="section-actions">
            <el-date-picker
              v-model="courseDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              @change="updateCourseData"
            >
            </el-date-picker>
          </div>
        </div>
        
        <div class="course-stats-grid">
          <div class="stats-card">
            <div class="stats-header">
              <h3>课程统计</h3>
              <el-button @click="viewCourseDetail" type="text" size="small">详细 >></el-button>
            </div>
            <div class="stats-content">
              <div class="stat-item">
                <span class="stat-label">课程总数</span>
                <span class="stat-value">{{ courseStats.totalCourses }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">活跃课程</span>
                <span class="stat-value">{{ courseStats.activeCourses }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">实训人次</span>
                <span class="stat-value">{{ courseStats.totalParticipants }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">实训人数</span>
                <span class="stat-value">{{ courseStats.uniqueUsers }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">平均用时</span>
                <span class="stat-value">{{ courseStats.avgDuration }}分钟</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">完成率</span>
                <span class="stat-value">{{ courseStats.completionRate }}%</span>
              </div>
            </div>
          </div>
          
          <div class="chart-card">
            <div class="chart-header">
              <h3>课程使用趋势</h3>
              <div class="chart-controls">
                <el-radio-group v-model="courseChartType" size="small" @change="updateCourseChart">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="week">周</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-content">
              <div ref="courseChart" class="chart-container"></div>
            </div>
          </div>
        </div>
        
        <div class="course-category-section">
          <div class="category-chart-card">
            <div class="chart-header">
              <h3>课程分类统计</h3>
            </div>
            <div class="chart-content">
              <div ref="categoryChart" class="chart-container"></div>
            </div>
          </div>
          
          <div class="popular-courses-card">
            <div class="card-header">
              <h3>热门课程排行</h3>
            </div>
            <div class="card-content">
              <div class="course-rank-list">
                <div class="rank-item" v-for="(course, index) in popularCourses" :key="course.id">
                  <div class="rank-number" :class="getRankClass(index)">{{ index + 1 }}</div>
                  <div class="course-info">
                    <div class="course-name">{{ course.name }}</div>
                    <div class="course-stats">
                      <span>{{ course.participants }}人参与</span>
                      <span>{{ course.avgScore }}分</span>
                    </div>
                  </div>
                  <div class="course-trend">
                    <i :class="getTrendIcon(course.trend)"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 产教融合数据 -->
      <div class="industry-section">
        <div class="section-header">
          <h2 class="section-title">产教融合数据</h2>
        </div>
        
        <div class="industry-stats-grid">
          <div class="industry-overview-card">
            <div class="card-header">
              <h3>产教融合概览</h3>
            </div>
            <div class="card-content">
              <div class="overview-stats">
                <div class="overview-item">
                  <div class="item-icon">
                    <i class="el-icon-office-building"></i>
                  </div>
                  <div class="item-content">
                    <div class="item-value">{{ industryStats.enterpriseCount }}</div>
                    <div class="item-label">合作企业</div>
                  </div>
                </div>
                
                <div class="overview-item">
                  <div class="item-icon">
                    <i class="el-icon-user"></i>
                  </div>
                  <div class="item-content">
                    <div class="item-value">{{ industryStats.studentCount }}</div>
                    <div class="item-label">优秀学生</div>
                  </div>
                </div>
                
                <div class="overview-item">
                  <div class="item-icon">
                    <i class="el-icon-s-order"></i>
                  </div>
                  <div class="item-content">
                    <div class="item-value">{{ industryStats.jobCount }}</div>
                    <div class="item-label">招聘岗位</div>
                  </div>
                </div>
                
                <div class="overview-item">
                  <div class="item-icon">
                    <i class="el-icon-connection"></i>
                  </div>
                  <div class="item-content">
                    <div class="item-value">{{ industryStats.projectCount }}</div>
                    <div class="item-label">合作项目</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="industry-chart-card">
            <div class="chart-header">
              <h3>企业类型分布</h3>
            </div>
            <div class="chart-content">
              <div ref="enterpriseChart" class="chart-container"></div>
            </div>
          </div>
        </div>
        
        <div class="industry-trend-section">
          <div class="trend-chart-card">
            <div class="chart-header">
              <h3>产教融合发展趋势</h3>
              <div class="chart-controls">
                <el-select v-model="industryTrendType" size="small" @change="updateIndustryTrend">
                  <el-option label="企业合作" value="enterprise"></el-option>
                  <el-option label="学生就业" value="employment"></el-option>
                  <el-option label="项目合作" value="project"></el-option>
                </el-select>
              </div>
            </div>
            <div class="chart-content">
              <div ref="industryTrendChart" class="chart-container"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源中心数据 -->
      <div class="resource-section">
        <div class="section-header">
          <h2 class="section-title">资源中心数据</h2>
        </div>
        
        <div class="resource-stats-grid">
          <div class="resource-overview-card">
            <div class="card-header">
              <h3>资源统计</h3>
            </div>
            <div class="card-content">
              <div class="resource-stats">
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-document"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ resourceStats.totalResources }}</div>
                    <div class="stat-label">总资源数</div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-view"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ resourceStats.totalViews }}</div>
                    <div class="stat-label">总浏览量</div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-download"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ resourceStats.totalDownloads }}</div>
                    <div class="stat-label">总下载量</div>
                  </div>
                </div>
                
                <div class="stat-item">
                  <div class="stat-icon">
                    <i class="el-icon-star-on"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-value">{{ resourceStats.totalFavorites }}</div>
                    <div class="stat-label">总收藏量</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="resource-type-card">
            <div class="chart-header">
              <h3>资源类型分布</h3>
            </div>
            <div class="chart-content">
              <div ref="resourceTypeChart" class="chart-container"></div>
            </div>
          </div>
        </div>
        
        <div class="resource-usage-section">
          <div class="usage-chart-card">
            <div class="chart-header">
              <h3>资源使用趋势</h3>
              <div class="chart-controls">
                <el-radio-group v-model="resourceUsageType" size="small" @change="updateResourceUsage">
                  <el-radio-button label="views">浏览量</el-radio-button>
                  <el-radio-button label="downloads">下载量</el-radio-button>
                  <el-radio-button label="favorites">收藏量</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-content">
              <div ref="resourceUsageChart" class="chart-container"></div>
            </div>
          </div>
          
          <div class="popular-resources-card">
            <div class="card-header">
              <h3>热门资源排行</h3>
            </div>
            <div class="card-content">
              <div class="resource-rank-list">
                <div class="rank-item" v-for="(resource, index) in popularResources" :key="resource.id">
                  <div class="rank-number" :class="getRankClass(index)">{{ index + 1 }}</div>
                  <div class="resource-info">
                    <div class="resource-name">{{ resource.name }}</div>
                    <div class="resource-stats">
                      <span>{{ resource.views }}次浏览</span>
                      <span>{{ resource.downloads }}次下载</span>
                    </div>
                  </div>
                  <div class="resource-type">{{ resource.typeName }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 综合数据分析 -->
      <div class="analysis-section">
        <div class="section-header">
          <h2 class="section-title">综合数据分析</h2>
        </div>
        
        <div class="analysis-grid">
          <div class="user-activity-card">
            <div class="chart-header">
              <h3>用户活跃度分析</h3>
            </div>
            <div class="chart-content">
              <div ref="userActivityChart" class="chart-container"></div>
            </div>
          </div>
          
          <div class="module-usage-card">
            <div class="chart-header">
              <h3>模块使用情况</h3>
            </div>
            <div class="chart-content">
              <div ref="moduleUsageChart" class="chart-container"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 1.3 样式设计
```less
<style lang="less" scoped>
.statistics-page {
  background: #f5f7fa;
  min-height: 100vh;
  padding: 30px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    .page-title {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .page-subtitle {
      font-size: 16px;
      color: #666;
      margin-bottom: 20px;
    }
    
    .data-update-info {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 15px;
      font-size: 14px;
      color: #999;
    }
  }
  
  .overview-section {
    margin-bottom: 40px;
    
    .overview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
      
      .overview-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .card-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          
          i {
            font-size: 24px;
            color: white;
          }
        }
        
        .card-content {
          flex: 1;
          
          .card-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }
          
          .card-value {
            display: flex;
            align-items: baseline;
            margin-bottom: 8px;
            
            .value {
              font-size: 28px;
              font-weight: bold;
              color: #333;
            }
            
            .unit {
              font-size: 14px;
              color: #666;
              margin-left: 5px;
            }
          }
          
          .card-trend {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            
            &.up {
              color: #67c23a;
            }
            
            &.down {
              color: #f56c6c;
            }
            
            &.stable {
              color: #909399;
            }
          }
        }
      }
    }
  }
  
  .course-section, .industry-section, .resource-section, .analysis-section {
    margin-bottom: 40px;
    
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
      
      .section-actions {
        display: flex;
        gap: 15px;
      }
    }
  }
  
  .course-stats-grid, .industry-stats-grid, .resource-stats-grid, .analysis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }
  
  .stats-card, .chart-card, .industry-overview-card, .resource-overview-card, .user-activity-card, .module-usage-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .stats-header, .chart-header, .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        font-size: 18px;
        color: #333;
        margin: 0;
      }
      
      .chart-controls {
        display: flex;
        gap: 10px;
      }
    }
    
    .stats-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      
      .stat-item {
        text-align: center;
        
        .stat-label {
          display: block;
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
        
        .stat-value {
          font-size: 20px;
          font-weight: bold;
          color: #4093f9;
        }
      }
    }
    
    .chart-content {
      .chart-container {
        height: 300px;
        width: 100%;
      }
    }
  }
  
  .course-category-section, .industry-trend-section, .resource-usage-section {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    
    .category-chart-card, .trend-chart-card, .usage-chart-card {
      background: white;
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          font-size: 18px;
          color: #333;
          margin: 0;
        }
      }
      
      .chart-content {
        .chart-container {
          height: 350px;
          width: 100%;
        }
      }
    }
    
    .popular-courses-card, .popular-resources-card {
      background: white;
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .card-header {
        margin-bottom: 20px;
        
        h3 {
          font-size: 18px;
          color: #333;
          margin: 0;
        }
      }
      
      .card-content {
        .course-rank-list, .resource-rank-list {
          .rank-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f0f2f5;
            
            &:last-child {
              border-bottom: none;
            }
            
            .rank-number {
              width: 30px;
              height: 30px;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: bold;
              color: white;
              margin-right: 15px;
              
              &.top1 {
                background: #ff6b6b;
              }
              
              &.top2 {
                background: #4ecdc4;
              }
              
              &.top3 {
                background: #45b7d1;
              }
              
              &.normal {
                background: #999;
              }
            }
            
            .course-info, .resource-info {
              flex: 1;
              
              .course-name, .resource-name {
                font-size: 14px;
                color: #333;
                margin-bottom: 5px;
              }
              
              .course-stats, .resource-stats {
                font-size: 12px;
                color: #666;
                display: flex;
                gap: 15px;
              }
            }
            
            .course-trend {
              font-size: 16px;
              
              &.up {
                color: #67c23a;
              }
              
              &.down {
                color: #f56c6c;
              }
            }
            
            .resource-type {
              font-size: 12px;
              color: #666;
              background: #f0f2f5;
              padding: 2px 8px;
              border-radius: 4px;
            }
          }
        }
      }
    }
  }
  
  .industry-overview-card, .resource-overview-card {
    .overview-stats, .resource-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 20px;
      
      .overview-item, .stat-item {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .item-icon, .stat-icon {
          width: 50px;
          height: 50px;
          border-radius: 50%;
          background: linear-gradient(135deg, #4093f9, #2c5aa0);
          display: flex;
          align-items: center;
          justify-content: center;
          
          i {
            font-size: 20px;
            color: white;
          }
        }
        
        .item-content, .stat-content {
          .item-value, .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
          }
          
          .item-label, .stat-label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .statistics-page {
    .course-category-section, .industry-trend-section, .resource-usage-section {
      grid-template-columns: 1fr;
    }
  }
}

@media (max-width: 768px) {
  .statistics-page {
    padding: 20px 0;
    
    .page-header {
      .page-title {
        font-size: 24px;
      }
      
      .data-update-info {
        flex-direction: column;
        gap: 10px;
      }
    }
    
    .overview-section {
      .overview-grid {
        grid-template-columns: 1fr;
      }
    }
    
    .course-stats-grid, .industry-stats-grid, .resource-stats-grid, .analysis-grid {
      grid-template-columns: 1fr;
    }
    
    .stats-content {
      grid-template-columns: repeat(2, 1fr);
    }
    
    .chart-content {
      .chart-container {
        height: 250px;
      }
    }
  }
}
</style>
```

### 2. 图表组件实现

#### 2.1 ECharts 图表组件
```javascript
// 图表组件的实现
export default {
  name: 'StatisticsPage',
  
  data() {
    return {
      // 图表实例
      courseChart: null,
      categoryChart: null,
      enterpriseChart: null,
      industryTrendChart: null,
      resourceTypeChart: null,
      resourceUsageChart: null,
      userActivityChart: null,
      moduleUsageChart: null,
      
      // 图表配置
      courseChartType: 'week',
      industryTrendType: 'enterprise',
      resourceUsageType: 'views',
      
      // 数据
      overviewData: [],
      courseStats: {},
      industryStats: {},
      resourceStats: {},
      popularCourses: [],
      popularResources: [],
      
      // 时间范围
      courseDateRange: null,
      lastUpdateTime: ''
    }
  },
  
  mounted() {
    this.initCharts()
    this.loadData()
  },
  
  methods: {
    // 初始化图表
    initCharts() {
      this.courseChart = this.$echarts.init(this.$refs.courseChart)
      this.categoryChart = this.$echarts.init(this.$refs.categoryChart)
      this.enterpriseChart = this.$echarts.init(this.$refs.enterpriseChart)
      this.industryTrendChart = this.$echarts.init(this.$refs.industryTrendChart)
      this.resourceTypeChart = this.$echarts.init(this.$refs.resourceTypeChart)
      this.resourceUsageChart = this.$echarts.init(this.$refs.resourceUsageChart)
      this.userActivityChart = this.$echarts.init(this.$refs.userActivityChart)
      this.moduleUsageChart = this.$echarts.init(this.$refs.moduleUsageChart)
      
      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },
    
    // 处理窗口大小变化
    handleResize() {
      this.courseChart?.resize()
      this.categoryChart?.resize()
      this.enterpriseChart?.resize()
      this.industryTrendChart?.resize()
      this.resourceTypeChart?.resize()
      this.resourceUsageChart?.resize()
      this.userActivityChart?.resize()
      this.moduleUsageChart?.resize()
    },
    
    // 加载数据
    async loadData() {
      try {
        await Promise.all([
          this.loadOverviewData(),
          this.loadCourseData(),
          this.loadIndustryData(),
          this.loadResourceData()
        ])
        
        this.updateAllCharts()
        this.lastUpdateTime = this.formatDate(new Date())
      } catch (error) {
        this.$message.error('数据加载失败')
      }
    },
    
    // 更新所有图表
    updateAllCharts() {
      this.updateCourseChart()
      this.updateCategoryChart()
      this.updateEnterpriseChart()
      this.updateIndustryTrendChart()
      this.updateResourceTypeChart()
      this.updateResourceUsageChart()
      this.updateUserActivityChart()
      this.updateModuleUsageChart()
    },
    
    // 课程使用趋势图
    updateCourseChart() {
      const option = {
        title: {
          text: '课程使用趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          }
        },
        legend: {
          data: ['实训人次', '完成人次'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: this.getCourseChartXData()
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '实训人次',
            type: 'line',
            stack: '总量',
            data: this.getCourseParticipantsData(),
            smooth: true,
            lineStyle: {
              color: '#4093f9'
            },
            areaStyle: {
              color: 'rgba(64, 147, 249, 0.1)'
            }
          },
          {
            name: '完成人次',
            type: 'line',
            stack: '总量',
            data: this.getCourseCompletionsData(),
            smooth: true,
            lineStyle: {
              color: '#67c23a'
            },
            areaStyle: {
              color: 'rgba(103, 194, 58, 0.1)'
            }
          }
        ]
      }
      
      this.courseChart.setOption(option)
    },
    
    // 课程分类统计图
    updateCategoryChart() {
      const option = {
        title: {
          text: '课程分类统计',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle'
        },
        series: [
          {
            name: '课程数量',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: this.getCategoryChartData()
          }
        ]
      }
      
      this.categoryChart.setOption(option)
    },
    
    // 企业类型分布图
    updateEnterpriseChart() {
      const option = {
        title: {
          text: '企业类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          top: 'middle'
        },
        series: [
          {
            name: '企业数量',
            type: 'pie',
            radius: '50%',
            data: this.getEnterpriseChartData(),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
      
      this.enterpriseChart.setOption(option)
    },
    
    // 资源类型分布图
    updateResourceTypeChart() {
      const option = {
        title: {
          text: '资源类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item'
        },
        legend: {
          bottom: 10,
          left: 'center'
        },
        series: [
          {
            type: 'pie',
            radius: ['20%', '50%'],
            center: ['50%', '50%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 5
            },
            data: this.getResourceTypeChartData()
          }
        ]
      }
      
      this.resourceTypeChart.setOption(option)
    },
    
    // 用户活跃度图
    updateUserActivityChart() {
      const option = {
        title: {
          text: '用户活跃度分析',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['日活跃', '周活跃', '月活跃'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: this.getUserActivityXData()
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '日活跃',
            type: 'bar',
            data: this.getDailyActiveData(),
            itemStyle: {
              color: '#4093f9'
            }
          },
          {
            name: '周活跃',
            type: 'bar',
            data: this.getWeeklyActiveData(),
            itemStyle: {
              color: '#67c23a'
            }
          },
          {
            name: '月活跃',
            type: 'bar',
            data: this.getMonthlyActiveData(),
            itemStyle: {
              color: '#e6a23c'
            }
          }
        ]
      }
      
      this.userActivityChart.setOption(option)
    },
    
    // 模块使用情况图
    updateModuleUsageChart() {
      const option = {
        title: {
          text: '模块使用情况',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          type: 'category',
          data: ['实训课程', '资源中心', '产教融合', '考试系统', '新闻通知']
        },
        series: [
          {
            type: 'bar',
            data: this.getModuleUsageData(),
            itemStyle: {
              color: function(params) {
                const colors = ['#4093f9', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
                return colors[params.dataIndex]
              }
            }
          }
        ]
      }
      
      this.moduleUsageChart.setOption(option)
    },
    
    // 获取模拟数据的方法
    getMockOverviewData() {
      return [
        {
          key: 'courses',
          title: '实训课程',
          value: 156,
          unit: '个',
          icon: 'el-icon-notebook-2',
          color: 'linear-gradient(135deg, #4093f9, #2c5aa0)',
          trend: { type: 'up', value: 12, text: '较上月' }
        },
        {
          key: 'users',
          title: '注册用户',
          value: 2340,
          unit: '人',
          icon: 'el-icon-user',
          color: 'linear-gradient(135deg, #67c23a, #5daf34)',
          trend: { type: 'up', value: 8, text: '较上月' }
        },
        {
          key: 'resources',
          title: '教学资源',
          value: 892,
          unit: '个',
          icon: 'el-icon-document',
          color: 'linear-gradient(135deg, #e6a23c, #cf9236)',
          trend: { type: 'up', value: 5, text: '较上月' }
        },
        {
          key: 'enterprises',
          title: '合作企业',
          value: 48,
          unit: '家',
          icon: 'el-icon-office-building',
          color: 'linear-gradient(135deg, #f56c6c, #dd6161)',
          trend: { type: 'up', value: 3, text: '较上月' }
        }
      ]
    },
    
    // 其他辅助方法
    getTrendIcon(type) {
      switch(type) {
        case 'up': return 'el-icon-top'
        case 'down': return 'el-icon-bottom'
        default: return 'el-icon-minus'
      }
    },
    
    getRankClass(index) {
      if (index === 0) return 'top1'
      if (index === 1) return 'top2'
      if (index === 2) return 'top3'
      return 'normal'
    },
    
    formatDate(date) {
      return this.$utils.formatDate(date, 'YYYY-MM-DD HH:mm:ss')
    }
  },
  
  beforeDestroy() {
    // 清理图表实例
    this.courseChart?.dispose()
    this.categoryChart?.dispose()
    this.enterpriseChart?.dispose()
    this.industryTrendChart?.dispose()
    this.resourceTypeChart?.dispose()
    this.resourceUsageChart?.dispose()
    this.userActivityChart?.dispose()
    this.moduleUsageChart?.dispose()
    
    // 移除事件监听
    window.removeEventListener('resize', this.handleResize)
  }
}
```

## 模拟数据结构

### 统计数据结构
```javascript
const statisticsData = {
  overview: {
    totalCourses: 156,
    totalUsers: 2340,
    totalResources: 892,
    totalEnterprises: 48,
    totalViews: 15680,
    totalDownloads: 8920,
    activeUsers: 1850,
    completionRate: 85.5
  },
  
  courseStats: {
    totalCourses: 156,
    activeCourses: 142,
    totalParticipants: 5680,
    uniqueUsers: 2340,
    avgDuration: 45,
    completionRate: 85.5,
    avgScore: 82.3,
    passRate: 78.9
  },
  
  industryStats: {
    enterpriseCount: 48,
    studentCount: 320,
    jobCount: 156,
    projectCount: 28,
    cooperationYears: 5,
    employmentRate: 92.5
  },
  
  resourceStats: {
    totalResources: 892,
    totalViews: 15680,
    totalDownloads: 8920,
    totalFavorites: 3450,
    avgRating: 4.2,
    updateFrequency: 15
  }
}
```

## 开发任务清单

### 基础设施
- [ ] 创建数据展示路由配置
- [ ] 引入ECharts图表库
- [ ] 设置页面基础结构
- [ ] 创建模拟数据文件

### 总体数据概览
- [ ] 概览卡片组件开发
  - [ ] 数据卡片设计
  - [ ] 趋势指标显示
  - [ ] 动画效果实现
- [ ] 实时数据更新
  - [ ] 数据刷新机制
  - [ ] 更新时间显示
  - [ ] 加载状态处理

### 实训课程数据
- [ ] 课程统计展示
  - [ ] 基础数据统计
  - [ ] 课程使用趋势图
  - [ ] 分类统计图表
- [ ] 热门课程排行
  - [ ] 排行榜组件
  - [ ] 趋势指标显示
  - [ ] 详情跳转功能
- [ ] 时间筛选功能
  - [ ] 日期范围选择
  - [ ] 数据动态更新
  - [ ] 图表重新渲染

### 产教融合数据
- [ ] 企业合作统计
  - [ ] 企业数量统计
  - [ ] 企业类型分布
  - [ ] 合作项目展示
- [ ] 学生就业数据
  - [ ] 就业率统计
  - [ ] 就业质量分析
  - [ ] 优秀学生展示
- [ ] 发展趋势分析
  - [ ] 多维度趋势图
  - [ ] 同比环比分析
  - [ ] 预测分析

### 资源中心数据
- [ ] 资源使用统计
  - [ ] 资源数量统计
  - [ ] 使用情况分析
  - [ ] 资源质量评估
- [ ] 资源类型分布
  - [ ] 饼图展示
  - [ ] 类型统计
  - [ ] 使用率分析
- [ ] 热门资源排行
  - [ ] 浏览量排行
  - [ ] 下载量排行
  - [ ] 收藏量排行

### 综合数据分析
- [ ] 用户活跃度分析
  - [ ] 活跃用户统计
  - [ ] 活跃度趋势
  - [ ] 用户行为分析
- [ ] 模块使用情况
  - [ ] 各模块使用量
  - [ ] 使用偏好分析
  - [ ] 功能热点分析

### 图表组件开发
- [ ] 折线图组件
  - [ ] 趋势展示
  - [ ] 多系列支持
  - [ ] 交互功能
- [ ] 柱状图组件
  - [ ] 数据对比
  - [ ] 分组显示
  - [ ] 动画效果
- [ ] 饼图组件
  - [ ] 比例展示
  - [ ] 环形图支持
  - [ ] 标签显示
- [ ] 仪表盘组件
  - [ ] 进度显示
  - [ ] 阈值设置
  - [ ] 颜色渐变

### 交互功能
- [ ] 图表交互
  - [ ] 点击事件
  - [ ] 悬停提示
  - [ ] 缩放功能
- [ ] 数据筛选
  - [ ] 时间范围筛选
  - [ ] 条件筛选
  - [ ] 快速筛选
- [ ] 数据导出
  - [ ] 图表导出
  - [ ] 数据导出
  - [ ] 报告生成

### 性能优化
- [ ] 图表性能优化
  - [ ] 数据采样
  - [ ] 懒加载
  - [ ] 内存管理
- [ ] 数据加载优化
  - [ ] 异步加载
  - [ ] 缓存策略
  - [ ] 增量更新

### 响应式设计
- [ ] 移动端适配
  - [ ] 图表响应式
  - [ ] 触摸支持
  - [ ] 手势操作
- [ ] 多屏幕适配
  - [ ] 大屏展示
  - [ ] 平板适配
  - [ ] 小屏优化

## 技术要点

### ECharts配置
- 图表主题定制
- 响应式配置
- 交互事件处理
- 性能优化设置

### 数据处理
- 数据格式化
- 数据聚合计算
- 实时数据更新
- 异常数据处理

### 用户体验
- 加载状态优化
- 错误处理机制
- 操作引导设计
- 视觉反馈优化