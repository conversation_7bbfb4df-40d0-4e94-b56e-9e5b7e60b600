# UI设计规范

## 色彩体系

### 主色调
```less
@main-color: #4093f9;     // 主题蓝色
@text-color: #333;        // 主要文字色
@text-secondary: #666;    // 次要文字色
@text-placeholder: #888;  // 占位符文字色
@border-color: #cecece;   // 边框色
@bg-color: #f2f2f2;       // 页面背景色
@white: #ffffff;          // 卡片背景色
```

### 状态色
```less
@success-color: #67c23a;  // 成功状态
@warning-color: #e6a23c;  // 警告状态
@danger-color: #f56c6c;   // 危险状态
@info-color: #909399;     // 信息状态
```

## 字体规范

### 字体家族
```css
font-family: "Segoe UI", "PingFangSC-Regular", "PingFang SC", "Microsoft YaHei", sans-serif;
```

### 字号规范
- **大标题**: 28px (页面主标题)
- **中标题**: 20px-24px (模块标题)
- **小标题**: 16px-18px (内容标题)
- **正文**: 14px (主要内容)
- **辅助文字**: 12px (次要信息)

### 字重规范
- **标题**: font-weight: bold (700)
- **正文**: font-weight: normal (400)
- **强调**: font-weight: 500

## 间距规范

### 页面间距
```less
@padding-large: 30px;     // 大间距
@padding-medium: 20px;    // 中间距
@padding-small: 15px;     // 小间距
@padding-mini: 10px;      // 微间距
```

### 组件间距
```less
@margin-large: 25px;      // 组件大间距
@margin-medium: 15px;     // 组件中间距
@margin-small: 10px;      // 组件小间距
@margin-mini: 5px;        // 组件微间距
```

## 布局规范

### 页面容器
```css
.content-container {
    width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

/* 响应式适配 */
@media (max-width: 1240px) {
    .content-container {
        width: 100%;
        padding: 0 20px;
    }
}
```

### Flex布局工具类
```css
.flex { display: flex; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; align-items: center; }
.flex-start { justify-content: flex-start; align-items: center; }
.flex-end { justify-content: flex-end; align-items: center; }
.flex-around { justify-content: space-around; align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.flex-column { flex-direction: column; }
.flex-1 { flex: 1; }
```

## 通用组件样式

### 页面标题
```vue
<template>
  <div class="page-header">
    <h1 class="page-title">{{ title }}</h1>
    <div class="page-subtitle" v-if="subtitle">{{ subtitle }}</div>
  </div>
</template>

<style lang="less" scoped>
.page-header {
  margin-bottom: 30px;
  text-align: center;
  
  .page-title {
    font-size: 28px;
    font-weight: bold;
    color: @text-color;
    margin-bottom: 10px;
  }
  
  .page-subtitle {
    font-size: 14px;
    color: @text-secondary;
  }
}
</style>
```

### 卡片组件
```vue
<template>
  <el-card class="content-card" :body-style="{ padding: '20px' }">
    <div slot="header" class="card-header" v-if="title">
      <span class="card-title">{{ title }}</span>
      <div class="card-extra" v-if="$slots.extra">
        <slot name="extra"></slot>
      </div>
    </div>
    <slot></slot>
  </el-card>
</template>

<style lang="less" scoped>
.content-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      font-size: 16px;
      font-weight: bold;
      color: @text-color;
    }
  }
}
</style>
```

### 列表项卡片
```vue
<template>
  <el-card class="list-card" :body-style="{ padding: '15px' }">
    <div class="list-item-header">
      <div class="item-title">{{ title }}</div>
      <div class="item-time">{{ time }}</div>
    </div>
    <div class="list-item-content">
      <p class="item-summary">{{ summary }}</p>
    </div>
    <div class="list-item-footer">
      <div class="item-tags">
        <el-tag v-for="tag in tags" :key="tag" size="small">{{ tag }}</el-tag>
      </div>
      <div class="item-stats">
        <span class="stat-item">
          <i class="el-icon-view"></i>
          {{ views }}
        </span>
        <span class="stat-item">
          <i class="el-icon-star-off"></i>
          {{ likes }}
        </span>
      </div>
    </div>
  </el-card>
</template>

<style lang="less" scoped>
.list-card {
  margin-bottom: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .list-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    .item-title {
      font-size: 16px;
      font-weight: bold;
      color: @text-color;
    }
    
    .item-time {
      font-size: 12px;
      color: @text-secondary;
    }
  }
  
  .list-item-content {
    margin-bottom: 15px;
    
    .item-summary {
      font-size: 14px;
      color: @text-secondary;
      line-height: 1.5;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
  
  .list-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .item-tags {
      .el-tag {
        margin-right: 5px;
      }
    }
    
    .item-stats {
      .stat-item {
        margin-left: 15px;
        font-size: 12px;
        color: @text-secondary;
        
        i {
          margin-right: 3px;
        }
      }
    }
  }
}
</style>
```

### 搜索框组件
```vue
<template>
  <div class="search-container">
    <el-input
      v-model="searchKeyword"
      placeholder="请输入搜索关键词"
      class="search-input"
      @keyup.enter.native="handleSearch"
    >
      <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
    </el-input>
  </div>
</template>

<style lang="less" scoped>
.search-container {
  margin-bottom: 20px;
  
  .search-input {
    max-width: 400px;
    
    /deep/ .el-input__inner {
      border-radius: 20px 0 0 20px;
    }
    
    /deep/ .el-input-group__append {
      border-radius: 0 20px 20px 0;
      border-left: none;
      
      .el-button {
        border-radius: 0 20px 20px 0;
      }
    }
  }
}
</style>
```

### 分页组件
```vue
<template>
  <div class="pagination-container">
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
    >
    </el-pagination>
  </div>
</template>

<style lang="less" scoped>
.pagination-container {
  text-align: center;
  margin-top: 30px;
  
  /deep/ .el-pagination {
    .el-pagination__total {
      color: @text-secondary;
    }
    
    .el-pager li {
      &.active {
        color: @main-color;
      }
    }
    
    .btn-next, .btn-prev {
      &:hover {
        color: @main-color;
      }
    }
  }
}
</style>
```

## 响应式设计

### 断点定义
```less
// 媒体查询断点
@screen-xs: 480px;
@screen-sm: 768px;
@screen-md: 1024px;
@screen-lg: 1200px;
@screen-xl: 1440px;
```

### 响应式工具类
```css
/* 显示/隐藏 */
@media (max-width: 768px) {
  .hidden-mobile { display: none !important; }
}

@media (min-width: 769px) {
  .hidden-desktop { display: none !important; }
}

/* 文字大小调整 */
@media (max-width: 768px) {
  .responsive-text {
    font-size: 14px;
  }
}

/* 间距调整 */
@media (max-width: 768px) {
  .responsive-padding {
    padding: 10px;
  }
}
```

## 动画效果

### 过渡动画
```css
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: all 0.3s ease;
}
.slide-enter, .slide-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
```

### 悬停效果
```css
.hover-effect {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
```

## 图标使用

### Element UI 图标
```html
<!-- 常用图标 -->
<i class="el-icon-view"></i>          <!-- 查看 -->
<i class="el-icon-star-off"></i>      <!-- 收藏 -->
<i class="el-icon-download"></i>      <!-- 下载 -->
<i class="el-icon-share"></i>         <!-- 分享 -->
<i class="el-icon-time"></i>          <!-- 时间 -->
<i class="el-icon-user"></i>          <!-- 用户 -->
<i class="el-icon-document"></i>      <!-- 文档 -->
<i class="el-icon-picture"></i>       <!-- 图片 -->
<i class="el-icon-video-play"></i>    <!-- 视频 -->
<i class="el-icon-headset"></i>       <!-- 音频 -->
```

### 自定义图标
```css
.custom-icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  vertical-align: middle;
}

.icon-news {
  background-image: url('assets/icons/news.svg');
}

.icon-industry {
  background-image: url('assets/icons/industry.svg');
}
```

## 样式调试

### 开发环境样式
```css
/* 仅在开发环境显示 */
.dev-only {
  border: 1px dashed #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
}

/* 占位符样式 */
.placeholder {
  background-color: #f5f5f5;
  border: 1px dashed #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}
```