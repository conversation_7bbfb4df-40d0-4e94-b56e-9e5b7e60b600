# 资源中心模块

## 模块概述

资源中心模块是基地教学资源的统一管理和展示平台，支持多种类型的教学资源（文本、文档、图片、音频、视频等）的上传、分类、搜索、预览和收藏功能。

office和rar压缩包提供下载功能，其它可以在线预览的只能预览，不提供下载功能！

要注意资源可以属于原有系统的某个课程，所有有courseId关联。但是也可以不属于某个课程

## 功能需求

### 核心功能
- 资源分类浏览
- 资源搜索和筛选
- 资源在线预览
- 资源收藏功能
- 资源下载功能
- 资源统计展示

### 资源类型支持
- 文档类：PDF、Word、PPT、Excel
- 图片类：PNG、JPG、GIF、SVG
- 音频类：MP3、WAV、AAC
- 视频类：MP4、AVI、MOV
- 压缩包：ZIP、RAR

## 页面结构

### 1. 资源中心主页 (`/resource`)

#### 1.1 路由配置
```javascript
// router/index.js
{
  path: '/resource',
  name: 'ResourceCenter',
  component: () => import('@/views/resource/index.vue'),
  meta: {
    title: '资源中心',
    requiresAuth: false
  }
},
{
  path: '/resource/category/:categoryId',
  name: 'ResourceCategory',
  component: () => import('@/views/resource/category.vue'),
  meta: {
    title: '资源分类',
    requiresAuth: false
  }
},
{
  path: '/resource/detail/:id',
  name: 'ResourceDetail',
  component: () => import('@/views/resource/detail.vue'),
  meta: {
    title: '资源详情',
    requiresAuth: false
  }
},
{
  path: '/resource/search',
  name: 'ResourceSearch',
  component: () => import('@/views/resource/search.vue'),
  meta: {
    title: '资源搜索',
    requiresAuth: false
  }
}
```

#### 1.2 主页布局
```vue
<template>
  <div class="resource-center-page">
    <div class="content-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">资源中心</h1>
        <div class="page-subtitle">丰富的教学资源，助力学习成长</div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索资源名称、关键词或描述"
            class="search-input"
            size="large"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
          
          <div class="search-suggestions" v-if="searchSuggestions.length > 0">
            <div class="suggestion-title">热门搜索</div>
            <div class="suggestion-tags">
              <el-tag 
                v-for="tag in searchSuggestions" 
                :key="tag"
                @click="searchByTag(tag)"
                class="suggestion-tag"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源统计 -->
      <div class="stats-section">
        <div class="stats-container">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalResources }}</div>
              <div class="stat-label">总资源数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-view"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalViews }}</div>
              <div class="stat-label">总浏览量</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalDownloads }}</div>
              <div class="stat-label">总下载量</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalFavorites }}</div>
              <div class="stat-label">总收藏量</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源分类 -->
      <div class="category-section">
        <div class="section-header">
          <h2 class="section-title">资源分类</h2>
          <div class="section-subtitle">按类型快速查找所需资源</div>
        </div>
        
        <div class="category-grid">
          <div class="category-card" v-for="category in categoryList" :key="category.id" @click="goToCategory(category.id)">
            <div class="category-icon">
              <i :class="category.icon"></i>
            </div>
            <div class="category-content">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-description">{{ category.description }}</p>
              <div class="category-stats">
                <span class="resource-count">{{ category.resourceCount }}个资源</span>
                <span class="view-count">{{ category.viewCount }}次浏览</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 推荐资源 -->
      <div class="featured-section">
        <div class="section-header">
          <h2 class="section-title">推荐资源</h2>
          <div class="section-subtitle">精选优质资源推荐</div>
        </div>
        
        <div class="featured-grid">
          <div class="resource-card" v-for="resource in featuredResources" :key="resource.id" @click="goToDetail(resource.id)">
            <div class="resource-thumbnail">
              <img :src="resource.thumbnail" :alt="resource.title" />
              <div class="resource-type">
                <i :class="getResourceTypeIcon(resource.type)"></i>
                {{ resource.typeName }}
              </div>
            </div>
            
            <div class="resource-content">
              <h3 class="resource-title">{{ resource.title }}</h3>
              <p class="resource-description">{{ resource.description }}</p>
              
              <div class="resource-meta">
                <span class="resource-author">
                  <i class="el-icon-user"></i>
                  {{ resource.author }}
                </span>
                <span class="resource-date">
                  <i class="el-icon-time"></i>
                  {{ formatDate(resource.uploadTime) }}
                </span>
              </div>
              
              <div class="resource-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ resource.views }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-download"></i>
                  {{ resource.downloads }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-star-off"></i>
                  {{ resource.favorites }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新资源 -->
      <div class="latest-section">
        <div class="section-header">
          <h2 class="section-title">最新资源</h2>
          <el-button type="text" @click="goToAllResources">查看更多 >></el-button>
        </div>
        
        <div class="latest-list">
          <div class="latest-item" v-for="resource in latestResources" :key="resource.id" @click="goToDetail(resource.id)">
            <div class="item-icon">
              <i :class="getResourceTypeIcon(resource.type)"></i>
            </div>
            <div class="item-content">
              <h4 class="item-title">{{ resource.title }}</h4>
              <p class="item-info">{{ resource.author }} · {{ formatDate(resource.uploadTime) }}</p>
              <div class="item-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ resource.views }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-download"></i>
                  {{ resource.downloads }}
                </span>
              </div>
            </div>
            <div class="item-actions">
              <el-button type="text" size="small" @click.stop="previewResource(resource)">
                <i class="el-icon-view"></i>
                预览
              </el-button>
              <el-button type="text" size="small" @click.stop="downloadResource(resource)">
                <i class="el-icon-download"></i>
                下载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 1.3 样式设计
```less
<style lang="less" scoped>
.resource-center-page {
  background-color: #f2f2f2;
  padding: 30px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    .page-title {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .page-subtitle {
      font-size: 16px;
      color: #666;
    }
  }
  
  .search-section {
    margin-bottom: 40px;
    
    .search-container {
      max-width: 600px;
      margin: 0 auto;
      
      .search-input {
        /deep/ .el-input__inner {
          height: 50px;
          border-radius: 25px;
          font-size: 16px;
        }
        
        /deep/ .el-input-group__append {
          border-radius: 0 25px 25px 0;
          
          .el-button {
            height: 50px;
            padding: 0 25px;
            border-radius: 0 25px 25px 0;
          }
        }
      }
      
      .search-suggestions {
        margin-top: 20px;
        text-align: center;
        
        .suggestion-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }
        
        .suggestion-tags {
          .suggestion-tag {
            margin: 0 5px;
            cursor: pointer;
            
            &:hover {
              color: #4093f9;
            }
          }
        }
      }
    }
  }
  
  .stats-section {
    margin-bottom: 40px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: linear-gradient(135deg, #4093f9, #2c5aa0);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          
          i {
            font-size: 24px;
            color: white;
          }
        }
        
        .stat-content {
          .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
  
  .category-section, .featured-section, .latest-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .section-header {
      margin-bottom: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
      
      .section-subtitle {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    
    .category-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #e9ecef;
        transform: translateY(-3px);
      }
      
      .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4093f9, #2c5aa0);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        
        i {
          font-size: 24px;
          color: white;
        }
      }
      
      .category-content {
        .category-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }
        
        .category-description {
          font-size: 14px;
          color: #666;
          margin-bottom: 15px;
          line-height: 1.5;
        }
        
        .category-stats {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
  
  .featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
    
    .resource-card {
      background: #f8f9fa;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
      
      .resource-thumbnail {
        position: relative;
        height: 180px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .resource-type {
          position: absolute;
          top: 10px;
          right: 10px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 5px 10px;
          border-radius: 15px;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }
      
      .resource-content {
        padding: 20px;
        
        .resource-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .resource-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 15px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .resource-meta {
          display: flex;
          justify-content: space-between;
          margin-bottom: 15px;
          font-size: 12px;
          color: #999;
          
          span {
            display: flex;
            align-items: center;
            gap: 5px;
          }
        }
        
        .resource-stats {
          display: flex;
          justify-content: space-between;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }
  
  .latest-list {
    .latest-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f8f9fa;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: linear-gradient(135deg, #4093f9, #2c5aa0);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        i {
          font-size: 18px;
          color: white;
        }
      }
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
        }
        
        .item-info {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
        
        .item-stats {
          display: flex;
          gap: 20px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #999;
          }
        }
      }
      
      .item-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-center-page {
    padding: 20px 0;
    
    .stats-section {
      .stats-container {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    .category-grid, .featured-grid {
      grid-template-columns: 1fr;
    }
    
    .latest-list {
      .latest-item {
        flex-direction: column;
        text-align: center;
        
        .item-icon {
          margin-right: 0;
          margin-bottom: 10px;
        }
        
        .item-content {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
```

### 2. 资源详情页 (`/resource/detail/:id`)

#### 2.1 页面布局
```vue
<template>
  <div class="resource-detail-page">
    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-navigation">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/resource' }">资源中心</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: `/resource/category/${resourceDetail.categoryId}` }">
            {{ resourceDetail.categoryName }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ resourceDetail.title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 资源详情 -->
      <div class="resource-detail" v-if="resourceDetail">
        <div class="resource-header">
          <div class="resource-info">
            <h1 class="resource-title">{{ resourceDetail.title }}</h1>
            <div class="resource-meta">
              <span class="meta-item">
                <i class="el-icon-user"></i>
                作者：{{ resourceDetail.author }}
              </span>
              <span class="meta-item">
                <i class="el-icon-time"></i>
                上传时间：{{ formatDate(resourceDetail.uploadTime) }}
              </span>
              <span class="meta-item">
                <i class="el-icon-folder"></i>
                分类：{{ resourceDetail.categoryName }}
              </span>
              <span class="meta-item">
                <i class="el-icon-document"></i>
                大小：{{ formatFileSize(resourceDetail.fileSize) }}
              </span>
            </div>
            <div class="resource-stats">
              <div class="stat-item">
                <i class="el-icon-view"></i>
                <span>{{ resourceDetail.views }}</span>
                <span>浏览</span>
              </div>
              <div class="stat-item">
                <i class="el-icon-download"></i>
                <span>{{ resourceDetail.downloads }}</span>
                <span>下载</span>
              </div>
              <div class="stat-item">
                <i class="el-icon-star-off"></i>
                <span>{{ resourceDetail.favorites }}</span>
                <span>收藏</span>
              </div>
            </div>
          </div>
          
          <div class="resource-actions">
            <el-button type="primary" size="large" @click="downloadResource" :loading="downloading">
              <i class="el-icon-download"></i>
              下载资源
            </el-button>
            <el-button 
              :type="isFavorited ? 'warning' : 'default'" 
              size="large" 
              @click="toggleFavorite"
              :loading="favoriting"
            >
              <i :class="isFavorited ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
              {{ isFavorited ? '已收藏' : '收藏' }}
            </el-button>
            <el-button size="large" @click="shareResource">
              <i class="el-icon-share"></i>
              分享
            </el-button>
          </div>
        </div>

        <!-- 资源预览 -->
        <div class="resource-preview">
          <div class="preview-header">
            <h3>资源预览</h3>
            <div class="preview-actions">
              <el-button @click="toggleFullscreen" size="small">
                <i class="el-icon-full-screen"></i>
                全屏预览
              </el-button>
            </div>
          </div>
          
          <div class="preview-content" :class="{ 'fullscreen': isFullscreen }">
            <!-- 图片预览 -->
            <div v-if="resourceDetail.type === 'image'" class="image-preview">
              <img :src="resourceDetail.fileUrl" :alt="resourceDetail.title" />
            </div>
            
            <!-- 视频预览 -->
            <div v-else-if="resourceDetail.type === 'video'" class="video-preview">
              <video :src="resourceDetail.fileUrl" controls></video>
            </div>
            
            <!-- 音频预览 -->
            <div v-else-if="resourceDetail.type === 'audio'" class="audio-preview">
              <audio :src="resourceDetail.fileUrl" controls></audio>
              <div class="audio-info">
                <img :src="resourceDetail.thumbnail" :alt="resourceDetail.title" />
                <div class="audio-meta">
                  <h4>{{ resourceDetail.title }}</h4>
                  <p>{{ resourceDetail.author }}</p>
                </div>
              </div>
            </div>
            
            <!-- PDF预览 -->
            <div v-else-if="resourceDetail.type === 'pdf'" class="pdf-preview">
              <iframe :src="resourceDetail.fileUrl" frameborder="0"></iframe>
            </div>
            
            <!-- 文档预览 -->
            <div v-else-if="resourceDetail.type === 'document'" class="document-preview">
              <div class="document-placeholder">
                <i class="el-icon-document"></i>
                <p>{{ resourceDetail.title }}</p>
                <p>请下载后查看完整内容</p>
              </div>
            </div>
            
            <!-- 其他类型 -->
            <div v-else class="default-preview">
              <div class="preview-placeholder">
                <i :class="getResourceTypeIcon(resourceDetail.type)"></i>
                <p>{{ resourceDetail.title }}</p>
                <p>{{ resourceDetail.typeName }}</p>
                <el-button type="primary" @click="downloadResource">下载查看</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 资源描述 -->
        <div class="resource-description">
          <h3>资源描述</h3>
          <div class="description-content" v-html="resourceDetail.description"></div>
          
          <div class="resource-tags" v-if="resourceDetail.tags.length > 0">
            <span class="tags-label">标签：</span>
            <el-tag v-for="tag in resourceDetail.tags" :key="tag" size="small">{{ tag }}</el-tag>
          </div>
        </div>

        <!-- 相关资源 -->
        <div class="related-resources">
          <h3>相关资源</h3>
          <div class="related-grid">
            <div class="related-item" v-for="item in relatedResources" :key="item.id" @click="goToDetail(item.id)">
              <img :src="item.thumbnail" :alt="item.title" />
              <div class="related-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.author }}</p>
                <div class="related-stats">
                  <span><i class="el-icon-view"></i>{{ item.views }}</span>
                  <span><i class="el-icon-download"></i>{{ item.downloads }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 2.2 预览组件样式
```less
<style lang="less" scoped>
.resource-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .resource-detail {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .resource-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #f0f0f0;
      
      .resource-info {
        flex: 1;
        
        .resource-title {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          margin-bottom: 15px;
          line-height: 1.4;
        }
        
        .resource-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          margin-bottom: 20px;
          
          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #666;
            
            i {
              color: #4093f9;
            }
          }
        }
        
        .resource-stats {
          display: flex;
          gap: 30px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            color: #333;
            
            i {
              color: #4093f9;
              font-size: 20px;
            }
          }
        }
      }
      
      .resource-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
        min-width: 200px;
      }
    }
    
    .resource-preview {
      margin-bottom: 30px;
      
      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          font-size: 20px;
          color: #333;
        }
      }
      
      .preview-content {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.fullscreen {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
          background: rgba(0, 0, 0, 0.9);
          border-radius: 0;
        }
        
        .image-preview {
          img {
            max-width: 100%;
            max-height: 600px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
        
        .video-preview {
          video {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 8px;
          }
        }
        
        .audio-preview {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20px;
          
          audio {
            width: 100%;
            max-width: 500px;
          }
          
          .audio-info {
            display: flex;
            align-items: center;
            gap: 15px;
            
            img {
              width: 80px;
              height: 80px;
              border-radius: 8px;
              object-fit: cover;
            }
            
            .audio-meta {
              h4 {
                font-size: 18px;
                color: #333;
                margin-bottom: 5px;
              }
              
              p {
                font-size: 14px;
                color: #666;
              }
            }
          }
        }
        
        .pdf-preview {
          iframe {
            width: 100%;
            height: 600px;
            border-radius: 8px;
          }
        }
        
        .document-preview, .default-preview {
          .document-placeholder, .preview-placeholder {
            text-align: center;
            color: #666;
            
            i {
              font-size: 64px;
              margin-bottom: 20px;
              color: #4093f9;
            }
            
            p {
              font-size: 16px;
              margin-bottom: 10px;
            }
          }
        }
      }
    }
    
    .resource-description {
      margin-bottom: 30px;
      
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 15px;
      }
      
      .description-content {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        margin-bottom: 20px;
        
        /deep/ {
          p {
            margin-bottom: 15px;
          }
          
          img {
            max-width: 100%;
            border-radius: 8px;
            margin: 20px 0;
          }
        }
      }
      
      .resource-tags {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .tags-label {
          font-size: 14px;
          color: #666;
        }
        
        .el-tag {
          margin-right: 5px;
        }
      }
    }
    
    .related-resources {
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
      }
      
      .related-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        
        .related-item {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 15px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
          }
          
          img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
          }
          
          .related-content {
            h4 {
              font-size: 14px;
              color: #333;
              margin-bottom: 5px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            p {
              font-size: 12px;
              color: #666;
              margin-bottom: 8px;
            }
            
            .related-stats {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #999;
              
              span {
                display: flex;
                align-items: center;
                gap: 3px;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-detail-page {
    padding: 20px 0;
    
    .resource-detail {
      padding: 20px;
      
      .resource-header {
        flex-direction: column;
        gap: 20px;
        
        .resource-actions {
          width: 100%;
          flex-direction: row;
          justify-content: space-between;
        }
      }
      
      .resource-meta {
        flex-direction: column;
        gap: 10px;
      }
      
      .related-resources {
        .related-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
```

### 3. 资源搜索页面

#### 3.1 搜索功能实现
```javascript
// 搜索页面的核心功能
export default {
  name: 'ResourceSearch',
  data() {
    return {
      searchKeyword: '',
      searchResults: [],
      searchFilters: {
        type: '',
        category: '',
        author: '',
        uploadTime: null
      },
      sortBy: 'relevance', // relevance, time, views, downloads
      currentPage: 1,
      pageSize: 20,
      total: 0,
      loading: false
    }
  },
  
  methods: {
    async performSearch() {
      this.loading = true
      try {
        const response = await this.mockSearchResources({
          keyword: this.searchKeyword,
          filters: this.searchFilters,
          sortBy: this.sortBy,
          page: this.currentPage,
          pageSize: this.pageSize
        })
        
        this.searchResults = response.data.list
        this.total = response.data.total
      } catch (error) {
        this.$message.error('搜索失败')
      } finally {
        this.loading = false
      }
    },
    
    // 高级搜索
    advancedSearch() {
      this.currentPage = 1
      this.performSearch()
    },
    
    // 排序
    handleSort(sortType) {
      this.sortBy = sortType
      this.currentPage = 1
      this.performSearch()
    }
  }
}
```

## 模拟数据结构

### 资源数据结构
```javascript
const resourceData = {
  id: 1,
  title: '数据结构与算法教程',
  description: '详细介绍数据结构与算法的基础知识',
  type: 'pdf',
  typeName: 'PDF文档',
  categoryId: 1,
  categoryName: '教学资料',
  author: '张教授',
  fileUrl: 'https://example.com/files/resource1.pdf',
  thumbnail: 'https://via.placeholder.com/300x200',
  fileSize: 15728640, // 字节
  uploadTime: '2024-01-15 10:00:00',
  views: 256,
  downloads: 89,
  favorites: 45,
  tags: ['数据结构', '算法', '教程'],
  relatedToCourse: true,
  courseId: 1,
  courseName: '数据结构课程',
  status: 'published'
}
```

### 资源分类数据
```javascript
const categoryData = {
  id: 1,
  name: '教学资料',
  description: '各类教学相关的资料文档',
  icon: 'el-icon-document',
  resourceCount: 128,
  viewCount: 5620,
  parentId: 0,
  sort: 1,
  status: 'active'
}
```

## 开发任务清单

### 基础设施
- [ ] 创建资源中心路由配置
- [ ] 设置页面基础结构
- [ ] 创建模拟数据文件
- [ ] 引入文件预览相关库

### 资源中心主页
- [ ] 页面布局开发
  - [ ] 页面头部设计
  - [ ] 搜索区域实现
  - [ ] 统计数据展示
  - [ ] 分类导航设计
- [ ] 资源分类功能
  - [ ] 分类卡片组件
  - [ ] 分类统计显示
  - [ ] 分类跳转功能
- [ ] 推荐资源展示
  - [ ] 资源卡片组件
  - [ ] 悬停效果实现
  - [ ] 统计信息展示
- [ ] 最新资源列表
  - [ ] 列表项设计
  - [ ] 快速操作按钮
  - [ ] 分页功能

### 资源详情页
- [ ] 资源信息展示
  - [ ] 资源基本信息
  - [ ] 作者和时间信息
  - [ ] 统计数据展示
- [ ] 资源预览功能
  - [ ] 图片预览组件
  - [ ] 视频预览组件
  - [ ] 音频预览组件
  - [ ] PDF预览组件
  - [ ] 全屏预览功能
- [ ] 资源操作功能
  - [ ] 下载功能实现
  - [ ] 收藏功能实现
  - [ ] 分享功能实现
- [ ] 相关资源推荐
  - [ ] 相关资源算法
  - [ ] 推荐卡片设计
  - [ ] 跳转功能

### 搜索和筛选
- [ ] 搜索功能实现
  - [ ] 关键词搜索
  - [ ] 搜索建议
  - [ ] 搜索历史
- [ ] 高级筛选功能
  - [ ] 资源类型筛选
  - [ ] 分类筛选
  - [ ] 作者筛选
  - [ ] 时间筛选
- [ ] 排序功能
  - [ ] 相关性排序
  - [ ] 时间排序
  - [ ] 浏览量排序
  - [ ] 下载量排序

### 用户交互功能
- [ ] 收藏管理
  - [ ] 收藏状态管理
  - [ ] 收藏列表页面
  - [ ] 收藏夹功能
- [ ] 个人中心集成
  - [ ] 我的收藏页面
  - [ ] 浏览历史页面
  - [ ] 下载记录页面

### 文件处理
- [ ] 文件上传处理
  - [ ] 多文件类型支持
  - [ ] 文件大小限制
  - [ ] 文件格式验证
- [ ] 文件预览处理
  - [ ] 缩略图生成
  - [ ] 预览图生成
  - [ ] 在线预览优化
- [ ] 文件下载处理
  - [ ] 下载权限控制
  - [ ] 下载统计
  - [ ] 断点续传支持

### 性能优化
- [ ] 图片懒加载
- [ ] 虚拟滚动
- [ ] 缓存策略
- [ ] 预加载优化

### 响应式设计
- [ ] 移动端适配
- [ ] 平板端适配
- [ ] 触摸手势支持
- [ ] 横屏适配

## 技术要点

### 文件预览技术
- PDF预览：使用vue-pdf-embed
- Office文档：提示暂时不能预览，只能够下载
- 图片预览：支持放大缩小、旋转，使用html原生实现
- 视频预览：支持多种格式，提供播放控制，使用html原生实现
- 音频预览：波形显示，播放控制，使用html原生实现

### 搜索优化
- 全文搜索算法
- 搜索结果高亮
- 搜索建议算法
- 相关性排序

### 用户体验
- 流畅的加载动画
- 友好的错误提示
- 直观的操作反馈
- 便捷的快捷键支持