# 校级虚拟仿真实训基地平台 - 前台开发背景文档

## 项目概述

### 项目性质
这是一个**校级虚拟仿真实训基地平台**的升级项目，需要在现有的虚拟仿真实验平台基础上进行**功能无缝平稳升级**，新增四大核心功能模块。

### 现有系统架构
- **前台**: Vue 2.x + Element UI + Vuex + Vue Router（学生门户网站）
- **后台**: Vue 2.x + vue-element-admin + Element UI + VXETable（管理员和教师管理系统）  
- **后端**: Spring Boot + Kotlin + MongoDB + Redis + JWT（API服务）

### 开发策略
采用**前台开发先行**的策略：
1. 先完成前台开发，使用硬编码模拟数据，不要使用mock，直接在代码里写入静态数据
2. 前台功能验证无误后，后续会进行后台和后端开发
3. 最后进行系统集成

## 需要新增的五大模块

### 1. 基地内容展示模块
**功能目标**: 建立基地信息发布和展示体系
- 基地新闻展示功能（列表页 + 详情页）
- 通知公告展示功能（列表页 + 详情页）
- 富文本内容详情（支持文字、图片、视频、链接）
- 搜索、筛选、分页功能
- 浏览量统计

### 2. 产教融合模块
**功能目标**: 展示基地与企业合作及学生就业信息
- 产教融合主页（轮播图、合作企业、优秀学生展示）
- 合作企业列表和详情
- 企业招聘信息列表和详情
- 优秀学生展示列表和详情
- 搜索、筛选功能

### 3. 资源中心模块
**功能目标**: 建立基地教学资源库和共享平台
- 资源分类浏览（文档、图片、音频、视频等）
- 资源搜索和筛选
- 资源在线预览
- 资源收藏功能
- 资源下载功能
- 用户个人中心收藏管理

### 4. 数据展示模块
**功能目标**: 提供基地整体运营数据和统计分析
- 基地整体数据概览
- 实训课程统计数据（图表展示）
- 产教融合数据展示
- 资源中心使用统计
- 数据可视化（使用ECharts）

## 技术规范

### 技术栈
- **Vue 2.x** - 核心框架
- **Element UI** - UI组件库
- **Vuex** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP请求
- **Less** - CSS预处理器
- **ECharts** - 图表库（数据展示模块）

### 开发原则
1. **静态硬编码数据** - 直接在vue或其它代码中内置数据，不要使用mock
2. **UI设计一致性** - 严格遵循现有UI设计规范
3. **组件化开发** - 充分复用现有组件
4. **代码规范** - 遵循现有代码结构和命名规范

### 现有系统集成点
- **用户系统**: 复用现有的用户认证和个人中心
- **课程系统**: 资源可关联现有课程
- **导航系统**: 在现有导航中添加新模块入口
- **权限系统**: 遵循现有的权限控制机制

## 项目文件结构

### 现有关键目录
```
src/
├── views/           # 页面组件
├── components/      # 通用组件
├── router/          # 路由配置
├── store/           # Vuex状态管理
├── api/             # API接口定义
├── model/           # 业务模型
├── utils/           # 工具函数
├── style/           # 样式文件
└── assets/          # 静态资源
```

### 需要新增的文件结构
```
src/
├── views/
│   ├── news/        # 新闻模块
│   ├── industry/    # 产教融合模块
│   ├── resource/    # 资源中心模块
│   ├── statistics/  # 数据展示模块
│   └── user/
│       └── favorite.vue # 个人中心收藏页面
├── api/
│   ├── news.js      # 新闻接口
│   ├── industry.js  # 产教融合接口
│   ├── resource.js  # 资源接口
│   └── statistics.js # 统计接口
```

## 设计规范

### 色彩体系
```less
@main-color: #4093f9;     // 主题蓝色
@text-color: #333;        // 主要文字色
@text-secondary: #666;    // 次要文字色
@text-placeholder: #888;  // 占位符文字色
@border-color: #cecece;   // 边框色
@bg-color: #f2f2f2;       // 页面背景色
@white: #ffffff;          // 卡片背景色
```

### 响应式断点
```less
@screen-xs: 480px;   // 手机
@screen-sm: 768px;   // 平板
@screen-md: 1024px;  // 小屏电脑
@screen-lg: 1200px;  // 大屏电脑
```

### 通用组件
- 页面标题组件
- 卡片组件
- 列表项卡片
- 搜索框组件
- 分页组件
- 面包屑导航

## 模拟数据规范

### 数据格式标准
- **时间格式**: 'YYYY-MM-DD HH:mm:ss'
- **响应格式**: `{ code: 200, message: 'success', data: {} }`
- **分页格式**: `{ list: [], total: 100, page: 1, pageSize: 10 }`

### 关键数据结构
- **新闻**: id, title, summary, content, coverImage, category, publishTime, views, author
- **企业**: id, name, logo, type, location, description, cooperationYears, studentCount
- **资源**: id, title, description, type, fileUrl, thumbnail, views, downloads, favorites
- **学生**: id, name, photo, major, company, achievements, gpa

## 开发优先级

### 第一阶段（相对简单，验证开发流程）
1. **基地内容展示模块**
   - 新闻列表页和详情页
   - 通知公告列表页和详情页

### 第二阶段（图表功能，为其他模块提供参考）
2. **数据展示模块**
   - 统计数据展示
   - ECharts图表集成

### 第三阶段（文件处理，相对复杂）
3. **资源中心模块**
   - 资源列表和分类
   - 资源预览和下载
   - 收藏功能

### 第四阶段（内容丰富，需要良好布局）
4. **产教融合模块**
   - 主页展示
   - 企业和学生信息
   - 招聘信息

## 关键技术要点

### 文件预览系统
- 多种文件类型支持（PDF、图片、音频、视频）
- 在线预览功能
- 文件下载管理
- 缩略图生成

### 图表可视化
- ECharts集成
- 响应式图表
- 实时数据更新
- 多种图表类型（折线图、饼图、柱状图）
- 
## 无需测试
不需要写测试用例和在开放过程中的测试，不需要运行npm run serve 进行测试

## 注意事项

### 开发注意点
1. **保持现有功能不变** - 新功能作为独立模块开发
2. **遵循现有代码规范** - 命名、结构、注释风格
5. **考虑性能优化** - 图片懒加载、代码分割

### 集成注意点
1. **路由配置** - 在现有路由基础上扩展
2. **导航菜单** - 合理添加新模块入口
3. **权限控制** - 遵循现有权限机制
4. **状态管理** - 适当使用Vuex管理状态
5. **API接口** - 为后续后端对接做好准备

## 文档参考

### 详细开发文档
- `UI设计规范.md` - 界面设计规范和通用组件
- `基地内容展示模块.md` - 新闻通知系统详细设计
- `产教融合模块.md` - 企业合作展示系统详细设计
- `资源中心模块.md` - 教学资源管理系统详细设计
- `数据展示模块.md` - 统计数据展示系统详细设计

### 现有系统文档
- `前台-现有功能.md` - 现有前台系统功能分析
- `后台-现有功能.md` - 现有后台系统功能分析
- `后端-现有功能.md` - 现有后端系统功能分析
- `整体-现有功能.md` - 系统整体功能分析

## 开发流程

### 单个模块开发流程
1. **需求理解** - 阅读对应模块的详细文档
2. **环境准备** - 确保开发环境配置正确
3. **路由配置** - 添加模块相关路由
4. **页面开发** - 按照设计规范开发界面
5. **功能实现** - 实现页面逻辑和交互
6. **样式调整** - 确保UI一致性和响应式

---

**重要提醒**: 每次开始新的开发任务时，请先阅读本背景文档，然后结合具体模块的详细文档进行开发。确保理解项目背景、技术规范和开发要求后再开始编码。