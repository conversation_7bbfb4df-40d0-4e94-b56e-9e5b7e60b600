# 校级虚拟仿真实训基地平台 - 前台开发背景文档

## 项目概述

### 项目性质
这是一个**校级虚拟仿真实训基地平台**的升级项目，需要在现有的虚拟仿真实验平台基础上进行**功能无缝平稳升级**，新增四大核心功能模块。本项目采用硬编码数据驱动的开发方式，确保前台功能完整实现后再进行后端对接。

### 现有系统架构
- **前台**: Vue 2.x + Element UI + Vuex + Vue Router（学生门户网站）
- **后台**: Vue 2.x + vue-element-admin + Element UI + VXETable（管理员和教师管理系统）  
- **后端**: Spring Boot + Kotlin + MongoDB + Redis + JWT（API服务）

### 开发策略
采用**前台开发先行**的策略：
1. 先完成前台开发，使用硬编码模拟数据，不使用mock，直接在代码里写入静态数据
2. 前台功能验证无误后，后续会进行后台和后端开发
3. 最后进行系统集成

### 注意事项
- **无需测试**：不需要写测试用例和开发过程中的测试，不需要运行npm run serve进行测试
- **保持现有功能不变**：新功能作为独立模块开发，不影响现有系统

## 技术规范

### 技术栈
- **Vue 2.x** - 核心框架
- **Element UI** - UI组件库
- **Vuex** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP请求
- **Less** - CSS预处理器
- **ECharts** - 图表库（数据展示模块）

### 开发原则
1. **静态硬编码数据** - 直接在vue或其它代码中内置数据，不要使用mock
2. **UI设计一致性** - 严格遵循现有UI设计规范
3. **组件化开发** - 充分复用现有组件
4. **响应式设计** - 确保移动端兼容性
5. **代码规范** - 遵循现有代码结构和命名规范

### 核心架构模式

#### 数据请求流程 (View -> Model -> API)
本项目采用三层数据请求结构，严格分离视图、业务逻辑和API调用：

1. **View (视图层, `views/**/*.vue`)**:
   - 负责UI展示和用户交互
   - 不直接调用 `axios` 或 `api` 层
   - 通过 `import` 引入 `model` 层的方法来获取或提交数据
   - 示例: `import {CourseModel} from "@/model/CourseModel";`

2. **Model (业务模型层, `model/*.js`)**:
   - 负责处理具体业务逻辑，是视图与API的桥梁
   - 封装对 `api` 层的调用
   - 对API返回的数据进行处理和格式化
   - 示例: `CourseModel.getOne(courseId)` 方法内部调用 `api/CourseApi.js`

3. **API (接口定义层, `api/*.js`)**:
   - 负责与后端接口的直接通信
   - 每个文件对应后端的一个Controller或业务模块
   - 只负责定义请求，不包含业务逻辑或数据处理

## 项目文件结构

### 现有核心目录
```
src/
├── api/          # API接口定义层，每个文件对应一个后端模块
├── assets/       # 静态资源（图片、图标等）
├── components/   # 全局可复用组件
├── config/       # 项目配置文件
├── directive/    # 自定义Vue指令
├── enums/        # 枚举常量
├── filters/      # 全局Vue过滤器
├── main.js       # 应用入口文件
├── model/        # 业务模型层，封装API调用和数据处理
├── router/       # 路由配置与导航守卫
├── store/        # Vuex状态管理
├── style/        # 全局样式文件
├── utils/        # 通用工具函数
└── views/        # 页面视图组件
```

### 需要新增的文件结构
```
src/
├── views/
│   ├── news/        # 基地内容展示模块
│   ├── industry/    # 产教融合模块
│   ├── resource/    # 资源中心模块
│   ├── statistics/  # 数据展示模块
│   └── user/
│       └── favorite.vue # 个人中心收藏页面
├── api/
│   ├── news.js      # 新闻接口
│   ├── industry.js  # 产教融合接口
│   ├── resource.js  # 资源接口
│   └── statistics.js # 统计接口
├── model/
│   ├── NewsModel.js      # 新闻业务模型
│   ├── IndustryModel.js  # 产教融合业务模型
│   ├── ResourceModel.js  # 资源业务模型
│   └── StatisticsModel.js # 统计业务模型
```

## 需要新增的四大模块

### 1. 基地内容展示模块
**功能目标**: 建立基地信息发布和展示体系
- 基地新闻展示功能（列表页 + 详情页）
- 通知公告展示功能（列表页 + 详情页）
- 富文本内容详情（支持文字、图片、视频、链接）
- 搜索、筛选、分页功能
- 浏览量统计

### 2. 产教融合模块
**功能目标**: 展示基地与企业合作及学生就业信息
- 产教融合主页（轮播图、合作企业、优秀学生展示）
- 合作企业列表和详情
- 企业招聘信息列表和详情
- 优秀学生展示列表和详情
- 搜索、筛选功能

### 3. 资源中心模块
**功能目标**: 建立基地教学资源库和共享平台
- 资源分类浏览（文档、图片、音频、视频等）
- 资源搜索和筛选
- 资源在线预览
- 资源收藏功能
- 资源下载功能
- 用户个人中心收藏管理

### 4. 数据展示模块
**功能目标**: 提供基地整体运营数据和统计分析
- 基地整体数据概览
- 实训课程统计数据（图表展示）
- 产教融合数据展示
- 资源中心使用统计
- 数据可视化（使用ECharts）

## UI设计规范

### 色彩体系
```less
// 主色调
@main-color: #4093f9;     // 主题蓝色
@text-color: #333;        // 主要文字色
@text-secondary: #666;    // 次要文字色
@text-placeholder: #888;  // 占位符文字色
@border-color: #cecece;   // 边框色
@bg-color: #f2f2f2;       // 页面背景色
@white: #ffffff;          // 卡片背景色

// 状态色
@success-color: #67c23a;  // 成功状态
@warning-color: #e6a23c;  // 警告状态
@danger-color: #f56c6c;   // 危险状态
@info-color: #909399;     // 信息状态
```

### 字体规范
- **字体家族**: "Segoe UI", "PingFangSC-Regular", "PingFang SC", "Microsoft YaHei", sans-serif
- **大标题**: 28px (页面主标题)
- **中标题**: 20px-24px (模块标题)
- **小标题**: 16px-18px (内容标题)
- **正文**: 14px (主要内容)
- **辅助文字**: 12px (次要信息)

### 间距规范
```less
// 页面间距
@padding-large: 30px;     // 大间距
@padding-medium: 20px;    // 中间距
@padding-small: 15px;     // 小间距
@padding-mini: 10px;      // 微间距

// 组件间距
@margin-large: 25px;      // 组件大间距
@margin-medium: 15px;     // 组件中间距
@margin-small: 10px;      // 组件小间距
@margin-mini: 5px;        // 组件微间距
```

### 响应式断点
```less
@screen-xs: 480px;   // 手机
@screen-sm: 768px;   // 平板
@screen-md: 1024px;  // 小屏电脑
@screen-lg: 1200px;  // 大屏电脑
@screen-xl: 1440px;  // 超大屏
```

### 布局规范
```css
.content-container {
    width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
}

/* 响应式适配 */
@media (max-width: 1240px) {
    .content-container {
        width: 100%;
        padding: 0 20px;
    }
}
```

## 通用组件规范

### 页面标题组件
用于统一页面标题的展示，支持主标题和副标题

### 卡片组件
基于Element UI的el-card进行封装，统一卡片样式和阴影效果

### 列表项卡片
用于新闻、资源等列表项的展示，包含标题、时间、摘要、标签和统计信息

### 搜索框组件
统一的搜索框样式，支持关键词搜索和回车事件

### 分页组件
基于Element UI分页组件，统一分页样式和功能

### Flex布局工具类
```css
.flex { display: flex; }
.flex-center { justify-content: center; align-items: center; }
.flex-between { justify-content: space-between; align-items: center; }
.flex-start { justify-content: flex-start; align-items: center; }
.flex-end { justify-content: flex-end; align-items: center; }
.flex-around { justify-content: space-around; align-items: center; }
.flex-wrap { flex-wrap: wrap; }
.flex-column { flex-direction: column; }
.flex-1 { flex: 1; }
```

## 编码规范

### 命名规范
- **文件名**: 使用驼峰命名法（camelCase），如 `courseInfo.vue`, `UserModel.js`
- **Vue组件名**: 在组件内部 `name` 属性使用大驼峰命名法（PascalCase），如 `name: "CourseInfo"`
- **变量与函数**: 使用驼峰命名法（camelCase），如 `courseList`, `getList()`
- **常量**: 在 `enums/index.js` 中定义，使用驼峰命名法

### 代码风格
- **JavaScript**:
  - 使用单引号 `'`
  - 语句结尾不加分号 `;`
  - 异步操作统一使用 `async/await`
  - 优先使用 `let` 和 `const`
- **Vue**:
  - 组件的 `<script>` 部分，`data`, `methods`, `computed`, `mounted` 等选项应按此逻辑顺序排列
  - 组件 `props` 定义应尽量详细，包含 `type` 和 `default`
  - 触发父组件事件使用 `this.$emit('event-name', payload)`，事件名使用 kebab-case
- **HTML**:
  - 属性名使用 kebab-case，如 `v-el-drag-dialog`
  - Element UI 组件属性遵循其官方文档

### 样式规范
- **全局基础样式**: `src/style/common.css`，包含 reset、flex布局、清浮动等通用工具类
- **全局变量**: `src/style/app.less`，定义了项目的主色调等变量
- **组件样式**: 推荐在 `.vue` 文件中使用 `<style scoped lang="less">` 来编写组件的局部样式
- **样式类名**: 应具有描述性，可参考BEM命名法

## 模拟数据规范

### 数据格式标准
- **时间格式**: 'YYYY-MM-DD HH:mm:ss'
- **响应格式**: `{ code: 200, message: 'success', data: {} }`
- **分页格式**: `{ list: [], total: 100, page: 1, pageSize: 10 }`

### 关键数据结构
- **新闻**: id, title, summary, content, coverImage, category, publishTime, views, author
- **企业**: id, name, logo, type, location, description, cooperationYears, studentCount
- **资源**: id, title, description, type, fileUrl, thumbnail, views, downloads, favorites
- **学生**: id, name, photo, major, company, achievements, gpa

## 现有系统集成点

### 用户系统
- 复用现有的用户认证和个人中心
- 利用现有的登录状态和权限机制

### 课程系统
- 资源可关联现有课程
- 统计数据可基于现有课程数据

### 导航系统
- 在现有导航中添加新模块入口
- 保持导航结构的一致性

### 权限系统
- 遵循现有的权限控制机制
- 确保数据安全和访问控制

## 开发优先级

### 第一阶段（相对简单，验证开发流程）
1. **基地内容展示模块**
   - 新闻列表页和详情页
   - 通知公告列表页和详情页

### 第二阶段（内容丰富，需要良好布局）
2. **产教融合模块**
    - 主页展示
    - 企业和学生信息
    - 招聘信息


### 第三阶段（文件处理，相对复杂）
3. **资源中心模块**
    - 资源列表和分类
    - 资源预览和下载
    - 收藏功能
- 
### 第四阶段（图表功能，为其他模块提供参考）
4. **数据展示模块**
   - 统计数据展示
   - ECharts图表集成

   

## 关键技术要点

### 文件预览系统
- 多种文件类型支持（PDF、图片、音频、视频）
- 在线预览功能
- 文件下载管理
- 缩略图生成

### 图表可视化
- ECharts集成
- 响应式图表
- 实时数据更新
- 多种图表类型（折线图、饼图、柱状图）

### 动画效果
```css
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.hover-effect {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}
```

## 开发流程

### 单个模块开发流程
1. **需求理解** - 阅读对应模块的详细文档
2. **环境准备** - 确保开发环境配置正确
3. **路由配置** - 添加模块相关路由
4. **页面开发** - 按照设计规范开发界面
5. **功能实现** - 实现页面逻辑和交互
6. **样式调整** - 确保UI一致性和响应式

### 新功能开发示例（添加页面的标准流程）
1. **API层** - 在对应的 `api/*.js` 文件中添加接口定义
2. **Model层** - 在对应的 `model/*.js` 文件中添加业务逻辑
3. **路由层** - 在 `router/index.js` 中添加新路由
4. **视图层** - 在 `views/` 中创建新页面文件
5. **导航层** - 在相应布局文件中添加导航入口

## 注意事项

### 开发注意点
1. **保持现有功能不变** - 新功能作为独立模块开发
2. **遵循现有代码规范** - 命名、结构、注释风格
3. **考虑性能优化** - 图片懒加载、代码分割
4. **确保响应式设计** - 适配不同屏幕尺寸

### 集成注意点
1. **路由配置** - 在现有路由基础上扩展
2. **导航菜单** - 合理添加新模块入口
3. **权限控制** - 遵循现有权限机制
4. **状态管理** - 适当使用Vuex管理状态
5. **API接口** - 为后续后端对接做好准备

## 文档参考

### 详细开发文档
- `UI设计规范.md` - 界面设计规范和通用组件
- `基地内容展示模块.md` - 新闻通知系统详细设计
- `产教融合模块.md` - 企业合作展示系统详细设计
- `资源中心模块.md` - 教学资源管理系统详细设计
- 
**重要提醒**: 每次开始新的开发任务时，请先阅读本背景文档，然后结合具体模块、的详细文档进行开发。确保理解项目背景、技术规范和开发要求后再开始编码。此文档将根据开发进度和需求变化进行更新，请及时关注最新版本。