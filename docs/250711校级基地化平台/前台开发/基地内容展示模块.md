# 基地内容展示模块

## 模块概述

基地内容展示模块包含新闻发布系统和通知公告系统，用于展示基地相关的动态信息。

## 功能需求

### 新闻系统
- 新闻列表展示
- 新闻详情页面
- 新闻搜索和分类筛选
- 新闻浏览量统计

### 通知公告系统
- 通知公告列表展示
- 通知公告详情页面
- 重要通知置顶功能
- 通知状态标识

## 页面结构

### 1. 新闻列表页 (`/news`)

#### 1.1 路由配置
```javascript
// router/index.js
{
  path: '/news',
  name: 'News',
  component: () => import('@/views/news/index.vue'),
  meta: {
    title: '基地新闻',
    requiresAuth: false
  }
}
```

#### 1.2 页面布局
```vue
<template>
  <div class="news-page">
    <div class="content-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">基地新闻</h1>
        <div class="page-subtitle">了解基地最新动态</div>
      </div>

      <!-- 搜索和筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入搜索关键词"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-area">
          <el-select v-model="selectedCategory" placeholder="选择分类" @change="handleCategoryChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="基地动态" value="base_news"></el-option>
            <el-option label="活动通知" value="activity"></el-option>
            <el-option label="学术交流" value="academic"></el-option>
            <el-option label="成果展示" value="achievement"></el-option>
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          >
          </el-date-picker>
        </div>
      </div>

      <!-- 新闻列表区域 -->
      <div class="news-list-section">
        <div class="news-grid">
          <div class="news-item" v-for="item in newsList" :key="item.id" @click="goToDetail(item.id)">
            <div class="news-image">
              <img :src="item.coverImage" :alt="item.title" />
              <div class="news-category">{{ item.categoryName }}</div>
            </div>
            <div class="news-content">
              <h3 class="news-title">{{ item.title }}</h3>
              <p class="news-summary">{{ item.summary }}</p>
              <div class="news-meta">
                <span class="news-date">
                  <i class="el-icon-time"></i>
                  {{ formatDate(item.publishTime) }}
                </span>
                <span class="news-views">
                  <i class="el-icon-view"></i>
                  {{ item.views }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>
```

#### 1.3 样式设计
```less
<style lang="less" scoped>
.news-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    .page-title {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .page-subtitle {
      font-size: 16px;
      color: #666;
    }
  }
  
  .search-filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .search-area {
      margin-bottom: 20px;
      text-align: center;
      
      .search-input {
        max-width: 500px;
        
        /deep/ .el-input__inner {
          border-radius: 25px 0 0 25px;
          height: 45px;
          line-height: 45px;
        }
        
        /deep/ .el-input-group__append {
          border-radius: 0 25px 25px 0;
          
          .el-button {
            border-radius: 0 25px 25px 0;
            height: 45px;
            padding: 0 20px;
          }
        }
      }
    }
    
    .filter-area {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
      
      .el-select, .el-date-picker {
        min-width: 180px;
      }
    }
  }
  
  .news-list-section {
    .news-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 25px;
      
      .news-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .news-image {
          position: relative;
          height: 200px;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }
          
          &:hover img {
            transform: scale(1.05);
          }
          
          .news-category {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(64, 147, 249, 0.9);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
          }
        }
        
        .news-content {
          padding: 20px;
          
          .news-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            
            &:hover {
              color: #4093f9;
            }
          }
          
          .news-summary {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
            
            .news-date, .news-views {
              display: flex;
              align-items: center;
              gap: 5px;
            }
          }
        }
      }
    }
  }
  
  .pagination-container {
    text-align: center;
    margin-top: 40px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-page {
    padding: 20px 0;
    
    .search-filter-section {
      .filter-area {
        flex-direction: column;
        align-items: center;
      }
    }
    
    .news-list-section {
      .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }
  }
}
</style>
```

#### 1.4 功能实现
```javascript
<script>
export default {
  name: 'NewsPage',
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedCategory: '',
      dateRange: null,
      
      // 分页
      currentPage: 1,
      pageSize: 12,
      total: 0,
      
      // 数据
      newsList: [],
      loading: false
    }
  },
  
  created() {
    this.fetchNewsList()
  },
  
  methods: {
    // 获取新闻列表
    async fetchNewsList() {
      this.loading = true
      try {
        // 模拟API请求
        const response = await this.mockGetNewsList({
          page: this.currentPage,
          pageSize: this.pageSize,
          keyword: this.searchKeyword,
          category: this.selectedCategory,
          startDate: this.dateRange ? this.dateRange[0] : null,
          endDate: this.dateRange ? this.dateRange[1] : null
        })
        
        this.newsList = response.data.list
        this.total = response.data.total
      } catch (error) {
        this.$message.error('获取新闻列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchNewsList()
    },
    
    // 分类筛选
    handleCategoryChange() {
      this.currentPage = 1
      this.fetchNewsList()
    },
    
    // 日期筛选
    handleDateChange() {
      this.currentPage = 1
      this.fetchNewsList()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchNewsList()
    },
    
    // 跳转详情
    goToDetail(id) {
      this.$router.push(`/news/${id}`)
    },
    
    // 格式化日期
    formatDate(date) {
      return this.$utils.formatDate(date, 'YYYY-MM-DD')
    },
    
    // 模拟数据
    mockGetNewsList(params) {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            data: {
              list: this.getMockNewsData(),
              total: 48
            }
          })
        }, 500)
      })
    },
    
    getMockNewsData() {
      return [
        {
          id: 1,
          title: '基地成功举办2024年虚拟仿真实验教学研讨会',
          summary: '为进一步推进虚拟仿真实验教学改革，提升实验教学质量，我基地于近日成功举办了2024年虚拟仿真实验教学研讨会。来自全国各地的专家学者齐聚一堂，共同探讨虚拟仿真技术在教学中的应用与发展。',
          coverImage: 'https://via.placeholder.com/400x200?text=研讨会',
          categoryName: '基地动态',
          category: 'base_news',
          publishTime: '2024-01-15 10:00:00',
          views: 256,
          author: '基地办公室'
        },
        {
          id: 2,
          title: '基地新增5个虚拟仿真实验项目通过教育部认定',
          summary: '近日，教育部发布了2024年度虚拟仿真实验教学项目认定结果，我基地申报的5个项目全部通过认定，涵盖了机械工程、电子信息、化学工程等多个学科领域。',
          coverImage: 'https://via.placeholder.com/400x200?text=项目认定',
          categoryName: '成果展示',
          category: 'achievement',
          publishTime: '2024-01-12 14:30:00',
          views: 189,
          author: '项目管理部'
        },
        {
          id: 3,
          title: '基地与多家企业签署产学研合作协议',
          summary: '为深化产教融合，推进校企合作，我基地与华为、腾讯、阿里巴巴等知名企业签署了产学研合作协议，共同推进虚拟仿真技术在工业界的应用。',
          coverImage: 'https://via.placeholder.com/400x200?text=产学研合作',
          categoryName: '基地动态',
          category: 'base_news',
          publishTime: '2024-01-10 09:15:00',
          views: 342,
          author: '合作发展部'
        },
        {
          id: 4,
          title: '基地开展虚拟仿真实验教学师资培训',
          summary: '为提高教师虚拟仿真实验教学能力，我基地组织开展了为期三天的师资培训活动。培训内容包括虚拟仿真技术基础、实验设计方法、教学案例分析等。',
          coverImage: 'https://via.placeholder.com/400x200?text=师资培训',
          categoryName: '活动通知',
          category: 'activity',
          publishTime: '2024-01-08 16:00:00',
          views: 128,
          author: '培训中心'
        }
      ]
    }
  }
}
</script>
```

### 2. 新闻详情页 (`/news/:id`)

#### 2.1 路由配置
```javascript
// router/index.js
{
  path: '/news/:id',
  name: 'NewsDetail',
  component: () => import('@/views/news/detail.vue'),
  meta: {
    title: '新闻详情',
    requiresAuth: false
  }
}
```

#### 2.2 页面布局
```vue
<template>
  <div class="news-detail-page">
    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回列表</el-button>
      </div>

      <!-- 新闻详情 -->
      <div class="news-detail" v-if="newsDetail">
        <div class="news-header">
          <h1 class="news-title">{{ newsDetail.title }}</h1>
          <div class="news-meta">
            <span class="meta-item">
              <i class="el-icon-time"></i>
              发布时间：{{ formatDate(newsDetail.publishTime) }}
            </span>
            <span class="meta-item">
              <i class="el-icon-user"></i>
              作者：{{ newsDetail.author }}
            </span>
            <span class="meta-item">
              <i class="el-icon-view"></i>
              浏览量：{{ newsDetail.views }}
            </span>
            <span class="meta-item">
              <i class="el-icon-folder"></i>
              分类：{{ newsDetail.categoryName }}
            </span>
          </div>
        </div>

        <div class="news-content">
          <div class="content-wrapper" v-html="newsDetail.content"></div>
        </div>

        <!-- 分享和操作 -->
        <div class="news-actions">
          <div class="action-buttons">
            <el-button type="primary" @click="shareNews">
              <i class="el-icon-share"></i>
              分享
            </el-button>
            <el-button @click="collectNews" :type="isCollected ? 'warning' : 'default'">
              <i :class="isCollected ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
              {{ isCollected ? '已收藏' : '收藏' }}
            </el-button>
          </div>
        </div>

        <!-- 相关推荐 -->
        <div class="related-news">
          <h3>相关新闻</h3>
          <div class="related-list">
            <div class="related-item" v-for="item in relatedNews" :key="item.id" @click="goToDetail(item.id)">
              <img :src="item.coverImage" :alt="item.title" />
              <div class="related-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.summary }}</p>
                <span class="related-date">{{ formatDate(item.publishTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
```

#### 2.3 样式设计
```less
<style lang="less" scoped>
.news-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-button {
    margin-bottom: 20px;
  }
  
  .news-detail {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .news-header {
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 20px;
      margin-bottom: 30px;
      
      .news-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        line-height: 1.4;
        margin-bottom: 15px;
      }
      
      .news-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        color: #666;
        font-size: 14px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 5px;
          
          i {
            color: #4093f9;
          }
        }
      }
    }
    
    .news-content {
      .content-wrapper {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        
        // 富文本内容样式
        /deep/ {
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
          }
          
          p {
            margin-bottom: 15px;
            text-align: justify;
          }
          
          h1, h2, h3, h4, h5, h6 {
            margin: 25px 0 15px 0;
            color: #333;
          }
          
          ul, ol {
            margin: 15px 0;
            padding-left: 30px;
          }
          
          blockquote {
            border-left: 4px solid #4093f9;
            padding-left: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 4px;
          }
        }
      }
    }
    
    .news-actions {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
      
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
      }
    }
    
    .related-news {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
      }
      
      .related-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        
        .related-item {
          display: flex;
          background: #f8f9fa;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
          
          img {
            width: 100px;
            height: 80px;
            object-fit: cover;
          }
          
          .related-content {
            padding: 15px;
            flex: 1;
            
            h4 {
              font-size: 14px;
              color: #333;
              margin-bottom: 8px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            p {
              font-size: 12px;
              color: #666;
              margin-bottom: 5px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            .related-date {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-detail-page {
    padding: 20px 0;
    
    .news-detail {
      padding: 20px;
      
      .news-header {
        .news-title {
          font-size: 24px;
        }
        
        .news-meta {
          flex-direction: column;
          gap: 10px;
        }
      }
      
      .related-news {
        .related-list {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>
```

### 3. 通知公告页面 (`/notice`)

#### 3.1 功能实现
```javascript
// 通知公告页面基本复用新闻页面结构
// 主要差异：
// 1. 增加重要通知置顶功能
// 2. 通知状态标识（新发布、即将过期等）
// 3. 不同的分类体系

export default {
  name: 'NoticePage',
  data() {
    return {
      // 通知公告特有的筛选项
      noticeTypes: [
        { label: '全部', value: '' },
        { label: '重要通知', value: 'important' },
        { label: '教学通知', value: 'teaching' },
        { label: '活动通知', value: 'activity' },
        { label: '系统通知', value: 'system' }
      ],
      
      // 通知状态
      noticeStatus: [
        { label: '全部', value: '' },
        { label: '有效', value: 'active' },
        { label: '即将过期', value: 'expiring' },
        { label: '已过期', value: 'expired' }
      ]
    }
  },
  
  methods: {
    // 获取通知数据（示例）
    getMockNoticeData() {
      return [
        {
          id: 1,
          title: '【重要】关于2024年春季学期实验教学安排的通知',
          summary: '为保障新学期实验教学工作顺利进行，现就相关事宜通知如下：1. 实验课程开课时间为2024年3月1日；2. 所有实验课程需提前预约...',
          type: 'important',
          typeName: '重要通知',
          status: 'active',
          publishTime: '2024-01-20 10:00:00',
          expireTime: '2024-03-01 23:59:59',
          views: 458,
          isTop: true,
          author: '教务处'
        },
        {
          id: 2,
          title: '基地实验室维护通知',
          summary: '定期维护设备，确保实验教学质量。维护时间：2024年1月25日-27日，期间相关实验室暂停使用。',
          type: 'system',
          typeName: '系统通知',
          status: 'active',
          publishTime: '2024-01-18 15:30:00',
          expireTime: '2024-01-28 23:59:59',
          views: 123,
          isTop: false,
          author: '设备管理部'
        }
      ]
    }
  }
}
```

## 模拟数据规范

### 新闻数据结构
```javascript
const newsItem = {
  id: 1,                          // 新闻ID
  title: '新闻标题',               // 新闻标题
  summary: '新闻摘要',             // 新闻摘要
  content: '<p>新闻内容</p>',      // 新闻内容（富文本）
  coverImage: 'image_url',         // 封面图片
  category: 'base_news',           // 分类代码
  categoryName: '基地动态',        // 分类名称
  publishTime: '2024-01-15 10:00:00', // 发布时间
  views: 256,                      // 浏览量
  author: '基地办公室',            // 作者
  tags: ['标签1', '标签2'],        // 标签
  isTop: false,                    // 是否置顶
  status: 'published'              // 状态：published/draft/deleted
}
```

### 通知公告数据结构
```javascript
const noticeItem = {
  id: 1,                          // 通知ID
  title: '通知标题',               // 通知标题
  summary: '通知摘要',             // 通知摘要
  content: '<p>通知内容</p>',      // 通知内容（富文本）
  type: 'important',               // 通知类型
  typeName: '重要通知',            // 通知类型名称
  status: 'active',                // 状态：active/expired
  publishTime: '2024-01-20 10:00:00', // 发布时间
  expireTime: '2024-03-01 23:59:59',  // 过期时间
  views: 458,                      // 浏览量
  author: '教务处',                // 发布部门
  isTop: true,                     // 是否置顶
  urgency: 'high'                  // 紧急程度：high/medium/low
}
```

## 开发任务清单

### 基础设施
- [ ] 创建路由配置
- [ ] 设置页面基础结构
- [ ] 引入必要的样式文件

### 新闻模块
- [ ] 新闻列表页面开发
  - [ ] 页面布局和样式
  - [ ] 搜索功能实现
  - [ ] 分类筛选功能
  - [ ] 日期筛选功能
  - [ ] 分页功能
  - [ ] 响应式适配
- [ ] 新闻详情页面开发
  - [ ] 详情页布局
  - [ ] 富文本内容展示
  - [ ] 分享功能
  - [ ] 收藏功能
  - [ ] 相关推荐功能
  - [ ] 浏览量统计

### 通知公告模块
- [ ] 通知公告列表页面开发
  - [ ] 页面布局（复用新闻页面）
  - [ ] 通知类型筛选
  - [ ] 通知状态筛选
  - [ ] 重要通知置顶显示
  - [ ] 过期通知标识
- [ ] 通知公告详情页面开发
  - [ ] 详情页布局（复用新闻详情页）
  - [ ] 通知有效期显示
  - [ ] 紧急程度标识

### 公共组件
- [ ] 面包屑导航组件
- [ ] 文章卡片组件
- [ ] 搜索组件
- [ ] 分页组件
- [ ] 返回顶部组件

### 数据和API
- [ ] 创建模拟数据文件
- [ ] 模拟API接口实现
- [ ] 数据格式化工具函数
- [ ] 错误处理机制

### 测试和优化
- [ ] 功能测试
- [ ] 响应式测试
- [ ] 性能优化
- [ ] 代码清理和注释

## 技术要点

### 图片处理
- 使用懒加载优化图片加载
- 提供图片加载失败的占位图
- 支持不同尺寸的响应式图片

### SEO优化
- 设置合适的页面title和meta信息
- 使用语义化的HTML结构
- 提供面包屑导航

### 用户体验
- 加载状态提示
- 空数据状态提示
- 错误状态处理
- 平滑的页面过渡动画

### 无障碍访问
- 为图片添加alt属性
- 使用语义化的HTML标签
- 支持键盘导航
- 提供高对比度模式支持