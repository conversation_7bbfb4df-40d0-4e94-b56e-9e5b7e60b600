# 虚拟仿真实验平台 - 整体现有功能分析

本文档旨在综合前台、后台管理系统及后端服务的分析结果，提供一个对当前平台整体功能、架构和业务流程的完整、统一的视图。

---

## 一、 总体技术架构

本平台是一个技术栈清晰、前后端分离的现代化Web应用，由三个核心部分构成：

1.  **后端服务 (Java/Kotlin)**: 基于 **Spring Boot** 和 **Kotlin** 构建，是整个平台的大脑和数据中枢。它负责所有业务逻辑处理、数据存储和API服务。
2.  **后台管理系统 (Vue)**: 基于 **vue-element-admin** 构建，是**管理员**和**教师**进行平台管理和教学管理的核心工具。
3.  **前端门户 (Vue)**: 面向**学生**和**公众**的门户网站，是课程学习、任务执行和信息展示的主要入口。

**核心技术栈**:
- **数据库**: **MongoDB**，用于持久化存储所有核心业务数据。
- **缓存/配置中心**: **Redis**，用于存储Token会话信息和动态网站配置。
- **安全认证**: 全系统采用基于 **JWT (JSON Web Token)** 的Token认证机制，由后端生成和验证，前后端通过HTTP请求头传递，并通过后端拦截器实现精细化的API权限控制。

---

## 二、 核心业务流程与功能模块 (一体化视角)

### 1. 组织架构与多角色用户体系

这是一个以高校组织架构为基础的多角色系统。

- **后端**: 定义了`College`(学院), `Major`(专业), `Grade`(年级), `Clazz`(行政班)等数据实体，并提供了完整的CRUD API。同时，定义了`User`(学生/教师)和`AdminUser`(管理员)实体，通过`role`字段和权限注解区分不同角色的能力。
- **后台**: 为管理员提供了直观的UI界面，用于管理上述所有组织架构和用户信息。支持单个用户的增、删、改、查，也支持通过Excel进行**批量导入和导出**学生/教师，极大提高了管理效率。
- **前台**: 学生和教师登录后，可以在个人中心看到自己所属的院系、班级等信息，这些信息均由后端通过API提供。

### 2. 课程与实验教学全生命周期

平台的核心是围绕“课程”和“任务”构建的一套完整的教、学、练、评闭环。

1.  **课程创建 (后台)**: 管理员或教秘在后台创建课程，设置课程名称、封面、所属学科，并通过**富文本编辑器**录入详细的图文介绍、帮助文档等。课程可以被设置为“开放”或“关闭”状态。

2.  **任务指派 (后台)**: 管理员或教师可以基于已有课程创建“教学任务”，设定起止时间，并从全校学生中选择、组建成一个或多个**教学班**来参与此任务。这是平台实现 targeted teaching 的核心功能。

3.  **课程学习 (前台)**: 学生在前台门户浏览课程列表，进入课程详情页。点击“开始实验”时：
    - **后端 `Open API` 介入**: 后端首先验证学生身份，然后为其生成一个有时效性的`ticket`。
    - **安全跳转**: 前端携带`ticket`向课程应用（无论是网页版还是客户端）发起请求。
    - **Token交换**: 课程应用在后端使用`ticket`和预置的`appSecret`向平台后端换取一个有时效性的`access_token`，用于后续的数据交互。

4.  **数据回传 (后端 `Open API`)**: 课程应用在学生完成实验后，使用`access_token`将包含详细步骤分、用时、总分等信息的成绩数据安全地回传给平台后端。

5.  **成绩处理与记录 (后端)**: 后端接收到回传数据后，会进行一系列处理：
    - 创建一条详细的`CourseScore`（成绩记录）。
    - 更新对应的`CourseRecord`（学习/任务记录），计算并更新学生的最高分、总分等。
    - 实时更新存储在**Redis**中的课程统计数据（如总人次、平均分等）。

6.  **结果查看与反馈 (前台)**: 学生可以在个人中心的“我的课程”或“我的任务”中，查看自己历次的实验成绩、详细的步骤分报告，并对课程进行评价或提交实验报告。

7.  **教学监控与批改 (后台)**: 教师或管理员可以在后台查看指派任务的完成情况，浏览每个学生的成绩列表，并对学生提交的实验报告进行**在线批阅和打分**。

### 3. 动态内容与系统配置

平台具备高度的灵活性，大部分内容和行为都可以在后台进行动态配置。

- **后端**: 使用 **Redis** 存储网站的各类配置信息（如网站名称、Logo、介绍栏目、友情链接等），并提供API供后台修改。
- **后台**: 提供了“系统设置”模块，管理员可以通过简单的表单和富文本编辑器，实时修改前端门户的几乎所有可见内容，无需改动代码。
- **前台**: 页面在创建时（如`pageHeader.vue`的`created`生命周期），会通过Vuex `action`从后端获取最新配置，并动态渲染页面内容。

### 4. 系统监控与安全

- **后端**: 通过**AOP切面**和自定义注解`@SysOperaLog`，自动记录所有关键API的操作日志，并存储到MongoDB中。通过`@NeedToken`系列注解和`TokenInterceptor`拦截器，对所有需要授权的API进行保护。
- **后台**: “日志管理”页面提供了对这些操作日志的查询和导出功能，便于进行系统审计和问题排查。

---

## 四、 整体总结

当前系统是一个设计精良、功能完备、技术先进的**实验实训教学管理平台**。其前后端职责分明，架构清晰，通过前后端分离的模式和RESTful API + JWT的认证机制高效协作。

- **对于学生**，它提供了一个流畅、直观的学习和反馈平台。
- **对于教师和管理员**，它提供了一个功能强大、操作便捷，覆盖了从组织管理、课程创建、任务指派到成绩批阅、数据导出的全功能管理后台。
- **对于第三方课程开发者**，它提供了一套标准、安全的开放API，便于课程内容的接入和集成。

该平台为后续扩展“产教融合”、“资源中心”等新功能打下了极为坚实的基础。