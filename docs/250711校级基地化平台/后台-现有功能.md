# 后台管理系统 - 现有功能分析

本文档通过分析`fe-bigPlatform-admin`项目源代码，对当前虚拟仿真实验平台的后台管理功能进行梳理和总结。

---

## 一、 总体架构与技术栈

- **框架**: Vue.js (基于 vue-element-admin 模板)。
- **UI组件库**: Element UI + VXETable。
- **核心技术**: Vue Router, Vuex, Axios, Sass, ECharts。
- **权限控制**: 实现了基于角色的动态路由生成和权限控制。主要角色有 `administrator` (超级管理员), `teacherSecretary` (教秘), `teacher` (教师)。

---

## 二、 核心功能模块分析

### 1. 学校组织与用户管理 (School & User Management)

这是后台系统的基础，提供了对学校组织架构和各类用户的全面管理能力。

- **组织架构管理**:
    - **学院管理**: 对学院进行增、删、改、查。
    - **专业管理**: 对隶属于某个学院的专业进行增、删、改、查。
    - **年级管理**: 对年级（如2021级、2022级）进行增、删、改、查。
    - **行政班管理**: 对隶属于某个专业和年级的行政班级进行增、删、改、查。

- **用户管理**:
    - **教师管理**: 在学院下管理教师列表，支持新增、编辑、重置密码、禁用/启用账户，以及在不同学院间**转移**教师。
    - **学生管理**: 在行政班下管理学生列表，支持新增、编辑、重置密码、禁用/启用账户，以及在不同行政班之间**转移**学生。
    - **批量操作**: 支持通过上传Excel文件的方式，**批量导入**教师和学生到指定的学院或班级，也支持**批量导出**教师和学生名单。

### 2. 课程与教学管理 (Course & Teaching Management)

这是平台的核心业务模块，覆盖了从课程创建到任务安排、成绩管理的全过程。

- **课程内容管理**:
    - **学科管理**: 管理课程所属的学科分类。
    - **课程创建与编辑**: 提供一个功能强大的表单，用于创建和编辑课程。内容包括：
        - 基本信息：名称、封面、所属学科、所属学院、课程类型（网页/客户端）。
        - 详细介绍：通过**富文本编辑器(Tinymce)** 编辑实验介绍、帮助、联系方式、软硬件要求等。
        - 高级设置：是否开放、是否在首页显示、是否限制登录次数等。

- **任务安排与管理 (教学核心)**:
    - **创建任务**: 管理员或教秘可以基于已有的课程创建教学任务，并设置任务名称、起止时间、是否需要实验报告以及分数权重等。
    - **分配教学班**: 核心功能。在任务下，可以为不同的**教师**分配一个或多个**教学班**。教学班的学生可以从现有的行政班级中批量导入，也可以手动添加。
    - **教师权限**: 教师登录后，只能看到分配给自己的教学任务，并管理自己教学班的学生和成绩。

- **成绩与评价管理**:
    - **成绩总览**: 可以查看某个课程或某个任务下所有学生的成绩列表，包括课程得分、报告得分、综合分等。
    - **成绩详情**: 能够下钻到单个学生的单次实验，查看包含每一个步骤的得分、用时、操作次数等详细的成绩报告。
    - **实验报告批改**: 教师或管理员可以查看学生提交的实验报告（富文本格式），并进行在线打分。
    - **批量导出成绩**: 提供强大的成绩导出功能，可以按**整个任务**、**单个教师**或**单个教学班**等不同维度，将学生成绩导出为Excel文件。
    - **课程评价管理**: 查看和管理学生对课程提交的所有评价内容。

### 3. 系统与内容配置 (System & Content Management)

此模块赋予了管理员动态配置前端门户内容和系统行为的能力。

- **系统设置**: 
    - **平台用途配置**: 可将平台配置为“校级”或“院级”模式，影响前端导航和数据展示逻辑。
    - **注册功能开关**: 控制前台是否开放“校外用户注册”功能，以及注册后的用户是否需要审核才能启用。
- **网站信息配置**: 管理员可以修改网站的名称、Logo、首页Banner图、学校简介（富文本）、备案号等。
- **平台介绍配置**: 动态管理前端“基地介绍”页面的**多标签栏目**，可以任意增、删、改栏目，并使用富文本编辑器维护每个栏目的内容。
- **友情链接管理**: 动态管理首页的友情链接列表，包括名称、URL和图片。
- **帮助中心管理**: 对“帮助中心”页面的常见问题进行增、删、改、查。
- **用户反馈管理**: 查看并管理用户通过前台提交的教学反馈。

### 4. 系统维护 (System Maintenance)

- **日志管理**: 查看后台系统的详细操作日志，支持按模块、功能、IP等条件进行筛选和导出。
- **数据库管理**: 提供数据库备份管理功能，可以查看历史备份记录，并能手动触发一次“立即备份”。

---

## 三、 总结

该后台管理系统是一个**功能强大、高度可配置的教学管理平台**。它不仅仅是前端的功能后台，更是一个**围绕“实验实训”这一核心业务的完整管理解决方案**。其核心优势在于：

1.  **精细化的组织与用户管理**，支持批量操作，符合高校实际管理场景。
2.  **强大的课程与任务管理流程**，打通了从课程创建、任务分配到成绩批改、数据导出的完整闭环。
3.  **高度灵活的内容配置能力**，使得门户网站的大部分内容都可以由管理员在后台动态更新，而无需修改代码。