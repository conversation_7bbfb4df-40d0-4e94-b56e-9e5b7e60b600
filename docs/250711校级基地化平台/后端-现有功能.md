# 后端服务 - 现有功能分析

本文档通过分析`java-big-platform`项目源代码，对当前虚拟仿真实验平台的后端服务功能进行梳理和总结。

---

## 一、 总体架构与技术栈

- **核心框架**: Spring Boot
- **主要语言**: Kotlin
- **数据库**: MongoDB (使用 Spring Data MongoDB 进行操作)
- **缓存**: Redis (用于配置存储和Token管理)
- **安全与认证**: 使用自定义的JWT (JSON Web Token) 机制，通过`@NeedToken`, `@NeedAdminToken`等注解对API接口进行权限控制。
- **API风格**: 提供 RESTful 风格的API接口，与前端进行数据交互。
- **项目结构**: 采用典型的分层结构，包括`controller`(接口层), `model`(业务逻辑层), `entity`(数据实体层), `repository`(数据访问层)等。

---

## 二、 核心功能模块分析

### 1. 统一认证与权限控制

- **多角色Token体系**: 
    - **管理员Token**: 为后台管理员和教师生成，用于访问受保护的管理API。Token中包含了用户ID和角色信息。
    - **课程访问Token (access_token)**: 为需要接入的第三方课程客户端或网页应用生成，通过`ticket`机制换取，用于实验数据回传等操作。
    - **前台用户Token**: 为学生和教师在前台门户登录时生成，用于访问个人中心等需要登录的功能。
- **注解式权限拦截 (`interceptor/TokenInterceptor.java`)**: 通过自定义注解，以声明式的方式对Controller中的方法进行权限校验，大大简化了安全代码。
    - `@NeedAdminToken`: 要求请求头中必须包含有效的管理员/教师Token。
    - `@NeedToken`: 要求请求头中必须包含有效的前台用户Token。

### 2. 学校组织与用户数据管理

- **数据实体 (Entity)**: 定义了`College`, `Major`, `Grade`, `Clazz`, `User`等MongoDB文档实体，完整地映射了学校的组织架构。
- **CRUD操作**: 为上述所有实体提供了完整的增、删、改、查API接口，供后台管理系统调用。
- **批量导入/导出**: 
    - **导入**: 提供了通过解析Excel文件批量导入学生和教师数据的功能，并能自动处理班级、专业的创建逻辑。
    - **导出**: 提供了将指定学院的教师列表或指定班级的学生列表导出为Excel文件的功能。
- **用户管理逻辑**: 
    - **密码管理**: 使用加盐的MD5对密码进行加密存储，提供重置密码（默认`123456`）和用户修改密码的功能。
    - **用户转移**: 提供了在不同学院或班级之间转移教师/学生的功能。
    - **用户注册与启用**: 支持校外人员注册，并可通过邮件发送账号启用通知。

### 3. 课程与教学核心业务

- **课程与任务管理**: 
    - 提供了对`CourseSubject`(学科), `Course`(课程), `CourseTask`(任务), `TeachingClazz`(教学班)等核心实体的完整CRUD操作。
    - **教学班管理**: 实现了复杂的教学班管理逻辑，包括将行政班学生批量加入教学班、在任务中为教师分配教学班等。
- **学习过程与结果管理**: 
    - **课程记录 (`CourseRecord`)**: 为每个学生参与的**开放课程**或**任务**创建一条记录，跟踪其学习状态（如是否完成、分数、报告状态等）。
    - **成绩记录 (`CourseScore`)**: 记录学生每一次的实验成绩详情，包括开始/结束时间、用时、总分以及详细的**步骤分**。
    - **实验报告 (`CourseReport`)**: 存储学生提交的富文本实验报告，并支持教师在线批改、打分。
- **数据聚合查询**: 大量使用MongoDB的`Aggregation Framework`进行复杂的关联查询（如查询学生信息时，同时聚合出其学院、专业、班级名称），为前端提供了一站式的数据，减少了前端的请求次数。

### 4. 对外开放接口 (Open API)

- **Ticket与Token机制**: 为第三方应用（如课程客户端）提供了一套标准的、安全的认证和授权流程。
    1.  应用通过`appId`和用户身份信息获取一个有时效性的`ticket`。
    2.  应用使用`ticket`, `appId`和`appSecret`进行签名，换取一个有时效性的`access_token`。
    3.  应用使用`access_token`进行后续的API调用（如数据回传）。
- **数据回传接口**: 提供了`/v1/open/data_upload`接口，允许课程客户端将详细的实验结果数据（包括总分、用时、步骤详情等）安全地回传到平台，并自动更新对应的课程记录和成绩记录。

### 5. 系统配置与维护

- **动态配置管理**: 
    - 使用**Redis**作为配置中心，存储`webConfig`, `systemConfig`, `introduceConfig`等JSON格式的配置信息。
    - 提供了API接口，允许管理员在后台动态修改这些配置，实现了前端门户内容和系统行为的**热更新**。
- **日志系统**: 
    - 通过**AOP (Aspect-Oriented Programming)** 和自定义注解`@SysOperaLog`，以非侵入的方式实现了对所有重要Controller操作的日志记录。
    - 日志内容包括操作人、IP、请求URL、方法、参数、耗时等，并能区分正常和异常日志，为系统审计和问题排查提供了有力支持。
- **数据库备份**: 提供了API接口，可以手动触发执行服务器上的数据库备份脚本(`backup.sh`)。

---

## 三、 总结

该后端系统是一个**健壮、安全、功能全面**的平台。它不仅完美支撑了现有前台和后台管理系统的所有功能，还具备了良好的扩展性。

其核心优势体现在：
1.  **清晰的数据模型和分层架构**，使得业务逻辑清晰，易于维护。
2.  **完善的多角色认证和权限体系**，保证了系统的安全性。
3.  **强大的数据聚合与处理能力**，为复杂的教学管理场景提供了高效的数据支持。
4.  **高度可配置的设计**，大量系统行为和前台内容可通过后台修改，灵活性高。
5.  **面向未来的开放API设计**，为第三方课程的无缝集成打下了坚实基础。