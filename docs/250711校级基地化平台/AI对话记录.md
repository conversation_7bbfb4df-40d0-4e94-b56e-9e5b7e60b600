# 250715 需求分析阶段
##
提供给你的目录下有这些文件，是当前系统的前台（docs/250711校级基地化平台/前台-现有功能.md）、后台（docs/250711校级基地化平台/后台-现有功能.md）、后端（docs/250711校级基地化平台/后端-现有功能.md）的现有功能。
和这个系统现在的整体功能理解（docs/250711校级基地化平台/整体-现有功能.md）
现在的需求是要根据提供的新参数（docs/250711校级基地化平台/最终需要实现的参数.md），在这个系统上进行功能新增
请你对新参数进行理解后
（1）在同目录下输出一个新功能理解文档.md
（2）结合当前系统功能，在新功能开发文档.md最后，给到前台、后台、后端，需要增加开发的功能，按todos的方式列出了，并对每一点进行细致描述。
以便了解新功能，规划各端功能开发规划

## 
根据这个“新功能开发文档.md”，我们来做另外一个文档，我们的开发流程是前台开发先行，先完成前台开发，开发中使用模拟数据，前台功能功能无误后，才进行后台和后端对应的管理和接口功能开发。
所以请你新写一份“新功能-前台开发.md”，这个文档中要更专注于前台的功能实现，要细致一点，不要写估时，要细细拆分任务。并且要注意风格、布局、ui等元素，因为这个项目是没有美工配合的，只有程序员自己实现。

##
很好，你已经在子目录的按文件输出了每个模块的文档。
你知道，AI code有上下文限制，每次新开对话就需要重新告诉背景信息，为了完成前台开发会开多个对话，所以请你在“前台开发”目录下创建一个前台开发背景文档，这个文档的目的是每次新开对话，把这个文档给AI，然后把模块子任务给AI
AI就能理解当前的开发需求和历史信息，最终顺利的完成开发。

## 单一完整开发背景文档
docs/250711校级基地化平台/前台开发，是当前正在进行的开发任务，当前阶段是刚刚完成了整体需求分析，
请你查看这个目录下的 docs/250711校级基地化平台/前台开发/README.md
docs/250711校级基地化平台/前台开发/前台开发背景文档.md
docs/250711校级基地化平台/前台开发/UI设计规范.md
docs/开发规范/前台开发规范.md这些背景文档，

理解后，在docs/250711校级基地化平台/前台开发/ 中合并成一份完整的校级基地前台开发背景文档.md

## 基地内容模块
当前任务是在原有系统中，新增开发 一系列模块，当前需要开发 基地内容展示模块
请你查看并熟悉背景文档后，docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
再阅读docs/250711校级基地化平台/前台开发/基地内容展示模块.md 文档
然后完整的晚餐基地内容模块的开发

## 产教融合模块
当前任务是在原有系统中，新增开发一系列模块，当前已经完成了基地内容展示模块，现在需要开发 产教融合模块
请你先查看并熟悉背景文档，docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
再阅读docs/250711校级基地化平台/前台开发/产教融合模块.md
然后完整的完成产教融合模块的开发

##
1、请你在顶部导航中增加产教融合模块的导航入口
2、需要有企业详情弹窗、学生详情弹窗、招聘详情弹窗。在首页和各个列表中，点击某个信息打开详情弹窗。
3、招聘不需要投递，只是展示信息

## 资源中心模块
当前任务是在原有系统中，新增开发一系列模块，当前已经完成了基地内容展示模块、产教融合模块，现在需要开发 资源中心模块
请你先查看并熟悉背景文档，docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
docs/250711校级基地化平台/前台开发/资源中心模块.md
然后完整的完成资源中心模块的开发

## 编程总结
经过3个小时，CC快速的完成了所有功能，并且几乎没有错误
相比之下，kiro花了一天时间才完成功能，但是有一些bug。但是布局好像要好看一点，并且生成了很多更符合开发工程的文件。

cc这种模式更适合当下我的工作习惯。

## 使用新的forge工具修复和新增
docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md 是现在正在进行的工作，已经完成了文档里面的3个模块的前台模拟开发。
现在的工作是修复bug和新增小功能
1、src/views/components/pageHeader.vue 是顶部的导航条。对齐进行功能修改，宽度和网页等宽，然后logo和右侧的登录组放到左右两侧。中间导航条部分居中。

###
2、src/views/news/index.vue 基地新闻首页顶部的title和副标题，布局美化一下，使其更美观大方，最好有一点动态感。
3、src/views/notice/index.vue 通知首页的顶部同样如此，布局美化一下。

###
给这两个页面的顶部再增加一个美观的轮播功能，注意数据来源是src/model/NewsModel.js

### 
两个页面的顶部标题和轮播部分改为和页面等宽，然后中间部分居中吧，现在看起来好丑

### 
1、两个页面顶部的纯色背景区域太大了，太亮了，感觉和轮播等不协调。
2、再美化下两个页面的搜索区域
3、底部的分页组下面和页面底部的footer没有了边距了

### 
通知页面的顶部和搜索处，不要使用粉色调，这是一个学校的官方网站，还是用新闻页一样的蓝色吧

###
请你再优化下两个页面底部的分页工具，现在有点小，并且不方便操作
src/views/news/index.vue src/views/notice/index.vue ,这两个页面底部的分页工具布局完全乱了，请你删除并重构一下，符合当前页面风格就行。

## 系统原有页面的样式优化
1、src/views/home.vue 是这个系统的首页 src/views/course.vue是课程列表页，里面都有课程卡片，请你对课程卡片的样式进行美化，使其更美观大方，有动态感，但是一定不要增加增加元素、改变逻辑，只是做布局优化
2、src/views/introduce.vue 是中心简介页，左侧是校级和每个二级学院的介绍title导航，右侧会显示左侧选中的介绍内容。左侧是html富文本。请你也对整个页面进行布局重构优化，使其更美观大方，但是也不要改变原有功能和逻辑。

## 页尾footer定位优化
src/views/components/pageFooter.vue 是这个网站的页尾，在 src/App.vue 中，会在每个页面的底部显示
现在他没有做绝对定位，是直接放在要动态加载的页面的后面的，我想把它改成放到页面最底部绝对定位，但是又不想遮挡动态加载的元素！

## 250721 新闻接口后端功能开发
当前开发是在原前台系统上进行功能新增开发，现在已经完成了 基地新闻展示模块的完整功能开发，当前前台开发是用的静态数据，固定在model里面的。
请你阅读
1、docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
2、docs/开发规范/前台开发规范.md
然后开始开发后端代码，并且完成前后端的对接，
后端代码的路径在 D:\code\java\zyhd\java-big-platform
后端代码规范文件路径在 D:\code\java\zyhd\java-big-platform\docs\开发规范文档.md
你需要
1、理解前台需要的数据结构
2、按照后端开发规范完成entity、repository、model、controller的开发
3、修改前台接口、model，但是不要删除原静态实现方法（后面好测试）。实现和真实后端的接口对接

## 管理后台对应管理功能开发
很好，现在我们来开发管理后台。
关于管理后台的开发规范文档路径是 D:\code\fe\zyhd\fe-bigPlatform-admin\docs\后台开发规范.md
然后请你回顾前台需要的管理功能和后端D:\code\java\zyhd\java-big-platform需要的接口。
在后台路由D:\code\fe\zyhd\fe-bigPlatform-admin\src\router\index.js 中创建一个基地内容管理模块，然后完成新闻和通知所有需要管理的功能开发！
注意：不需要写测试用例、不需要你运行npm

## 
src/views/news/index.vue是新闻列表页， src/views/notice/index.vue是通知列表页。把新闻列表第70行、通知列表第77行，searchKeyword，需要改成mongo的模糊搜索，{'$regex': `.*${v}.*`}

##
两个页面的startDate和endDate两个搜索参数，实际上对应mongo后端的public Long publishTime，需要合并转换成这个的get和lte参数。

## 新闻和通知页面分类，改为redis配置 花费codemirror 3000多积分！！
现在需要你修改前台和后台，让新闻和通知的分类，改为从redis读取的配置，当前是写死在前台和后台的。
关于前台 获取配置，参考 src/views/home.vue第 198行
然后 
1、修改 src/views/news/index.vue 筛选中的分类，改为从redis读取的配置，并修改 src/views/news/detail.vue中可能涉及到的地方
2、修改 src/views/notice/index.vue 筛选中的分类，改为从redis读取的配置，并修改 src/views/notice/detail.vue中可能涉及到的地方

关于后台 获取和设置配置，参考 fe-bigPlatform-admin/src/views/systemManage/setting.vue 第299页
然后在 fe-bigPlatform-admin/src/views/contentManage 目录下是新闻和通知的列表和编辑页面，修改分类相关联的地方，改为从redis获取配置。

##
1、在后台 fe-bigPlatform-admin/src/views/contentManage/baseNewsEdit.vue和/Users/<USER>/code/fe/zyhd/fe-bigPlatform_2/fe-bigPlatform-admin/src/views/contentManage/noticeEdit.vue中
选择分类下来框的后面，增加一个编辑按钮，点击可以编辑这两个分类，然后调用ConfigModel.editConfig进行保存。

2、前台src/views/news/detail.vue和src/views/notice/detail.vue，两个详情页，没有正确获取并显示分类

##
对Config.get没有jsonParse使用错误

## 产教融合-后端接口开发
当前开发是在原前台系统上进行功能新增开发，现在已经完成了 前台产教融合模块的完整布局功能开发，但是当前前台开发是用的静态数据，固定在model里面的。
请你阅读
1、docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
2、docs/开发规范/前台开发规范.md
产教融合模块的前台view在 src/views/industry，有首页、企业、学生、照片的列表和详情页

然后开始开发后端代码，并且完成前后端的对接，
后端代码的路径在 java-big-platform
后端代码规范文件路径在 java-big-platform/docs/开发规范文档.md
你需要
1、查看产教融合前台的所有页面和src/model/IndustryModel.js，当然最终需要的数据以页面中的为准， 然后理解前台需要的数据结构
2、按照后端开发规范完成entity、repository、model、controller的开发
3、修改前台接口、model，但是不要删除原静态实现方法（后面好测试），最终实现和真实后端的接口对接

## 产教融合-后台开发
很好，现在我们来开发管理后台。
后台路径 fe-bigPlatform-admin

关于管理后台的开发规范文档路径是 fe-bigPlatform-admin/docs/后台开发规范.md
然后请你回顾前台需要的管理功能和后端java-big-platform需要的接口。
在后台路由fe-bigPlatform-admin\src\router\index.js 中创建一个产教融合管理模块，然后完成首页关联管理功能的开发！
注意：不需要写测试用例、不需要你运行npm

## 
package.json src/main.js,给当前项目增加fontawesome支持，然后使用fontawesome，优化下首页的布局，注意只改布局，要保持现有逻辑和接口不变。src/views/home.vue 
优化首页 中心简介页 课程列表页 课程详情页 反馈页 帮助页 个人中心相关
继续按此规则优化 src/views/courseInfo.vue 课程详情页
继续按此规则优化 src/views/feedback.vue 反馈页
继续按此规则优化  src/views/help.vue 帮助页

##
个人中心页 我的课程、我的任务，加载状态动态提醒

## 校企合作，企业信息，前后台
fe-bigPlatform-admin/src/views/industryManage/enterprise.vue 是企业信息的后台管理页面
src/views/industry/detail/enterprise.vue 是企业信息详情
java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/entity/IndustryEnterpriseEntity.java 是企业信息的后端mongo实体定义
检查前台页面没有把后台的那些字段信息显示出来，然后合理的在前台页面显示。

## 
java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/entity/IndustryEnterpriseEntity.java 是企业信息的后端mongo实体定义
1、详情页顶部，把企业规模也和地址、类型、成立时间放到一行
2、需要在前台、后台、后端中新增合作详情，是一个tinymce的富文本，后台关于tinymce的使用可以查看fe-bigPlatform-admin/src/views/systemManage/setting.vue

用户会特别关注校企合作，这个企业有哪些合作信息

## tinymce的用法
fe-bigPlatform-admin/src/views/industryManage/enterprise.vue 在这个后台管理页面里查找所有关于tinymce使用的信息，然后把tinymce的使用方法总结到docs/开发规范/后台的一个新md中

##
优化下这个企业信息卡片，可以用fontawesome图标，而且要根据信息类型做好布局和显示优化，使其更好看

## 产品融合-学生后台
现在来开发后台，参考 fe-bigPlatform-admin/src/views/systemManage/setting.vue第19行 把 fe-bigPlatform-admin/src/views/industryManage/student.vue 132行，学生照片现在是文本输入框，需要改成改成同样的上传组件，并且注意原有这个上传组件的依赖方法

### 前后台对齐
参考前台 src/model/IndustryModel.js 第1048行，这是学生信息的数据格式
然后对应的修改后台  fe-bigPlatform-admin/src/views/industryManage/student.vue ，按照前台的数据格式，对后台功能进行修改，实现前台的功能。
个人故事是富文本编辑器，参考文件docs/开发规范/后台/tinymce使用规范.md

###
成就列表和项目经历，必须用用户友好的方式编辑，现在太复杂了，文本分割用户不会弄的

###
技能标签现在是逗号分割，也不友好，改成tab新增删除这样的

### 优化学生详情页
这是学生详情的mongo记录，请你再根据后台的 fe-bigPlatform-admin/src/views/industryManage/student.vue 学生管理页，最终完善优化 fe-bigPlatform-admin/src/views/industryManage/student.vue学生页，
现在问题是
1、缺失部分信息
2、样式不好看
3、对某些信息的展示不合理
`
{
    "_id" : ObjectId("6882f7988b51a2000aff1337"),
    "studentId" : "406045237111164928",
    "name" : "杨文峰",
    "photo" : "http://*************:8085/20250725/615142823565463552.png",
    "major" : "测试专业",
    "graduationYear" : "24",
    "company" : "成都智云鸿道",
    "position" : "全栈开发",
    "category" : "outstanding",
    "categoryName" : "",
    "projectCount" : 1,
    "quote" : "1",
    "story" : "<div>2</div>",
    "achievements" : [ 
        {
            "id" : "1753413515318.7007",
            "title" : "1",
            "type" : "honor"
        }, 
        {
            "id" : "1753413517729.0845",
            "title" : "2",
            "type" : "competition"
        }
    ],
    "skills" : [ 
        "java", 
        "code"
    ],
    "projects" : [ 
        {
            "tech" : [ 
                "4"
            ],
            "role" : "2",
            "name" : "1",
            "description" : "3",
            "id" : "1753413520607.5376"
        }
    ],
    "createTime" : NumberLong(1753413528483),
    "deleted" : 0,
    "contact" : {
        "phone" : "6",
        "email" : "5"
    },
    "status" : "active",
    "featured" : false,
    "sort" : 1,
    "viewCount" : 0,
    "favoriteCount" : 0,
    "_class" : "com.cdzyhd.big_platform.entity.IndustryStudentEntity"
}
`

####
1、使用fontawesome图标优化这个页面的布局
2、active不需要，不是在线的意思
3、浏览次数、收藏次数不需要，不需要学术表现
4、技能特长中的文字没有居中
5、不需要联系方式和邮箱
6、页头的专业、公司、职位放到一行显示

#####
1、顶部的届毕业还没有和上面放到一行
2、详情中的各个区域的title图标还是黑色的，不好看
3、技能特长标签中的文字还是没有垂直居中
4 、src/main.js里面需要引入fontawesome图标，算了，直接改为引入所有fontawesome图标，这样就不必每次重复操作

###
当前页面是 src/views/industry/students.vue
1、参考 src/views/industry/enterprise.vue 底部的分页，给这个页面增加分页
2、用fontawesome 优化本页图标

##
这是招聘详情的实体数据，请你根据数据完善 fe-bigPlatform-admin/src/views/industryManage/job.vue 工作管理页面
1、补充缺失的信息管理
2、技能标签等需要通过逗号管理的方式，改为更友好的管理方式

完成后台管理后，对应的修改前台详情页面 src/views/industry/detail/recruitment.vue
1、补充缺失的信息
2、采用fontawesome优化图标显示
3、顶部的公司、地址、岗位全职类型放到一行显示
4、根据数据的情况，调整整个页面的布局，使其更符合工作岗位展示需求

`
{
        jobId: 1,
        title: '前端开发工程师',
        companyId: 1,
        companyName: '华为技术有限公司',
        companyLogo: 'https://via.placeholder.com/120x120?text=华为',
        category: 'development',
        categoryName: '技术开发',
        location: '深圳',
        salary: '15-25K',
        experience: '1-3年',
        education: '本科',
        jobType: '全职',
        description: '负责前端页面开发和用户体验优化，与产品设计师和后端开发工程师紧密合作，确保产品功能的完整实现。',
        fullDescription: `
<h3>工作职责</h3>
<ul>
<li>负责Web前端页面的开发和维护</li>
<li>与UI设计师、产品经理紧密配合，实现产品设计和交互效果</li>
<li>优化前端性能，提升用户体验</li>
<li>编写高质量的代码，确保代码的可维护性和可扩展性</li>
<li>参与技术方案讨论，推动前端技术创新</li>
</ul>

          <h3>任职要求</h3>
          <ul>
            <li>计算机相关专业本科以上学历</li>
            <li>熟练掌握HTML5、CSS3、JavaScript等前端基础技术</li>
            <li>熟练使用Vue.js、React等前端框架</li>
            <li>熟悉前端工程化工具，如Webpack、Vite等</li>
            <li>具有良好的代码规范和团队协作能力</li>
            <li>有移动端开发经验者优先</li>
          </ul>
          
          <h3>福利待遇</h3>
          <ul>
            <li>具有竞争力的薪酬体系</li>
            <li>完善的社会保险和住房公积金</li>
            <li>带薪年假、法定节假日</li>
            <li>员工培训和职业发展机会</li>
            <li>健身房、员工餐厅等生活设施</li>
          </ul>
        `,
        skills: ['Vue.js', 'React', 'JavaScript', 'CSS3', 'TypeScript'],
        urgent: true,
        hot: false,
        publishTime: '2024-01-15 10:00:00',
        requirements: [
          '计算机相关专业本科以上学历',
          '熟练掌握Vue.js、React等前端框架',
          '具有良好的代码规范和团队协作能力',
          '有移动端开发经验者优先'
        ],
        contactInfo: {
          contact: '李女士',
          phone: '0755-28780808',
          email: '<EMAIL>'
        }
`

## 资源中心，根据现有代码总结当前实际逻辑
src/views/resource 是资源中心的前台页面目录，src/model/ResourceModel.js是对应的model，src/api/resource.js是对应的api
当前只是完成了前台布局和模拟数据开发。
docs/开发规范/前台开发规范.md 是前台开发规范

请你阅读所有代码和所有文档，进行整体理解,然后在docs/250711校级基地化平台/前台开发 目录中，创建一个资源中心模块实际功能.md

###
这个项目中还存在一个实验课程的模块，资源中心的资源需要满足下面参数
2、资源可以关联到某个课程，也可以不关联作为公共资源。
课程的关键信息是 courseId,courseName
所以一个资源可以有课程的信息，也可以没有，没有就是公共资源。

当前的资源中心没有和课程关联的信息，也没有公共资源的信息。

请你修改api、model、views里面的页面和逻辑，完成课程关联或公共资源的逻辑。

###
请你根据修改的信息，也更新下刚刚的总结文档 docs/250711校级基地化平台/前台开发/资源中心模块实际功能.md

## 250728 资源中心后端功能开发
当前开发是在原前台系统上进行功能新增开发，现在已经完成了 资源中心模块的完整功能开发，当前前台开发是用的静态数据，固定在model里面的。
请你阅读
1、docs/250711校级基地化平台/前台开发/资源中心模块实际功能.md
2、docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
3、docs/开发规范/前台开发规范.md
然后开始开发后端代码，并且完成前后端的对接，
后端代码的路径在 D:\code\java\zyhd\java-big-platform
后端代码规范文件路径在 D:\code\java\zyhd\java-big-platform\docs\开发规范文档.md
你需要
1、理解前台需要的数据结构
2、按照后端开发规范完成entity、repository、model、controller的开发
3、修改前台接口、model，但是不要删除原静态实现方法（后面好测试）。实现和真实后端的接口对接
注意：不需要写测试用例、不需要你运行前台npm和后台java

## 管理后台对应管理功能开发
很好，现在我们来开发管理后台。
关于管理后台的开发规范文档路径是 D:\code\fe\zyhd\fe-bigPlatform-admin\docs\后台开发规范.md
然后请你回顾前台需要的管理功能和后端D:\code\java\zyhd\java-big-platform需要的接口。
在后台路由D:\code\fe\zyhd\fe-bigPlatform-admin\src\router\index.js 中创建一个资源中心管理模块，然后完成所有需要管理的功能开发！
注意：不需要写测试用例、不需要你运行npm

## 统一文件类型筛选
src/views/resource/category.vue第40行开始、src/views/resource/search.vue第56行开始都是资源的类型筛选，需要统一管理。
需要放到 src/enums/index.js 中进行统一管理
另外筛选的类型还不完整，需要参考后台的完整类型 fe-bigPlatform-admin/src/enums/index.js 第27行

## 资源详情页预览增强
详情页预览这儿可以根据所规定的可能的资源类型，再增加对应的预览方式。
src/enums/index.js 中的26行开始有资源文件类型的枚举
然后在 src/views/resource/detail.vue 126行增加文本文档的预览，然后office、压缩包类型还是像现在一样让下载查看
另外预览可以增加一个小按钮，直接在浏览器新窗口打开，因为浏览器本身也有针对不同文件类型的内置预览。

## 通知详情附件列表和下载
src/views/notice/detail.vue，请你先理解当前页面的附件功能。
然后通知详情的附件是一个数组，格式为
"attachments" : [
{
"name" : "Snipaste_2023-08-18_17-44-51.jpg",
"url" : "http://*************:8085/20250806/619652394472443904.jpg",
"uid" : NumberLong(1754488643321),
"status" : "success",
"fileId" : "619652394472443904",
"size" : 6212
}
],

需要你在 src/views/notice/detail.vue 第80行处增加一个附件列表的展示，展示样式要美观合理，符合当前页面风格。
然后点击附件列表中的文件，需要触发浏览器下载功能，直接下载文件。

## 我的收藏
我的收藏，现在不要全部、不要新闻、不要通知公告，只要教学资源

### 收藏接口对接
现在开始对接收藏这个页面的接口
我的收藏页路径是 src/views/user/favorite.vue
后端java kotlin接口路径是 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/ResourceController.kt 第270行

需要查看后端对应的实体、返回的数据、分页的数据
来修改 前台的 fetchFavoriteList和template中的分页

另外还需要使用 ResourceModel.toggleResourceFavorite来切换收藏状态

## 虚仿基地数据展示模块
当前任务是在原有系统中，新增开发一系列模块，当前已经完成了大部分模块内容的开发，现在需要开发  虚仿基地数据展示模块
请你先查看并熟悉背景文档，docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
再理解熟记最终要求实现的参数，这些功能是必须要实现的
1、拥有实训基地统计数据的展示页面，可以展示系统内的各模块的相关数据。
2、可以展示实训课程的相关数据，展示课程数量、实训人次、实训人数、实训用时等信息。
3、可以展示产教融合相关数据，展示产教融合信息数量、流量量等信息。
4、可以展示资源中心相关数据，可以展示各资源分类的资源数量、浏览量等数据。
需要参考的信息
一、课程统计，现有的统计功能
1、前台 课程详情页 src/views/courseInfo.vue 62行开始有这个课程的统计数据
2、后台课程统计页 fe-bigPlatform-admin/src/views/courseManage/statistic.vue 也有这个课程的统计数据
3、后端 统计 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/StatisticController.kt 有课程的两个统计接口
后端课程接口 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/CourseController.kt
后端课程记录接口 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/CourseController.kt
你需要顺着接口、找到实体和model进行进一步理解
二、产教融合
1、前台路径 src/views/industry
2、后端接口路径 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/IndustryController.kt，你需要顺着接口、找到实体和model进行进一步理解
三、资源中心
1、前台路径 src/views/resource
2、后端接口路径 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/ResourceController.kt，你需要顺着接口、找到实体和model进行进一步理解

最终
1、理解课程、产教融合、资源中心三个模块的前后端逻辑和数据结构，和可以展示的统计数据，务必要全面完善
2、在前台新增一个虚仿基地数据展示模块，完成课程、产教融合、资源中心三个模块的数据统计展示结果
3、在后端 java-big-platform/src/main/kotlin/com/cdzyhd/big_platform/controller/v1/StatisticController.kt 增加必要的接口

统计模块前台规范
1、是一个对外的综合统计页面，要像一个大数据展示的动态页面，要炫酷、要动态、要有各种图形统计表格，风格是蓝色，就是那种可视化大屏统计。
2、页面和浏览器等高等宽，不要竖向滚动条，而且可以切换全屏展示，也要注意响应式动态适配大小。
3、内部有tab可以切换3个统计模块
4、各项统计数据要尽量完整全面，但也要注意不要展示普通用户不能看的敏感数据。
