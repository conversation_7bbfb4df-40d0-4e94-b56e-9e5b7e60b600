# 现有平台功能分析 (VUE前端)

本文档通过分析`src`目录下的源代码，对当前虚拟仿真实验平台前端项目的功能进行梳理和总结。

---

## 一、 总体架构与技术栈

- **框架**: Vue.js (版本 2.x)。
- **UI组件库**: Element UI。
- **路由**: Vue Router，采用 history 模式。
- **状态管理**: Vuex，用于管理全局的用户信息、网站配置等。
- **HTTP请求**: Axios，封装在 `src/utils/requestAsync.js` 中，实现了请求/响应拦截，统一处理认证Token和错误。
- **代码结构**: 按照功能类型进行组织，`api` (接口定义), `model` (业务模型), `views` (页面视图), `components` (可复用组件), `store` (Vuex), `router` (路由) 等，结构清晰。

---

## 二、 核心功能模块分析

### 1. 用户认证与会话管理

- **登录 (`views/login.vue`, `model/UserModel.js`)**: 
    - 提供账号密码登录功能。
    - 登录成功后，从后端获取`token`和`userInfo`。
    - `token`被存储在Cookie中 (`BigPlatform-User-Token`)，用于后续所有API请求的身份验证。
    - `userInfo`被存储在Vuex中，用于全局展示用户信息。
- **首次登录处理**: 如果返回的用户信息中`hasLogin`为`false`，会弹出对话框，强制用户完善信息（设置新密码、绑定邮箱）。
- **用户注册 (`views/login.vue`)**: 系统配置中`regFunction`为`open`时，提供“校外人员注册”入口。注册时需要填写账号、密码、姓名、性别、手机、身份和邮箱，并通过邮箱验证码进行验证。
- **忘记密码**: 提供“忘记密码”功能，用户输入账号后，系统会向该账号绑定的邮箱发送重置密码邮件。
- **自动登录与权限控制 (`router/hook.js`)**: 
    - 通过路由前置守卫实现页面刷新后的状态恢复。
    - 如果Cookie中存在Token，则会自动请求用户信息并存入Vuex。
    - 如果Token过期或无效（API返回401），拦截器会清除Token并强制刷新页面（实际效果是跳转到首页，由于大部分页面没有强制登录，用户会看到未登录状态）。
- **退出登录**: 清除Cookie中的Token并跳转到首页。

### 2. 页面路由与布局

- **主布局 (`App.vue`)**: 应用的根组件，包含一个全局的页首(`pageHeader`)和页尾(`pageFooter`)，以及一个`<router-view/>`用于渲染各个页面。页首和页尾在登录页不显示。
- **路由定义 (`router/index.js`)**: 定义了平台的所有页面路径：
    - `/`: **首页** (`views/home.vue`)
    - `/login`: **登录页** (`views/login.vue`)
    - `/course`: **课程列表页** (`views/course.vue`)
    - `/courseInfo`: **课程详情页** (`views/courseInfo.vue`)
    - `/help`: **帮助中心** (`views/help.vue`)
    - `/feedback`: **教学反馈** (`views/feedback.vue`)
    - `/introduce`: **基地介绍页** (`views/introduce.vue`)
    - `/commentList`: **课程评价列表页** (`views/commentList.vue`)
    - `/user/*`: **个人中心** (使用嵌套路由，`views/user/layout.vue`为布局)
        - `info`: 我的信息
        - `course`: 我的课程
        - `task`: 我的任务

### 3. 门户与内容展示

- **首页 (`views/home.vue`)**:
    - 显示由后台配置的Banner图、学校/学院介绍。
    - **课程展示**: 默认显示“学校课程”，可按“学科”进行筛选。如果平台是校级模式，顶部导航可以选择具体学院，首页课程列表会相应地只显示该学院的课程。
    - **友情链接**: 显示后台配置的友情链接列表。
- **课程列表页 (`views/course.vue`)**: 一个专门的课程浏览页面，功能与首页的课程展示区类似，提供按学科筛选课程的功能。
- **课程详情页 (`views/courseInfo.vue`)**: 
    - 展示课程的详细图文介绍、帮助信息、联系方式、软硬件要求。
    - **开始实验**: 核心功能。根据课程类型（网页版/客户端版）提供不同的入口。网页版会获取一个`ticket`并拼接URL让用户在新窗口打开；客户端版则显示下载和使用说明。
    - **数据统计**: 实时从后端获取并展示该课程的浏览量、实验人次、人数、平均用时、完成率、通过率等，并用饼图展示“优秀/达标/不达标”的人次分布。
    - **任务安排**: 显示与该课程相关的最新3个任务。
    - **课程评价**: 轮播显示最新的5条课程评价。
- **帮助中心 (`views/help.vue`)**: 以Q&A的形式，按“学生用户”和“教师用户”分类展示常见问题及解答。
- **基地介绍 (`views/introduce.vue`)**: 根据后台配置，显示一个带左侧导航的多标签介绍页面（如基地概况、师资队伍等）。

### 4. 个人中心 (`views/user/*`)

- **整体布局 (`layout.vue`)**: 左侧为用户信息卡片和功能导航（我的信息、我的课程、我的任务），右侧为内容区。
- **我的信息 (`info.vue`)**: 
    - 展示用户的姓名、性别、学号、院系、专业、班级等基本信息。
    - 提供“修改密码”和“修改邮箱”的入口。
- **我的课程 (`course.vue`)**: 
    - 列表展示用户所有学习过的课程记录。
    - 每条记录都统计了学习次数、最高/最低/平均分。
    - **操作**: 进入课程、课程评价、查看我的成绩。
    - **成绩弹窗**: 以列表形式展示每一次的学习成绩，包括开始/结束时间、用时，并可查看包含详细步骤得分的“成绩详情”。
- **我的任务 (`task.vue`)**: 
    - 以标签页形式分类展示“未开始”、“进行中”、“已结束”的任务列表。
    - 每个任务都清晰地展示了负责教师、任务时间、课程得分、报告得分和总分。
    - **操作**: 进入课程、任务介绍、课程评价、我的成绩、填写/查看报告。
    - **填写报告**: 如果任务要求且课程已完成，用户可以通过一个富文本编辑器(`Tinymce`)来填写和提交实验报告。

### 5. API 与业务模型

- **API层 (`api/*.js`)**: 定义了与后端各模块交互的接口函数，覆盖了用户、课程、学院、配置、评论、记录、报告、任务、统计等所有业务实体。
- **Model层 (`model/*.js`)**: 对API进行了封装，处理了API调用的具体逻辑和返回数据的初步格式化。例如 `CourseRecordModel.getOneUserRecordList` 不仅调用API，还会对返回的分页数据进行处理，并对每条记录的分数进行计算（最高分、最低分、平均分）。这种分层使视图层的代码更简洁，只关心业务展示。

### 6. 通用组件与工具

- **富文本编辑器 (`components/Tinymce`)**: 封装了 Tinymce 编辑器，支持从CDN动态加载，并配置了丰富的插件和自定义的图片上传功能。
- **可拖拽对话框 (`directive/el-drag-dialog`)**: 一个Vue指令，应用在Element UI的Dialog上，可以使其头部可拖拽。
- **通用工具 (`utils/common.js`)**: 提供了大量通用辅助函数，如日期格式化、URL参数获取、Cookie操作、数组处理、精确计算等。
- **枚举 (`enums/index.js`)**: 定义了角色、课程类型等在代码中使用的常量，便于维护和理解。
- **过滤器 (`filters/index.js`)**: 定义了多个Vue过滤器，用于在模板中方便地格式化数据，如日期、数字、分数、角色名称等。

---

## 三、 现有功能总结

综上所述，该平台目前是一个功能相对完善的、面向学生的**虚拟仿真课程学习与实践平台**。其核心功能链路已经形成闭环：

1.  **发现课程**: 学生可以通过首页、课程页发现并了解课程。
2.  **参与学习**: 学生可以“开始实验”进入课程学习，学习过程和结果会被记录。
3.  **完成任务**: 如果课程被教师布置为任务，学生可以在“我的任务”中看到，并按要求完成。
4.  **提交产出**: 学生可以提交实验报告、对课程进行评价。
5.  **查看反馈**: 学生可以在“我的课程”和“我的任务”中查看自己的历次成绩、得分详情和报告分数。
6.  **获取帮助**: “帮助中心”和“教学反馈”为学生提供了支持渠道。
7.  **账号管理**: 学生可以在个人中心管理自己的密码和邮箱。

平台也为**校级/院级**运营提供了基础支持，可以通过后台配置首页内容、课程、任务等，并通过顶部导航在不同学院的视图间进行切换。