# 校级虚拟仿真实训基地平台 - 新功能开发文档

## 概述

基于现有虚拟仿真实验平台，本文档详细规划五大新功能模块的开发任务，确保各端协调开发，实现功能无缝升级。

---

## 一、整体开发架构规划

### 技术栈保持
- **前台**: Vue 2.x + Element UI + Vuex + Vue Router
- **后台**: Vue2 + vue-element-admin + Element UI + VXETable
- **后端**: Spring Boot + Kotlin + MongoDB + Redis + JWT

### 开发原则
- 保持现有代码结构和命名规范
- 新功能作为独立模块开发，避免影响现有功能
- 复用现有组件和工具函数
- 统一错误处理和权限控制机制

---

## 二、前台开发任务清单

### 模块1: 基地内容展示模块

#### T1.1 新增路由配置
- **任务描述**: 在 `router/index.js` 中新增基地新闻和通知公告相关路由
- **具体内容**: 
  - `/news` - 新闻列表页
  - `/news/:id` - 新闻详情页
  - `/notice` - 通知公告列表页  
  - `/notice/:id` - 通知公告详情页
- **依赖**: 无
- **预估工时**: 0.5天

#### T1.2 新闻列表页面开发
- **任务描述**: 开发 `views/news.vue` 新闻列表页面
- **具体内容**:
  - 新闻列表展示，包含标题、摘要、发布时间、浏览量
  - 分页功能
  - 搜索和筛选功能
  - 响应式布局设计
- **依赖**: T1.1, API接口
- **预估工时**: 2天

#### T1.3 新闻详情页面开发
- **任务描述**: 开发 `views/newsDetail.vue` 新闻详情页面
- **具体内容**:
  - 富文本内容展示组件
  - 支持图片、视频、链接等多媒体内容
  - 浏览量统计
  - 相关新闻推荐
- **依赖**: T1.1, API接口
- **预估工时**: 2天

#### T1.4 通知公告页面开发
- **任务描述**: 开发通知公告列表和详情页面
- **具体内容**:
  - 复用新闻页面组件架构
  - 适配通知公告的展示格式
  - 重要通知置顶功能
- **依赖**: T1.2, T1.3
- **预估工时**: 1.5天

#### T1.5 API接口集成
- **任务描述**: 在 `api/` 和 `model/` 中新增新闻通知相关接口
- **具体内容**:
  - `api/news.js` - 新闻接口定义
  - `api/notice.js` - 通知接口定义  
  - `model/NewsModel.js` - 新闻业务模型
  - `model/NoticeModel.js` - 通知业务模型
- **依赖**: 后端API
- **预估工时**: 1天

### 模块2: 产教融合模块

#### T2.1 产教融合主页开发
- **任务描述**: 开发 `views/industry.vue` 产教融合主页
- **具体内容**:
  - 轮播图组件（复用首页轮播图组件）
  - 合作企业展示模块
  - 优秀学生展示模块
  - 响应式布局
- **依赖**: 新增路由配置
- **预估工时**: 3天

#### T2.2 企业信息列表页开发
- **任务描述**: 开发企业信息和招聘信息列表页
- **具体内容**:
  - 企业信息列表展示
  - 招聘信息列表展示
  - 搜索、筛选、分页功能
  - 列表项卡片式布局
- **依赖**: API接口
- **预估工时**: 2.5天

#### T2.3 产教融合详情页开发
- **任务描述**: 开发产教融合信息详情页面
- **具体内容**:
  - 详情内容展示
  - 相关推荐功能
  - 分享功能
- **依赖**: API接口
- **预估工时**: 2天

#### T2.4 API接口集成
- **任务描述**: 新增产教融合相关API接口
- **具体内容**:
  - `api/industry.js` - 产教融合接口
  - `model/IndustryModel.js` - 产教融合业务模型
- **依赖**: 后端API
- **预估工时**: 1天

### 模块3: 资源中心模块

#### T3.1 资源中心主页开发
- **任务描述**: 开发 `views/resource.vue` 资源中心主页
- **具体内容**:
  - 资源分类导航
  - 资源列表展示
  - 搜索、筛选功能
  - 资源类型图标展示
- **依赖**: 新增路由配置
- **预估工时**: 3天

#### T3.2 资源预览页面开发
- **任务描述**: 开发 `views/resourceDetail.vue` 资源预览页面
- **具体内容**:
  - 多媒体文件在线预览组件
  - 文本、图片、音频、视频预览支持
  - 收藏功能
  - 浏览量和收藏量展示
  - 下载功能
- **依赖**: API接口
- **预估工时**: 4天

#### T3.3 用户收藏功能开发
- **任务描述**: 在个人中心添加资源收藏管理
- **具体内容**:
  - 在 `views/user/` 下新增收藏页面
  - 收藏列表展示
  - 取消收藏功能
- **依赖**: 现有个人中心架构
- **预估工时**: 2天

#### T3.4 API接口集成
- **任务描述**: 新增资源中心相关API接口
- **具体内容**:
  - `api/resource.js` - 资源接口
  - `model/ResourceModel.js` - 资源业务模型
- **依赖**: 后端API
- **预估工时**: 1.5天

### 模块4: 实训课程作业考试模块

#### T4.1 学生考试列表页开发
- **任务描述**: 在个人中心添加考试模块
- **具体内容**:
  - 在 `views/user/` 下新增 `exam.vue`
  - 待考试、已完成考试列表
  - 考试状态标识
  - 成绩查看功能
- **依赖**: 现有个人中心架构
- **预估工时**: 2天

#### T4.2 H5答题页面开发
- **任务描述**: 开发移动端优化的答题页面
- **具体内容**:
  - 新增 `views/exam.vue` H5答题页面
  - 单选、多选、判断题组件
  - 答题进度显示
  - 倒计时功能
  - 自动提交功能
  - 移动端适配
- **依赖**: API接口
- **预估工时**: 4天

#### T4.3 二维码扫描功能
- **任务描述**: 集成二维码扫描进入考试功能
- **具体内容**:
  - 二维码解析功能
  - 考试权限验证
  - 错误处理机制
- **依赖**: T4.2
- **预估工时**: 1天

#### T4.4 API接口集成
- **任务描述**: 新增考试相关API接口
- **具体内容**:
  - `api/exam.js` - 考试接口
  - `model/ExamModel.js` - 考试业务模型
- **依赖**: 后端API
- **预估工时**: 1.5天

### 模块5: 实训基地数据展示模块

#### T5.1 数据展示页面开发
- **任务描述**: 开发 `views/statistics.vue` 数据展示页面
- **具体内容**:
  - 数据卡片展示组件
  - ECharts图表集成
  - 实时数据更新
  - 响应式图表布局
- **依赖**: 新增路由配置
- **预估工时**: 3天

#### T5.2 图表组件开发
- **任务描述**: 基于ECharts开发通用图表组件
- **具体内容**:
  - 饼图、柱状图、折线图组件
  - 数据格式适配
  - 图表交互功能
- **依赖**: ECharts库
- **预估工时**: 2天

#### T5.3 API接口集成
- **任务描述**: 新增统计数据API接口
- **具体内容**:
  - `api/statistics.js` - 统计接口
  - `model/StatisticsModel.js` - 统计业务模型
- **依赖**: 后端API
- **预估工时**: 1天

### 通用任务

#### T6.1 导航菜单更新
- **任务描述**: 更新 `components/pageHeader.vue` 导航菜单
- **具体内容**:
  - 添加新模块导航入口
  - 保持导航层级合理性
  - 响应式导航适配
- **依赖**: 各模块路由完成
- **预估工时**: 1天

#### T6.2 通用组件扩展
- **任务描述**: 扩展 `components/` 下的通用组件
- **具体内容**:
  - 富文本展示组件
  - 文件预览组件
  - 列表搜索组件
  - 分页组件扩展
- **依赖**: 具体需求确定
- **预估工时**: 2天

---

## 三、后台开发任务清单

### 模块1: 基地内容展示管理

#### B1.1 新闻管理页面开发
- **任务描述**: 开发新闻管理CRUD页面
- **具体内容**:
  - 新闻列表页面，支持搜索、筛选、排序
  - 新闻新增/编辑表单，集成Tinymce富文本编辑器
  - 新闻发布状态管理
  - 批量操作功能
- **依赖**: 后端API
- **预估工时**: 3天

#### B1.2 通知公告管理页面开发
- **任务描述**: 开发通知公告管理CRUD页面
- **具体内容**:
  - 复用新闻管理页面架构
  - 通知重要性标识设置
  - 通知范围设置功能
- **依赖**: B1.1, 后端API
- **预估工时**: 2天

### 模块2: 产教融合管理

#### B2.1 产教融合轮播图管理
- **任务描述**: 开发轮播图管理页面
- **具体内容**:
  - 轮播图列表管理
  - 图片上传和编辑
  - 排序和显示控制
- **依赖**: 后端API
- **预估工时**: 2天

#### B2.2 合作企业管理
- **任务描述**: 开发合作企业信息管理页面
- **具体内容**:
  - 企业信息CRUD操作
  - 企业Logo和介绍管理
  - 合作状态管理
- **依赖**: 后端API
- **预估工时**: 2.5天

#### B2.3 优秀学生管理
- **任务描述**: 开发优秀学生展示管理页面
- **具体内容**:
  - 学生信息管理
  - 成就和作品展示管理
  - 显示状态控制
- **依赖**: 后端API
- **预估工时**: 2天

#### B2.4 招聘信息管理
- **任务描述**: 开发招聘信息管理页面
- **具体内容**:
  - 招聘信息CRUD操作
  - 职位信息和要求管理
  - 有效期管理
- **依赖**: 后端API
- **预估工时**: 2天

### 模块3: 资源中心管理

#### B3.1 资源管理页面开发
- **任务描述**: 开发资源管理CRUD页面
- **具体内容**:
  - 资源列表展示，支持文件类型筛选
  - 文件上传功能，支持多种类型
  - 资源分类管理
  - 资源上线/下线控制
  - 批量操作功能
- **依赖**: 后端API
- **预估工时**: 4天

#### B3.2 资源统计页面开发
- **任务描述**: 开发资源使用统计页面
- **具体内容**:
  - 资源浏览量和收藏量统计
  - 资源使用趋势图表
  - 热门资源排行
- **依赖**: 后端API, ECharts
- **预估工时**: 2.5天

### 模块4: 实训课程作业考试管理

#### B4.1 题库管理页面开发
- **任务描述**: 开发题库管理CRUD页面
- **具体内容**:
  - 题库列表和分类管理
  - 题目录入表单（单选、多选、判断）
  - 题目批量导入功能
  - 公有/私有题库权限管理
- **依赖**: 后端API
- **预估工时**: 4天

#### B4.2 考卷管理页面开发
- **任务描述**: 开发考卷组建和管理页面
- **具体内容**:
  - 考卷创建向导
  - 手动选题和随机组卷功能
  - 题目分数设置
  - 考卷预览功能
- **依赖**: B4.1, 后端API
- **预估工时**: 3.5天

#### B4.3 考试管理页面开发
- **任务描述**: 开发考试安排和监控页面
- **具体内容**:
  - 考试创建和时间设置
  - 参考学生管理
  - 考试状态监控
  - 二维码生成功能
- **依赖**: B4.2, 后端API
- **预估工时**: 3天

#### B4.4 成绩管理页面开发
- **任务描述**: 开发成绩查看和管理页面
- **具体内容**:
  - 考试成绩列表展示
  - 成绩统计分析
  - 成绩导出功能
  - 答题详情查看
- **依赖**: 后端API
- **预估工时**: 3天

### 模块5: 数据统计管理

#### B5.1 统计配置页面开发
- **任务描述**: 开发统计数据配置页面
- **具体内容**:
  - 统计项目配置
  - 数据展示方式设置
  - 统计周期设置
- **依赖**: 后端API
- **预估工时**: 2天

### 通用任务

#### B6.1 菜单权限配置
- **任务描述**: 更新后台菜单和权限配置
- **具体内容**:
  - 在现有权限系统中添加新模块权限
  - 更新角色权限分配
  - 菜单显示控制
- **依赖**: 现有权限系统
- **预估工时**: 1天

#### B6.2 文件上传组件扩展
- **任务描述**: 扩展现有文件上传组件
- **具体内容**:
  - 支持音频、视频文件上传
  - 文件大小和格式验证
  - 上传进度显示
  - 预览功能集成
- **依赖**: 现有组件库
- **预估工时**: 2天

---

## 四、后端开发任务清单

### 数据模型设计

#### E1.1 新增数据实体
- **任务描述**: 设计并实现新功能相关的数据实体
- **具体内容**:
  - `News` - 新闻实体
  - `Notice` - 通知公告实体
  - `Enterprise` - 企业信息实体
  - `Student` - 优秀学生实体
  - `Resource` - 资源实体
  - `UserFavorite` - 用户收藏实体
  - `Question` - 题目实体
  - `Paper` - 试卷实体
  - `Exam` - 考试实体
  - `ExamRecord` - 考试记录实体
- **依赖**: MongoDB
- **预估工时**: 3天

#### E1.2 数据关系设计
- **任务描述**: 设计实体间的关系和索引
- **具体内容**:
  - 资源与课程的关联关系
  - 考试与任务的关联关系
  - 用户收藏关系设计
  - MongoDB索引优化
- **依赖**: E1.1
- **预估工时**: 2天

### API接口开发

#### E2.1 基地内容展示API
- **任务描述**: 开发新闻和通知公告相关API
- **具体内容**:
  - NewsController - 新闻CRUD API
  - NoticeController - 通知公告CRUD API
  - 内容搜索和筛选API
  - 浏览量统计API
- **依赖**: E1.1
- **预估工时**: 3天

#### E2.2 产教融合API
- **任务描述**: 开发产教融合相关API
- **具体内容**:
  - IndustryController - 产教融合内容API
  - EnterpriseController - 企业信息API
  - 轮播图管理API
  - 优秀学生展示API
- **依赖**: E1.1
- **预估工时**: 3天

#### E2.3 资源中心API
- **任务描述**: 开发资源中心相关API
- **具体内容**:
  - ResourceController - 资源CRUD API
  - 文件上传和下载API
  - 资源收藏API
  - 资源统计API
  - 文件预览API
- **依赖**: E1.1
- **预估工时**: 4天

#### E2.4 考试系统API
- **任务描述**: 开发考试系统相关API
- **具体内容**:
  - QuestionController - 题库管理API
  - PaperController - 试卷管理API
  - ExamController - 考试管理API
  - 答题提交和评分API
  - 成绩查询和统计API
- **依赖**: E1.1
- **预估工时**: 5天

#### E2.5 统计数据API
- **任务描述**: 开发数据统计相关API
- **具体内容**:
  - StatisticsController - 统计数据API
  - 各模块数据聚合
  - 实时统计数据更新
  - 图表数据格式化
- **依赖**: 所有其他模块
- **预估工时**: 3天

### 系统功能扩展

#### E3.1 文件处理服务
- **任务描述**: 扩展文件处理能力
- **具体内容**:
  - 音频、视频文件上传处理
  - 文件格式验证和转换
  - 文件存储路径管理
  - 缩略图生成
- **依赖**: 现有文件系统
- **预估工时**: 3天

#### E3.2 权限系统扩展
- **任务描述**: 扩展现有权限控制系统
- **具体内容**:
  - 新模块权限注解定义
  - 角色权限配置扩展
  - API接口权限控制
- **依赖**: 现有权限系统
- **预估工时**: 2天

#### E3.3 Redis配置扩展
- **任务描述**: 扩展Redis配置管理
- **具体内容**:
  - 新模块配置项定义
  - 缓存策略设计
  - 统计数据缓存
- **依赖**: 现有Redis配置
- **预估工时**: 1.5天

#### E3.4 日志系统扩展
- **任务描述**: 扩展操作日志记录
- **具体内容**:
  - 新模块操作日志注解
  - 关键操作审计记录
  - 日志分类和查询优化
- **依赖**: 现有日志系统
- **预估工时**: 1天

### 数据迁移和初始化

#### E4.1 数据库迁移脚本
- **任务描述**: 开发数据库升级脚本
- **具体内容**:
  - MongoDB集合创建脚本
  - 索引创建脚本
  - 初始数据导入脚本
- **依赖**: E1.1, E1.2
- **预估工时**: 2天

#### E4.2 配置项初始化
- **任务描述**: 初始化新模块配置项
- **具体内容**:
  - Redis配置项初始化
  - 系统默认参数设置
  - 权限配置初始化
- **依赖**: E3.2, E3.3
- **预估工时**: 1天

---

## 五、开发排期建议

### 第一阶段 (2周)
**基础设施准备**
- 后端数据模型设计 (E1.1, E1.2)
- 后端基础API开发 (E2.1)
- 前台基地内容展示模块 (T1.1-T1.5)
- 后台新闻管理功能 (B1.1, B1.2)

### 第二阶段 (3周)
**核心功能开发**
- 后端资源中心API (E2.3)
- 后端统计数据API (E2.5)
- 前台资源中心模块 (T3.1-T3.4)
- 前台数据展示模块 (T5.1-T5.3)
- 后台资源管理功能 (B3.1, B3.2)

### 第三阶段 (3周)
**产教融合模块**
- 后端产教融合API (E2.2)
- 前台产教融合模块 (T2.1-T2.4)
- 后台产教融合管理 (B2.1-B2.4)
- 系统集成测试

### 第四阶段 (4周)
**考试系统开发**
- 后端考试系统API (E2.4)
- 前台H5答题系统 (T4.1-T4.4)
- 后台考试管理系统 (B4.1-B4.4)
- 移动端适配和优化

### 第五阶段 (1周)
**系统集成和优化**
- 全系统集成测试
- 性能优化
- 文档完善
- 部署准备

---

## 六、技术风险和应对措施

### 高风险项目
1. **H5答题系统**: 移动端兼容性和用户体验
2. **多媒体文件处理**: 服务器性能和存储
3. **实时统计数据**: 数据库查询性能

### 应对措施
1. **提前进行移动端测试**, 确保主流设备兼容性
2. **采用CDN或对象存储**处理大文件，减轻服务器压力
3. **使用Redis缓存统计数据**，设置合理的更新频率

### 依赖管理
- **前台依赖后端API**: 优先开发后端接口，提供Mock数据
- **后台依赖权限系统**: 早期完成权限扩展设计
- **统计依赖各模块数据**: 采用渐进式开发，先实现基础统计

---

## 七、质量保证措施

### 代码质量
- 遵循现有代码规范和命名约定
- 进行Code Review，确保代码质量
- 编写单元测试，特别是核心业务逻辑

### 功能测试
- 每个模块开发完成后进行功能测试
- 集成测试确保模块间协调工作
- 用户体验测试，特别是移动端功能

### 性能测试
- 大文件上传和下载性能测试
- 并发考试场景测试
- 统计查询性能测试

---

## 八、总结

本开发文档详细规划了校级虚拟仿真实训基地平台新功能的开发任务。通过合理的任务分解和排期安排，确保各端协调开发，最终实现功能无缝升级。

**关键成功因素**:
1. **严格按照现有技术架构**进行开发，确保系统一致性
2. **优先完成基础设施和数据模型**，为后续开发奠定基础
3. **采用渐进式开发策略**，降低技术风险
4. **重视移动端用户体验**，确保H5功能的实用性
5. **加强模块间的集成测试**，确保系统整体稳定性