## 250721 新闻接口后端功能开发
当前开发是在原前台系统上进行功能新增开发，现在已经完成了 基地新闻展示模块的完整功能开发，当前前台开发是用的静态数据，固定在model里面的。
请你阅读
1、docs/250711校级基地化平台/前台开发/校级基地前台开发背景文档.md
2、docs/开发规范/前台开发规范.md
然后开始开发后端代码，并且完成前后端的对接，
后端代码的路径在 D:\code\java\zyhd\java-big-platform
后端代码规范文件路径在 D:\code\java\zyhd\java-big-platform\docs\开发规范文档.md
你需要
1、理解前台需要的数据结构
2、按照后端开发规范完成entity、repository、model、controller的开发
3、修改前台接口、model，但是不要删除原静态实现方法（后面好测试）。实现和真实后端的接口对接