# 开发规范 forge
当前项目是一个nodejs12 vue2的后台管理系统，请你查看所有代码，特别是api model views目录，总结出本项目的开发规范，在docs目录下，创建一个后台开发规范.md文件

# forge
请你查看src/api/ClazzApi.js、src/model/ClazzModel.js、src/views/userManage/clazz.vue，年纪管理的所有页面，并查看依赖的的src/views/components/list/listSearchFilter.vue这个顶部搜索组件、并主义clazz.vue中js部分的methods方法里面的归类命名方法，在上面文档中补充或修复新建一个页面的模板要求

# forge
请你查看src/views/systemManage/setting.vue，熟悉tinymce这个富文本编辑器的使用，然后给src/views/contentManage/baseNewsEdit.vue中136行，src/views/contentManage/noticeEdit.vue的174行，增加tinymce富文本编辑器的使用，注意ref这些属性。

# forge
请你查看src/views/systemManage/setting.vue的19行，熟悉erp-uploader-one-pic这个单图上次组件的使用，然后给src/views/contentManage/baseNewsEdit.vue中85行，src/views/contentManage/noticeEdit.vue的144行，删除现有的删除方法，使用这种组件的方法，然后在docs目录下，新建一个这个组件的使用说明文档。

## 产教融合管理后台
### 1、轮播图片上次改成组件
参考 src/views/systemManage/setting.vue第19行 把 src/views/industryManage/banner.vue 这个轮播图管理页面的119行，轮播图上传改成同样的组件，并且注意原有这个上传组件的依赖方法

## 企业Logo图片
参考 src/views/systemManage/setting.vue第19行 把 src/views/industryManage/enterprise.vue 124行，企业Logo现在是文 本输入框，需要改成改成同样的上传组件，并且注意原有这个上传组件的依赖方法

## 资源文件上传
这儿的文件和图片上传都需要调用 src/model/FileModel.js FileModel.uploadOne 这一个方法，没有分开
返回的实体定义如下
public class FileEntity {
@Id
@JsonIgnore
public ObjectId id;

    // id
    public String fileId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 文件名称
    public String name;

    // 文件最后修改时间
    public Long lastModified;

    // 字节大小
    public Long byteSize;

    // 文件类型 存储前端的file中的type
    public String fileType;

    // 存储位置
    public String location;

    // 其他信息
    public JSONObject info;

}

返回的完整地址应该是main.js中的FILE_URL+location

## 关联课程
这儿现在是让直接输入课程名称，应该改为一个element的一个输入搜索下拉列表，列表数据是从课程接口获取的
src/model/CourseModel.js 的getList方法，传入
{
 "name":{
'$regex': `.*${v}.*`
}
}
获取的是一个课程列表，有courseId和name两个字段
然后注意处理选择后，本实体的courseId字段需要赋值为选择的课程的courseId，courseName需要赋值为course.name

## 统一分类配置
src/views/resourceManage/resource.vue 264行开始的分类配置
和 src/views/resourceManage/resourceEdit.vue 48行的分类配置
需要统一，并且提取到 src/enums/index.js中进行配置

## 通知附件上传
1、附件上传不限制文件类型
2、
上传需要调用 src/model/FileModel.js FileModel.uploadOne 这个方法
返回的实体定义如下
public class FileEntity {
@Id
@JsonIgnore
public ObjectId id;

    // id
    public String fileId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 文件名称
    public String name;

    // 文件最后修改时间
    public Long lastModified;

    // 字节大小
    public Long byteSize;

    // 文件类型 存储前端的file中的type
    public String fileType;

    // 存储位置
    public String location;

    // 其他信息
    public JSONObject info;

}

返回的完整地址应该是main.js中的FILE_URL+location

3、后端的附件列表 mongo实体定义如下
public JSONArray attachments;           // 附件信息数组
注意是一个数组