# erp-uploader-one-pic 单图上传组件使用说明

## 组件概述

`erp-uploader-one-pic` 是一个功能完善的单图片上传组件，提供图片上传、预览、删除等功能。该组件基于 Element UI 的 upload 组件封装，提供了更便捷的使用方式。

## 组件特性

- ✅ 单图片上传
- ✅ 图片预览
- ✅ 图片删除（带删除按钮）
- ✅ 图片尺寸限制
- ✅ 文件大小限制
- ✅ 文件类型验证
- ✅ 自定义上传尺寸显示
- ✅ 上传成功/删除回调

## 基本用法

### 1. 简单使用

```vue
<template>
  <erp-uploader-one-pic 
    :img-in="form.imageUrl"
    uploader-id="imageField"
    @uploadSuccess="data=>handleUploadSuccess(data)"
    @afterDelete="data=>handleDelete(data)">
  </erp-uploader-one-pic>
</template>

<script>
  import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
export default {
  components: {erpUploaderOnePic},
  data() {
    return {
      form: {
        imageUrl: ""
      }
    }
  },
  methods: {
    // 上传成功回调
    handleUploadSuccess(data) {
      let imgSrc = data[1] // 成功后文件地址
      let uploaderId = data[0] // 上传id
      this.form.imageUrl = imgSrc
      this.$message.success('图片上传成功')
    },
    
    // 删除图片回调
    handleDelete(data) {
      let uploaderId = data[0] // 上传id
      this.form.imageUrl = ""
      this.$message.success('图片已删除')
    }
  }
}
</script>
```

### 2. 完整配置示例

```vue
<template>
  <erp-uploader-one-pic 
    :img-in="form.logoUrl"
    uploader-id="logoUrl"
    uploader-title="网站Logo" 
    :uploader-size="[200,100]" 
    :pixel-limit="[550,50]"
    :size-limit="1024"
    :show-des="true"
    @uploadSuccess="data=>handleLogoUpload(data)"
    @afterDelete="data=>handleLogoDelete(data)">
  </erp-uploader-one-pic>
</template>

<script>
export default {
  data() {
    return {
      form: {
        logoUrl: "https://example.com/logo.png" // 已有图片
      }
    }
  },
  methods: {
    handleLogoUpload(data) {
      this.form.logoUrl = data[1]
      this.$message.success('Logo上传成功')
    },
    
    handleLogoDelete(data) {
      this.form.logoUrl = ""
      this.$message.success('Logo已删除')
    }
  }
}
</script>
```

## Props 参数

| 参数名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| imgIn | String | "" | 图片地址，用于显示已有图片 |
| uploaderId | String | "" | 上传器唯一ID，**必须提供** |
| uploaderSize | Array | [100, 100] | 上传器显示尺寸 [宽度, 高度] (px) |
| uploaderTitle | String | "" | 上传器标题 |
| sizeLimit | Number | 1024 | 文件大小限制 (KB) |
| pixelLimit | Array | [200, 200] | 图片分辨率限制 [宽度, 高度] (px) |
| showDes | Boolean | true | 是否显示描述信息 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| uploadSuccess | [uploaderId, imageUrl] | 上传成功时触发，返回上传器ID和图片URL |
| afterDelete | [uploaderId] | 删除图片时触发，返回上传器ID |

## 使用注意事项

### 1. uploaderId 必须唯一

每个组件实例都必须提供唯一的 `uploaderId`，这是组件内部 DOM 操作和事件处理的关键标识。

```vue
<!-- ✅ 正确：每个组件都有唯一的 uploaderId -->
<erp-uploader-one-pic uploader-id="avatar" />
<erp-uploader-one-pic uploader-id="banner" />

<!-- ❌ 错误：使用相同的 uploaderId -->
<erp-uploader-one-pic uploader-id="image" />
<erp-uploader-one-pic uploader-id="image" />
```

### 2. 回调函数参数格式

组件的回调函数返回数组格式的参数：

- `uploadSuccess`: `[uploaderId, imageUrl]`
- `afterDelete`: `[uploaderId]`

```javascript
// 正确的回调处理方式
handleUploadSuccess(data) {
  const uploaderId = data[0]
  const imageUrl = data[1]
  // 处理逻辑
}

handleDelete(data) {
  const uploaderId = data[0]
  // 处理逻辑
}
```

### 3. 文件限制

组件会自动进行以下验证：

- **文件类型**：只允许图片格式
- **文件大小**：根据 `sizeLimit` 参数限制
- **图片尺寸**：根据 `pixelLimit` 参数建议（仅提示，不强制）

### 4. 样式说明

组件提供默认样式，包括：

- 上传区域边框和悬停效果
- 删除按钮（红色圆形按钮，位于右上角）
- 图片预览区域
- 上传图标

如需自定义样式，可以通过 CSS 覆盖：

```scss
// 自定义上传区域样式
.erp-uploader-one-pic {
  .el-upload {
    border: 2px solid #409EFF !important;
    border-radius: 8px;
  }
  
  .delete-btn {
    background-color: #ff4757 !important;
  }
}
```

## 实际项目应用示例

### 1. 新闻编辑页面封面图片

```vue
<!-- src/views/contentManage/baseNewsEdit.vue -->
<el-form-item label="封面图片:">
  <erp-uploader-one-pic 
    :img-in="newsForm.coverImage"
    uploader-id="coverImage"
    uploader-title="封面图片" 
    :uploader-size="[200,100]" 
    :pixel-limit="[800,400]"
    :size-limit="2048"
    @uploadSuccess="data=>handleCoverUpload(data)"
    @afterDelete="data=>handleCoverDelete(data)">
  </erp-uploader-one-pic>
  <div class="upload-tip" style="margin-top: 10px;">
    <p>建议尺寸：800×400px，支持jpg、png格式，最大2MB</p>
  </div>
</el-form-item>
```

### 2. 系统设置页面 Logo

```vue
<!-- src/views/systemManage/setting.vue -->
<el-form-item label="网站Logo:" prop="logoUrl">
  <erp-uploader-one-pic 
    style="float:left;" 
    :img-in="webConfig.config.logoUrl"
    uploader-id="logoUrl"
    uploader-title="" 
    :uploader-size="[200,100]" 
    :pixel-limit="[550,50]"
    :size-limit="1024"
    @uploadSuccess="data=>fileUpload(data,webConfig.config,'')"
    @afterDelete="data=>fileDelete(data,webConfig.config,'')">
  </erp-uploader-one-pic>
</el-form-item>
```

### 3. 轮播图管理

```vue
<!-- 多个轮播图上传 -->
<template v-for="(item,index) in webConfig.config.indexFocus" :key="index">
  <erp-uploader-one-pic 
    style="float:left;margin-right: 15px" 
    :img-in="item.image"
    :uploader-id="'focus_' + index"
    uploader-title="" 
    :uploader-size="[200,100]" 
    :pixel-limit="[1920,500]"
    :size-limit="2048"
    @uploadSuccess="data=>handleFocusUpload(data, index)"
    @afterDelete="data=>handleFocusDelete(data, index)">
  </erp-uploader-one-pic>
</template>
```

## 常见问题

### Q: 为什么图片上传后没有显示？

A: 请检查：
1. `img-in` 属性是否正确绑定了图片URL
2. 上传成功回调中是否正确设置了图片地址
3. 图片URL是否可访问

### Q: 如何自定义上传接口？

A: 组件内部使用 `FileModel.uploadOne()` 方法，需要确保该方法已正确配置上传接口。

### Q: 删除按钮不显示怎么办？

A: 删除按钮只在有图片时显示，请确保 `img-in` 属性有值。

### Q: 如何禁用组件？

A: 可以通过 CSS 或包装一个外层条件来控制：

```vue
<erp-uploader-one-pic 
  v-if="!disabled"
  :img-in="form.imageUrl"
  uploader-id="image" />

<img v-else :src="form.imageUrl" style="width: 200px; height: 100px;" />
```

## 版本信息

- 当前版本：1.0.0
- 依赖：Element UI, Vue 2.x
- 作者：edgar
- 创建时间：2021.3.18
- 最后更新：2025.7.21

## 技术支持

如有问题或建议，请联系开发团队或查看项目文档。