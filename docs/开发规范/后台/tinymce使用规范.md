# TinyMCE 富文本编辑器使用规范

## 概述

本项目使用的是基于TinyMCE 4.9.3版本的富文本编辑器组件，支持图片上传、Word文档粘贴、多媒体插入等功能。

## 组件引入

```javascript
import Tinymce from "@/components/Tinymce";

export default {
  components: {
    Tinymce
  }
}
```

## 基本使用

### 1. 模板中使用

```vue
<template>
  <el-form-item label="内容详情:" prop="content">
    <tinymce
      v-model="formData.content"
      :height="300"
      id="tinymce_content"
      ref="tinymce_content"
      @initEd="handleEditorInit()"
    />
  </el-form-item>
</template>
```

### 2. 组件属性配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `v-model` | String | '' | 双向绑定的内容 |
| `height` | Number/String | 360 | 编辑器高度 |
| `width` | Number/String | 'auto' | 编辑器宽度 |
| `id` | String | 自动生成 | 编辑器唯一标识 |

### 3. 事件处理

```javascript
methods: {
  // 编辑器初始化完成回调
  handleEditorInit() {
    // 设置初始内容（重要：必须在初始化后设置）
    this.$refs['tinymce_content'].setContent(this.formData.content || '')
  }
}
```

## 弹窗中使用 TinyMCE

### 重要注意事项

在弹窗中使用TinyMCE时，需要特别处理编辑器的重新初始化：

```javascript
export default {
  watch: {
    // 监听弹窗显示状态
    'dialogInfo.dialog'(newVal) {
      if (newVal) {
        // 弹窗打开时，延迟初始化tinymce
        this.$nextTick(() => {
          setTimeout(() => {
            this.reinitializeTinymce()
          }, 100)
        })
      }
    }
  },
  
  methods: {
    // 重新初始化所有tinymce编辑器
    reinitializeTinymce() {
      const tinymceRefs = [
        'tinymce_content',
        // 添加其他编辑器ref名称
      ]

      tinymceRefs.forEach(refName => {
        const tinymceRef = this.$refs[refName]
        if (tinymceRef) {
          // 销毁现有编辑器
          try {
            tinymceRef.destroyTinymce()
          } catch (e) {
            console.log('销毁tinymce失败:', e)
          }

          // 重新初始化编辑器
          setTimeout(() => {
            try {
              tinymceRef.initTinymce()
            } catch (e) {
              console.log('重新初始化tinymce失败:', e)
            }
          }, 50)
        }
      })
    }
  }
}
```


## 常见问题及解决方案

### 1. 弹窗中编辑器无法显示

**原因**: DOM还未完全渲染完成就初始化了编辑器

**解决方案**: 
```javascript
// 在弹窗打开时延迟初始化
this.$nextTick(() => {
  setTimeout(() => {
    this.reinitializeTinymce()
  }, 100)
})
```

### 2. 编辑器内容无法回显

**原因**: 在编辑器初始化完成前设置了内容

**解决方案**: 
```javascript
// 在initEd回调中设置内容
handleEditorInit() {
  this.$refs['tinymce_content'].setContent(this.formData.content || '')
}
```

### 3. 编辑器高度不合适

**解决方案**: 
```vue
<!-- 设置合适的高度 -->
<tinymce :height="300" />
```

### 4. 全屏模式层级问题

组件已处理，全屏时z-index自动设置为10000。

## 最佳实践

### 1. 命名规范
- 编辑器ID使用 `tinymce_` 前缀，如：`tinymce_content`、`tinymce_description`
- ref名称与ID保持一致

### 2. 内容验证
```javascript
// 表单验证中检查内容是否为空
const validateContent = (rule, value, callback) => {
  if (!value || value.trim() === '' || value === '<p></p>') {
    callback(new Error('请输入内容'))
    return
  }
  callback()
}
```

### 3. 内容清理
```javascript
// 提交前清理HTML标签（如需要）
const cleanContent = (htmlContent) => {
  return htmlContent.replace(/<[^>]*>/g, '').trim()
}
```

### 4. 内存管理
```javascript
// 组件销毁时确保编辑器被正确销毁
destroyed() {
  if (this.$refs.tinymce_content) {
    this.$refs.tinymce_content.destroyTinymce()
  }
}
```


完整的使用示例，包含弹窗处理、内容回显、编辑器重新初始化等功能。