# 前台开发规范文档

## 1. 简介

本文档旨在为“虚拟仿真实验学习和管理平台”的前端开发提供一套统一的规范和指南，以确保代码的一致性、可维护性，并帮助新的开发成员（包括AI）快速熟悉项目。

---

## 2. 技术栈

- **核心框架**: [Vue.js](https://vuejs.org/) (v2.x)
- **UI 组件库**: [Element UI](https://element.eleme.io/)
- **状态管理**: [Vuex](https://vuex.vuejs.org/)
- **路由**: [Vue Router](https://router.vuejs.org/)
- **HTTP 请求**: [Axios](https://axios-http.com/)
- **CSS 预处理器**: [Less](https://lesscss.org/)
- **JS 工具库**: [js-cookie](https://github.com/js-cookie/js-cookie)

---

## 3. 项目结构

项目源代码位于 `src` 目录下，其核心子目录结构如下：

```
src/
├── api/          # API接口定义层，每个文件对应一个后端模块
├── assets/       # 静态资源（图片、图标等）
├── components/   # 全局可复用组件（如富文本编辑器）
├── config/       # 项目配置文件（如API地址）
├── directive/    # 自定义Vue指令
├── enums/        # 枚举常量
├── filters/      # 全局Vue过滤器
├── main.js       # 应用入口文件
├── model/        # 业务模型层，封装API调用和数据处理
├── router/       # 路由配置与导航守卫
├── store/        # Vuex状态管理
├── style/        # 全局样式文件
├── utils/        # 通用工具函数
└── views/        # 页面视图组件
```

---

## 4. 核心架构与模式

### 4.1. 数据请求流程 (View -> Model -> API)

本项目采用三层数据请求结构，严格分离视图、业务逻辑和API调用。

1.  **View (视图层, `views/**/*.vue`)**:
    - 负责UI展示和用户交互。
    - **不直接调用** `axios` 或 `api` 层。
    - 通过 `import` 引入 `model` 层的方法来获取或提交数据。
    - **示例**: `import {CourseModel} from "@/model/CourseModel";`

2.  **Model (业务模型层, `model/*.js`)**:
    - 负责处理具体业务逻辑，是视图与API的桥梁。
    - 封装对 `api` 层的调用。
    - 对API返回的数据进行处理和格式化，转换成视图需要的结构。例如，将分页数据和列表数据分离，或者计算统计值。
    - **示例**: `CourseModel.getOne(courseId)` 方法内部调用 `api/CourseApi.js` 中的 `getOne` 函数，并处理返回结果。

3.  **API (接口定义层, `api/*.js`)**:
    - 负责与后端接口的直接通信。
    - 每个文件对应后端的-个Controller或业务模块。
    - 所有请求都通过 `src/utils/requestAsync.js` 中封装的 `request_async` 函数发出。
    - **只负责定义请求**，不包含业务逻辑或数据处理。

**遵循此模式，可以极大地提高代码的可读性和可维护性。**

### 4.2. 状态管理 (Vuex)

- **位置**: `src/store/index.js`
- **核心状态 (`state`)**:
    - `userInfo`: 当前登录的用户信息。
    - `webConfig`, `systemConfig`, `introduceConfig`: 从后端获取的网站全局配置。
    - `isLoginPage`: 用于判断当前是否为登录页，以控制全局页眉页脚的显示。
    - `collegeId`: 用于校级平台模式下，切换不同学院的视图。
- **使用方法**:
    - **获取状态**: 在组件中，使用 `mapState` 辅助函数或 `this.$store.state.xxx`。
    - **修改状态**: **必须**通过 `dispatch` 一个 `action` 来实现。Action内部会获取数据，然后 `commit` 一个 `mutation` 来修改 `state`。不允许在组件中直接 `commit` mutation。
    - **示例**: 获取网站配置，应调用 `store.dispatch("getWebConfig")`。

### 4.3. 路由管理

- **配置文件**: `src/router/index.js`
- **导航守卫**: `src/router/hook.js`
    - `beforeEach` 守卫负责在每次路由跳转前进行权限检查。
    - 它会检查Cookie中是否存在 `BigPlatform-User-Token`。
    - 如果Token存在但Vuex中没有用户信息，它会自动调用 `UserModel.getInfoByToken()` 来恢复用户登录状态。
    - 如果用户未登录（无Token），访问非白名单页面会被处理（当前逻辑下，大部分页面允许未登录访问）。
- **添加新页面**:
    1.  在 `src/views/` 目录下创建新的 `.vue` 文件。
    2.  在 `src/router/index.js` 的 `routes` 数组中添加新的路由配置，使用动态 `import()` 实现懒加载。

---

## 5. 编码规范

### 5.1. 命名规范

- **文件名**: 使用驼峰命名法（camelCase），如 `courseInfo.vue`, `UserModel.js`。
- **Vue组件名**: 在组件内部 `name` 属性使用大驼峰命名法（PascalCase），如 `name: "CourseInfo"`。
- **变量与函数**: 使用驼峰命名法（camelCase），如 `courseList`, `getList()`。
- **常量**: 在 `enums/index.js` 中定义，使用驼峰命名法。

### 5.2. 代码风格

- **JavaScript**:
    - 使用单引号 `'`。
    - 语句结尾不加分号 `;`。
    - 异步操作统一使用 `async/await`。
    - 优先使用 `let` 和 `const`。
- **Vue**:
    - 组件的 `<script>` 部分，`data`, `methods`, `computed`, `mounted` 等选项应按此逻辑顺序排列。
    - 组件 `props` 定义应尽量详细，包含 `type` 和 `default`。
    - 触发父组件事件使用 `this.$emit('event-name', payload)`，事件名使用 kebab-case。
- **HTML**:
    - 属性名使用 kebab-case，如 `v-el-drag-dialog`。
    - Element UI 组件属性遵循其官方文档。

---

## 6. 样式与资源

### 6.1. 样式

- **全局基础样式**: `src/style/common.css`，包含 reset、flex布局、清浮动等通用工具类。新加的全局样式或工具类应放入此文件。
- **全局变量**: `src/style/app.less`，定义了项目的主色调（`@main-color`）等变量。
- **组件样式**:
    - 推荐在 `.vue` 文件中使用 `<style scoped lang="less">` 来编写组件的局部样式，避免全局污染。
    - 可以使用 `@import '../../style/app.less';` 来引入全局Less变量。
    - 样式类名应具有描述性，可参考BEM命名法（如 `.page-userCourse .course-list .li`）。

### 6.2. 静态资源

- **图片**: 存放于 `src/assets/imgs/`。
- **图标**: 存放于 `src/assets/icons/`。
- 在 `.vue` 模板中引用时，使用 `@` 别名，如 `<img src="@/assets/imgs/user.png">`。

---

## 7. 新功能开发流程示例 (添加“我的收藏”页面)

1.  **后端接口确认**: 假设后端提供了 `GET /v1/user/favorites` 接口。

2.  **API层 (`src/api/UserApi.js`)**: 添加一个新的导出函数。
    ```javascript
    export async function getMyFavorites(page, size, sort, data) {
      return request_async(API_URL + `/v1/user/favorites?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
    }
    ```

3.  **Model层 (`src/model/UserModel.js`)**: 添加一个处理业务逻辑的方法。
    ```javascript
    import { getMyFavorites } from '../api/UserApi';
    import { CommonModel } from "./CommonModel";

    class UserModel {
        // ... 其他方法
        static async getMyFavorites(page, size, sort, query) {
            let [data] = await getMyFavorites(page, size, sort, query);
            // 使用 CommonModel 格式化分页数据
            return CommonModel.generateListMongo(data.data);
        }
    }
    ```

4.  **路由层 (`src/router/index.js`)**: 在个人中心的 `children` 中添加新路由。
    ```javascript
    {
        path: '/user/',
        name: "UserLayout",
        component: () => import('@/views/user/layout'),
        children: [
            // ... 其他子路由
            {
                path: 'favorites',
                name: "UserFavorites",
                component: () => import('@/views/user/favorites')
            },
        ]
    },
    ```

5.  **视图层 (`src/views/user/favorites.vue`)**: 创建新页面文件。
    ```vue
    <template>
      <div class="page-user-favorites">
        <!-- 页面内容 -->
      </div>
    </template>
    <script>
    import { UserModel } from '../../model/UserModel';

    export default {
      name: "UserFavorites",
      data() {
        return {
          favoritesList: []
        }
      },
      async mounted() {
        let [list, pages] = await UserModel.getMyFavorites(0, 10, "", {});
        this.favoritesList = list;
      }
    }
    </script>
    <style scoped lang="less">
      /* 页面样式 */
    </style>
    ```

6.  **导航 (`src/views/user/layout.vue`)**: 在左侧导航菜单中添加入口。
    ```html
    <div :class="url==='/user/favorites'?'li active':'li'" @click="gotoUrl('/user/favorites')">我的收藏</div>
    ```

通过遵循以上步骤和规范，可以确保新开发的功能与现有系统保持一致。
