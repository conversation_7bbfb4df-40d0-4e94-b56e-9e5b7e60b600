# 后台开发规范

## 项目概述

本项目是基于Vue 2.6.10 + Element UI 2.13.2 + Node.js 12的后台管理系统，采用前后端分离架构。

## 技术栈

- **前端框架**: Vue 2.6.10
- **UI组件库**: Element UI 2.13.2  
- **构建工具**: Vue CLI 4.4.4
- **状态管理**: Vuex 3.1.0
- **路由管理**: Vue Router 3.0.2
- **HTTP客户端**: Axios 0.18.1
- **样式预处理器**: Sass
- **图表库**: ECharts 4.2.1
- **表格组件**: vxe-table 3.3.13
- **编辑器**: TinyMCE、TUI Editor
- **工具库**: jQuery、lodash、pinyin等

## 目录结构规范

```
src/
├── api/              # API接口管理
├── assets/           # 静态资源
├── components/       # 通用组件
├── config/           # 配置文件
├── directive/        # 自定义指令
├── filters/          # 过滤器
├── icons/            # 图标资源
├── lang/             # 国际化
├── layout/           # 布局组件
├── model/            # 数据模型层
├── router/           # 路由配置
├── store/            # Vuex状态管理
├── styles/           # 样式文件
├── utils/            # 工具函数
├── vendor/           # 第三方库
├── views/            # 页面视图
└── main.js           # 入口文件
```

## API层开发规范

### 1. 文件命名
- API文件采用`PascalCase + Api`命名：如`UserApi.js`、`CourseApi.js`
- 一个业务模块对应一个API文件

### 2. API函数规范
```javascript
import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/clazz/", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/clazz/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/clazz/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/clazz/list`, "post_body", data);
}

// 搜索列表（特殊查询接口）
export async function getSearchList(data) {
  return request_async(API_URL + `/v1/clazz/searchList`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/clazz/", "delete", data);
}
```

### 3. 函数命名规范
- **查询单个**: `getOne`
- **新增/修改**: `addOrEdit`
- **分页列表**: `getPageList`
- **全部列表**: `getList`
- **搜索列表**: `getSearchList`
- **删除**: `deleteOne`
- **特殊操作**: 使用动词+名词形式，如`transferStudentToNewClazz`

### 4. 请求方法规范
- `get`: GET请求，参数通过params传递
- `post_body`: POST请求，数据通过request payload传递
- `post_json`: POST请求，JSON格式数据
- `post_form`: POST请求，FormData格式
- `delete`: DELETE请求
- `put_body`: PUT请求，数据通过request payload传递

## Model层开发规范

### 1. 文件命名
- Model文件采用`PascalCase + Model`命名：如`UserModel.js`、`CourseModel.js`

### 2. Model类结构
```javascript
import {CommonModel} from "@/model/CommonModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  getSearchList
} from "@/api/ClazzApi";
import {msg_success, msg_err} from "@/utils/ele_component";
import axios from "axios";
import {API_URL} from "@/config/main";
import {getToken} from "@/utils/auth";

class ClazzModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      clazzId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 搜索列表
  static async getSearchList(document) {
    let [data] = await getSearchList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 特殊业务方法（如文件上传、导入导出等）
  static importStudentsByClazzId(file, clazzId) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('clazzId', clazzId)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/clazz/importStudentsByClazzId`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }
}

export {ClazzModel}
```

### 3. Model方法规范
- 所有方法使用`static async`声明
- 方法名与API函数保持一致
- 统一处理响应数据格式
- 成功返回数据，失败返回false或抛出异常
- 使用`CommonModel`处理分页数据
- 文件上传等特殊操作需要单独封装

### 4. 错误处理规范
```javascript
static async getOne(id) {
  let [res] = await getOne({clazzId: id})
  if (res.code === "000000") {
    return res.data
  } else {
    return false
  }
}
```

## Views层开发规范

### 1. 文件结构
```
views/
├── userManage/       # 用户管理模块
│   ├── student.vue   # 学生管理页面
│   ├── teacher.vue   # 教师管理页面
│   ├── clazz.vue     # 班级管理页面
│   └── grade.vue     # 年级管理页面
├── courseManage/     # 课程管理模块
└── systemManage/     # 系统管理模块
```

### 2. 新建页面完整模板

#### 复杂列表页面模板（带分页、搜索、筛选）
```vue
<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" 
                     @click="ListMethods().clickAddBtn()" 
                     style="background-color: #67C23A;border-color:#67C23A">
            新增{{entityName}}
          </el-button>
          <el-button class="el-button" type="success"
                     @click="importEntity.dialog=true" 
                     :loading="importEntity.doing">
            批量导入{{entityName}}
          </el-button>
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickExportBtn()" 
                     :loading="exportEntity.doing">
            批量导出{{entityName}}
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading" 
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="text" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row)">查看
          </el-button>
          <!-- 更多操作按钮 -->
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background 
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" 
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" 
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--新增/编辑弹窗-->
    <el-dialog :title="entityInfo.title"
               :visible.sync="entityInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="600px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" 
                 :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <!-- 更多表单项 -->
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" 
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>

    <!--导入文件input-->
    <input id="importEntityFile" type="file" style="display: none"
           @change="(files)=>{ListMethods().importEntityFileChange(files)}">

    <!--导入弹窗-->
    <el-dialog title="批量导入{{entityName}}"
               :visible.sync="importEntity.dialog"
               width="500px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">{{entityName}}导入模板.xlsx</span>
            <el-button type="default" size="mini" 
                       @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default" @click="importEntity.dialog=false">取 消</el-button>
        <el-button type="success" :loading="importEntity.doing"
                   @click="ListMethods().clickImportEntityBtn()">导入{{entityName}}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {EntityModel} from "@/model/EntityModel";
import {CommonModel} from "@/model/CommonModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";

export default {
  name: "entityManage",
  components: {ListSearchFilter},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 统一表单验证函数
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；""'、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文和数字
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {
        // 通过验证
      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,
      
      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 20  // 默认分页大小
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '名称',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '启用', value: 1},
                {label: '禁用', value: 0}
              ],
              change: function (value) {
                // 状态变化回调
              }
            }
          ]
        }
      },

      // 实体信息相关
      entityInfo: {
        dialog: false,
        title: "新增实体",
        type: "add",
        filter: {},
        edit: {
          name: ""
        },
        formRules: {
          'name': {
            required: true, 
            validator: (r, v, c) => validateName(r, v, c, 20, "名称"), 
            trigger: 'blur'
          }
        }
      },

      // 导入导出相关
      importEntity: {
        dialog: false,
        doing: false
      },
      exportEntity: {
        doing: false
      }
    }
  },

  async mounted() {
    // 初始化筛选条件
    await this.ListMethods().initFilter()
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await EntityModel.getPageList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增实体"
          $this.entityInfo.edit = {};
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },

        // 编辑按钮点击
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑实体"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },

        // 查看按钮点击
        clickViewBtn(entity) {
          // 跳转到详情页面或其他操作
          $this.$router.push(`/entity/detail?id=${entity.id}`);
        },

        // 初始化筛选条件
        async initFilter() {
          // 初始化下拉选项等
        },

        // 导出按钮点击
        async clickExportBtn() {
          $this.exportEntity.doing = true
          // 执行导出逻辑
          $this.exportEntity.doing = false
        },

        // 导入按钮点击
        clickImportEntityBtn() {
          const uploader = document.getElementById('importEntityFile')
          uploader.click()
        },

        // 文件选择变化
        async importEntityFileChange(files) {
          const file = files.target.files[0]
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importEntityFile').value = ''
          $this.importEntity.doing = true
          // 执行导入逻辑
          $this.importEntity.doing = false
        },

        // 下载模板
        clickDownloadBtn() {
          downloadFile("template_url", "模板文件名.xlsx")
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 新增提交
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查
              let listResult = await EntityModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名实体，请修改后再试！")
                return
              }
              // 确认提交
              if (await msg_confirm('确认要新增该实体吗？')) {
                let result = await EntityModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查（排除自身）
              let listResult = await EntityModel.getList({
                name: $this.entityInfo.edit.name,
                entityId: {"$ne": $this.entityInfo.edit.entityId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名实体，请修改后再试！")
                return
              }
              // 确认提交
              if (await msg_confirm('确认要编辑该实体吗？')) {
                let result = await EntityModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        }
      }
    },

    // 其他方法集合（如果需要）
    OtherMethods() {
      let $this = this;
      return {
        // 其他业务方法
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}
</style>
```

#### 简单列表页面模板（无分页）
```vue
<template>
  <div class="app-container">
    <!--顶部操作按钮-->
    <div class="top-tools">
      <div style="text-align: right">
        <el-button class="el-button" type="success" 
                   style="background-color: #67C23A;border-color:#67C23A"
                   @click="ListMethods().clickAddBtn()">
          新增{{entityName}}
        </el-button>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" 
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--弹窗-->
    <el-dialog :title="entityInfo.title"
               :visible.sync="entityInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="600px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" 
                 :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" 
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {EntityModel} from "@/model/EntityModel";

export default {
  name: "entityManage",
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 表单验证函数
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；""'、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文和数字
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {
        // 通过验证
      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,
      
      // 列表数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000  // 不分页时设置较大值
        }
      },

      // 实体信息
      entityInfo: {
        dialog: false,
        title: "新增实体",
        type: "add",
        filter: {},
        edit: {
          name: ""
        },
        formRules: {
          'name': {
            required: true, 
            validator: (r, v, c) => validateName(r, v, c, 20, "实体名称"), 
            trigger: 'blur'
          }
        }
      }
    }
  },

  mounted() {
    // 获取列表
    this.ListMethods().getList({})
  },

  methods: {
    // 列表方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表（不分页）
        async getList(query) {
          $this.lists.loading = true;
          $this.lists.list = await EntityModel.getList(query)
          $this.lists.loading = false
        },

        // 新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增实体"
          $this.entityInfo.edit = {
            type: "document"  // 定义类型
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          });
        },

        // 编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑实体"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 新增提交
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查
              let listResult = await EntityModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名实体，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该实体吗？')) {
                let result = await EntityModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查（排除自身）
              let listResult = await EntityModel.getList({
                name: $this.entityInfo.edit.name,
                entityId: {"$ne": $this.entityInfo.edit.entityId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名实体，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该实体吗？')) {
                let result = await EntityModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
```


### 3. 方法归类命名规范

基于clazz.vue的方法组织方式，所有页面必须按以下方式组织methods：

#### 方法集合命名规范
```javascript
methods: {
  // 列表相关方法集合 - 必须命名为ListMethods
  ListMethods() {
    let $this = this
    return {
      // 获取列表数据
      async getList(page, size, query) {},
      
      // 分页相关
      async pageChange(page) {},
      async pageLimitChange(size) {},
      
      // 搜索筛选相关
      clickSearchFilterBtn(query) {},
      clickCleanBtn() {},
      async initFilter() {},
      
      // 操作按钮相关
      async clickAddBtn() {},
      async clickEditBtn(entity) {},
      clickViewBtn(entity) {},
      clickDeleteBtn(entity) {},
      
      // 导入导出相关
      clickExportBtn() {},
      clickImportEntityBtn() {},
      importEntityFileChange(files) {},
      clickDownloadBtn() {},
      
      // 其他列表操作
      clickCustomBtn(entity) {}
    }
  },

  // 实体操作方法集合 - 必须命名为EntityMethods
  EntityMethods() {
    let $this = this;
    return {
      // 表单提交相关
      async clickAddBtn() {},
      async clickEditBtn() {},
      clickCancelBtn() {},
      
      // 表单联动相关
      onMajorChange(value) {},
      onGradeChange(value) {},
      
      // 远程搜索相关
      async queryMajor(value) {},
      async queryCollege(value) {},
      
      // 初始化筛选数据
      async initFilter() {},
      
      // 其他表单操作
      resetForm() {},
      validateForm() {}
    }
  },

  // 其他业务方法集合 - 根据业务需要命名，如TransferStudentMethods
  TransferStudentMethods() {
    let $this = this;
    return {
      clickTransferBtn(student) {},
      clickResetPasswordBtn(student) {},
      clickEnableUserBtn(student) {},
      clickDisableUserBtn(student) {}
    }
  }
}

特别注意：
<el-select v-model="jobInfo.edit.category" @change="v=>EntityMethods().onCategoryChange(v)">
  当在template里面使用这种命名的方法，需要v=>这样调用，否则无法正确调用方法

```

#### 方法命名具体规范
1. **列表操作类**：
   - `getList` - 获取列表数据
   - `pageChange` - 分页页码变化
   - `pageLimitChange` - 分页大小变化
   - `clickSearchFilterBtn` - 点击搜索按钮
   - `clickCleanBtn` - 点击清空按钮
   - `initFilter` - 初始化筛选条件

2. **按钮点击类**：
   - `clickAddBtn` - 点击新增按钮
   - `clickEditBtn` - 点击编辑按钮
   - `clickViewBtn` - 点击查看按钮
   - `clickDeleteBtn` - 点击删除按钮
   - `clickExportBtn` - 点击导出按钮
   - `clickImportEntityBtn` - 点击导入按钮

3. **表单操作类**：
   - `clickCancelBtn` - 点击取消按钮
   - `resetForm` - 重置表单
   - `validateForm` - 验证表单

4. **联动操作类**：
   - `onXxxChange` - 某字段变化时的处理
   - `queryXxx` - 远程搜索某数据

5. **文件操作类**：
   - `importEntityFileChange` - 导入文件选择变化
   - `clickDownloadBtn` - 点击下载模板

### 4. listSearchFilter组件使用规范

顶部搜索筛选组件的配置规范：

#### 搜索配置（search）
```javascript
searchFilter: {
  search: [
    {
      type: 'input',           // 输入框类型
      label: '班级名称',        // 显示标签
      key: 'name',             // 查询字段名
      value: '',               // 绑定值
      placeholder: '请输入',   // 占位符（可选）
      format: function (v) {   // 值格式化函数
        return {'$regex': `.*${v}.*`}  // MongoDB正则查询
      }
    },
    {
      type: 'autocomplete',    // 自动完成输入框
      label: '用户名',
      key: 'username',
      value: '',
      width: '200px',          // 宽度（可选）
      placeholder: '请输入用户名',
      searchFunction: async function(query, callback) {
        // 远程搜索函数
        let results = await SomeModel.search(query);
        callback(results);
      }
    }
  ],
  filter: [
    {
      type: 'select',          // 下拉选择
      label: '所属专业',
      key: 'majorId',
      value: '',
      data: [],                // 选项数据
      dataObject: {},          // 选项对象映射
      dataOrigin: [],          // 原始数据
      change: function(value) {
        // 选择变化回调
      }
    },
    {
      type: 'selectRemote',    // 远程搜索下拉
      label: '所属专业',
      placeholder: "请选择专业",
      key: 'majorId',
      width: "270px",
      value: '',
      data: [],
      loading: false,          // 加载状态
      searchFunction: async function (query) {
        // 远程搜索实现
        let list = await MajorModel.getList({name: {'$regex': `.*${query}.*`}})
        let results = list.map(item => ({
          label: item.name,
          value: item.majorId
        }))
        // 更新data数组
        this.data = results;
      },
      change: function(value) {
        // 选择变化处理
      }
    },
    {
      type: 'timeRange',       // 时间范围选择
      label: ['开始时间', '结束时间', '时间范围'],
      key: 'timeRange',
      value: '',
      change: function(value) {
        // 时间变化处理
      }
    },
    {
      type: 'timeHourRange',   // 带时分秒的时间范围
      label: ['开始时间', '结束时间', '创建时间'],
      key: 'createTimeRange',
      value: '',
      change: function(value) {
        // 时间变化处理
      }
    }
  ]
}
```

#### 组件事件处理
```javascript
// 在模板中绑定事件
<list-search-filter :search-filter="lists.searchFilter"
                    @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                    @clickCleanBtn="ListMethods().clickCleanBtn()">
</list-search-filter>

// 在方法中处理
ListMethods() {
  return {
    clickSearchFilterBtn(query) {
      this.lists.query = query
      this.getList(0, this.lists.pages.size, this.lists.query)
    },
    
    clickCleanBtn() {
      this.lists.query = {}
      this.getList(0, this.lists.pages.size, this.lists.query)
    }
  }
}
```

### 5. 数据结构规范

#### lists对象结构
```javascript
lists: {
  list: [],              // 列表数据
  loading: false,        // 加载状态
  query: {},             // 当前查询条件
  queryBase: {},         // 基础查询条件（不变的）
  sort: "",              // 排序条件
  pages: {               // 分页信息
    size: 20,            // 每页数量
    number: 1,           // 当前页码
    totalElements: 0,    // 总数量
    totalPages: 0        // 总页数
  },
  searchFilter: {        // 搜索筛选配置
    search: [],          // 搜索项配置
    filter: []           // 筛选项配置
  }
}
```

#### entityInfo对象结构
```javascript
entityInfo: {
  dialog: false,         // 弹窗显示状态
  title: "新增实体",     // 弹窗标题
  type: "add",           // 操作类型：add/edit
  filter: {},            // 表单筛选数据
  edit: {                // 编辑的实体数据
    name: ""
  },
  formRules: {           // 表单验证规则
    'name': {
      required: true,
      validator: validatorFunction,
      trigger: 'blur'
    }
  }
}
```

### 6. 表单验证规范

#### 统一验证函数
```javascript
// 在data()方法中定义通用验证函数
const validateName = (rule, value, callback, maxLength, propName) => {
  if (!(value === 0 || value)) {
    callback(new Error('请输入' + propName))
    return
  }
  if (value.length > maxLength) {
    callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
  }
  // 特殊字符检测
  let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
    regCn = /[·！#￥（——）：；""'、，|《。》？、【】[\]]/im;
  if (regEn.test(value) || regCn.test(value)) {
    callback(new Error('不支持特殊符号'));
  }
  // 仅支持中英文和数字
  regEn = /[a-zA-Z]+/
  regCn = /[\u4e00-\u9fa5]+/g
  let regNumber = /^[0-9]+$/
  if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {
    // 通过验证
  } else {
    callback(new Error('仅支持中英文和数字'));
  }
  callback()
}

// 在formRules中使用
formRules: {
  'name': {
    required: true, 
    validator: (r, v, c) => validateName(r, v, c, 20, "班级名称"), 
    trigger: 'blur'
  }
}
```

### 7. 样式规范

#### 必要的样式类
```scss
.app-container {
  // 页面容器样式
}

.header-container {
  // 头部筛选区域样式
  margin-bottom: 15px;
  
  .right {
    // 右侧操作按钮区域
    text-align: right;
  }
}

.top-tools {
  // 简单页面的顶部工具栏
  margin-bottom: 15px;
}

.pagination-container {
  // 分页容器样式
  margin-top: 20px;
}

.dialog-container {
  // 弹窗内容容器
}
```

## 组件开发规范

### 1. 通用组件目录
```
components/
├── Pagination/       # 分页组件
├── Upload/           # 上传组件
├── Charts/           # 图表组件
├── Tinymce/          # 富文本编辑器
└── ...
```

### 2. 组件命名规范
- 组件文件夹使用`PascalCase`命名
- 组件文件名使用`index.vue`或描述性名称
- 组件name属性使用`PascalCase`

### 3. Props定义规范
```javascript
props: {
  total: {
    required: true,
    type: Number
  },
  page: {
    type: Number,
    default: 1
  },
  limit: {
    type: Number,
    default: 20
  }
}
```

## 样式开发规范

### 1. 样式文件组织
```
styles/
├── index.scss        # 主样式文件
├── variables.scss    # 变量定义
├── mixin.scss        # 混入函数
├── element-ui.scss   # Element UI样式重写
├── sidebar.scss      # 侧边栏样式
└── btn.scss          # 按钮样式
```

### 2. 样式命名规范
- 使用BEM命名规范
- 组件样式使用scoped或模块化
- 全局样式统一管理

### 3. 变量使用规范
```scss
// variables.scss
$sidebar-width: 210px;
$navbar-height: 50px;
$tagsview-height: 34px;

// 使用
.sidebar-container {
  width: $sidebar-width;
}
```

## 路由管理规范

### 1. 路由文件结构
```javascript
// 常量路由（无需权限）
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  }
]

// 异步路由（需要权限控制）
export const asyncRoutes = [
  {
    path: '/user/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'userManage',
    meta: {
      title: '学校管理',
      icon: 'user',
      roles: ['administrator', 'teacherSecretary']
    },
    children: []
  }
]
```

### 2. 路由元信息规范
```javascript
meta: {
  title: 'title',           // 页面标题
  icon: 'svg-name',         // 图标
  roles: ['admin'],         // 访问角色
  noCache: true,            // 是否缓存
  affix: true,              // 是否固定标签
  breadcrumb: false,        // 是否显示面包屑
  activeMenu: '/path'       // 激活菜单路径
}
```

## 状态管理规范

### 1. Store模块划分
```
store/
├── index.js          # 主store文件
├── getters.js        # 全局getters
└── modules/          # 模块化store
    ├── app.js        # 应用状态
    ├── user.js       # 用户状态
    ├── permission.js # 权限状态
    └── tagsView.js   # 标签页状态
```

### 2. Module结构规范
```javascript
const state = {
  // 状态定义
}

const mutations = {
  // 同步操作
}

const actions = {
  // 异步操作
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

## 工具函数规范

### 1. 工具文件组织
```
utils/
├── auth.js           # 认证相关
├── common.js         # 通用工具
├── validate.js       # 验证函数
├── requestAsync.js   # 请求封装
└── ele_component.js  # Element组件工具
```

### 2. 函数命名规范
- 使用驼峰命名法
- 函数名要描述性强
- 工具函数要有注释说明

### 3. 请求工具使用规范
```javascript
import {request_async} from "@/utils/requestAsync";

// 使用方式
const [data, response] = await request_async(url, method, params);
```

## 配置管理规范

### 1. 配置文件结构
```javascript
// config/main.js
const API_URL = "http://localhost:9999/";
const FILE_URL = "/upload/";
const PLATFORM_ID = "bigPlatform";

export {
  API_URL,
  PLATFORM_ID,
  FILE_URL
}
```

### 2. 环境配置
- 开发环境配置
- 测试环境配置  
- 生产环境配置

## 国际化规范

### 1. 语言文件组织
```
lang/
├── index.js          # 国际化配置
├── zh.js            # 中文
├── en.js            # 英文
└── ja.js            # 日文
```

### 2. 翻译key命名
```javascript
// zh.js
export default {
  route: {
    dashboard: '首页',
    userManage: '学校管理'
  },
  navbar: {
    logOut: '退出登录',
    profile: '个人中心'
  }
}
```

## 权限控制规范

### 1. 指令权限
```vue
<el-button v-permission="['administrator']">管理员按钮</el-button>
```

### 2. 路由权限
```javascript
// 在路由meta中定义角色权限
meta: {
  roles: ['administrator', 'teacher']
}
```

### 3. 功能权限
```javascript
// 在组件中检查权限
computed: {
  ...mapState({
    adminRoles: state => state.user.roles,
    permissionArr: state => state.user.permission
  })
}
```

## 代码质量规范

### 1. ESLint配置
- 使用标准的JavaScript代码风格
- 强制使用分号
- 强制使用单引号
- 禁止未使用的变量

### 2. 注释规范
```javascript
/**
 * 获取用户列表
 * @param {number} page 页码
 * @param {number} size 每页数量
 * @param {Object} query 查询条件
 * @returns {Array} 用户列表数据
 */
async getList(page, size, query) {
  // 实现逻辑
}
```

### 3. 异常处理
```javascript
try {
  const result = await UserModel.getList(query);
  // 处理成功逻辑
} catch (error) {
  console.error('获取用户列表失败:', error);
  msg_err('获取数据失败，请稍后重试');
}
```

## 构建部署规范

### 1. 构建命令
```bash
# 开发环境
npm run dev

# 生产环境构建
npm run build:prod

# 测试环境构建
npm run build:stage
```

### 2. 环境变量配置
- `.env.development` - 开发环境
- `.env.staging` - 测试环境
- `.env.production` - 生产环境

## 性能优化规范

### 1. 代码分割
- 路由懒加载
- 组件按需加载
- 第三方库按需引入

### 2. 图片资源
- 使用适当的图片格式
- 图片压缩优化
- 雪碧图合并

### 3. 缓存策略
- 页面缓存配置
- API数据缓存
- 静态资源缓存

## 测试规范

### 1. 单元测试
- 使用Jest测试框架
- 组件测试覆盖率要求
- 工具函数测试

### 2. 集成测试
- API接口测试
- 页面功能测试
- 用户流程测试

## 版本控制规范

### 1. Git工作流
- 使用Git Flow工作流
- 分支命名规范
- 提交信息规范

### 2. 代码审查
- 必须进行代码审查
- 审查重点关注点
- 审查通过标准

## 文档维护规范

### 1. 文档类型
- API文档
- 组件文档
- 部署文档
- 用户手册

### 2. 文档更新
- 代码变更时同步更新文档
- 定期审查文档准确性
- 文档版本管理

---

## 总结

本开发规范涵盖了项目开发的各个方面，从代码组织到部署上线的完整流程。遵循这些规范可以确保：

1. **代码一致性**: 团队成员编写的代码风格统一
2. **可维护性**: 代码结构清晰，易于理解和修改
3. **可扩展性**: 模块化设计，便于功能扩展
4. **开发效率**: 标准化流程提高开发效率
5. **代码质量**: 规范的代码审查和测试流程

建议团队成员认真学习并严格执行这些规范，在项目开发过程中不断完善和优化规范内容。特别要注意：

- **严格按照方法集合方式组织代码**：所有列表页面必须使用`ListMethods()`、`EntityMethods()`等方法集合
- **统一使用listSearchFilter组件**：所有需要搜索筛选的页面都要使用标准配置
- **遵循页面模板规范**：根据业务需要选择合适的页面模板（复杂列表或简单列表）
- **保持命名一致性**：方法名、变量名都要遵循统一的命名规范