# 成都智云鸿道虚拟仿真大平台开发规范文档

## 项目概述

### 项目信息
- **项目名称**: 成都智云鸿道虚拟仿真大平台
- **技术栈**: JDK 8 + Spring Boot 2.7.1 + Kotlin 1.4.10
- **数据库**: MongoDB + Redis
- **构建工具**: Maven
- **项目结构**: MVC 分层架构

### 依赖版本
- Spring Boot: 2.7.1
- Kotlin: 1.4.10
- Java: 1.8
- HuTool: 5.8.3
- FastJSON: 2.0.7
- Apache POI: 5.2.2
- AspectJ: 1.9.9.1

## 项目结构规范

### 目录结构
```
src/main/kotlin/com/cdzyhd/big_platform/
├── BigPlatformApplication.kt          # 主启动类
├── a/                                 # 工具类和静态Bean
├── annotation/                        # 自定义注解
├── aop/                              # AOP切面
├── config/                           # 配置类
├── controller/v1/                    # 控制器层（v1版本）
├── entity/                           # 实体类
├── event/                            # 事件处理
├── exception/                        # 异常处理
├── filter/                           # 过滤器
├── interceptor/                      # 拦截器
├── model/                            # 业务逻辑层
├── po/                               # 参数对象
├── repository/                       # 数据访问层
├── util/                             # 工具类
└── vo/                               # 视图对象
```

### 包命名规范
- 采用反向域名规范：`com.cdzyhd.big_platform`
- 包名全部小写，用点号分隔
- 功能模块按层次分包组织

## 编码规范

### 1. 命名规范

#### 类命名
- **实体类**: 以`Entity`结尾，如`UserEntity`、`CourseEntity`
- **控制器**: 以`Controller`结尾，如`UserController`、`CourseController`
- **业务模型**: 以`Model`结尾，如`UserModel`、`OpenModel`
- **数据仓库**: 以`Repository`结尾，如`UserRepository`
- **配置类**: 以`Config`结尾，如`RedisConfig`、`WebMvcConfigurer`
- **异常类**: 以`Exception`结尾，如`CommonException`
- **注解类**: 功能性命名，如`NeedToken`、`SysOperaLog`

#### 字段和方法命名
- 使用驼峰命名法
- 字段名要有意义，如`userId`、`createTime`、`hasLogin`
- 方法名体现功能，如`checkUserInfoRepeat`、`sendAccountEnableEmail`

#### 常量命名
- 全大写，下划线分隔
- 有明确的业务含义

### 2. 代码结构规范

#### 控制器规范

##### 标准MongoDB CRUD控制器模板
所有MongoDB实体的控制器应遵循以下标准模板，提供统一的增删改查接口：

```kotlin
@RestController
@RequestMapping(value = ["/v1/实体名/"])
class EntityController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.entityRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, 
        @PageableDefault(
            value = 20, 
            sort = ["entityId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.entityRepository, 
                queryObject.toJSONString(), 
                pageable
            )
        )
    }

    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam entityId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.entityRepository, 
                "entityId", 
                entityId
            )
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam entityId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.entityRepository, 
                "entityId", 
                entityId
            )
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.entityRepository,
                EntityClass(),
                "entityId",
                infoObject
            )
        )
    }
}
```

##### 模板说明
1. **路径规范**: `/v1/实体名小写/`
2. **方法名统一**: `getList`、`getPageList`、`getOne`、`deleteOne`、`addOrEdit`
3. **参数规范**:
   - 查询条件使用JSON字符串
   - 分页使用Spring Data Pageable
   - 主键参数使用`entityId`格式
4. **权限控制**: 查询接口无需权限，增删改需要管理员权限
5. **响应格式**: 统一使用`OutResponse`包装
6. **工具类使用**: 
   - 通过`StaticBean`获取Repository
   - 使用`CommonMongoEntityModel`进行通用操作

##### 业务扩展控制器规范
对于有特殊业务逻辑的控制器，在标准CRUD基础上扩展：

```kotlin
@RestController
@RequestMapping(value = ["/v1/模块名/"])
class BusinessController {
    
    // 标准CRUD方法（使用上述模板）
    // ...
    
    // 业务扩展方法
    @GetMapping("businessMethod")
    @NeedToken // 根据需要添加权限注解
    @SysOperaLog(moduleName = "模块名", methodName = "业务操作描述")
    fun businessMethod(@RequestParam param: String): OutResponse<Any> {
        return OutResponse("000000", "操作成功", BusinessModel().businessLogic(param))
    }
}
```

#### 业务模型规范
```kotlin
class BusinessModel {
    
    // 业务方法应该包含完整的业务逻辑
    fun businessMethod(params: String): ResultType {
        // 1. 参数验证
        // 2. 业务逻辑处理
        // 3. 数据操作
        // 4. 返回结果
    }
    
    // 异常处理使用统一的CommonException
    private fun validateParams(param: String) {
        if (condition) {
            throw CommonException("000001", "错误信息")
        }
    }
}
```

#### 实体类规范
只能使用String Long Integer Double Float Boolean com.alibaba.fastjson.JSONObject com.alibaba.fastjson.JSONArray 这几种类型的字段
list array之类的都要用 JSONObject JSONArray替代
```java
@Document(collection = "collection_name")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class EntityName {
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键，使用雪花算法生成
    public String entityId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 创建时间
    public Long createTime = System.currentTimeMillis();
    
    // 逻辑删除标识
    public Integer deleted = 0;
    
    // 敏感信息添加@JsonIgnore注解
    @JsonIgnore
    public String password;
    
    // 关联数据使用POJO字段
    public JSONArray relatedEntity;
}
```

### 3. 数据访问层规范

#### Repository接口规范

##### 标准MongoDB Repository模板
所有MongoDB实体的Repository应遵循以下标准模板：

```java
public interface EntityRepository extends MongoRepository<EntityClass, ObjectId> {
    
    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByEntityId(String id);
    
    // 通过业务ID获取单个对象
    EntityClass findFirstByEntityId(String id);
    
    // 通过业务ID删除
    Integer deleteByEntityId(String id);
    
    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<EntityClass> getPageList(Document d, Pageable pageable);
    
    // 不分页查询
    @Query(value = "?0")
    List<EntityClass> getList(Document d);
    
    // 获取单个对象
    @Query(value = "?0")
    EntityClass getOne(Document d);
    
    // 业务特定查询方法
    // 示例：通过名称查找
    EntityClass findFirstByName(String name);
    
    // 示例：通过状态统计数量
    Integer countByStatus(String status);
    
    // 示例：通过关联ID获取列表
    List<EntityClass> getAllByRelatedIdAndStatus(String relatedId, String status);
}
```

##### Repository规范说明
1. **继承规范**: 必须继承`MongoRepository<EntityClass, ObjectId>`
2. **基础方法**: 包含存在性检查、单个查询、删除等基础方法
3. **通用查询**: 使用`@Query`注解支持Document参数的灵活查询
4. **命名规范**: 
   - 业务ID方法使用`ByEntityId`格式
   - 业务查询方法使用Spring Data JPA方法命名规范
   - 统计方法使用`countBy`前缀
   - 列表查询使用`getAllBy`或`findAllBy`前缀

##### 标准Entity模板
所有MongoDB实体应遵循以下标准模板：

```java
@Document(collection = "collection_name")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class EntityClass {
    
    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String entityId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 基础字段
    public String name;              // 名称
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Integer deleted = 0;      // 逻辑删除标识（可选）
    
    // 业务字段
    public String status;            // 状态
    public String description;       // 描述
    public String relatedId;         // 关联ID
    
    // 关联查询结果（POJO字段，不存储）
    public JSONArray relatedEntity;  // 关联实体数据
    
    // 敏感字段（不序列化）
    @JsonIgnore
    public String sensitiveData;
}
```

##### Entity规范说明
1. **注解规范**:
   - `@Document`: 指定MongoDB集合名称
   - `@Data`: Lombok自动生成getter/setter
   - `@JsonSerialize`: 排除null值序列化
   - `@Id`: MongoDB内部ID
   - `@JsonIgnore`: 敏感字段不序列化

2. **字段规范**:
   - 业务主键使用雪花算法生成，命名为`{实体名}Id`
   - 创建时间使用时间戳格式
   - 逻辑删除使用`deleted`字段（0-正常，1-删除）
   - 关联数据使用POJO字段存储聚合查询结果

3. **数据类型规范**:
   - 时间使用`Long`类型存储时间戳
   - 状态使用`String`类型便于扩展
   - 关联ID统一使用`String`类型
   - 复杂数据使用`JSONArray`或`JSONObject`
```

## API设计规范

### 1. URL设计
- 基础路径：`/v1/模块名/`
- 版本控制通过路径体现，当前为v1
- RESTful风格，资源名用复数形式
- 使用HTTP动词表示操作：
  - GET：查询
  - POST：创建、复杂查询
  - PUT：完整更新
  - DELETE：删除

### 2. 响应格式
统一使用`OutResponse`包装响应：
```json
{
    "code": "000000",     // 响应码，000000表示成功
    "message": "操作描述", // 提示信息
    "data": {}           // 响应数据
}
```

### 3. 错误码规范
- `000000`: 操作成功
- `000001`: 业务异常（默认错误码）
- 自定义错误码应有明确的业务含义

### 4. 分页参数
使用Spring Data的Pageable接口：
```kotlin
@PostMapping("pageList")
fun getPageList(
    @RequestBody query: String,
    @PageableDefault(value = 20, sort = ["createTime"], direction = Sort.Direction.DESC) 
    pageable: Pageable
): OutResponse<Any>
```

## 认证授权规范

### 1. 注解使用
- `@NeedToken`: 需要普通用户认证
- `@NeedAdminToken`: 需要管理员权限
- `@NeedTeacherToken`: 需要教师权限

### 2. Token格式
- Header: `Authorization: Bearer {token}`
- 使用JWT格式
- Token失效时返回401状态码

### 3. 权限控制
```kotlin
// 通过注解控制接口权限
@GetMapping("adminOnly")
@NeedAdminToken
@SysOperaLog(moduleName = "管理模块", methodName = "管理员操作")
fun adminOnlyMethod(): OutResponse<Any>
```

## 异常处理规范

### 1. 异常类设计
```kotlin
@Data
class CommonException constructor(_code: String, _message: String) : Exception() {
    var code = "000001"
    override var message = ""
    
    init {
        code = _code
        message = _message
    }
}
```

### 2. 全局异常处理
```kotlin
@ControllerAdvice
class GlobalExceptionHandler {
    
    @ExceptionHandler(CommonException::class)
    fun commonException(response: HttpServletResponse, e: CommonException) {
        // 统一异常响应格式
    }
    
    @ExceptionHandler(Exception::class)
    fun globalExceptionHandlerAll(response: HttpServletResponse, e: Exception) {
        // 兜底异常处理
    }
}
```

### 3. 异常使用原则
- 业务异常统一使用`CommonException`
- 异常信息要对用户友好
- 关键错误要记录日志

## 日志规范

### 1. 操作日志注解
```kotlin
@SysOperaLog(
    moduleName = "用户模块", 
    methodName = "用户登录"
)
```

### 2. 日志级别
- ERROR: 系统错误，需要立即处理
- WARN: 警告信息，需要关注
- INFO: 一般信息，业务流程记录
- DEBUG: 调试信息，开发阶段使用

### 3. 日志内容
- 关键业务操作要记录日志
- 敏感信息不能记录到日志中
- 异常要记录完整的堆栈信息

## 配置管理规范

### 1. 配置文件结构
```yaml
# application.yml
server:
  port: 9999

# 外部服务配置
redis:
  host: host
  port: port
  db: database
  password: password

spring:
  application:
    name: system-big_platform
  data:
    mongodb:
      host: host
      database: database
      # 其他配置

# 自定义配置
bigPlatform:
  storageFolder: "/path/to/storage/"
  apiUrl: "http://api.domain.com/"
```

### 2. 配置类设计
```java
@Configuration
public class ServiceConfig {
    
    @Bean
    public ServiceBean serviceBean() {
        return new ServiceBean();
    }
}
```

## 数据库设计规范

### 1. 集合命名
- 使用单数形式，如`user`、`course`
- 全小写，下划线分隔复合词

### 2. 字段设计
- 主键使用业务ID，如`userId`、`courseId`
- 时间字段使用时间戳，如`createTime`
- 逻辑删除使用`deleted`字段（0-正常，1-删除）
- 关联字段以`Id`结尾，如`clazzId`、`collegeId`

### 3. 索引设计
- 业务主键建立唯一索引
- 查询频繁的字段建立索引
- 复合查询建立复合索引

## 缓存使用规范

### 1. Redis键命名
```
{系统名}_{模块}_{具体用途}_{标识}
例如：bigPlatform_user_token_userId
     bigPlatform_email_code_email
```

### 2. 缓存策略
- 热点数据缓存，设置合理的过期时间
- 验证码类数据设置短期过期
- 配置类数据长期缓存

## 静态Bean管理规范

### StaticBean设计模式
项目采用StaticBean模式统一管理所有Repository和服务Bean，便于在Model层直接访问数据层：

```java
public class StaticBean {
    // 基础服务
    public static CommonConfig commonConfig = SpringUtil.getBeanByClass(CommonConfig.class);
    public static RedisService redisService = SpringUtil.getBeanByClass(RedisService.class);
    public static MongoTemplate mongoTemplate = SpringUtil.getBeanByClass(MongoTemplate.class);
    
    // 业务Repository
    public static UserRepository userRepository = SpringUtil.getBeanByClass(UserRepository.class);
    public static GradeRepository gradeRepository = SpringUtil.getBeanByClass(GradeRepository.class);
    public static CollegeRepository collegeRepository = SpringUtil.getBeanByClass(CollegeRepository.class);
    // ... 其他Repository
}
```

### 使用规范
1. **Controller层**: 通过StaticBean访问Repository进行标准CRUD操作
2. **Model层**: 直接使用StaticBean访问Repository和服务
3. **新增Repository**: 必须在StaticBean中注册静态引用
4. **命名规范**: 静态字段名采用驼峰命名，与Repository类名对应

### SpringUtil工具类
配合StaticBean使用的Spring上下文工具类：

```java
@Component
public class SpringUtil implements ApplicationContextAware {
    private static ApplicationContext applicationContext;
    
    public static <T> T getBeanByClass(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }
    
    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
    }
}
```

### CommonMongoEntityModel工具类
提供通用的MongoDB实体操作方法：

1. **getPageList**: 分页查询
2. **getOneById**: 根据业务ID获取单个对象
3. **deleteOneById**: 根据业务ID删除对象
4. **addOrEdit**: 新增或修改操作（根据ID是否存在判断）

使用示例：
```kotlin
// 分页查询
CommonMongoEntityModel.getPageList(StaticBean.entityRepository, query, pageable)

// 获取单个对象
CommonMongoEntityModel.getOneById(StaticBean.entityRepository, "entityId", id)

// 删除对象
CommonMongoEntityModel.deleteOneById(StaticBean.entityRepository, "entityId", id)

// 新增或修改
CommonMongoEntityModel.addOrEdit(StaticBean.entityRepository, EntityClass(), "entityId", dataObject)
```
### MongoDB聚合查询规范

在需要复杂查询或关联查询时，使用`StaticBean.mongoTemplate.aggregate`进行聚合操作：

#### 聚合查询基本模式
```kotlin
// 1. 构建查询条件
val criteria = Criteria("")
if (searchParam != null) {
    criteria.and("fieldName").regex(searchParam)
}
if (filterParam != null) {
    criteria.and("relatedEntity.field").`is`(filterParam)
}

// 2. 构建聚合管道
val matchOperation = Aggregation.match(criteria)
val lookupOperation = Aggregation.lookup("relatedCollection", "localField", "foreignField", "aliasName")
val sortOperation = Aggregation.sort(Sort.Direction.DESC, "_id")

// 3. 统计总数
val countAggregation = Aggregation.count().`as`("count")
var countPipeline = Aggregation.newAggregation(lookupOperation, matchOperation, countAggregation)
val countResult = StaticBean.mongoTemplate.aggregate(
    countPipeline,
    "collectionName",
    HashMap::class.java
).mappedResults
val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int

// 4. 分页查询
val skipOperation = Aggregation.skip(skipNumber)
val limitOperation = Aggregation.limit(limitNumber)
val dataPipeline = Aggregation.newAggregation(
    lookupOperation,
    matchOperation,
    sortOperation,
    skipOperation,
    limitOperation
)

// 5. 执行聚合查询
val resultList = StaticBean.mongoTemplate.aggregate(
    dataPipeline,
    "collectionName",
    EntityClass::class.java
).mappedResults
```

#### 聚合操作类型

##### 1. Lookup操作（关联查询）
```kotlin
// 关联用户信息
val lookupUser = Aggregation.lookup("user", "userId", "userId", "userEntity")

// 关联多个集合
val lookupClazz = Aggregation.lookup("clazz", "clazzId", "clazzId", "clazzEntity")
val lookupGrade = Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
```

##### 2. Match操作（条件过滤）
```kotlin
// 基础条件匹配
val matchOperation = Aggregation.match(Criteria.where("status").`is`("active"))

// 复杂条件匹配
val criteria = Criteria()
criteria.and("userEntity.account").regex(account)
criteria.and("userEntity.role").`is`(role)
val matchOperation = Aggregation.match(criteria)

// 或条件匹配
val orCriteria = Criteria()
orCriteria.orOperator(
    Criteria.where("name").regex(searchText),
    Criteria.where("account").regex(searchText)
)
```

##### 3. Sort操作（排序）
```kotlin
// 单字段排序
val sortOperation = Aggregation.sort(Sort.Direction.DESC, "_id")

// 多字段排序
val sortOperation = Aggregation.sort(
    Sort.by(Sort.Direction.DESC, "createTime")
        .and(Sort.Direction.ASC, "name")
)
```

##### 4. 分页操作
```kotlin
// 跳过和限制
val skipOperation = Aggregation.skip(pageNumber * pageSize.toLong())
val limitOperation = Aggregation.limit(pageSize.toLong())

// 支持自定义分页参数
var skipNumber = pageable.pageNumber * pageSize.toLong()
var limitNumber = pageSize.toLong()
if (queryObject.containsKey("skipNumber")) {
    skipNumber = queryObject.getLong("skipNumber")
}
if (queryObject.containsKey("limitNumber")) {
    limitNumber = queryObject.getLong("limitNumber")
}
```

#### 聚合查询执行规范

##### 1. 分两次执行（推荐）
```kotlin
// 第一次：获取总数
val countPipeline = Aggregation.newAggregation(lookupOperation, matchOperation, countAggregation)
val totalElements = StaticBean.mongoTemplate.aggregate(
    countPipeline, 
    "collectionName", 
    HashMap::class.java
).mappedResults

// 第二次：获取分页数据
val dataPipeline = Aggregation.newAggregation(lookupOperation, matchOperation, sortOperation, skipOperation, limitOperation)
val resultList = StaticBean.mongoTemplate.aggregate(
    dataPipeline, 
    "collectionName", 
    EntityClass::class.java
).mappedResults
```

##### 2. 返回格式规范
```kotlin
// 构建分页返回对象
val returnObject = JSONObject.parseObject("""
    {
        "number":${pageable.pageNumber},
        "size":${pageable.pageSize},
        "totalElements":${totalElements},
        "totalPages":${totalPages}
    }
""".trimIndent())
returnObject["content"] = resultList

return OutResponse("000000", "", returnObject)
```

#### 聚合查询最佳实践

1. **管道顺序优化**: 
   - 先执行过滤条件（match）
   - 再执行关联查询（lookup）
   - 最后执行排序和分页

2. **性能考虑**:
   - 合理使用索引支持match条件
   - 避免不必要的lookup操作
   - 控制返回字段数量

3. **错误处理**:
   - 检查聚合结果是否为空
   - 处理关联字段不存在的情况

4. **参数验证**:
   - 验证分页参数合法性
   - 检查查询条件安全性


## 工具类规范

### 1. 工具类设计
- 静态方法，无状态
- 功能单一，职责明确
- 有完整的异常处理

### 2. 常用工具类
- `PasswordUtil`: 密码加密工具
- `EmailTools`: 邮件发送工具
- `SnowflakeIdWorker`: ID生成工具
- `ImportExcelUtil`: Excel导入工具

## 测试规范

### 1. 单元测试
- 业务逻辑方法要有单元测试
- 测试覆盖率要达到要求
- 测试数据要清理干净

### 2. 集成测试
- 重要接口要有集成测试
- 测试环境数据要独立

## 性能优化规范

### 1. 数据库优化
- 合理使用索引
- 避免N+1查询
- 使用聚合查询优化关联查询

### 2. 缓存优化
- 热点数据缓存
- 查询结果缓存
- 避免缓存雪崩

### 3. 代码优化
- 避免在循环中进行数据库操作
- 合理使用异步处理
- 资源要及时释放

## 安全规范

### 1. 认证安全
- 密码加密存储
- Token有效期控制
- 登录失败次数限制

### 2. 数据安全
- 敏感数据加密
- SQL注入防护
- XSS攻击防护

### 3. 接口安全
- 重要操作要权限控制
- 敏感接口要审计日志
- 频繁操作要限流

## 部署规范

### 1. 环境配置
- 开发、测试、生产环境分离
- 配置文件分环境管理
- 敏感信息使用环境变量

### 2. 监控告警
- 系统性能监控
- 错误日志告警
- 业务指标监控

## 代码质量规范

### 2. 代码规范检查
- 使用IDEA代码格式化
- 遵循Kotlin编码规范

### 3. 文档规范
- 重要方法要有注释
- 复杂业务逻辑要有说明
- API文档要及时更新

## 总结

本规范文档基于现有代码分析总结，涵盖了项目开发的各个方面。开发团队应严格遵循本规范，确保代码质量和项目的可维护性。规范会根据项目发展不断完善和更新。