{"name": "big-platform", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.0", "@fortawesome/free-solid-svg-icons": "^6.4.0", "@fortawesome/free-regular-svg-icons": "^6.4.0", "@fortawesome/vue-fontawesome": "^2.0.10", "axios": "^0.27.2", "core-js": "^3.6.5", "echarts": "^5.3.3", "element-ui": "^2.15.7", "jquery": "^3.6.0", "js-cookie": "2.2.0", "normalize.css": "^8.0.1", "objectFitPolyfill": "^2.3.5", "vue": "2.6.11", "vue-router": "^3.5.4", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "less": "^3.0.4", "less-loader": "^5.0.0", "vue-template-compiler": "2.6.11"}}