import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

// 避免跳转同路由时报错 https://www.caorui.net/blog/147173074460999680.html
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
    return originalPush.call(this, location).catch(err => err)
}

const routes = [
    {
        // 首页
        path: '/',
        name: "Home",
        component: () => import('@/views/home')
    },
    {
        // 评价列表页
        path: '/login',
        name: "Login",
        component: () => import('@/views/login')
    },
    {
        // 课程页
        path: '/course',
        name: "Course",
        component: () => import('@/views/course')
    },
    {
        // 帮助页
        path: '/help',
        name: "Help",
        component: () => import('@/views/help')
    },
    {
        // 反馈页
        path: '/feedback',
        name: "Feedback",
        component: () => import('@/views/feedback')
    },
    {
        // 介绍页
        path: '/introduce',
        name: "Introduce",
        component: () => import('@/views/introduce')
    },
    {
        // 课程详情页
        path: '/courseInfo',
        name: "CourseInfo",
        component: () => import('@/views/courseInfo')
    },
    {
        // 评价列表页
        path: '/commentList',
        name: "CommentList",
        component: () => import('@/views/commentList')
    },
    {
        // 个人中心
        path: '/user/',
        name: "UserLayout",
        component: () => import('@/views/user/layout'),
        children:[
            {
                // 我的信息
                path: 'info',
                name: "UserInfo",
                component: () => import('@/views/user/info')
            },
            {
                // 我的课程
                path: 'course',
                name: "UserCourse",
                component: () => import('@/views/user/course')
            },
            {
                // 我的任务
                path: 'task',
                name: "UserTask",
                component: () => import('@/views/user/task')
            },
            {
                // 我的收藏
                path: 'favorite',
                name: "UserFavorite",
                component: () => import('@/views/user/favorite')
            },
        ]
    },
    {
        // 基地新闻列表页
        path: '/news',
        name: "News",
        component: () => import('@/views/news/index')
    },
    {
        // 基地新闻详情页
        path: '/news/:id',
        name: "NewsDetail",
        component: () => import('@/views/news/detail')
    },
    {
        // 通知公告列表页
        path: '/notice',
        name: "Notice", 
        component: () => import('@/views/notice/index')
    },
    {
        // 通知公告详情页
        path: '/notice/:id',
        name: "NoticeDetail",
        component: () => import('@/views/notice/detail')
    },
    {
        // 产教融合主页
        path: '/industry',
        name: "Industry",
        component: () => import('@/views/industry/index'),
        meta: {
            title: '产教融合',
            requiresAuth: false
        }
    },
    {
        // 合作企业列表页
        path: '/industry/enterprise',
        name: "EnterpriseList",
        component: () => import('@/views/industry/enterprise'),
        meta: {
            title: '合作企业',
            requiresAuth: false
        }
    },
    {
        // 企业招聘列表页
        path: '/industry/recruitment',
        name: "RecruitmentList",
        component: () => import('@/views/industry/recruitment'),
        meta: {
            title: '企业招聘',
            requiresAuth: false
        }
    },
    {
        // 优秀学生展示页
        path: '/industry/students',
        name: "ExcellentStudents",
        component: () => import('@/views/industry/students'),
        meta: {
            title: '优秀学生',
            requiresAuth: false
        }
    },
    {
        // 企业详情页
        path: '/industry/enterprise/:id',
        name: "EnterpriseDetail",
        component: () => import('@/views/industry/detail/enterprise'),
        meta: {
            title: '企业详情',
            requiresAuth: false
        }
    },
    {
        // 招聘详情页
        path: '/industry/recruitment/:id',
        name: "RecruitmentDetail",
        component: () => import('@/views/industry/detail/recruitment'),
        meta: {
            title: '招聘详情',
            requiresAuth: false
        }
    },
    {
        // 学生详情页
        path: '/industry/student/:id',
        name: "StudentDetail",
        component: () => import('@/views/industry/detail/student'),
        meta: {
            title: '学生详情',
            requiresAuth: false
        }
    },
    {
        // 资源中心主页
        path: '/resource',
        name: "ResourceCenter",
        component: () => import('@/views/resource/index'),
        meta: {
            title: '资源中心',
            requiresAuth: false
        }
    },
    {
        // 资源分类页
        path: '/resource/category/:categoryId',
        name: "ResourceCategory",
        component: () => import('@/views/resource/category'),
        meta: {
            title: '资源分类',
            requiresAuth: false
        }
    },
    {
        // 资源详情页
        path: '/resource/detail/:id',
        name: "ResourceDetail",
        component: () => import('@/views/resource/detail'),
        meta: {
            title: '资源详情',
            requiresAuth: false
        }
    },
    {
        // 资源搜索页
        path: '/resource/search',
        name: "ResourceSearch",
        component: () => import('@/views/resource/search'),
        meta: {
            title: '资源搜索',
            requiresAuth: false
        }
    },
]

const router = new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    routes
})

export default router
