import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/* Router Modules */

/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    noCache: true                if set true, the page will no be cached(default is false)
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  {
    path: '/auth-redirect',
    component: () => import('@/views/login/auth-redirect'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },
  {
    path: '/test',
    component: () => import('@/views/test/index'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: {title: 'dashboard', icon: 'international', breadcrumb: false}
      }
    ]
  }

]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [

  /** when your routing map is too long, you can split it into small modules **/
  // 学生管理
  {
    path: '/user/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'userManage',
    alwaysShow: true,
    meta: {
      title: '学校管理',
      icon: 'user',
      roles: ['administrator', 'teacherSecretary']
    },
    children: [
      {
        path: 'grade',
        component: () => import('@/views/userManage/grade'),
        name: 'gradeManage',
        meta: {title: '年级管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'college',
        component: () => import('@/views/userManage/college'),
        name: 'collegeManage',
        meta: {title: '学院和教师管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'major',
        component: () => import('@/views/userManage/major'),
        name: 'majorManage',
        meta: {title: '专业管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'clazz',
        component: () => import('@/views/userManage/clazz'),
        name: 'clazzManage',
        meta: {title: '行政班级和学生管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'student',
        component: () => import('@/views/userManage/student'),
        name: 'studentManage',
        hidden: true,
        meta: {title: '学生管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'teacher',
        component: () => import('@/views/userManage/teacher'),
        name: 'teacherManage',
        hidden: true,
        meta: {title: '教师管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'teachingClazz',
        component: () => import('@/views/userManage/teachingClazz'),
        name: 'teachingClazzManage',
        hidden: true,
        meta: {title: '教学班管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      }
    ]
  },
  // 课程管理
  {
    path: '/course/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'courseManage',
    alwaysShow: true,
    meta: {
      title: '课程管理',
      icon: 'education',
      roles: ['administrator', 'teacherSecretary', 'teacher']
    },
    children: [
      {
        path: 'subject',
        component: () => import('@/views/courseManage/subject'),
        name: 'subjectManage',
        meta: {title: '学科管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'course',
        component: () => import('@/views/courseManage/course'),
        name: 'courseManage',
        meta: {title: '课程管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'courseDetail',
        component: () => import('@/views/courseManage/detail'),
        name: 'courseDetail',
        hidden: true,
        meta: {title: '课程信息', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'courseComment',
        component: () => import('@/views/courseManage/comment'),
        name: 'courseComment',
        hidden: true,
        meta: {title: '评价管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'courseTask',
        component: () => import('@/views/courseManage/task'),
        name: 'courseTask',
        meta: {title: '任务安排', noCache: true, roles: ['administrator', 'teacherSecretary', 'teacher']}
      },
      {
        path: 'courseTaskDetail',
        component: () => import('@/views/courseManage/taskDetail'),
        name: 'courseTaskDetail',
        hidden: true,
        meta: {title: '任务安排信息', noCache: true, roles: ['administrator', 'teacherSecretary', 'teacher']}
      },
      {
        path: 'courseTaskScore',
        component: () => import('@/views/courseManage/taskScore'),
        name: 'courseTaskScore',
        hidden: true,
        meta: {title: '任务成绩管理', noCache: true, roles: ['administrator', 'teacherSecretary', 'teacher']}
      },
      {
        path: 'courseScore',
        component: () => import('@/views/courseManage/score'),
        name: 'courseScore',
        hidden: true,
        meta: {title: '成绩管理', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
      {
        path: 'courseStatistic',
        component: () => import('@/views/courseManage/statistic'),
        name: 'courseStatistic',
        hidden: true,
        meta: {title: '课程统计', noCache: true, roles: ['administrator', 'teacherSecretary']}
      },
    ]
  },
  // 产教融合管理
  {
    path: '/industry/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'industryManage',
    alwaysShow: true,
    meta: {
      title: '产教融合管理',
      icon: 'skill',
      roles: ['administrator']
    },
    children: [
      {
        path: 'banner',
        component: () => import('@/views/industryManage/banner'),
        name: 'industryBannerManage',
        meta: {title: '轮播图管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'enterprise',
        component: () => import('@/views/industryManage/enterprise'),
        name: 'industryEnterpriseManage',
        meta: {title: '企业管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'job',
        component: () => import('@/views/industryManage/job'),
        name: 'industryJobManage',
        meta: {title: '招聘信息管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'student',
        component: () => import('@/views/industryManage/student'),
        name: 'industryStudentManage',
        meta: {title: '优秀学生管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'statistics',
        component: () => import('@/views/industryManage/statistics'),
        name: 'industryStatistics',
        meta: {title: '统计数据', noCache: true, roles: ['administrator']}
      }
    ]
  },
  // 基地内容管理
  {
    path: '/content/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'contentManage',
    alwaysShow: true,
    meta: {
      title: '基地内容管理',
      icon: 'documentation',
      roles: ['administrator']
    },
    children: [
      {
        path: 'baseNews',
        component: () => import('@/views/contentManage/baseNews'),
        name: 'baseNewsManage',
        meta: {title: '基地新闻管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'notice',
        component: () => import('@/views/contentManage/notice'),
        name: 'noticeManage',
        meta: {title: '通知公告管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'baseNewsEdit',
        component: () => import('@/views/contentManage/baseNewsEdit'),
        name: 'baseNewsEdit',
        hidden: true,
        meta: {title: '编辑基地新闻', noCache: true, roles: ['administrator']}
      },
      {
        path: 'noticeEdit',
        component: () => import('@/views/contentManage/noticeEdit'),
        name: 'noticeEdit',
        hidden: true,
        meta: {title: '编辑通知公告', noCache: true, roles: ['administrator']}
      }
    ]
  },
  // 资源中心管理
  {
    path: '/resource/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'resourceManage',
    alwaysShow: true,
    meta: {
      title: '资源中心管理',
      icon: 'nested',
      roles: ['administrator']
    },
    children: [
      {
        path: 'category',
        component: () => import('@/views/resourceManage/category'),
        name: 'resourceCategoryManage',
        meta: {title: '资源分类管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'resource',
        component: () => import('@/views/resourceManage/resource'),
        name: 'resourceManage',
        meta: {title: '资源管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'resourceEdit',
        component: () => import('@/views/resourceManage/resourceEdit'),
        name: 'resourceEdit',
        hidden: true,
        meta: {title: '编辑资源', noCache: true, roles: ['administrator']}
      },
    ]
  },
  // 系统
  {
    path: '/system/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'systemManage',
    alwaysShow: true,
    meta: {
      title: '系统管理',
      icon: 'education',
      roles: ['administrator']
    },
    children: [
      {
        path: 'setting',
        component: () => import('@/views/systemManage/setting'),
        name: 'systemSetting',
        meta: {title: '系统设置', noCache: true, roles: ['administrator']}
      },
      {
        path: 'help',
        component: () => import('@/views/systemManage/help'),
        name: 'systemHelp',
        meta: {title: '帮助管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'feedback',
        component: () => import('@/views/systemManage/feedback'),
        name: 'systemFeedback',
        meta: {title: '反馈管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'log',
        component: () => import('@/views/systemManage/log'),
        name: 'systemLog',
        meta: {title: '日志管理', noCache: true, roles: ['administrator']}
      },
      {
        path: 'database',
        component: () => import('@/views/systemManage/database'),
        name: 'systemDatabase',
        meta: {title: '数据库管理', noCache: true, roles: ['administrator']}
      },
    ]
  },
  // 账号
  {
    path: '/account/',
    component: Layout,
    redirect: 'noRedirect',
    name: 'accountManage',
    alwaysShow: true,
    meta: {
      title: '我的信息',
      icon: 'user'
    },
    children: [
      {
        path: 'info',
        component: () => import('@/views/account/info'),
        name: 'accountInfo',
        meta: {title: '我的信息', noCache: true}
      }
    ]
  },

  // 404 page must be placed at the end !!!
  {path: '*', redirect: '/404', hidden: true}
]

const createRouter = () => new Router({
  // mode: 'history', // require service support
  scrollBehavior: () => ({y: 0}),
  routes: constantRoutes
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-*********
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
