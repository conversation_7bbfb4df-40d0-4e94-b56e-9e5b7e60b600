import {AdminUserModel} from '@/model/AdminUserModel'
import {loginOut} from '@/api/AdminUserApi'
import {getToken, setToken, removeToken, getLoginRole} from '@/utils/auth'
import router, {resetRouter} from '@/router'
import {PLATFORM_ID} from '@/config/main'

const state = {
  token: getToken(),
  name: '',
  userName: '',
  avatar: '',
  introduction: '',
  roles: [],
  id: '',
  loginRole: "",
  infos: {},// 原始信息
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USERNAME: (state, userName) => {
    state.userName = userName
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_ID: (state, id) => {
    state.id = id
  },
  SET_PERMISSION: (state, permission) => {
    state.permission = permission
  },
  SET_LOGIN_ROLE: (state, loginRole) => {
    state.loginRole = loginRole
  },
  SET_INFOS: (state, info) => {
    state.infos = info
  }
}

const actions = {
  // 用户登录
  async login({commit}, result) {
    let loginRole = result['loginRole'].indexOf("teacher") === 0 ? "teacher" : result['loginRole']
    commit('SET_TOKEN', result['token'])
    commit('SET_LOGIN_ROLE', loginRole)
    setToken(result['token'], loginRole)
  },

  // 每次刷新页面时重新获取用户信息，填充store
  async getInfo({commit, state}) {
    return new Promise(async (resolve, reject) => {
      // 获取用户信息
      const user = await AdminUserModel.getMineInfo(getLoginRole())
      if (user) {
        const roles = user.roles[PLATFORM_ID]
        commit('SET_NAME', user.realName ? user.realName : user.name)
        commit('SET_USERNAME', user.username ? user.username : user.account)
        // todo 更换头像
        commit('SET_AVATAR', '')
        commit('SET_INTRODUCTION', '')
        commit('SET_ROLES', user.roles[PLATFORM_ID])
        commit('SET_ID', user.adminUserId ? user.adminUserId : user.userId)
        commit('SET_PERMISSION', [])
        commit('SET_INFOS', user)
        resolve({'roles': roles})
      }
    })
  },

  // user logout
  logout({commit, state, dispatch}) {
    return new Promise((resolve, reject) => {
      loginOut({token: getToken()}).then(() => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        removeToken()
        resetRouter()
        // 清空session
        sessionStorage.clear()
        // reset visited views and cached views
        // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
        dispatch('tagsView/delAllViews', null, {root: true})

        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },

  // remove token
  resetToken({commit}) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({commit, dispatch}, role) {
    const token = role + '-token'

    commit('SET_TOKEN', token)
    setToken(token)

    const {roles} = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, {root: true})
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, {root: true})
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
