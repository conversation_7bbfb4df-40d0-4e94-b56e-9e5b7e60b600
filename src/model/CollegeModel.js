import {getList, getOne} from "@/api/CollegeApi";

class CollegeModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      collegeId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }
}

export {CollegeModel}
