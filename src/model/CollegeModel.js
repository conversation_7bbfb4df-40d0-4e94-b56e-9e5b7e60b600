import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne} from "@/api/CollegeApi";
import axios from "axios";
import {API_URL} from "@/config/main";
import {getToken} from "@/utils/auth";
import {msg_err} from "@/utils/ele_component";
import {UserModel} from "@/model/UserModel";
import {excel_export_from_json} from "@/utils/excel";

class CollegeModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      collegeId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 批量导入教师-班级内
  static importTeachersByCollegeId(file, collegeId) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('collegeId', collegeId)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/college/importTeachersByCollegeId`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 批量导出教师-班级内
  static async exportTeacherByCollegeId(collegeId, collegeName) {
    // 获取班级内的教师列表
    let list = await UserModel.getList({
      collegeId: collegeId
    })

    // map reduce生成arr
    function formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          case "asSecretary":
            value = v[j] ? "是" : "否"
            break;
          default:
            value = v[j]
        }
        return value
      }))
    }

    const header = ['*教师工号', '*教师姓名', "*教师性别", "教秘权限"];
    const filter = ["account", "name", "sex", "asSecretary"];
    // 导出excel
    excel_export_from_json(list, header, filter, formatJson, collegeName + "教师列表")
  }

  // 批量导入教师-班级外
  static importTeachers(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/college/importTeachers`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }
}

export {CollegeModel}
