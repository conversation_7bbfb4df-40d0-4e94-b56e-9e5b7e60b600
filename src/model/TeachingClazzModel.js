import {CommonModel} from "@/model/CommonModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  getOneDetail,
  addOne,
  editOne,
  getStudentList,
  deleteOneStudent, addOneStudent
} from "@/api/TeachingClazzApi";
import axios from "axios";
import {API_URL} from "@/config/main";
import {getToken} from "@/utils/auth";
import {msg_err} from "@/utils/ele_component";

class TeachingClazzModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      teachingClazzId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取一个教学班详情
  static async getOneDetail(id) {
    let [res] = await getOneDetail({
      teachingClazzId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 新增一个教学班
  static async addOne(entity) {
    let [data] = await addOne(entity);
    return data;
  }

  // 编辑一个教学班
  static async editOne(entity) {
    let [data] = await editOne(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取学生分页列表
  static async getStudentList(page, size, sort, document) {
    let [data] = await getStudentList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 删除一个学生
  static async deleteOneStudent(studentId, teachingClazzId) {
    let [res] = await deleteOneStudent({
      studentId,
      teachingClazzId
    })
    return res.code === "000000";
  }

  // 新增一个学生
  static async addOneStudent(studentId, teachingClazzId) {
    let [res] = await addOneStudent({
      studentId,
      teachingClazzId
    })
    return res.code === "000000";
  }

  // 批量导入学生-班级内
  static importStudentsByTeachingClazzId(file, teachingClazzId) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('teachingClazzId', teachingClazzId)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/teachingClazz/importStudentsByTeachingClazzId`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 批量导入教学班
  static importTeachingClazz(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/teachingClazz/importTeachingClazz`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(response.data.data)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

}

export {TeachingClazzModel}
