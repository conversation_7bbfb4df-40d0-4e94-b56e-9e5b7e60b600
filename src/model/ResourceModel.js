import {CommonModel} from "@/model/CommonModel";
import {
  // 资源分类接口
  getCategoryPageList,
  getCategoryList,
  getCategoryOne,
  addOrEditCategory,
  deleteCategoryOne,
  // 资源接口
  getResourcePageList,
  getResourceList,
  getResourceOne,
  addOrEditResource,
  deleteResourceOne,
  getResourceDetail,
  getResourceCategories,
  getFeaturedResources,
  getLatestResources,
  getResourceStats,
  // 收藏接口
  getFavoritePageList,
  getFavoriteList,
  deleteFavoriteOne,
  // 统计接口
  getResourceStatistics,
  getResourceTypeStatistics,
  getCategoryStatistics,
  getFavoriteStatistics
} from "@/api/ResourceApi";

// ============== 资源分类Model ==============
class ResourceCategoryModel {
  // 获取资源分类分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getCategoryPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取资源分类列表
  static async getList(document) {
    let [data] = await getCategoryList(document);
    return data.data;
  }

  // 获取一个资源分类
  static async getOne(categoryId) {
    let [res] = await getCategoryOne({categoryId: categoryId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 新增或修改资源分类
  static async addOrEdit(entity) {
    let [data] = await addOrEditCategory(entity);
    return data;
  }

  // 删除一个资源分类
  static async deleteOne(categoryId) {
    let [res] = await deleteCategoryOne({categoryId: categoryId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }
}

// ============== 资源Model ==============
class ResourceModel {
  // 获取资源分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getResourcePageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取资源列表
  static async getList(document) {
    let [data] = await getResourceList(document);
    return data.data;
  }

  // 获取一个资源
  static async getOne(resourceId) {
    let [res] = await getResourceOne({resourceId: resourceId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 新增或修改资源
  static async addOrEdit(entity) {
    let [data] = await addOrEditResource(entity);
    return data;
  }

  // 删除一个资源
  static async deleteOne(resourceId) {
    let [res] = await deleteResourceOne({resourceId: resourceId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 获取资源详情（前台接口）
  static async getDetail(resourceId) {
    let [res] = await getResourceDetail(resourceId);
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 获取推荐资源
  static async getFeaturedResources() {
    let [res] = await getFeaturedResources();
    if (res.code === "000000") {
      return res.data;
    } else {
      return [];
    }
  }

  // 获取最新资源
  static async getLatestResources() {
    let [res] = await getLatestResources();
    if (res.code === "000000") {
      return res.data;
    } else {
      return [];
    }
  }

  // 获取资源统计数据
  static async getStats() {
    let [res] = await getResourceStats();
    if (res.code === "000000") {
      return res.data;
    } else {
      return {};
    }
  }
}

// ============== 资源统计Model ==============
class ResourceStatisticsModel {
  // 获取资源统计概览
  static async getOverview() {
    let [res] = await getResourceStatistics();
    if (res.code === "000000") {
      return res.data;
    } else {
      return {};
    }
  }

  // 获取资源类型统计
  static async getTypeStatistics() {
    let [res] = await getResourceTypeStatistics();
    if (res.code === "000000") {
      return res.data;
    } else {
      return [];
    }
  }

  // 获取分类统计
  static async getCategoryStatistics() {
    let [res] = await getCategoryStatistics();
    if (res.code === "000000") {
      return res.data;
    } else {
      return [];
    }
  }
}

export {
  ResourceCategoryModel,
  ResourceModel,
  ResourceStatisticsModel
}
