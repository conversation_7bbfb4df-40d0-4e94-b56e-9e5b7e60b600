import {
  getResourcePageList,
  getResourceDetail,
  getRelatedResources,
  increaseResourceViews,
  increaseResourceDownloads,
  toggleResourceFavorite,
  getUserFavoriteResources,
  getResourceCategories,
  getResourcesByCategory,
  searchResources,
  getFeaturedResources,
  getLatestResources,
  getResourceStats,
  getHotSearchTags,
  downloadResource,
  isResourceFavorited
} from "@/api/resource";
import {date_format} from '@/utils/common';

class ResourceModel {
  // 获取资源分页列表
  static async getResourcePageList(page, size, params = {}) {
    try {
      let [data] = await getResourcePageList(page - 1, size, params); // 后端从0开始分页
      data=data.data || {}
      if (data && data.content) {
        return {
          list: data.content,
          total: data.totalElements,
          page: data.number + 1, // 转换为前端的1开始分页
          size: data.size
        };
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockResourcePageList(page, size, params);
  }

  // 获取资源详情
  static async getResourceDetail(id) {
    try {
      let [data] = await getResourceDetail(id);
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockResourceDetail(id);
  }

  // 获取相关资源
  static async getRelatedResources(id, categoryId) {
    try {
      let [data] = await getRelatedResources(id, categoryId);
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockRelatedResources(id, categoryId);
  }

  // 增加资源浏览量
  static async increaseResourceViews(id) {
    try {
      let [data] = await increaseResourceViews(id);
      data=data.data
      return data;
    } catch (error) {
      console.error('API调用失败:', error);
      return true; // 失败时返回true避免影响用户体验
    }
  }

  // 增加资源下载量
  static async increaseResourceDownloads(id) {
    try {
      let [data] = await increaseResourceDownloads(id);
      data=data.data
      return data;
    } catch (error) {
      console.error('API调用失败:', error);
      return true; // 失败时返回true避免影响用户体验
    }
  }

  // 资源收藏/取消收藏
  static async toggleResourceFavorite(id) {
    try {
      let [data] = await toggleResourceFavorite(id);
      data=data.data
      return data.isFavorited;
    } catch (error) {
      console.error('API调用失败:', error);
      return false; // 失败时返回false
    }
  }

  // 获取用户收藏的资源列表
  static async getUserFavoriteResources(page, size, params = {}) {
    try {
      let [data] = await getUserFavoriteResources(page - 1, size, params);
      data=data.data || {}
      if (data && data.content) {
        return {
          list: data.content,
          total: data.totalElements,
          page: data.number + 1,
          size: data.size
        };
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockUserFavoriteResources(page, size, params);
  }

  // 获取资源分类列表
  static async getResourceCategories() {
    try {
      let [data] = await getResourceCategories();
      data=data.data || []
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    //return this.getMockResourceCategories();
  }

  // 根据分类获取资源列表
  static async getResourcesByCategory(categoryId, page, size, params = {}) {
    try {
      let [data] = await getResourcesByCategory(categoryId, page - 1, size, params);
      data=data.data || {}
      if (data && data.content) {
        return {
          list: data.content,
          total: data.totalElements,
          page: data.number + 1,
          size: data.size
        };
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockResourcesByCategory(categoryId, page, size, params);
  }

  // 搜索资源
  static async searchResources(page, size, params = {}) {
    try {
      let [data] = await searchResources(page - 1, size, params);
      data=data.data || {}
      if (data && data.content) {
        return {
          list: data.content,
          total: data.totalElements,
          page: data.number + 1,
          size: data.size
        };
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockSearchResources(page, size, params);
  }

  // 获取推荐资源
  static async getFeaturedResources() {
    try {
      let [data] = await getFeaturedResources();
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockFeaturedResources();
  }

  // 获取最新资源
  static async getLatestResources() {
    try {
      let [data] = await getLatestResources();
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockLatestResources();
  }

  // 获取资源统计数据
  static async getResourceStats() {
    try {
      let [data] = await getResourceStats();
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockResourceStats();
  }

  // 获取热门搜索标签
  static async getHotSearchTags() {
    try {
      let [data] = await getHotSearchTags();
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockHotSearchTags();
  }

  // 下载资源
  static async downloadResource(id) {
    try {
      let [data] = await downloadResource(id);
      data=data.data
      if (data) {
        return data;
      }
    } catch (error) {
      console.error('API调用失败，使用模拟数据:', error);
    }
    // API调用失败时使用模拟数据
    return this.getMockDownloadResource(id);
  }

  // 检查用户是否收藏了某个资源
  static async isResourceFavorited(id) {
    try {
      let [data] = await isResourceFavorited(id);
      data=data.data
      return data.isFavorited;
    } catch (error) {
      console.error('API调用失败:', error);
      return false; // 失败时返回false
    }
  }

  // =================模拟数据方法=================

  // 模拟资源列表数据
  static getMockResourcePageList(page = 1, size = 12, params = {}) {
    const mockResourceList = [
      {
        id: 1,
        title: '数据结构与算法教程（完整版）',
        description: '详细介绍数据结构与算法的基础知识，包含线性表、栈、队列、树、图等核心内容，适合计算机专业学生学习使用。',
        type: 'pdf',
        typeName: 'PDF文档',
        categoryId: 1,
        categoryName: '教学资料',
        author: '张教授',
        fileUrl: 'https://example.com/files/data-structure.pdf',
        thumbnail: 'https://picsum.photos/300/200?random=1',
        fileSize: 15728640, // 15MB
        uploadTime: '2024-01-15 10:00:00',
        views: 256,
        downloads: 89,
        favorites: 45,
        tags: ['数据结构', '算法', '计算机'],
        relatedToCourse: true,
        courseId: 1,
        courseName: '数据结构课程',
        canDownload: true,
        canPreview: true
      },
      {
        id: 2,
        title: '机械工程制图标准规范',
        description: '机械工程制图的国家标准和行业规范，包含各类图形符号、标注方法、技术要求等内容。',
        type: 'pdf',
        typeName: 'PDF文档',
        categoryId: 1,
        categoryName: '教学资料',
        author: '李工程师',
        fileUrl: 'https://example.com/files/mechanical-drawing.pdf',
        thumbnail: 'https://picsum.photos/300/200?random=2',
        fileSize: 8954321,
        uploadTime: '2024-01-12 14:30:00',
        views: 189,
        downloads: 67,
        favorites: 23,
        tags: ['机械制图', '工程图', '标准规范'],
        relatedToCourse: true,
        courseId: 2,
        courseName: '机械制图课程',
        canDownload: true,
        canPreview: true
      },
      {
        id: 3,
        title: '虚拟仿真实验操作视频教程',
        description: '详细演示虚拟仿真实验的操作流程，包含设备使用、实验步骤、注意事项等内容。',
        type: 'video',
        typeName: '视频文件',
        categoryId: 2,
        categoryName: '实验视频',
        author: '实验中心',
        fileUrl: 'https://example.com/files/experiment-video.mp4',
        thumbnail: 'https://picsum.photos/300/200?random=3',
        fileSize: 156789012, // 150MB
        uploadTime: '2024-01-10 09:15:00',
        views: 342,
        downloads: 128,
        favorites: 67,
        tags: ['虚拟仿真', '实验操作', '教学视频'],
        relatedToCourse: false,
        courseId: null,
        courseName: null,
        canDownload: false, // 视频不提供下载
        canPreview: true
      },
      {
        id: 4,
        title: '化学分子结构模型图集',
        description: '包含常见化学分子的三维结构模型图，有助于理解分子空间构型和化学键特性。',
        type: 'image',
        typeName: '图片文件',
        categoryId: 3,
        categoryName: '多媒体资源',
        author: '化学系',
        fileUrl: 'https://example.com/files/molecular-structure.jpg',
        thumbnail: 'https://picsum.photos/300/200?random=4',
        fileSize: 2048576, // 2MB
        uploadTime: '2024-01-08 16:00:00',
        views: 128,
        downloads: 45,
        favorites: 28,
        tags: ['化学', '分子结构', '模型图'],
        relatedToCourse: true,
        courseId: 3,
        courseName: '有机化学课程',
        canDownload: false, // 图片不提供下载
        canPreview: true
      },
      {
        id: 5,
        title: '英语听力训练音频材料',
        description: '专门为工科学生设计的英语听力训练材料，包含专业词汇和日常对话练习。',
        type: 'audio',
        typeName: '音频文件',
        categoryId: 3,
        categoryName: '多媒体资源',
        author: '外语系',
        fileUrl: 'https://example.com/files/english-listening.mp3',
        thumbnail: 'https://picsum.photos/300/200?random=5',
        fileSize: 12582912, // 12MB
        uploadTime: '2024-01-05 11:20:00',
        views: 398,
        downloads: 156,
        favorites: 89,
        tags: ['英语听力', '专业英语', '音频训练'],
        relatedToCourse: true,
        courseId: 4,
        courseName: '专业英语课程',
        canDownload: false, // 音频不提供下载
        canPreview: true
      },
      {
        id: 6,
        title: '毕业设计模板合集',
        description: '包含各专业毕业设计论文模板、答辩PPT模板等，符合学校规范要求。',
        type: 'rar',
        typeName: 'RAR压缩包',
        categoryId: 4,
        categoryName: '软件工具',
        author: '教务处',
        fileUrl: 'https://example.com/files/graduation-templates.rar',
        thumbnail: 'https://picsum.photos/300/200?random=6',
        fileSize: 52428800, // 50MB
        uploadTime: '2024-01-03 13:45:00',
        views: 521,
        downloads: 234,
        favorites: 123,
        tags: ['毕业设计', '论文模板', '答辩PPT'],
        relatedToCourse: false,
        courseId: null,
        courseName: null,
        canDownload: true, // 压缩包提供下载
        canPreview: false
      },
      {
        id: 7,
        title: 'CAD绘图软件安装包',
        description: 'AutoCAD 2024教育版安装包，包含详细安装说明和激活指南。',
        type: 'zip',
        typeName: 'ZIP压缩包',
        categoryId: 4,
        categoryName: '软件工具',
        author: '软件中心',
        fileUrl: 'https://example.com/files/autocad-installer.zip',
        thumbnail: 'https://picsum.photos/300/200?random=7',
        fileSize: 524288000, // 500MB
        uploadTime: '2024-01-01 10:00:00',
        views: 456,
        downloads: 189,
        favorites: 98,
        tags: ['CAD软件', 'AutoCAD', '设计软件'],
        relatedToCourse: true,
        courseId: 2,
        courseName: '机械制图课程',
        canDownload: true, // 软件包提供下载
        canPreview: false
      },
      {
        id: 8,
        title: '电路仿真实验指导书',
        description: '详细的电路仿真实验指导，包含电路搭建、参数设置、结果分析等内容。',
        type: 'docx',
        typeName: 'Word文档',
        categoryId: 1,
        categoryName: '教学资料',
        author: '电气工程系',
        fileUrl: 'https://example.com/files/circuit-simulation.docx',
        thumbnail: 'https://picsum.photos/300/200?random=8',
        fileSize: 3145728, // 3MB
        uploadTime: '2023-12-28 14:20:00',
        views: 298,
        downloads: 145,
        favorites: 67,
        tags: ['电路仿真', '实验指导', '电气工程'],
        relatedToCourse: true,
        courseId: 5,
        courseName: '电路分析课程',
        canDownload: true, // Office文档提供下载
        canPreview: false
      },
      {
        id: 9,
        title: '编程规范与代码风格指南',
        description: '编程开发过程中的代码规范和风格指南，包含命名规则、注释规范、代码结构等内容。',
        type: 'txt',
        typeName: '文本文件',
        categoryId: 1,
        categoryName: '教学资料',
        author: '计算机系',
        fileUrl: 'https://example.com/files/coding-standards.txt',
        thumbnail: 'https://picsum.photos/300/200?random=9',
        fileSize: 102400, // 100KB
        uploadTime: '2023-12-25 09:30:00',
        views: 167,
        downloads: 89,
        favorites: 34,
        tags: ['编程规范', '代码风格', '软件开发'],
        relatedToCourse: true,
        courseId: 6,
        courseName: '软件工程课程',
        canDownload: true, // 文本文件提供下载
        canPreview: true
      }
    ];

    // 应用筛选条件
    let filteredList = mockResourceList;
    
    if (params.keyword) {
      filteredList = filteredList.filter(item =>
        item.title.includes(params.keyword) || 
        item.description.includes(params.keyword) ||
        item.tags.some(tag => tag.includes(params.keyword)) ||
        (item.courseName && item.courseName.includes(params.keyword)) ||
        (item.author && item.author.includes(params.keyword))
      );
    }

    if (params.categoryId) {
      filteredList = filteredList.filter(item => item.categoryId == params.categoryId);
    }

    if (params.type) {
      filteredList = filteredList.filter(item => item.type === params.type);
    }

    if (params.author) {
      filteredList = filteredList.filter(item => item.author.includes(params.author));
    }

    if (params.courseName) {
      filteredList = filteredList.filter(item => 
        item.courseName && item.courseName.includes(params.courseName)
      );
    }

    if (params.isPublic === true) {
      filteredList = filteredList.filter(item => !item.relatedToCourse);
    } else if (params.isPublic === false) {
      filteredList = filteredList.filter(item => item.relatedToCourse);
    }

    // 排序
    if (params.sortBy) {
      switch (params.sortBy) {
        case 'time':
          filteredList.sort((a, b) => new Date(b.uploadTime) - new Date(a.uploadTime));
          break;
        case 'views':
          filteredList.sort((a, b) => b.views - a.views);
          break;
        case 'downloads':
          filteredList.sort((a, b) => b.downloads - a.downloads);
          break;
        case 'favorites':
          filteredList.sort((a, b) => b.favorites - a.favorites);
          break;
      }
    }

    const total = filteredList.length;
    const start = (page - 1) * size;
    const list = filteredList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }

  // 模拟资源详情数据
  static getMockResourceDetail(id) {
    const mockResourceDetails = {
      1: {
        id: 1,
        title: '数据结构与算法教程（完整版）',
        description: `
          <p>这是一本全面介绍数据结构与算法的教程，内容涵盖了计算机科学中最重要的基础知识。</p>
          
          <h3>主要内容包括：</h3>
          <ul>
            <li><strong>线性数据结构</strong>：数组、链表、栈、队列的原理和实现</li>
            <li><strong>树形结构</strong>：二叉树、平衡树、B树等的概念和应用</li>
            <li><strong>图论算法</strong>：图的表示、遍历、最短路径等经典算法</li>
            <li><strong>排序与查找</strong>：各种排序算法的比较和优化</li>
            <li><strong>动态规划</strong>：经典问题的解决思路和实现方法</li>
          </ul>
          
          <h3>适用对象：</h3>
          <p>本教程适合计算机科学与技术、软件工程等相关专业的本科生和研究生学习使用，也可作为程序员技能提升的参考资料。</p>
          
          <h3>学习建议：</h3>
          <p>建议结合实际编程练习，通过代码实现来加深对算法原理的理解。每章后面都有相应的练习题目，可以帮助巩固所学知识。</p>
        `,
        type: 'pdf',
        typeName: 'PDF文档',
        categoryId: 1,
        categoryName: '教学资料',
        author: '张教授',
        authorIntro: '计算机科学系教授，博士生导师，专注于算法优化和数据结构研究20余年。',
        fileUrl: 'https://example.com/files/data-structure.pdf',
        thumbnail: 'https://picsum.photos/600/400?random=1',
        fileSize: 15728640,
        uploadTime: '2024-01-15 10:00:00',
        views: 256,
        downloads: 89,
        favorites: 45,
        tags: ['数据结构', '算法', '计算机', '编程基础'],
        relatedToCourse: true,
        courseId: 1,
        courseName: '数据结构课程',
        canDownload: true,
        canPreview: true,
        previewPages: 5, // PDF预览页数
        language: '中文',
        difficulty: '中级',
        estimatedReadTime: '6小时'
      },
      3: {
        id: 3,
        title: '虚拟仿真实验操作视频教程',
        description: `
          <p>本视频详细演示了虚拟仿真实验平台的使用方法，帮助学生快速掌握实验操作技能。</p>
          
          <h3>视频内容：</h3>
          <ul>
            <li>平台登录和界面介绍</li>
            <li>实验项目选择和配置</li>
            <li>虚拟设备操作演示</li>
            <li>数据采集和分析方法</li>
            <li>实验报告生成流程</li>
          </ul>
          
          <p><strong>时长：</strong>约45分钟</p>
          <p><strong>分辨率：</strong>1920x1080 高清</p>
        `,
        type: 'video',
        typeName: '视频文件',
        categoryId: 2,
        categoryName: '实验视频',
        author: '实验中心',
        authorIntro: '虚拟仿真实验教学中心，负责全校虚拟仿真实验教学的技术支持和培训工作。',
        fileUrl: 'https://example.com/files/experiment-video.mp4',
        thumbnail: 'https://picsum.photos/600/400?random=3',
        fileSize: 156789012,
        uploadTime: '2024-01-10 09:15:00',
        views: 342,
        downloads: 128,
        favorites: 67,
        tags: ['虚拟仿真', '实验操作', '教学视频', '操作指南'],
        relatedToCourse: false,
        courseId: null,
        courseName: null,
        canDownload: false,
        canPreview: true,
        duration: '45分钟',
        resolution: '1920x1080',
        format: 'MP4'
      }
    };

    return mockResourceDetails[id] || null;
  }

  // 模拟相关资源数据
  static getMockRelatedResources(id, categoryId) {
    return [
      {
        id: 2,
        title: '机械工程制图标准规范',
        description: '机械工程制图的国家标准和行业规范...',
        thumbnail: 'https://picsum.photos/200/150?random=2',
        author: '李工程师',
        views: 189,
        downloads: 67
      },
      {
        id: 4,
        title: '化学分子结构模型图集',
        description: '包含常见化学分子的三维结构模型图...',
        thumbnail: 'https://picsum.photos/200/150?random=4',
        author: '化学系',
        views: 128,
        downloads: 45
      },
      {
        id: 5,
        title: '英语听力训练音频材料',
        description: '专门为工科学生设计的英语听力训练材料...',
        thumbnail: 'https://picsum.photos/200/150?random=5',
        author: '外语系',
        views: 398,
        downloads: 156
      }
    ];
  }

  // 模拟资源分类数据
  static getMockResourceCategories() {
    return [
      {
        id: 1,
        name: '教学资料',
        description: '各类教学相关的资料文档，包含教案、课件、参考书籍等',
        icon: 'el-icon-document',
        resourceCount: 128,
        viewCount: 5620,
        parentId: 0,
        sort: 1,
        color: '#409EFF'
      },
      {
        id: 2,
        name: '实验视频',
        description: '虚拟仿真实验操作演示视频，帮助学生理解实验流程',
        icon: 'el-icon-video-camera',
        resourceCount: 45,
        viewCount: 2340,
        parentId: 0,
        sort: 2,
        color: '#67C23A'
      },
      {
        id: 3,
        name: '多媒体资源',
        description: '图片、音频等多媒体教学素材',
        icon: 'el-icon-picture',
        resourceCount: 89,
        viewCount: 3450,
        parentId: 0,
        sort: 3,
        color: '#E6A23C'
      },
      {
        id: 4,
        name: '软件工具',
        description: '专业软件安装包、工具合集等',
        icon: 'el-icon-setting',
        resourceCount: 32,
        viewCount: 1890,
        parentId: 0,
        sort: 4,
        color: '#F56C6C'
      },
      {
        id: 5,
        name: '课程作业',
        description: '各门课程的作业模板、参考答案等',
        icon: 'el-icon-edit-outline',
        resourceCount: 76,
        viewCount: 2780,
        parentId: 0,
        sort: 5,
        color: '#909399'
      }
    ];
  }

  // 模拟推荐资源数据
  static getMockFeaturedResources() {
    return [
      {
        id: 1,
        title: '数据结构与算法教程（完整版）',
        description: '详细介绍数据结构与算法的基础知识，适合计算机专业学生学习使用。',
        type: 'pdf',
        typeName: 'PDF文档',
        thumbnail: 'https://picsum.photos/300/200?random=1',
        author: '张教授',
        uploadTime: '2024-01-15 10:00:00',
        views: 256,
        downloads: 89,
        favorites: 45
      },
      {
        id: 3,
        title: '虚拟仿真实验操作视频教程',
        description: '详细演示虚拟仿真实验的操作流程，包含设备使用、实验步骤等。',
        type: 'video',
        typeName: '视频文件',
        thumbnail: 'https://picsum.photos/300/200?random=3',
        author: '实验中心',
        uploadTime: '2024-01-10 09:15:00',
        views: 342,
        downloads: 128,
        favorites: 67
      },
      {
        id: 6,
        title: '毕业设计模板合集',
        description: '包含各专业毕业设计论文模板、答辩PPT模板等。',
        type: 'rar',
        typeName: 'RAR压缩包',
        thumbnail: 'https://picsum.photos/300/200?random=6',
        author: '教务处',
        uploadTime: '2024-01-03 13:45:00',
        views: 521,
        downloads: 234,
        favorites: 123
      }
    ];
  }

  // 模拟最新资源数据
  static getMockLatestResources() {
    return [
      {
        id: 1,
        title: '数据结构与算法教程（完整版）',
        author: '张教授',
        type: 'pdf',
        uploadTime: '2024-01-15 10:00:00',
        views: 256,
        downloads: 89
      },
      {
        id: 2,
        title: '机械工程制图标准规范',
        author: '李工程师',
        type: 'pdf',
        uploadTime: '2024-01-12 14:30:00',
        views: 189,
        downloads: 67
      },
      {
        id: 3,
        title: '虚拟仿真实验操作视频教程',
        author: '实验中心',
        type: 'video',
        uploadTime: '2024-01-10 09:15:00',
        views: 342,
        downloads: 128
      },
      {
        id: 4,
        title: '化学分子结构模型图集',
        author: '化学系',
        type: 'image',
        uploadTime: '2024-01-08 16:00:00',
        views: 128,
        downloads: 45
      },
      {
        id: 5,
        title: '英语听力训练音频材料',
        author: '外语系',
        type: 'audio',
        uploadTime: '2024-01-05 11:20:00',
        views: 398,
        downloads: 156
      }
    ];
  }

  // 模拟资源统计数据
  static getMockResourceStats() {
    return {
      totalResources: 370,
      totalViews: 12580,
      totalDownloads: 4560,
      totalFavorites: 1890
    };
  }

  // 模拟热门搜索标签
  static getMockHotSearchTags() {
    return [
      '数据结构', '算法', '机械制图', '虚拟仿真', 
      '实验指导', '毕业设计', 'CAD', '编程',
      '电路分析', '化学', '英语听力', '软件工具'
    ];
  }

  // 模拟用户收藏资源列表
  static getMockUserFavoriteResources(page = 1, size = 10, params = {}) {
    const mockFavoriteList = [
      {
        id: 1,
        title: '数据结构与算法教程（完整版）',
        type: 'pdf',
        typeName: 'PDF文档',
        author: '张教授',
        favoriteTime: '2024-01-16 15:30:00',
        thumbnail: 'https://picsum.photos/300/200?random=1'
      },
      {
        id: 3,
        title: '虚拟仿真实验操作视频教程',
        type: 'video',
        typeName: '视频文件',
        author: '实验中心',
        favoriteTime: '2024-01-14 10:20:00',
        thumbnail: 'https://picsum.photos/300/200?random=3'
      }
    ];

    const total = mockFavoriteList.length;
    const start = (page - 1) * size;
    const list = mockFavoriteList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }

  // 模拟搜索资源
  static getMockSearchResources(page = 1, size = 12, params = {}) {
    // 使用通用的资源列表进行搜索
    return this.getMockResourcePageList(page, size, params);
  }

  // 模拟根据分类获取资源
  static getMockResourcesByCategory(categoryId, page = 1, size = 12, params = {}) {
    // 添加分类筛选条件
    const searchParams = { ...params, categoryId };
    return this.getMockResourcePageList(page, size, searchParams);
  }

  // 模拟下载资源
  static getMockDownloadResource(id) {
    return {
      success: true,
      downloadUrl: `https://example.com/download/resource_${id}.zip`,
      message: '下载链接已生成，请点击下载'
    };
  }

  // 获取资源类型图标
  static getResourceTypeIcon(type) {
    const iconMap = {
      'pdf': 'el-icon-document',
      'docx': 'el-icon-document',
      'doc': 'el-icon-document',
      'pptx': 'el-icon-document',
      'ppt': 'el-icon-document',
      'xlsx': 'el-icon-document',
      'xls': 'el-icon-document',
      'video': 'el-icon-video-camera',
      'mp4': 'el-icon-video-camera',
      'avi': 'el-icon-video-camera',
      'mov': 'el-icon-video-camera',
      'audio': 'el-icon-headset',
      'mp3': 'el-icon-headset',
      'wav': 'el-icon-headset',
      'aac': 'el-icon-headset',
      'image': 'el-icon-picture',
      'jpg': 'el-icon-picture',
      'jpeg': 'el-icon-picture',
      'png': 'el-icon-picture',
      'gif': 'el-icon-picture',
      'svg': 'el-icon-picture',
      'zip': 'el-icon-folder-opened',
      'rar': 'el-icon-folder-opened',
      '7z': 'el-icon-folder-opened'
    };
    
    return iconMap[type] || 'el-icon-document';
  }

  // 格式化文件大小
  static formatFileSize(bytes) {
    if (!bytes || bytes === 0) return '0 B';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = (bytes / Math.pow(1024, i)).toFixed(1);
    
    return `${size} ${sizes[i]}`;
  }

  // 格式化日期
  static formatDate(dateStr, format = 'YYYY-MM-DD') {
    if (!dateStr) return '';
    
    // 处理不同的格式需求
    let formatTemplate = format;
    if (format === 'YYYY-MM-DD HH:mm') {
      formatTemplate = 'yyyy-MM-dd HH:mm';
    } else if (format === 'YYYY-MM-DD') {
      formatTemplate = 'yyyy-MM-dd';
    } else if (format === 'MM-DD') {
      formatTemplate = 'MM-dd';
    } else if (format === 'YYYY年MM月DD日') {
      formatTemplate = 'yyyy年MM月dd日';
    }
    
    // 使用现有的date_format函数
    return date_format(dateStr, formatTemplate);
  }

  // 判断文件是否可以预览
  static canPreview(type) {
    const previewableTypes = ['pdf', 'image', 'jpg', 'jpeg', 'png', 'gif', 'svg', 'video', 'mp4', 'avi', 'mov', 'audio', 'mp3', 'wav', 'aac'];
    return previewableTypes.includes(type);
  }

  // 判断文件是否可以下载
  static canDownload(type) {
    const downloadableTypes = ['pdf', 'docx', 'doc', 'pptx', 'ppt', 'xlsx', 'xls', 'zip', 'rar', '7z'];
    return downloadableTypes.includes(type);
  }

  // 获取资源类型标签（课程关联或公共资源）
  static getResourceTypeLabel(resource) {
    if (resource.relatedToCourse && resource.courseName) {
      return {
        type: 'course',
        label: resource.courseName,
        color: '#f2f2f2'
      };
    } else {
      return {
        type: 'public',
        label: '公共资源',
        color: '#f2f2f2'
      };
    }
  }
}

export {ResourceModel}