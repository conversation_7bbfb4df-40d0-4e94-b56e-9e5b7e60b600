import {
  getBannerList,
  getIndustryStats,
  getEnterprisePageList,
  getEnterpriseDetail,
  getEnterpriseList,
  getJobPageList,
  getJobDetail,
  getJobList,
  getStudentPageList,
  getStudentDetail,
  getStudentList
} from "@/api/industry";
import {date_format} from '@/utils/common';

class IndustryModel {
  
  // ============== 产教融合主页 ==============
  
  // 获取轮播图列表
  static async getBannerList() {
    try {
      // 首先尝试调用真实API
      const [res] = await getBannerList();
      if (res && res.code === '000000') {
        return res.data || [];
      }
    } catch (error) {
      console.warn('获取轮播图API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockBannerList();
  }

  // 获取统计数据
  static async getIndustryStats() {
    try {
      // 首先尝试调用真实API
      const [res] = await getIndustryStats();
      if (res && res.code === '000000') {
        return res.data || {};
      }
    } catch (error) {
      console.warn('获取统计数据API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockIndustryStats();
  }
  
  // ============== 合作企业 ==============
  
  // 获取企业分页列表
  static async getEnterprisePageList(page, size, params = {}) {
    try {
      // 首先尝试调用真实API
      const [res] = await getEnterprisePageList(page, size, params);
      if (res && res.code === '000000') {
        return res.data || { list: [], total: 0, page, size };
      }
    } catch (error) {
      console.warn('获取企业分页列表API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockEnterprisePageList(page, size, params);
  }
  
  // 获取企业详情
  static async getEnterpriseDetail(id) {
    try {
      // 首先尝试调用真实API
      const [res] = await getEnterpriseDetail(id);
      if (res && res.code === '000000') {
        return res.data || null;
      }
    } catch (error) {
      console.warn('获取企业详情API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockEnterpriseDetail(id);
  }
  
  // 获取企业列表(简化版)
  static async getEnterpriseList(params = {}) {
    try {
      // 首先尝试调用真实API
      const [res] = await getEnterpriseList();
      if (res && res.code === '000000') {
        return res.data || [];
      }
    } catch (error) {
      console.warn('获取企业列表API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockEnterpriseList(params);
  }
  
  // ============== 招聘信息 ==============
  
  // 获取招聘信息分页列表
  static async getJobPageList(page, size, params = {}) {
    try {
      // 首先尝试调用真实API
      const [res] = await getJobPageList(page, size, params);
      if (res && res.code === '000000') {
        return res.data || { list: [], total: 0, page, size };
      }
    } catch (error) {
      console.warn('获取招聘信息分页列表API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockJobPageList(page, size, params);
  }
  
  // 获取招聘信息详情
  static async getJobDetail(id) {
    try {
      // 首先尝试调用真实API
      const [res] = await getJobDetail(id);
      if (res && res.code === '000000') {
        return res.data || null;
      }
    } catch (error) {
      console.warn('获取招聘信息详情API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockJobDetail(id);
  }
  
  // 获取招聘信息列表(简化版)
  static async getJobList(params = {}) {
    try {
      // 首先尝试调用真实API
      const [res] = await getJobList();
      if (res && res.code === '000000') {
        return res.data || [];
      }
    } catch (error) {
      console.warn('获取招聘信息列表API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockJobList(params);
  }
  
  // ============== 优秀学生 ==============
  
  // 获取学生分页列表
  static async getStudentPageList(page, size, params = {}) {
    try {
      // 首先尝试调用真实API
      const [res] = await getStudentPageList(page, size, params);
      if (res && res.code === '000000') {
        return res.data || { list: [], total: 0, page, size };
      }
    } catch (error) {
      console.warn('获取学生分页列表API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    return this.getMockStudentPageList(page, size, params);
  }
  
  // 获取学生详情
  static async getStudentDetail(id) {
    try {
      // 首先尝试调用真实API
      const [res] = await getStudentDetail(id);
      if (res && res.code === '000000') {
        return res.data || null;
      }
    } catch (error) {
      console.warn('获取学生详情API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockStudentDetail(id);
  }
  
  // 获取学生列表(简化版)
  static async getStudentList(params = {}) {
    try {
      // 首先尝试调用真实API
      const [res] = await getStudentList();
      if (res && res.code === '000000') {
        return res.data || [];
      }
    } catch (error) {
      console.warn('获取学生列表API调用失败，使用模拟数据:', error);
    }
    
    // API调用失败时，使用模拟数据
    // return this.getMockStudentList(params);
  }
  
  // =================模拟数据方法=================
  
  // 模拟轮播图数据
  static getMockBannerList() {
    return [
      {
        id: 1,
        title: '产教融合新突破',
        description: '与知名企业深度合作，打造实践教学新模式',
        image: 'https://picsum.photos/800/350?random=101',
        link: '/industry/cooperation/1',
        status: 'active',
        sort: 1
      },
      {
        id: 2,
        title: '校企合作共育人才',
        description: '华为、腾讯等知名企业加入人才培养计划',
        image: 'https://picsum.photos/800/350?random=102',
        link: '/industry/cooperation/2',
        status: 'active',
        sort: 2
      },
      {
        id: 3,
        title: '优秀学生就业典型',
        description: '毕业生在各行业展现优异表现，成为行业精英',
        image: 'https://picsum.photos/800/350?random=103',
        link: '/industry/students',
        status: 'active',
        sort: 3
      },
      {
        id: 4,
        title: '创新创业孵化基地',
        description: '为学生提供创业平台，助力创新项目落地',
        image: 'https://picsum.photos/800/350?random=104',
        link: '/industry/innovation',
        status: 'active',
        sort: 4
      }
    ];
  }
  
  // 模拟统计数据
  static getMockIndustryStats() {
    return {
      enterpriseCount: 128,
      studentCount: 456,
      jobCount: 89,
      projectCount: 36
    };
  }
  
  // 模拟企业分页列表数据
  static getMockEnterprisePageList(page = 1, size = 12, params = {}) {
    const mockEnterpriseList = [
      {
        id: 1,
        name: '华为技术有限公司',
        logo: 'https://via.placeholder.com/120x120?text=华为',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '深圳',
        foundYear: '1987',
        description: '全球领先的ICT（信息与通信）基础设施和智能终端提供商，致力于把数字世界带入每个人、每个家庭、每个组织，构建万物互联的智能世界。',
        tags: ['5G', '人工智能', '云计算', '物联网'],
        cooperationYears: 5,
        studentCount: 128,
        projectCount: 15,
        website: 'https://www.huawei.com'
      },
      {
        id: 2,
        name: '腾讯科技（深圳）有限公司',
        logo: 'https://via.placeholder.com/120x120?text=腾讯',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '深圳',
        foundYear: '1998',
        description: '中国领先的互联网增值服务提供商之一，为用户提供一站式在线生活服务。业务涵盖社交、娱乐、金融、资讯、工具等领域。',
        tags: ['社交网络', '游戏', '金融科技', '云服务'],
        cooperationYears: 4,
        studentCount: 96,
        projectCount: 12,
        website: 'https://www.tencent.com'
      },
      {
        id: 3,
        name: '阿里巴巴集团控股有限公司',
        logo: 'https://via.placeholder.com/120x120?text=阿里',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '杭州',
        foundYear: '1999',
        description: '以"让天下没有难做的生意"为使命，是中国最大的电子商务公司，业务涵盖电商、云计算、数字媒体及娱乐等领域。',
        tags: ['电子商务', '云计算', '大数据', '人工智能'],
        cooperationYears: 3,
        studentCount: 72,
        projectCount: 10,
        website: 'https://www.alibaba.com'
      },
      {
        id: 4,
        name: '百度在线网络技术（北京）有限公司',
        logo: 'https://via.placeholder.com/120x120?text=百度',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '北京',
        foundYear: '2000',
        description: '全球最大的中文搜索引擎、最大的中文网站。百度愿景是成为最懂用户，并能帮助人们成长的全球顶级高科技公司。',
        tags: ['搜索引擎', '人工智能', '自动驾驶', '智能云'],
        cooperationYears: 3,
        studentCount: 54,
        projectCount: 8,
        website: 'https://www.baidu.com'
      },
      {
        id: 5,
        name: '京东集团股份有限公司',
        logo: 'https://via.placeholder.com/120x120?text=京东',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '北京',
        foundYear: '1998',
        description: '中国领先的技术驱动的电商和零售基础设施服务商。致力于为消费者、品牌合作伙伴、中小企业等提供高质量服务。',
        tags: ['电子商务', '物流', '金融科技', '数字医疗'],
        cooperationYears: 2,
        studentCount: 38,
        projectCount: 6,
        website: 'https://www.jd.com'
      },
      {
        id: 6,
        name: '字节跳动有限公司',
        logo: 'https://via.placeholder.com/120x120?text=字节跳动',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '北京',
        foundYear: '2012',
        description: '全球化的科技公司，致力于用技术创造价值。旗下产品包括抖音、今日头条、西瓜视频等，服务全球数十亿用户。',
        tags: ['短视频', '信息流', '教育', '企业服务'],
        cooperationYears: 2,
        studentCount: 42,
        projectCount: 5,
        website: 'https://www.bytedance.com'
      },
      {
        id: 7,
        name: '中国建设银行股份有限公司',
        logo: 'https://via.placeholder.com/120x120?text=建设银行',
        type: 'finance',
        typeName: '金融',
        scale: 'large',
        scaleName: '大型企业',
        location: '北京',
        foundYear: '1954',
        description: '中国主要的大型商业银行之一，致力于为客户提供全面的金融服务。在公司金融、个人金融和资金业务等方面处于领先地位。',
        tags: ['银行', '金融科技', '普惠金融', '数字化'],
        cooperationYears: 4,
        studentCount: 65,
        projectCount: 9,
        website: 'https://www.ccb.com'
      },
      {
        id: 8,
        name: '中国工商银行股份有限公司',
        logo: 'https://via.placeholder.com/120x120?text=工商银行',
        type: 'finance',
        typeName: '金融',
        scale: 'large',
        scaleName: '大型企业',
        location: '北京',
        foundYear: '1984',
        description: '中国最大的商业银行，为全球客户提供全面、优质的金融服务。在传统银行业务和创新金融服务方面均处于行业领先地位。',
        tags: ['银行', '投资银行', '资产管理', '金融科技'],
        cooperationYears: 5,
        studentCount: 78,
        projectCount: 11,
        website: 'https://www.icbc.com.cn'
      },
      {
        id: 9,
        name: '海尔集团公司',
        logo: 'https://via.placeholder.com/120x120?text=海尔',
        type: 'manufacturing',
        typeName: '制造业',
        scale: 'large',
        scaleName: '大型企业',
        location: '青岛',
        foundYear: '1984',
        description: '全球领先的美好生活解决方案服务商，致力于成为高科技引领的物联网生态品牌。业务涵盖智慧家庭、工业互联网等领域。',
        tags: ['智能家电', '物联网', '工业互联网', '智慧家庭'],
        cooperationYears: 3,
        studentCount: 45,
        projectCount: 7,
        website: 'https://www.haier.com'
      },
      {
        id: 10,
        name: '比亚迪股份有限公司',
        logo: 'https://via.placeholder.com/120x120?text=比亚迪',
        type: 'manufacturing',
        typeName: '制造业',
        scale: 'large',
        scaleName: '大型企业',
        location: '深圳',
        foundYear: '1995',
        description: '致力于用技术创新满足人们对美好生活的向往。业务布局涵盖电子、汽车、新能源和轨道交通等领域。',
        tags: ['新能源汽车', '电池技术', '轨道交通', '电子'],
        cooperationYears: 3,
        studentCount: 56,
        projectCount: 8,
        website: 'https://www.byd.com'
      }
    ];

    // 应用筛选条件
    let filteredList = mockEnterpriseList;
    
    if (params.keyword) {
      filteredList = filteredList.filter(item =>
        item.name.includes(params.keyword) || 
        item.description.includes(params.keyword) ||
        item.tags.some(tag => tag.includes(params.keyword))
      );
    }

    if (params.type) {
      filteredList = filteredList.filter(item => item.type === params.type);
    }
    
    if (params.scale) {
      filteredList = filteredList.filter(item => item.scale === params.scale);
    }
    
    if (params.location) {
      filteredList = filteredList.filter(item => item.location === params.location);
    }

    const total = filteredList.length;
    const start = (page - 1) * size;
    const list = filteredList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }
  
  // 模拟企业详情数据
  static getMockEnterpriseDetail(id) {
    const mockEnterpriseDetails = {
      1: {
        id: 1,
        name: '华为技术有限公司',
        logo: 'https://via.placeholder.com/200x200?text=华为',
        type: 'internet',
        typeName: '互联网',
        scale: 'large',
        scaleName: '大型企业',
        location: '深圳',
        foundYear: '1987',
        description: '全球领先的ICT（信息与通信）基础设施和智能终端提供商，致力于把数字世界带入每个人、每个家庭、每个组织，构建万物互联的智能世界。',
        fullDescription: `
          <p>华为创立于1987年，是全球领先的ICT（信息与通信）基础设施和智能终端提供商。</p>
          <h3>企业愿景</h3>
          <p>把数字世界带入每个人、每个家庭、每个组织，构建万物互联的智能世界。</p>
          <h3>主要业务</h3>
          <ul>
            <li>运营商业务：为全球电信运营商提供通信网络设备</li>
            <li>企业业务：为政府及企业客户提供ICT解决方案</li>
            <li>消费者业务：为消费者提供智能终端产品</li>
          </ul>
        `,
        tags: ['5G', '人工智能', '云计算', '物联网'],
        cooperationYears: 5,
        studentCount: 128,
        projectCount: 15,
        website: 'https://www.huawei.com',
        address: '广东省深圳市龙岗区坂田华为基地',
        employeeCount: '19万+',
        revenue: '6369亿元人民币（2021年）',
        cooperationProjects: [
          {
            id: 1,
            name: '5G通信技术实验室共建',
            description: '与华为共建5G通信技术实验室，开展前沿技术研究',
            startTime: '2019-09-01',
            status: '进行中'
          },
          {
            id: 2,
            name: '人工智能人才培养计划',
            description: '华为AI工程师认证培训项目',
            startTime: '2020-03-01',
            status: '进行中'
          }
        ],
        contactInfo: {
          contact: '张经理',
          phone: '0755-28780808',
          email: '<EMAIL>'
        }
      }
    };

    return mockEnterpriseDetails[id] || null;
  }
  
  // 模拟企业列表(简化版)数据
  static getMockEnterpriseList(params = {}) {
    const list = this.getMockEnterprisePageList(1, 6, params).list;
    return list.map(item => ({
      id: item.id,
      name: item.name,
      logo: item.logo,
      type: item.type,
      typeName: item.typeName,
      tags: item.tags.slice(0, 3) // 只显示前3个标签
    }));
  }
  
  // 模拟招聘信息分页列表数据
  static getMockJobPageList(page = 1, size = 12, params = {}) {
    const mockJobList = [
      {
        id: 1,
        title: '前端开发工程师',
        companyId: 1,
        companyName: '华为技术有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=华为',
        category: 'development',
        categoryName: '技术开发',
        location: '深圳',
        salary: '15-25K',
        experience: '1-3年',
        education: '本科',
        jobType: '全职',
        description: '负责前端页面开发和用户体验优化，与产品设计师和后端开发工程师紧密合作，确保产品功能的完整实现。',
        skills: ['Vue.js', 'React', 'JavaScript', 'CSS3', 'TypeScript'],
        urgent: true,
        hot: false,
        publishTime: '2024-01-15 10:00:00',
        requirements: [
          '计算机相关专业本科以上学历',
          '熟练掌握Vue.js、React等前端框架',
          '具有良好的代码规范和团队协作能力',
          '有移动端开发经验者优先'
        ]
      },
      {
        id: 2,
        title: '后端开发工程师',
        companyId: 1,
        companyName: '华为技术有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=华为',
        category: 'development',
        categoryName: '技术开发',
        location: '深圳',
        salary: '18-30K',
        experience: '3-5年',
        education: '本科',
        jobType: '全职',
        description: '负责后端系统架构设计和开发，确保系统的高可用性、可扩展性和安全性。',
        skills: ['Java', 'Spring Boot', 'MySQL', 'Redis', 'Docker'],
        urgent: false,
        hot: true,
        publishTime: '2024-01-14 14:30:00',
        requirements: [
          '计算机相关专业本科以上学历',
          '精通Java、Spring Boot等后端技术',
          '熟悉MySQL、Redis等数据库技术',
          '有微服务架构经验者优先'
        ]
      },
      {
        id: 3,
        title: 'UI/UX设计师',
        companyId: 2,
        companyName: '腾讯科技（深圳）有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=腾讯',
        category: 'design',
        categoryName: '产品设计',
        location: '深圳',
        salary: '12-20K',
        experience: '2-4年',
        education: '本科',
        jobType: '全职',
        description: '负责产品的用户界面设计和用户体验优化，制作高保真原型和设计规范。',
        skills: ['Sketch', 'Figma', 'Photoshop', 'Illustrator', 'Principle'],
        urgent: false,
        hot: false,
        publishTime: '2024-01-13 09:15:00',
        requirements: [
          '设计相关专业本科以上学历',
          '熟练使用Sketch、Figma等设计工具',
          '具有良好的审美能力和创新思维',
          '有移动端产品设计经验者优先'
        ]
      },
      {
        id: 4,
        title: '产品经理',
        companyId: 2,
        companyName: '腾讯科技（深圳）有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=腾讯',
        category: 'product',
        categoryName: '产品管理',
        location: '深圳',
        salary: '20-35K',
        experience: '3-5年',
        education: '本科',
        jobType: '全职',
        description: '负责产品规划、需求分析、功能设计和项目管理，推动产品从概念到上线的全流程。',
        skills: ['产品规划', '需求分析', '数据分析', 'Axure', '项目管理'],
        urgent: true,
        hot: true,
        publishTime: '2024-01-12 16:00:00',
        requirements: [
          '理工科或相关专业本科以上学历',
          '具有3年以上互联网产品经验',
          '具备优秀的逻辑思维和沟通能力',
          '有B端产品经验者优先'
        ]
      },
      {
        id: 5,
        title: '数据分析师',
        companyId: 3,
        companyName: '阿里巴巴集团控股有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=阿里',
        category: 'analysis',
        categoryName: '数据分析',
        location: '杭州',
        salary: '15-28K',
        experience: '2-4年',
        education: '本科',
        jobType: '全职',
        description: '负责业务数据分析、用户行为分析和商业策略支持，为业务决策提供数据支撑。',
        skills: ['SQL', 'Python', 'R', 'Tableau', 'Excel'],
        urgent: false,
        hot: true,
        publishTime: '2024-01-11 11:20:00',
        requirements: [
          '数学、统计学或相关专业本科以上学历',
          '熟练使用SQL、Python等数据分析工具',
          '具有良好的业务理解能力',
          '有电商数据分析经验者优先'
        ]
      },
      {
        id: 6,
        title: '运营专员',
        companyId: 3,
        companyName: '阿里巴巴集团控股有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=阿里',
        category: 'operation',
        categoryName: '运营管理',
        location: '杭州',
        salary: '10-18K',
        experience: '1-3年',
        education: '本科',
        jobType: '全职',
        description: '负责用户运营、活动策划和内容营销，提升用户活跃度和转化率。',
        skills: ['用户运营', '活动策划', '内容营销', '数据分析', '文案策划'],
        urgent: false,
        hot: false,
        publishTime: '2024-01-10 13:45:00',
        requirements: [
          '市场营销或相关专业本科以上学历',
          '具有1年以上运营相关工作经验',
          '具备优秀的文案策划和沟通能力',
          '有电商运营经验者优先'
        ]
      },
      {
        id: 7,
        title: '算法工程师',
        companyId: 4,
        companyName: '百度在线网络技术（北京）有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=百度',
        category: 'development',
        categoryName: '技术开发',
        location: '北京',
        salary: '25-40K',
        experience: '3-5年',
        education: '硕士',
        jobType: '全职',
        description: '负责机器学习算法研究和开发，包括推荐系统、自然语言处理和计算机视觉等领域。',
        skills: ['Python', 'TensorFlow', 'PyTorch', '机器学习', '深度学习'],
        urgent: true,
        hot: true,
        publishTime: '2024-01-09 10:30:00',
        requirements: [
          '计算机相关专业硕士以上学历',
          '精通Python、TensorFlow等深度学习框架',
          '具有扎实的数学基础和算法能力',
          '有大规模机器学习系统经验者优先'
        ]
      },
      {
        id: 8,
        title: '人力资源专员',
        companyId: 5,
        companyName: '京东集团股份有限公司',
        companyLogo: 'https://via.placeholder.com/60x60?text=京东',
        category: 'hr',
        categoryName: '人力资源',
        location: '北京',
        salary: '8-15K',
        experience: '1-3年',
        education: '本科',
        jobType: '全职',
        description: '负责招聘、培训、绩效管理和员工关系等人力资源工作。',
        skills: ['招聘', '培训', '绩效管理', '员工关系', 'HRBP'],
        urgent: false,
        hot: false,
        publishTime: '2024-01-08 15:00:00',
        requirements: [
          '人力资源或相关专业本科以上学历',
          '具有1年以上人力资源相关工作经验',
          '具备良好的沟通协调能力',
          '有互联网公司HR经验者优先'
        ]
      }
    ];

    // 应用筛选条件
    let filteredList = mockJobList;
    
    if (params.keyword) {
      filteredList = filteredList.filter(item =>
        item.title.includes(params.keyword) || 
        item.companyName.includes(params.keyword) ||
        item.description.includes(params.keyword)
      );
    }

    if (params.category) {
      filteredList = filteredList.filter(item => item.category === params.category);
    }
    
    if (params.experience) {
      filteredList = filteredList.filter(item => item.experience.includes(params.experience));
    }
    
    if (params.location) {
      filteredList = filteredList.filter(item => item.location === params.location);
    }

    const total = filteredList.length;
    const start = (page - 1) * size;
    const list = filteredList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }
  
  // 模拟招聘信息详情数据
  static getMockJobDetail(id) {
    const mockJobDetails = {
      1: {
        jobId: 1,
        title: '前端开发工程师',
        companyId: 1,
        companyName: '华为技术有限公司',
        companyLogo: 'https://via.placeholder.com/120x120?text=华为',
        category: 'development',
        categoryName: '技术开发',
        location: '深圳',
        salary: '15-25K',
        experience: '1-3年',
        education: '本科',
        jobType: '全职',
        description: '负责前端页面开发和用户体验优化，与产品设计师和后端开发工程师紧密合作，确保产品功能的完整实现。',
        fullDescription: `
          <h3>工作职责</h3>
          <ul>
            <li>负责Web前端页面的开发和维护</li>
            <li>与UI设计师、产品经理紧密配合，实现产品设计和交互效果</li>
            <li>优化前端性能，提升用户体验</li>
            <li>编写高质量的代码，确保代码的可维护性和可扩展性</li>
            <li>参与技术方案讨论，推动前端技术创新</li>
          </ul>
          
          <h3>任职要求</h3>
          <ul>
            <li>计算机相关专业本科以上学历</li>
            <li>熟练掌握HTML5、CSS3、JavaScript等前端基础技术</li>
            <li>熟练使用Vue.js、React等前端框架</li>
            <li>熟悉前端工程化工具，如Webpack、Vite等</li>
            <li>具有良好的代码规范和团队协作能力</li>
            <li>有移动端开发经验者优先</li>
          </ul>
          
          <h3>福利待遇</h3>
          <ul>
            <li>具有竞争力的薪酬体系</li>
            <li>完善的社会保险和住房公积金</li>
            <li>带薪年假、法定节假日</li>
            <li>员工培训和职业发展机会</li>
            <li>健身房、员工餐厅等生活设施</li>
          </ul>
        `,
        skills: ['Vue.js', 'React', 'JavaScript', 'CSS3', 'TypeScript'],
        urgent: true,
        hot: false,
        publishTime: '2024-01-15 10:00:00',
        requirements: [
          '计算机相关专业本科以上学历',
          '熟练掌握Vue.js、React等前端框架',
          '具有良好的代码规范和团队协作能力',
          '有移动端开发经验者优先'
        ],
        contactInfo: {
          contact: '李女士',
          phone: '0755-28780808',
          email: '<EMAIL>'
        }
      }
    };

    return mockJobDetails[id] || null;
  }
  
  // 模拟招聘信息列表(简化版)数据
  static getMockJobList(params = {}) {
    const list = this.getMockJobPageList(1, 4, params).list;
    return list.map(item => ({
      id: item.id,
      title: item.title,
      companyName: item.companyName,
      companyLogo: item.companyLogo,
      location: item.location,
      salary: item.salary,
      experience: item.experience,
      urgent: item.urgent,
      publishTime: item.publishTime
    }));
  }
  
  // 模拟学生分页列表数据
  static getMockStudentPageList(page = 1, size = 12, params = {}) {
    const mockStudentList = [
      {
        id: 1,
        name: '张晓明',
        photo: 'https://via.placeholder.com/120x120?text=张晓明',
        major: '计算机科学与技术',
        graduationYear: '2023',
        company: '华为技术有限公司',
        position: '高级软件工程师',
        category: 'outstanding',
        categoryName: '优秀毕业生',
        projectCount: 8,
        quote: '持续学习，追求卓越，用技术改变世界',
        achievements: [
          { id: 1, title: '国家奖学金', type: 'scholarship' },
          { id: 2, title: 'ACM程序设计竞赛金奖', type: 'competition' },
          { id: 3, title: '优秀毕业生', type: 'honor' }
        ],
        story: '在校期间积极参与各类竞赛和项目实践，获得多项荣誉。毕业后加入华为，参与5G核心网项目开发，表现突出。'
      },
      {
        id: 2,
        name: '李雨涵',
        photo: 'https://via.placeholder.com/120x120?text=李雨涵',
        major: '软件工程',
        graduationYear: '2023',
        company: '腾讯科技（深圳）有限公司',
        position: '前端开发工程师',
        category: 'scholarship',
        categoryName: '奖学金获得者',
        projectCount: 6,
        quote: '代码如诗，用心编写每一行',
        achievements: [
          { id: 4, title: '校级奖学金', type: 'scholarship' },
          { id: 5, title: '优秀学生干部', type: 'honor' },
          { id: 6, title: '创新创业大赛二等奖', type: 'competition' }
        ],
        story: '在校期间担任学生会主席，组织多项活动。专注前端技术学习，毕业后加入腾讯负责微信小程序开发。'
      },
      {
        id: 3,
        name: '王子豪',
        photo: 'https://via.placeholder.com/120x120?text=王子豪',
        major: '电子信息工程',
        graduationYear: '2022',
        company: '阿里巴巴集团控股有限公司',
        position: '算法工程师',
        category: 'competition',
        categoryName: '竞赛获奖者',
        projectCount: 10,
        quote: '算法之美在于解决问题的优雅',
        achievements: [
          { id: 7, title: '全国大学生数学建模竞赛一等奖', type: 'competition' },
          { id: 8, title: '国家励志奖学金', type: 'scholarship' },
          { id: 9, title: '省级优秀学生', type: 'honor' }
        ],
        story: '对算法和数据结构有深入研究，多次获得编程竞赛奖项。现在阿里负责推荐算法优化工作。'
      },
      {
        id: 4,
        name: '陈思雨',
        photo: 'https://via.placeholder.com/120x120?text=陈思雨',
        major: '数据科学与大数据技术',
        graduationYear: '2023',
        company: '百度在线网络技术（北京）有限公司',
        position: '数据科学家',
        category: 'innovation',
        categoryName: '创新创业',
        projectCount: 7,
        quote: '数据背后隐藏着改变世界的力量',
        achievements: [
          { id: 10, title: '创新创业大赛特等奖', type: 'competition' },
          { id: 11, title: '发明专利2项', type: 'innovation' },
          { id: 12, title: '学术论文发表', type: 'academic' }
        ],
        story: '在校期间创立数据分析工作室，为多家企业提供数据服务。现在百度负责搜索推荐系统优化。'
      },
      {
        id: 5,
        name: '刘建华',
        photo: 'https://via.placeholder.com/120x120?text=刘建华',
        major: '机械工程',
        graduationYear: '2022',
        company: '比亚迪股份有限公司',
        position: '机械设计工程师',
        category: 'outstanding',
        categoryName: '优秀毕业生',
        projectCount: 5,
        quote: '精密制造，匠心工艺',
        achievements: [
          { id: 13, title: '机械创新设计大赛一等奖', type: 'competition' },
          { id: 14, title: '优秀毕业设计', type: 'honor' },
          { id: 15, title: '学术奖学金', type: 'scholarship' }
        ],
        story: '专注于新能源汽车关键零部件设计，毕业设计获得优秀。现在比亚迪负责电池包结构设计工作。'
      },
      {
        id: 6,
        name: '周梦琪',
        photo: 'https://via.placeholder.com/120x120?text=周梦琪',
        major: '金融学',
        graduationYear: '2023',
        company: '中国建设银行股份有限公司',
        position: '金融科技专员',
        category: 'scholarship',
        categoryName: '奖学金获得者',
        projectCount: 4,
        quote: '科技赋能金融，创新引领未来',
        achievements: [
          { id: 16, title: '连续四年获得奖学金', type: 'scholarship' },
          { id: 17, title: '金融创新大赛二等奖', type: 'competition' },
          { id: 18, title: '三好学生', type: 'honor' }
        ],
        story: '深入学习金融科技知识，参与多个金融创新项目。现在建设银行负责移动银行产品优化工作。'
      },
      {
        id: 7,
        name: '赵志强',
        photo: 'https://via.placeholder.com/120x120?text=赵志强',
        major: '人工智能',
        graduationYear: '2022',
        company: '字节跳动有限公司',
        position: '机器学习工程师',
        category: 'competition',
        categoryName: '竞赛获奖者',
        projectCount: 9,
        quote: '人工智能将重塑未来',
        achievements: [
          { id: 19, title: 'AI挑战赛冠军', type: 'competition' },
          { id: 20, title: '国际会议论文发表', type: 'academic' },
          { id: 21, title: '校长奖学金', type: 'scholarship' }
        ],
        story: '在人工智能领域有深入研究，发表多篇高质量论文。现在字节跳动负责推荐系统算法优化。'
      },
      {
        id: 8,
        name: '孙美玲',
        photo: 'https://via.placeholder.com/120x120?text=孙美玲',
        major: '电子商务',
        graduationYear: '2023',
        company: '京东集团股份有限公司',
        position: '产品经理',
        category: 'innovation',
        categoryName: '创新创业',
        projectCount: 6,
        quote: '用户体验是产品的灵魂',
        achievements: [
          { id: 22, title: '电商创新大赛一等奖', type: 'competition' },
          { id: 23, title: '创业项目孵化成功', type: 'innovation' },
          { id: 24, title: '优秀实习生', type: 'honor' }
        ],
        story: '在校期间创立电商平台，成功孵化并获得投资。毕业后加入京东，负责移动端产品设计。'
      }
    ];

    // 应用筛选条件
    let filteredList = mockStudentList;
    
    if (params.category) {
      filteredList = filteredList.filter(item => item.category === params.category);
    }
    
    if (params.major) {
      filteredList = filteredList.filter(item => item.major.includes(params.major));
    }
    
    if (params.year) {
      filteredList = filteredList.filter(item => item.graduationYear === params.year);
    }

    const total = filteredList.length;
    const start = (page - 1) * size;
    const list = filteredList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }
  
  // 模拟学生详情数据
  static getMockStudentDetail(id) {
    const mockStudentDetails = {
      1: {
        id: 1,
        name: '张晓明',
        photo: 'https://via.placeholder.com/200x200?text=张晓明',
        major: '计算机科学与技术',
        graduationYear: '2023',
        company: '华为技术有限公司',
        position: '高级软件工程师',
        category: 'outstanding',
        categoryName: '优秀毕业生',
        projectCount: 8,
        quote: '持续学习，追求卓越，用技术改变世界',
        achievements: [
          { id: 1, title: '国家奖学金', type: 'scholarship' },
          { id: 2, title: 'ACM程序设计竞赛金奖', type: 'competition' },
          { id: 3, title: '优秀毕业生', type: 'honor' }
        ],
        story: `
          <h3>成长经历</h3>
          <p>张晓明同学在校期间表现优异，不仅学习成绩优秀，还积极参与各类竞赛和项目实践。他认为理论与实践相结合是学习的最佳方式。</p>
          
          <h3>项目经验</h3>
          <ul>
            <li><strong>智能校园管理系统</strong> - 团队负责人，使用Spring Boot + Vue.js开发</li>
            <li><strong>分布式文件存储系统</strong> - 核心开发者，基于Java实现</li>
            <li><strong>机器学习推荐系统</strong> - 算法优化，使用Python和TensorFlow</li>
          </ul>
          
          <h3>获奖情况</h3>
          <ul>
            <li>2021年、2022年连续获得国家奖学金</li>
            <li>2022年ACM程序设计竞赛亚洲区域赛金奖</li>
            <li>2023年被评为校级优秀毕业生</li>
          </ul>
          
          <h3>工作表现</h3>
          <p>毕业后加入华为技术有限公司，参与5G核心网项目开发。在工作中表现突出，多次获得项目组表彰，现已成为团队的技术骨干。</p>
        `,
        skills: ['Java', 'Spring Boot', 'Vue.js', 'MySQL', 'Redis'],
        projects: [
          {
            id: 1,
            name: '智能校园管理系统',
            description: '基于Spring Boot + Vue.js的校园信息化管理平台',
            role: '团队负责人',
            tech: ['Spring Boot', 'Vue.js', 'MySQL']
          },
          {
            id: 2,
            name: '分布式文件存储系统',
            description: '高并发、高可用的分布式文件存储解决方案',
            role: '核心开发者',
            tech: ['Java', 'Redis', 'Docker']
          }
        ],
        contact: {
          email: '<EMAIL>',
          phone: '158****8888'
        }
      }
    };

    return mockStudentDetails[id] || null;
  }
  
  // 模拟学生列表(简化版)数据
  static getMockStudentList(params = {}) {
    const list = this.getMockStudentPageList(1, 4, params).list;
    return list.map(item => ({
      id: item.id,
      name: item.name,
      photo: item.photo,
      major: item.major,
      company: item.company,
      achievements: item.achievements.slice(0, 2) // 只显示前2个成就
    }));
  }
  
  // 格式化日期
  static formatDate(dateStr, format = 'YYYY-MM-DD') {
    if (!dateStr) return '';
    
    // 处理不同的格式需求
    let formatTemplate = format;
    if (format === 'YYYY-MM-DD HH:mm') {
      formatTemplate = 'yyyy-MM-dd HH:mm';
    } else if (format === 'YYYY-MM-DD') {
      formatTemplate = 'yyyy-MM-dd';
    } else if (format === 'MM-DD') {
      formatTemplate = 'MM-dd';
    } else if (format === 'YYYY年MM月DD日') {
      formatTemplate = 'yyyy年MM月dd日';
    }
    
    // 使用现有的date_format函数
    return date_format(dateStr, formatTemplate);
  }
}

export {IndustryModel}