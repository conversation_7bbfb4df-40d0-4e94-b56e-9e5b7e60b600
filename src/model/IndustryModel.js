import {CommonModel} from "@/model/CommonModel";
import {
  // 轮播图接口
  getBannerPageList,
  getBannerList,
  getBannerOne,
  addOrEditBanner,
  deleteBannerOne,
  // 企业接口
  getEnterprisePageList,
  getEnterpriseList,
  getEnterpriseOne,
  addOrEditEnterprise,
  deleteEnterpriseOne,
  // 招聘信息接口
  getJobPageList,
  getJobList,
  getJobOne,
  addOrEditJob,
  deleteJobOne,
  // 学生接口
  getStudentPageList,
  getStudentList,
  getStudentOne,
  addOrEditStudent,
  deleteStudentOne,
  // 统计数据接口
  getIndustryStats,
  getCategoryStats
} from "@/api/IndustryApi";

// ============== 轮播图Model ==============
class IndustryBannerModel {
  // 获取轮播图分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getBannerPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取轮播图列表
  static async getList(document) {
    let [data] = await getBannerList(document);
    return data.data;
  }

  // 获取一个轮播图
  static async getOne(bannerId) {
    let [res] = await getBannerOne({bannerId: bannerId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 新增或修改轮播图
  static async addOrEdit(entity) {
    let [data] = await addOrEditBanner(entity);
    return data;
  }

  // 删除一个轮播图
  static async deleteOne(bannerId) {
    let [res] = await deleteBannerOne({bannerId: bannerId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }
}

// ============== 企业Model ==============
class IndustryEnterpriseModel {
  // 获取企业分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getEnterprisePageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取企业列表
  static async getList(document) {
    let [data] = await getEnterpriseList(document);
    return data.data;
  }

  // 获取一个企业
  static async getOne(enterpriseId) {
    let [res] = await getEnterpriseOne({enterpriseId: enterpriseId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 新增或修改企业
  static async addOrEdit(entity) {
    let [data] = await addOrEditEnterprise(entity);
    return data;
  }

  // 删除一个企业
  static async deleteOne(enterpriseId) {
    let [res] = await deleteEnterpriseOne({enterpriseId: enterpriseId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }
}

// ============== 招聘信息Model ==============
class IndustryJobModel {
  // 获取招聘信息分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getJobPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取招聘信息列表
  static async getList(document) {
    let [data] = await getJobList(document);
    return data.data;
  }

  // 获取一个招聘信息
  static async getOne(jobId) {
    let [res] = await getJobOne({jobId: jobId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 新增或修改招聘信息
  static async addOrEdit(entity) {
    let [data] = await addOrEditJob(entity);
    return data;
  }

  // 删除一个招聘信息
  static async deleteOne(jobId) {
    let [res] = await deleteJobOne({jobId: jobId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }
}

// ============== 优秀学生Model ==============
class IndustryStudentModel {
  // 获取学生分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getStudentPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取学生列表
  static async getList(document) {
    let [data] = await getStudentList(document);
    return data.data;
  }

  // 获取一个学生
  static async getOne(studentId) {
    let [res] = await getStudentOne({studentId: studentId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }

  // 新增或修改学生
  static async addOrEdit(entity) {
    let [data] = await addOrEditStudent(entity);
    return data;
  }

  // 删除一个学生
  static async deleteOne(studentId) {
    let [res] = await deleteStudentOne({studentId: studentId});
    if (res.code === "000000") {
      return res.data;
    } else {
      return false;
    }
  }
}

// ============== 统计数据Model ==============
class IndustryStatisticsModel {
  // 获取产教融合统计数据
  static async getIndustryStats() {
    let [res] = await getIndustryStats();
    if (res.code === "000000") {
      return res.data;
    } else {
      return {};
    }
  }

  // 获取分类统计数据
  static async getCategoryStats() {
    let [res] = await getCategoryStats();
    if (res.code === "000000") {
      return res.data;
    } else {
      return {};
    }
  }
}

export {
  IndustryBannerModel,
  IndustryEnterpriseModel,
  IndustryJobModel,
  IndustryStudentModel,
  IndustryStatisticsModel
};