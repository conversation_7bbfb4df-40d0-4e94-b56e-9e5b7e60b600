import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne} from "@/api/CourseCommentApi";
import {excel_export_from_json} from "@/utils/excel";
import {dateFormat} from "@/filters";

class CourseCommentModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      courseCommentId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 到期某个课程的全部评价 todo 解决导出没有自动换行问题
  static async exportAll(courseId, courseName) {
    // 获取班级内的学生列表
    let [list] = await this.getPageList(0, 50000, "", {
      courseId: courseId
    })

    // map reduce生成arr
    function formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          case "createTime":
            value = dateFormat(v[j], "yyyy-MM-dd HH:mm:ss")
            break
          default:
            value = v[j]
        }
        return value
      }))
    }

    const header = ['学号/工号', '姓名', "评价内容", "评价时间"];
    const filter = ["userAccount", "userName", "content", "createTime"];
    // 导出excel
    excel_export_from_json(list, header, filter, formatJson, courseName + "全部评价列表")
  }
}

export {CourseCommentModel}
