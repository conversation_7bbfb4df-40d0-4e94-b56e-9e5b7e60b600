import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne, getSearchList} from "@/api/ClazzApi";
import axios from "axios";
import {API_URL} from "@/config/main";
import {msg_err} from "@/utils/ele_component";
import {getToken} from "@/utils/auth";
import {excel_export_from_json} from "@/utils/excel";
import {UserModel} from "@/model/UserModel";

class ClazzModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      clazzId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 搜索班级列表
  static async getSearchList(document) {
    let [data] = await getSearchList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 批量导入学生-班级内
  static importStudentsByClazzId(file, clazzId) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('clazzId', clazzId)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/clazz/importStudentsByClazzId`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }

  // 批量导出学生-班级内
  static async exportStudentByClazzId(clazzId, clazzName) {
    // 获取班级内的学生列表
    let list = await UserModel.getList({
      clazzId: clazzId
    })

    // map reduce生成arr
    function formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          default:
            value = v[j]
        }
        return value
      }))
    }

    const header = ['*学生学号', '*学生姓名', "*学生性别"];
    const filter = ["account", "name", "sex"];
    // 导出excel
    excel_export_from_json(list, header, filter, formatJson, clazzName + "学生列表")
  }

  // 批量导入学生-班级外
  static importStudents(file) {
    return new Promise((resolve, reject) => {
      const formData = new FormData()
      formData.append('multipartFile', file)
      formData.append('startNum', "1")
      axios.create({
        baseURL: API_URL
      }).request({
        url: `v1/clazz/importStudents`,
        method: 'post',
        headers: {'Content-Type': 'multipart/form-data', 'Authorization': "Bearer " + getToken()},
        data: formData
      }).then(response => {
        if (response.status === 200) {
          if (response.data.code === "000000") {
            resolve(true)
          } else {
            msg_err(response.data.msg)
            resolve(false)
          }
        } else {
          msg_err(response.data.msg)
          resolve(false)
        }
      })
    })
  }
}

export {ClazzModel}
