import {CommonModel} from "@/model/CommonModel";
import {getPageList, getList, getOne, deleteOne,getLogList} from "@/api/LogApi";
import {dateFormat} from "@/filters";
import {excel_export_from_json} from "@/utils/excel";

/**
 * Log model
 */
class LogModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne(id)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取分页日志列表
  static async getLogList(page, size, sort, document) {
    let [data] = await getLogList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

// 导出成绩列表
  static async exportList(startNumber, endNumber, query) {
    // 计算
    let skipNumber = startNumber - 1
    let limitNumber = endNumber - skipNumber;
    // 获取列表
    query["skipNumber"] = skipNumber
    query["limitNumber"] = limitNumber
    let [list,] = await this.getLogList(-1, 20, "", query)

    // 导出Excel 数据字段和表头
    const filter = ["moduleName", "methodName", "userId", "ip", "createTime"];
    const header = ["模块名称", "功能名称", "用户id", "ip", "创建时间"];
    // map reduce生成arr
    let formatJson = function (filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          case "createTime":
            value = dateFormat(v["createTime"])
            break
          default:
            value = v[j]
        }
        return value
      }))
    }
    excel_export_from_json(list, header, filter, formatJson, `第${startNumber}条到第${endNumber}日志列表_${dateFormat(new Date())}`)
  }

}

export {LogModel}
