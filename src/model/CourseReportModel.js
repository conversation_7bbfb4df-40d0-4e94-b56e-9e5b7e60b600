import {CommonModel} from "@/model/CommonModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  getOneCourseRecordReport,
  teacherCorrectReport
} from "@/api/CourseReportApi";

class CourseReportModel {
  // 获取某个记录的报告
  static async getOneCourseRecordReport(id) {
    let [res] = await getOneCourseRecordReport({
      courseRecordId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      courseReportId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 教师批改报告
  static async teacherCorrectReport(courseRecordId, score, comment) {
    let [res] = await teacherCorrectReport(courseRecordId, score, comment);
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }
}

export {CourseReportModel}
