import {
    getOne,
    getOneCourseRecordReport, fillReportApi,
} from "@/api/CourseReportApi";

class CourseReportModel {
    // 获取某个记录的报告
    static async getOneCourseRecordReport(courseRecordId) {
        let [res] = await getOneCourseRecordReport({
            courseRecordId: courseRecordId
        })
        if (res.code === "000000") {
            return res.data
        } else {
            return false
        }
    }

    // 获取一个
    static async getOne(courseReportId) {
        let [res] = await getOne({
            courseReportId: courseReportId
        })
        if (res.code === "000000") {
            return res.data
        } else {
            return false
        }
    }

    // 填写课程报告
    static async fillReport(courseRecordId, content) {
        let [res] = await fillReportApi(
            courseRecordId,
            content
        );
        return res.code === "000000";
    }
}

export {CourseReportModel}
