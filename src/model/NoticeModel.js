import {CommonModel} from "@/model/CommonModel";
import {ConfigModel} from "@/model/ConfigModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  getCarouselList,
  getTopList,
  getByType,
  searchNotice,
  getRelatedNotice,
  increaseViews,
  getActiveNotices,
  getExpiringNotices,
  publishNotice,
  unpublishNotice,
  setTopNotice,
  processExpiredNotices,
  confirmRead,
  getStatistics
} from "@/api/NoticeApi";
import {msg_success, msg_err} from "@/utils/ele_component";

/**
 * 通知公告业务模型
 */
class NoticeModel {

  // 获取一个通知公告
  static async getOne(noticeId) {
    let [res] = await getOne({
      noticeId: noticeId
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改通知公告
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取通知公告分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取通知公告不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个通知公告
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取通知轮播列表
  static async getCarouselList() {
    let [res] = await getCarouselList()
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 获取置顶通知列表
  static async getTopList() {
    let [res] = await getTopList()
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 根据类型获取通知
  static async getByType(type, limit = 10) {
    let [res] = await getByType(type, limit)
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 搜索通知
  static async searchNotice(page, size, query) {
    let [data] = await searchNotice(page, size, query);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取相关通知
  static async getRelatedNotice(noticeId, type, limit = 5) {
    let [res] = await getRelatedNotice(noticeId, type, limit)
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 增加浏览量
  static async increaseViews(noticeId) {
    let [res] = await increaseViews(noticeId)
    return res.code === "000000"
  }

  // 获取有效期内的通知
  static async getActiveNotices() {
    let [res] = await getActiveNotices()
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 获取即将过期的通知
  static async getExpiringNotices(days = 7) {
    let [res] = await getExpiringNotices(days)
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 发布通知
  static async publishNotice(noticeId) {
    let [res] = await publishNotice(noticeId)
    if (res.code === "000000") {
      msg_success("通知发布成功")
      return true
    } else {
      msg_err(res.message || "通知发布失败")
      return false
    }
  }

  // 撤回通知
  static async unpublishNotice(noticeId) {
    let [res] = await unpublishNotice(noticeId)
    if (res.code === "000000") {
      msg_success("通知撤回成功")
      return true
    } else {
      msg_err(res.message || "通知撤回失败")
      return false
    }
  }

  // 设置置顶通知
  static async setTopNotice(noticeId, isTop) {
    let [res] = await setTopNotice(noticeId, isTop)
    if (res.code === "000000") {
      msg_success(isTop ? "设置置顶成功" : "取消置顶成功")
      return true
    } else {
      msg_err(res.message || "设置失败")
      return false
    }
  }

  // 处理过期通知
  static async processExpiredNotices() {
    let [res] = await processExpiredNotices()
    if (res.code === "000000") {
      msg_success(`处理过期通知成功，共处理${res.data}条`)
      return res.data
    } else {
      msg_err(res.message || "处理失败")
      return 0
    }
  }

  // 确认阅读通知
  static async confirmRead(noticeId, userId) {
    let [res] = await confirmRead(noticeId, userId)
    return res.code === "000000"
  }

  // 获取通知统计信息
  static async getStatistics() {
    let [res] = await getStatistics()
    if (res.code === "000000") {
      return res.data
    } else {
      return {
        totalCount: 0,
        topCount: 0,
        urgentCount: 0,
        typeStats: {},
        expiringCount: 0
      }
    }
  }

  // 格式化通知状态显示
  static formatStatus(status) {
    const statusMap = {
      'draft': '草稿',
      'active': '生效中',
      'expired': '已过期',
      'archived': '已归档'
    }
    return statusMap[status] || status
  }

  // 格式化通知类型显示
  static formatType(type) {
    const typeMap = {
      'important': '重要通知',
      'teaching': '教学通知',
      'system': '系统通知',
      'activity': '活动通知'
    }
    return typeMap[type] || type
  }

  // 格式化紧急程度显示
  static formatUrgency(urgency) {
    const urgencyMap = {
      'high': '高',
      'medium': '中',
      'low': '低'
    }
    return urgencyMap[urgency] || urgency
  }

  // 格式化审核状态显示
  static formatAuditStatus(auditStatus) {
    const auditStatusMap = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝'
    }
    return auditStatusMap[auditStatus] || auditStatus
  }

  // 获取类型选项
  static async getTypeOptions() {
    try {
      const config = JSON.parse(await ConfigModel.getConfig("noticeConfig"))
      if (config && config.types) {
        return JSON.parse(config.types)
      } else {
        // 如果配置不存在，返回默认分类
        return [
          {label: '重要通知', value: 'important'},
          {label: '教学通知', value: 'teaching'},
          {label: '系统通知', value: 'system'},
          {label: '活动通知', value: 'activity'}
        ]
      }
    } catch (error) {
      console.error('获取通知类型配置失败:', error)
      // 错误情况下返回默认分类
      return [
        {label: '重要通知', value: 'important'},
        {label: '教学通知', value: 'teaching'},
        {label: '系统通知', value: 'system'},
        {label: '活动通知', value: 'activity'}
      ]
    }
  }

  // 获取状态选项
  static getStatusOptions() {
    return [
      {label: '草稿', value: 'draft'},
      {label: '生效中', value: 'active'},
      {label: '已过期', value: 'expired'},
      {label: '已归档', value: 'archived'}
    ]
  }

  // 获取紧急程度选项
  static getUrgencyOptions() {
    return [
      {label: '高', value: 'high'},
      {label: '中', value: 'medium'},
      {label: '低', value: 'low'}
    ]
  }

  // 获取审核状态选项
  static getAuditStatusOptions() {
    return [
      {label: '待审核', value: 'pending'},
      {label: '已通过', value: 'approved'},
      {label: '已拒绝', value: 'rejected'}
    ]
  }

  // 获取通知级别选项
  static getNoticeLevelOptions() {
    return [
      {label: '紧急', value: 'urgent'},
      {label: '重要', value: 'important'},
      {label: '普通', value: 'normal'}
    ]
  }

  // 获取目标范围选项
  static getTargetScopeOptions() {
    return [
      {label: '全体', value: 'all'},
      {label: '学院', value: 'college'},
      {label: '专业', value: 'major'},
      {label: '班级', value: 'class'}
    ]
  }
}

export {NoticeModel}
