import {CommonModel} from "@/model/CommonModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  getOneTeachingClazzDetail,
  getTeachingClazzStudentList, getOneCourseScoreList, resetRecordLeftLimitNumber
} from "@/api/CourseRecordApi";

class CourseRecordModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      courseTaskId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }


  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取一个教学班详情
  static async getOneTeachingClazzDetail(courseTaskId, teacherId, teachingClazzId) {
    let [res] = await getOneTeachingClazzDetail({
      courseTaskId,
      teacherId,
      teachingClazzId
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取教学班学生分页列表
  static async getTeachingClazzStudentList(page, size, sort, document) {
    let [data] = await getTeachingClazzStudentList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取某个课程的分数记录
  static async getOneCourseScoreList(page, size, sort, document) {
    let [data] = await getOneCourseScoreList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 重置登录次数
  static async resetRecordLeftLimitNumber(courseRecordId) {
    let [res] = await resetRecordLeftLimitNumber(courseRecordId)
    return res.code === "000000";
  }
}

export {CourseRecordModel}
