import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne} from "@/api/CourseScore";
import {CourseModel} from "@/model/CourseModel";
import {CourseRecordModel} from "@/model/CourseRecordModel";
import {excel_export_from_json} from "@/utils/excel";
import {dateFormat} from "@/filters";
import {date_format} from "@/utils/common";

class CourseScoreModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      courseScoreId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 导出开放成绩记录列表
  static async exportScoreList(courseId, startNumber, endNumber, query) {
    // 获取课程详情
    let course = await CourseModel.getOne(courseId)
    // 计算
    let skipNumber = startNumber - 1
    let limitNumber = endNumber - skipNumber;
    // 获取列表
    query["skipNumber"] = skipNumber
    query["limitNumber"] = limitNumber
    let [list,] = await CourseRecordModel.getOneCourseScoreList(-1, 20, "", query)

    // 导出Excel 数据字段和表头
    const filter = ["clazzName", "userAccount", "userName", "courseCompleted", "courseScore", "createTime"];
    const header = ["行政班名称", "学号", "姓名", "是否已完成课程", "课程分数", "创建时间"];
    // map reduce生成arr
    let formatJson = function (filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          case "clazzName":
            value = v["gradeEntity"][0]["name"] + "-" + v["majorEntity"][0]["name"] + "-" + v["clazzEntity"][0]["name"]
            break
          case "userAccount":
            value = v["userEntity"][0]["account"]
            break
          case "userName":
            value = v["userEntity"][0]["name"]
            break
          case "courseCompleted":
            value = v[j] ? "已完成" : "未完成"
            break
          case "courseScore":
            value = v[j].toFixed(2)
            break
          case "createTime":
            value = dateFormat(v["createTime"])
            break
          default:
            value = v[j]
        }
        return value
      }))
    }
    excel_export_from_json(list, header, filter, formatJson, `${course.name}第${startNumber}条到第${endNumber}成绩列表_${dateFormat(new Date())}`)
  }
}

export {CourseScoreModel}
