import {CommonModel} from "@/model/CommonModel";
import {ConfigModel} from "@/model/ConfigModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  getCarouselList,
  getHotList,
  getTopList,
  getByCategory,
  searchNews,
  getRelatedNews,
  increaseViews,
  publishNews,
  unpublishNews,
  setHotNews,
  setTopNews,
  getStatistics
} from "@/api/BaseNewsApi";
import {msg_success, msg_err} from "@/utils/ele_component";

/**
 * 基地新闻业务模型
 */
class BaseNewsModel {

  // 获取一个基地新闻
  static async getOne(newsId) {
    let [res] = await getOne({
      newsId: newsId
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改基地新闻
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取基地新闻分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取基地新闻不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个基地新闻
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取新闻轮播列表
  static async getCarouselList() {
    let [res] = await getCarouselList()
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 获取热门新闻列表
  static async getHotList(limit = 10) {
    let [res] = await getHotList(limit)
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 获取置顶新闻列表
  static async getTopList() {
    let [res] = await getTopList()
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 根据分类获取新闻
  static async getByCategory(category, limit = 10) {
    let [res] = await getByCategory(category, limit)
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 搜索新闻
  static async searchNews(page, size, query) {
    let [data] = await searchNews(page, size, query);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取相关新闻
  static async getRelatedNews(newsId, category, limit = 5) {
    let [res] = await getRelatedNews(newsId, category, limit)
    if (res.code === "000000") {
      return res.data
    } else {
      return []
    }
  }

  // 增加浏览量
  static async increaseViews(newsId) {
    let [res] = await increaseViews(newsId)
    return res.code === "000000"
  }

  // 发布新闻
  static async publishNews(newsId) {
    let [res] = await publishNews(newsId)
    if (res.code === "000000") {
      msg_success("新闻发布成功")
      return true
    } else {
      msg_err(res.message || "新闻发布失败")
      return false
    }
  }

  // 撤回新闻
  static async unpublishNews(newsId) {
    let [res] = await unpublishNews(newsId)
    if (res.code === "000000") {
      msg_success("新闻撤回成功")
      return true
    } else {
      msg_err(res.message || "新闻撤回失败")
      return false
    }
  }

  // 设置热门新闻
  static async setHotNews(newsId, isHot) {
    let [res] = await setHotNews(newsId, isHot)
    if (res.code === "000000") {
      msg_success(isHot ? "设置热门成功" : "取消热门成功")
      return true
    } else {
      msg_err(res.message || "设置失败")
      return false
    }
  }

  // 设置置顶新闻
  static async setTopNews(newsId, isTop) {
    let [res] = await setTopNews(newsId, isTop)
    if (res.code === "000000") {
      msg_success(isTop ? "设置置顶成功" : "取消置顶成功")
      return true
    } else {
      msg_err(res.message || "设置失败")
      return false
    }
  }

  // 获取新闻统计信息
  static async getStatistics() {
    let [res] = await getStatistics()
    if (res.code === "000000") {
      return res.data
    } else {
      return {
        totalCount: 0,
        hotCount: 0,
        topCount: 0,
        categoryStats: {}
      }
    }
  }

  // 格式化新闻状态显示
  static formatStatus(status) {
    const statusMap = {
      'draft': '草稿',
      'published': '已发布',
      'archived': '已归档'
    }
    return statusMap[status] || status
  }

  // 格式化分类显示
  static formatCategory(category) {
    const categoryMap = {
      'base_news': '基地动态',
      'activity': '活动通知',
      'academic': '学术交流',
      'achievement': '成果展示'
    }
    return categoryMap[category] || category
  }

  // 格式化审核状态显示
  static formatAuditStatus(auditStatus) {
    const auditStatusMap = {
      'pending': '待审核',
      'approved': '已通过',
      'rejected': '已拒绝'
    }
    return auditStatusMap[auditStatus] || auditStatus
  }

  // 获取分类选项
  static async getCategoryOptions() {
    try {
      const config = JSON.parse(await ConfigModel.getConfig("newsConfig"))
      if (config && config.categories) {
        return JSON.parse(config.categories)
      } else {
        // 如果配置不存在，返回默认分类
        return [
          {label: '基地动态', value: 'base_news'},
          {label: '活动通知', value: 'activity'},
          {label: '学术交流', value: 'academic'},
          {label: '成果展示', value: 'achievement'}
        ]
      }
    } catch (error) {
      console.error('获取新闻分类配置失败:', error)
      // 错误情况下返回默认分类
      return [
        {label: '基地动态', value: 'base_news'},
        {label: '活动通知', value: 'activity'},
        {label: '学术交流', value: 'academic'},
        {label: '成果展示', value: 'achievement'}
      ]
    }
  }

  // 获取状态选项
  static getStatusOptions() {
    return [
      {label: '草稿', value: 'draft'},
      {label: '已发布', value: 'published'},
      {label: '已归档', value: 'archived'}
    ]
  }

  // 获取审核状态选项
  static getAuditStatusOptions() {
    return [
      {label: '待审核', value: 'pending'},
      {label: '已通过', value: 'approved'},
      {label: '已拒绝', value: 'rejected'}
    ]
  }
}

export {BaseNewsModel}
