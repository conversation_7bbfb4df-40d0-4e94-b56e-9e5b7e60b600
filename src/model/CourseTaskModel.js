import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne, updateTeachingClazzInfo} from "@/api/CourseTaskApi";
import {CourseRecordModel} from "@/model/CourseRecordModel";
import {excel_export_from_json} from "@/utils/excel";
import {UserModel} from "@/model/UserModel";
import {TeachingClazzModel} from "@/model/TeachingClazzModel";
import {CourseModel} from "@/model/CourseModel";
import {find_obj_from_arr_by_id} from "@/utils/common";
import {msg_err} from "@/utils/ele_component";

class CourseTaskModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      courseTaskId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 更新教学班信息
  static async updateTeachingClazzInfo(courseTaskId, info) {
    let [data] = await updateTeachingClazzInfo(courseTaskId, info);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 通用-导出成绩Excel
  static exportScoreList(lists, teacherObject, teachingClazzObject, excelName) {
    // 导出Excel 数据字段和表头
    const filter = ["teacherName", "teacherAccount", "teachingClazzName", "clazzName", "userAccount", "userName", "courseCompleted", "courseScore",
      "reportFilled", "reportCorrected", "reportScore", "totalScore"];
    const header = ['教师姓名', "教师工号", '教学班名称', "行政班名称", "学号", "姓名", "是否已完成课程", "课程分数", "是否已填写报告", "教师是否批改报告", "报告分数", "综合分数"];
    // map reduce生成arr
    let formatJson = function (filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let value = '';
        switch (j) {
          case "teacherName":
            value = teacherObject[v["teacherId"]]["name"]
            break
          case "teacherAccount":
            value = teacherObject[v["teacherId"]]["account"]
            break
          case "teachingClazzName":
            value = teachingClazzObject[v["teachingClazzId"]]
            break
          case "clazzName":
            value = v["gradeEntity"][0]["name"] + "-" + v["majorEntity"][0]["name"] + "-" + v["clazzEntity"][0]["name"]
            break
          case "userAccount":
            value = v["userEntity"][0]["account"]
            break
          case "userName":
            value = v["userEntity"][0]["name"]
            break
          case "courseCompleted":
            value = v[j] ? "已完成" : "未完成"
            break
          case "courseScore":
            value = v[j].toFixed(2)
            break
          case "reportFilled":
            value = v[j] ? "已填写" : "未填写"
            break
          case "reportCorrected":
            value = v[j] ? "已批改" : "未批改"
            break
          case "reportScore":
            value = v[j].toFixed(2)
            break
          case "totalScore":
            value = v[j].toFixed(2)
            break
          default:
            value = v[j]
        }
        return value
      }))
    }
    excel_export_from_json(lists, header, filter, formatJson, excelName)
  }

  // 导出某个教学班的成绩列表
  static async exportOneTeachingClazzScoreList(courseTaskId, teacherId, teachingClazzId) {
    let query = {
      courseTaskId: courseTaskId,
      teacherId: teacherId,
      teachingClazzId: teachingClazzId,
    };
    let [list, pages] = await CourseRecordModel.getTeachingClazzStudentList(-1, 0, "", query)

    // 获取课程任务信息
    let courseTask = await CourseTaskModel.getOne(courseTaskId)
    // 获取课程信息
    let course = await CourseModel.getOne(courseTask.courseId)

    // 获取教师信息
    let teacher = await UserModel.getOne(teacherId)
    let teacherObject = {}
    teacherObject[teacherId] = {
      name: teacher.name,
      account: teacher.account
    }
    // 获取教学班信息
    let teachingClazz = await TeachingClazzModel.getOne(teachingClazzId)
    let teachingClazzName = teachingClazz.name
    let teachingClazzObject = {}
    teachingClazzObject[teachingClazzId] = teachingClazzName

    // 执行导出
    this.exportScoreList(list, teacherObject, teachingClazzObject, `${teacher.name}-${courseTask.name}-${course.name}-${teachingClazzName}-成绩列表`)
  }

  // 导出某个教师的所有教学班成绩列表
  static async exportOneTeacherAllTeachingClazzScoreList(courseTaskId, teacherId) {
    // 获取课程任务信息
    let courseTask = await CourseTaskModel.getOne(courseTaskId)
    let teachingClazzInfo = courseTask.teachingClazzInfo
    let [, teacherFind] = find_obj_from_arr_by_id("teacherId", teacherId, teachingClazzInfo.teacherList)
    if (!teacherFind) {
      msg_err("未找到该老师的教学班记录！")
      return
    }
    // 获取课程信息
    let course = await CourseModel.getOne(courseTask.courseId)
    // 获取教师信息
    let teacher = await UserModel.getOne(teacherId)
    let teacherObject = {}
    teacherObject[teacherId] = {
      name: teacher.name,
      account: teacher.account
    }
    let teachingClazzIdList = teacherFind.teachingClazzIdList
    let lastList = []
    let teachingClazzObject = {}
    for (let i = 0; i < teachingClazzIdList.length; i++) {
      let teachingClazzId = teachingClazzIdList[i]
      let query = {
        courseTaskId: courseTaskId,
        teacherId: teacherId,
        teachingClazzId: teachingClazzId,
      };
      let [list, pages] = await CourseRecordModel.getTeachingClazzStudentList(-1, 0, "", query)
      // 获取教学班信息
      let teachingClazz = await TeachingClazzModel.getOne(teachingClazzId)
      teachingClazzObject[teachingClazzId] = teachingClazz.name
      // 合并列表
      lastList = lastList.concat(list)
    }

    // 执行导出
    this.exportScoreList(lastList, teacherObject, teachingClazzObject, `${teacher.name}-${courseTask.name}-${course.name}-成绩列表`)
  }

  // 导出所有教师的所有教学班成绩列表
  static async exportAllTeacherAllTeachingClazzScoreList(courseTaskId) {
    // 获取课程任务信息
    let courseTask = await CourseTaskModel.getOne(courseTaskId)
    let teachingClazzInfo = courseTask.teachingClazzInfo
    let teacherList = teachingClazzInfo.teacherList
    let lastList = []
    let teacherObject = {}
    let teachingClazzObject = {}
    // 获取课程信息
    let course = await CourseModel.getOne(courseTask.courseId)
    for (let j = 0; j < teacherList.length; j++) {
      let teacherFind = teacherList[j]
      let teacherId = teacherFind["teacherId"]
      // 获取教师信息
      let teacher = await UserModel.getOne(teacherId)
      teacherObject[teacherId] = {
        name: teacher.name,
        account: teacher.account
      }
      let teachingClazzIdList = teacherFind.teachingClazzIdList
      for (let i = 0; i < teachingClazzIdList.length; i++) {
        let teachingClazzId = teachingClazzIdList[i]
        let query = {
          courseTaskId: courseTaskId,
          teacherId: teacherId,
          teachingClazzId: teachingClazzId,
        };
        let [list, pages] = await CourseRecordModel.getTeachingClazzStudentList(-1, 0, "", query)
        // 获取教学班信息
        let teachingClazz = await TeachingClazzModel.getOne(teachingClazzId)
        teachingClazzObject[teachingClazzId] = teachingClazz.name
        // 合并列表
        lastList = lastList.concat(list)
      }
    }

    // 执行导出
    this.exportScoreList(lastList, teacherObject, teachingClazzObject, `${courseTask.name}-${course.name}-成绩列表`)
  }


}

export {CourseTaskModel}
