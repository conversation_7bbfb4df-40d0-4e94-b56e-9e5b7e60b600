import {CommonModel} from "@/model/CommonModel";
import {getPageList, getList, backupNow} from "@/api/DatabaseApi";

class DatabaseModel {
  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 请求立即备份
  static async backupNow() {
    let [data] = await backupNow({})
    return data.code === "000000";
  }
}

export {DatabaseModel}
