import {
  getNewsPageList,
  getNewsDetail,
  getRelatedNews,
  increaseNewsViews,
  getNewsCarouselList,
  getHotNewsList,
  getTopNewsList,
  getNewsByCategory,
  searchNews,
  getNoticePageList,
  getNoticeDetail,
  getRelatedNotice,
  increaseNoticeViews,
  getNoticeCarouselList,
  getTopNoticeList,
  getNoticeByType,
  searchNotice,
  getActiveNotices
} from "@/api/news";
import {date_format} from '@/utils/common';

class NewsModel {
  // ======================= 新闻相关方法 =======================
  
  // 获取新闻分页列表
  static async getNewsPageList(page, size, params = {}) {
    try {
      // 调用真实API
      const [data] = await getNewsPageList(page, size, params);
      if (data && data.code === '000000') {
        return this.formatPageListData(data.data);
      }
      // 失败时返回模拟数据
      return this.getMockNewsPageList(page, size, params);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNewsPageList(page, size, params);
    }
  }

  // 获取新闻详情
  static async getNewsDetail(id) {
    try {
      // 调用真实API
      const [data] = await getNewsDetail(id);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockNewsDetail(id);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNewsDetail(id);
    }
  }

  // 获取相关新闻
  static async getRelatedNews(id, category) {
    try {
      // 调用真实API
      const [data] = await getRelatedNews(id, category);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockRelatedNews(id, category);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockRelatedNews(id, category);
    }
  }

  // 增加新闻浏览量
  static async increaseNewsViews(id) {
    try {
      // 调用真实API
      const [data] = await increaseNewsViews(id);
      return data && data.code === '000000';
    } catch (error) {
      console.warn('增加浏览量失败:', error);
      return false;
    }
  }

  // 获取新闻轮播数据
  static async getNewsCarouselList() {
    try {
      // 调用真实API
      const [data] = await getNewsCarouselList();
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockNewsCarouselList();
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNewsCarouselList();
    }
  }

  // 获取热门新闻
  static async getHotNewsList(limit = 10) {
    try {
      // 调用真实API
      const [data] = await getHotNewsList(limit);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据（从轮播数据中取）
      return this.getMockNewsCarouselList().slice(0, limit);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNewsCarouselList().slice(0, limit);
    }
  }

  // 根据分类获取新闻
  static async getNewsByCategory(category, limit = 10) {
    try {
      // 调用真实API
      const [data] = await getNewsByCategory(category, limit);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockNewsPageList(1, limit, {category}).list;
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNewsPageList(1, limit, {category}).list;
    }
  }

  // 搜索新闻
  static async searchNews(page, size, keyword) {
    try {
      // 调用真实API
      const [data] = await searchNews(page, size, {keyword});
      if (data && data.code === '000000') {
        return this.formatPageListData(data.data);
      }
      // 失败时返回模拟数据
      return this.getMockNewsPageList(page, size, {keyword});
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNewsPageList(page, size, {keyword});
    }
  }

  // ======================= 通知公告相关方法 =======================

  // 获取通知公告分页列表
  static async getNoticePageList(page, size, params = {}) {
    try {
      // 调用真实API
      const [data] = await getNoticePageList(page, size, params);
      if (data && data.code === '000000') {
        return this.formatPageListData(data.data);
      }
      // 失败时返回模拟数据
      return this.getMockNoticePageList(page, size, params);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNoticePageList(page, size, params);
    }
  }

  // 获取通知公告详情
  static async getNoticeDetail(id) {
    try {
      // 调用真实API
      const [data] = await getNoticeDetail(id);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockNoticeDetail(id);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNoticeDetail(id);
    }
  }

  // 获取相关通知公告
  static async getRelatedNotice(id, type) {
    try {
      // 调用真实API
      const [data] = await getRelatedNotice(id, type);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockRelatedNotice(id, type);
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockRelatedNotice(id, type);
    }
  }

  // 增加通知公告浏览量
  static async increaseNoticeViews(id) {
    try {
      // 调用真实API
      const [data] = await increaseNoticeViews(id);
      return data && data.code === '000000';
    } catch (error) {
      console.warn('增加浏览量失败:', error);
      return false;
    }
  }

  // 获取通知公告轮播数据
  static async getNoticeCarouselList() {
    try {
      // 调用真实API
      const [data] = await getNoticeCarouselList();
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockNoticeCarouselList();
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNoticeCarouselList();
    }
  }

  // 根据类型获取通知
  static async getNoticeByType(type, limit = 10) {
    try {
      // 调用真实API
      const [data] = await getNoticeByType(type, limit);
      if (data && data.code === '000000') {
        return data.data;
      }
      // 失败时返回模拟数据
      return this.getMockNoticePageList(1, limit, {type}).list;
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNoticePageList(1, limit, {type}).list;
    }
  }

  // 搜索通知
  static async searchNotice(page, size, keyword) {
    try {
      // 调用真实API
      const [data] = await searchNotice(page, size, {keyword});
      if (data && data.code === '000000') {
        return this.formatPageListData(data.data);
      }
      // 失败时返回模拟数据
      return this.getMockNoticePageList(page, size, {keyword});
    } catch (error) {
      console.warn('调用真实API失败，使用模拟数据:', error);
      return this.getMockNoticePageList(page, size, {keyword});
    }
  }

  // ======================= 工具方法 =======================
  
  // 格式化分页数据
  static formatPageListData(data) {
    if (!data) return {list: [], total: 0, page: 1, size: 10};
    
    return {
      list: data.content || [],
      total: data.totalElements || 0,
      page: (data.number || 0) + 1, // 后端从0开始，前端从1开始
      size: data.size || 10
    };
  }

  // =================模拟数据方法=================
  // 模拟新闻轮播数据
  static getMockNewsCarouselList() {
    return [
      {
        id: 1,
        title: '基地成功举办2024年虚拟仿真实验教学研讨会',
        summary: '来自全国各地的专家学者齐聚一堂，共同探讨虚拟仿真技术在教学中的应用与发展，形成重要共识。',
        coverImage: 'https://picsum.photos/1200/400?random=101',
        categoryName: '基地动态',
        publishTime: '2024-01-15 10:00:00',
        views: 256,
        isHot: true
      },
      {
        id: 2,
        title: '基地新增5个虚拟仿真实验项目通过教育部认定',
        summary: '涵盖机械工程、电子信息、化学工程等多个学科领域，标志着基地在虚拟仿真教学领域取得重要突破。',
        coverImage: 'https://picsum.photos/1200/400?random=102',
        categoryName: '成果展示',
        publishTime: '2024-01-12 14:30:00',
        views: 189,
        isHot: true
      },
      {
        id: 3,
        title: '基地与多家知名企业签署产学研合作协议',
        summary: '与华为、腾讯、阿里巴巴等知名企业深化合作，共同推进虚拟仿真技术在工业界的创新应用。',
        coverImage: 'https://picsum.photos/1200/400?random=103',
        categoryName: '合作交流',
        publishTime: '2024-01-10 09:15:00',
        views: 342,
        isHot: true
      },
      {
        id: 4,
        title: '基地虚拟仿真平台获国家级教学成果奖',
        summary: '自主开发的虚拟仿真实验教学平台获得国家级教学成果二等奖，充分体现创新成果和示范作用。',
        coverImage: 'https://picsum.photos/1200/400?random=104',
        categoryName: '成果展示',
        publishTime: '2024-01-03 13:45:00',
        views: 521,
        isHot: true
      }
    ];
  }

  // 模拟通知公告轮播数据
  static getMockNoticeCarouselList() {
    return [
      {
        id: 1,
        title: '【重要】关于2024年春季学期实验教学安排的通知',
        summary: '新学期实验教学即将开始，请各位师生及时关注相关安排，确保教学工作顺利进行。',
        coverImage: 'https://picsum.photos/1200/400?random=201',
        type: 'important',
        typeName: '重要通知',
        publishTime: '2024-01-20 10:00:00',
        expireTime: '2024-03-01 23:59:59',
        views: 458,
        isTop: true,
        urgency: 'high'
      },
      {
        id: 2,
        title: '2024年第一期虚拟仿真实验教学培训开始报名',
        summary: '为提升教师教学能力，基地将举办专业培训，现开始接受报名，名额有限，先到先得。',
        coverImage: 'https://picsum.photos/1200/400?random=202',
        type: 'teaching',
        typeName: '教学通知',
        publishTime: '2024-01-16 09:00:00',
        expireTime: '2024-02-15 23:59:59',
        views: 234,
        isTop: false,
        urgency: 'medium'
      },
      {
        id: 3,
        title: '基地实验室升级改造完成，全面恢复使用',
        summary: '经过为期一周的升级改造，基地所有实验室设备性能大幅提升，现已全面恢复正常使用。',
        coverImage: 'https://picsum.photos/1200/400?random=203',
        type: 'system',
        typeName: '系统通知',
        publishTime: '2024-01-18 15:30:00',
        expireTime: '2024-01-28 23:59:59',
        views: 123,
        isTop: false,
        urgency: 'low'
      },
      {
        id: 4,
        title: '基地开放日活动邀请函',
        summary: '诚挚邀请各界人士参观基地，了解虚拟仿真技术发展成果，共同探讨未来发展方向。',
        coverImage: 'https://picsum.photos/1200/400?random=204',
        type: 'activity',
        typeName: '活动通知',
        publishTime: '2024-01-14 11:00:00',
        expireTime: '2024-02-28 23:59:59',
        views: 167,
        isTop: false,
        urgency: 'medium'
      }
    ];
  }

  // 模拟新闻列表数据
  static getMockNewsPageList(page = 1, size = 12, params = {}) {
    const mockNewsList = [
      {
        id: 1,
        title: '基地成功举办2024年虚拟仿真实验教学研讨会',
        summary: '为进一步推进虚拟仿真实验教学改革，提升实验教学质量，我基地于近日成功举办了2024年虚拟仿真实验教学研讨会。来自全国各地的专家学者齐聚一堂，共同探讨虚拟仿真技术在教学中的应用与发展。',
        coverImage: 'https://picsum.photos/400/200?random=1',
        categoryName: '基地动态',
        category: 'base_news',
        publishTime: '2024-01-15 10:00:00',
        views: 256,
        author: '基地办公室'
      },
      {
        id: 2,
        title: '基地新增5个虚拟仿真实验项目通过教育部认定',
        summary: '近日，教育部发布了2024年度虚拟仿真实验教学项目认定结果，我基地申报的5个项目全部通过认定，涵盖了机械工程、电子信息、化学工程等多个学科领域。',
        coverImage: 'https://picsum.photos/400/200?random=2',
        categoryName: '成果展示',
        category: 'achievement',
        publishTime: '2024-01-12 14:30:00',
        views: 189,
        author: '项目管理部'
      },
      {
        id: 3,
        title: '基地与多家企业签署产学研合作协议',
        summary: '为深化产教融合，推进校企合作，我基地与华为、腾讯、阿里巴巴等知名企业签署了产学研合作协议，共同推进虚拟仿真技术在工业界的应用。',
        coverImage: 'https://picsum.photos/400/200?random=3',
        categoryName: '基地动态',
        category: 'base_news',
        publishTime: '2024-01-10 09:15:00',
        views: 342,
        author: '合作发展部'
      },
      {
        id: 4,
        title: '基地开展虚拟仿真实验教学师资培训',
        summary: '为提高教师虚拟仿真实验教学能力，我基地组织开展了为期三天的师资培训活动。培训内容包括虚拟仿真技术基础、实验设计方法、教学案例分析等。',
        coverImage: 'https://picsum.photos/400/200?random=4',
        categoryName: '活动通知',
        category: 'activity',
        publishTime: '2024-01-08 16:00:00',
        views: 128,
        author: '培训中心'
      },
      {
        id: 5,
        title: '基地举办首届虚拟仿真创新设计大赛',
        summary: '为激发学生创新思维，提升实践能力，我基地成功举办了首届虚拟仿真创新设计大赛。来自全校各专业的200余名学生参与了比赛，共收到创新作品80余件。',
        coverImage: 'https://picsum.photos/400/200?random=5',
        categoryName: '活动通知',
        category: 'activity',
        publishTime: '2024-01-05 11:20:00',
        views: 398,
        author: '学生工作部'
      },
      {
        id: 6,
        title: '基地虚拟仿真平台获国家级教学成果奖',
        summary: '我基地自主开发的虚拟仿真实验教学平台在国家级教学成果奖评选中获得二等奖，充分体现了基地在虚拟仿真教学领域的创新成果和示范作用。',
        coverImage: 'https://picsum.photos/400/200?random=6',
        categoryName: '成果展示',
        category: 'achievement',
        publishTime: '2024-01-03 13:45:00',
        views: 521,
        author: '教学发展中心'
      }
    ];

    // 应用筛选条件
    let filteredList = mockNewsList;
    
    if (params.keyword) {
      filteredList = filteredList.filter(item =>
        item.title.includes(params.keyword) || item.summary.includes(params.keyword)
      );
    }

    if (params.category) {
      filteredList = filteredList.filter(item => item.category === params.category);
    }

    const total = filteredList.length;
    const start = (page - 1) * size;
    const list = filteredList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }

  // 模拟新闻详情数据
  static getMockNewsDetail(id) {
    const mockNewsDetails = {
      1: {
        id: 1,
        title: '基地成功举办2024年虚拟仿真实验教学研讨会',
        summary: '为进一步推进虚拟仿真实验教学改革，提升实验教学质量，我基地于近日成功举办了2024年虚拟仿真实验教学研讨会。',
        content: `
          <p>为进一步推进虚拟仿真实验教学改革，提升实验教学质量，我基地于2024年1月15日成功举办了2024年虚拟仿真实验教学研讨会。来自全国各地的专家学者齐聚一堂，共同探讨虚拟仿真技术在教学中的应用与发展。</p>
          
          <h3>会议亮点</h3>
          <p>本次研讨会邀请了教育部高等教育司相关负责人、知名高校虚拟仿真教学专家、企业技术负责人等50余位嘉宾参会。</p>
          
          <img src="https://picsum.photos/800/400?random=10" alt="研讨会现场" />
          
          <h3>主要议题</h3>
          <ul>
            <li>虚拟仿真实验教学项目建设与应用</li>
            <li>虚拟仿真技术发展趋势与教学创新</li>
            <li>产教融合背景下的虚拟仿真人才培养</li>
            <li>虚拟仿真实验教学质量评价体系</li>
          </ul>
          
          <h3>会议成果</h3>
          <p>会议期间，与会专家就虚拟仿真实验教学的发展方向、技术标准、质量保障等关键问题进行了深入交流，形成了《虚拟仿真实验教学发展共识》，为推动虚拟仿真教育高质量发展提供了重要指导。</p>
          
          <blockquote>
            "虚拟仿真技术为实验教学带来了革命性变化，我们要充分发挥其优势，为学生提供更加丰富、安全、高效的实验学习体验。" —— 教育部专家组组长
          </blockquote>
        `,
        coverImage: 'https://picsum.photos/800/400?random=1',
        categoryName: '基地动态',
        category: 'base_news',
        publishTime: '2024-01-15 10:00:00',
        views: 256,
        author: '基地办公室',
        tags: ['研讨会', '教学改革', '虚拟仿真']
      },
      2: {
        id: 2,
        title: '基地新增5个虚拟仿真实验项目通过教育部认定',
        summary: '近日，教育部发布了2024年度虚拟仿真实验教学项目认定结果，我基地申报的5个项目全部通过认定。',
        content: `
          <p>近日，教育部发布了2024年度虚拟仿真实验教学项目认定结果，我基地申报的5个项目全部通过认定，涵盖了机械工程、电子信息、化学工程等多个学科领域。</p>
          
          <h3>通过认定的项目</h3>
          <ol>
            <li>机械设计制造及其自动化专业虚拟仿真实验</li>
            <li>电子信息工程专业虚拟仿真实验</li>
            <li>化学工程与工艺专业虚拟仿真实验</li>
            <li>土木工程专业虚拟仿真实验</li>
            <li>计算机科学与技术专业虚拟仿真实验</li>
          </ol>
          
          <img src="https://picsum.photos/800/400?random=11" alt="项目展示" />
          
          <p>这些项目的成功认定，标志着我基地在虚拟仿真实验教学领域取得了重要突破，为培养高素质应用型人才提供了有力支撑。</p>
        `,
        coverImage: 'https://picsum.photos/800/400?random=2',
        categoryName: '成果展示',
        category: 'achievement',
        publishTime: '2024-01-12 14:30:00',
        views: 189,
        author: '项目管理部',
        tags: ['教育部认定', '项目建设', '成果展示']
      }
    };

    return mockNewsDetails[id] || null;
  }

  // 模拟相关新闻数据
  static getMockRelatedNews(id, categoryId) {
    return [
      {
        id: 2,
        title: '基地新增5个虚拟仿真实验项目通过教育部认定',
        summary: '近日，教育部发布了2024年度虚拟仿真实验教学项目认定结果...',
        coverImage: 'https://picsum.photos/100/80?random=2',
        publishTime: '2024-01-12 14:30:00'
      },
      {
        id: 3,
        title: '基地与多家企业签署产学研合作协议',
        summary: '为深化产教融合，推进校企合作，我基地与华为、腾讯、阿里巴巴等知名企业...',
        coverImage: 'https://picsum.photos/100/80?random=3',
        publishTime: '2024-01-10 09:15:00'
      },
      {
        id: 4,
        title: '基地开展虚拟仿真实验教学师资培训',
        summary: '为提高教师虚拟仿真实验教学能力，我基地组织开展了为期三天的师资培训活动...',
        coverImage: 'https://picsum.photos/100/80?random=4',
        publishTime: '2024-01-08 16:00:00'
      }
    ];
  }

  // 模拟通知公告列表数据
  static getMockNoticePageList(page = 1, size = 12, params = {}) {
    const mockNoticeList = [
      {
        id: 1,
        title: '【重要】关于2024年春季学期实验教学安排的通知',
        summary: '为保障新学期实验教学工作顺利进行，现就相关事宜通知如下：1. 实验课程开课时间为2024年3月1日；2. 所有实验课程需提前预约...',
        type: 'important',
        typeName: '重要通知',
        status: 'active',
        publishTime: '2024-01-20 10:00:00',
        expireTime: '2024-03-01 23:59:59',
        views: 458,
        isTop: true,
        author: '教务处',
        urgency: 'high'
      },
      {
        id: 2,
        title: '基地实验室维护通知',
        summary: '定期维护设备，确保实验教学质量。维护时间：2024年1月25日-27日，期间相关实验室暂停使用。',
        type: 'system',
        typeName: '系统通知',
        status: 'active',
        publishTime: '2024-01-18 15:30:00',
        expireTime: '2024-01-28 23:59:59',
        views: 123,
        isTop: false,
        author: '设备管理部',
        urgency: 'medium'
      },
      {
        id: 3,
        title: '2024年第一期虚拟仿真实验教学培训报名通知',
        summary: '为提升教师虚拟仿真实验教学能力，基地将于2024年2月举办第一期培训班，现开始接受报名。',
        type: 'teaching',
        typeName: '教学通知',
        status: 'active',
        publishTime: '2024-01-16 09:00:00',
        expireTime: '2024-02-15 23:59:59',
        views: 234,
        isTop: false,
        author: '培训中心',
        urgency: 'medium'
      }
    ];

    // 应用筛选条件
    let filteredList = mockNoticeList;
    
    if (params.keyword) {
      filteredList = filteredList.filter(item =>
        item.title.includes(params.keyword) || item.summary.includes(params.keyword)
      );
    }

    if (params.type) {
      filteredList = filteredList.filter(item => item.type === params.type);
    }

    const total = filteredList.length;
    const start = (page - 1) * size;
    const list = filteredList.slice(start, start + size);

    return {
      list,
      total,
      page,
      size
    };
  }

  // 模拟通知公告详情数据
  static getMockNoticeDetail(id) {
    const mockNoticeDetails = {
      1: {
        id: 1,
        title: '【重要】关于2024年春季学期实验教学安排的通知',
        summary: '为保障新学期实验教学工作顺利进行，现就相关事宜通知如下。',
        content: `
          <p>为保障2024年春季学期实验教学工作顺利进行，现就相关事宜通知如下：</p>
          
          <h3>一、开课时间安排</h3>
          <p>1. 实验课程开课时间为2024年3月1日（周五）</p>
          <p>2. 所有实验课程需提前一周进行预约</p>
          <p>3. 预约系统开放时间：2024年2月20日上午9:00</p>
          
          <h3>二、实验室使用规定</h3>
          <ul>
            <li>学生需提前10分钟到达实验室</li>
            <li>严格按照实验操作规程进行操作</li>
            <li>实验结束后须完成实验报告提交</li>
            <li>爱护实验设备，如有损坏需照价赔偿</li>
          </ul>
          
          <h3>三、注意事项</h3>
          <p><strong>重要提醒：</strong>请各位同学务必按时参加实验课程，无故缺席将影响课程成绩。如有特殊情况无法参加，需提前向任课教师请假。</p>
          
          <h3>四、联系方式</h3>
          <p>如有疑问，请联系教务处：</p>
          <p>电话：0851-12345678</p>
          <p>邮箱：<EMAIL></p>
          
          <p style="text-align: right; margin-top: 30px;">教务处<br/>2024年1月20日</p>
        `,
        type: 'important',
        typeName: '重要通知',
        status: 'active',
        publishTime: '2024-01-20 10:00:00',
        expireTime: '2024-03-01 23:59:59',
        views: 458,
        isTop: true,
        author: '教务处',
        urgency: 'high'
      }
    };

    return mockNoticeDetails[id] || null;
  }

  // 模拟相关通知公告数据
  static getMockRelatedNotice(id, type) {
    return [
      {
        id: 2,
        title: '基地实验室维护通知',
        summary: '定期维护设备，确保实验教学质量...',
        coverImage: 'https://picsum.photos/100/80?random=12',
        publishTime: '2024-01-18 15:30:00'
      },
      {
        id: 3,
        title: '2024年第一期虚拟仿真实验教学培训报名通知',
        summary: '为提升教师虚拟仿真实验教学能力...',
        coverImage: 'https://picsum.photos/100/80?random=13',
        publishTime: '2024-01-16 09:00:00'
      }
    ];
  }

  // 格式化日期
  static formatDate(dateStr, format = 'YYYY-MM-DD') {
    if (!dateStr) return '';
    
    // 处理不同的格式需求
    let formatTemplate = format;
    if (format === 'YYYY-MM-DD HH:mm') {
      formatTemplate = 'yyyy-MM-dd HH:mm';
    } else if (format === 'YYYY-MM-DD') {
      formatTemplate = 'yyyy-MM-dd';
    } else if (format === 'MM-DD') {
      formatTemplate = 'MM-dd';
    } else if (format === 'YYYY年MM月DD日') {
      formatTemplate = 'yyyy年MM月dd日';
    }
    
    // 使用现有的date_format函数
    return date_format(dateStr, formatTemplate);
  }
}

export {NewsModel}