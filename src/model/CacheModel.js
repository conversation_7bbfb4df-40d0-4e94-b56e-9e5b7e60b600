import {getOneByName, getOne, getPageList} from "@/api/CacheApi";
import {CommonModel} from "./CommonModel";

class CacheModel {
    // 获取一个
    static async getOne(id) {
        let [res] = await getOne({
            gradeId: id
        })
        if (res.code === "000000") {
            return res.data
        } else {
            return false
        }
    }

    // 获取分页列表
    static async getPageList(page, size, sort, document) {
        let [data] = await getPageList(page, size, sort, document);
        return CommonModel.generateListMongo(data.data);
    }

    // 获取一个-通过名称
    static async getOneByName(name) {
        let [res] = await getOneByName({
            name
        })
        if (res.code === "000000") {
            return res.data
        } else {
            return false
        }
    }

    // 获取某个课程最近的统计
    static async getCourseLastStatistic(courseId) {
        let query = {
            type: "courseStatistic",
            "info.courseId": courseId
        }
        let [list] = await this.getPageList(0, 1, "", query)
        if (list.length === 1) {
            return list[0]
        } else {
            return {}
        }
    }

}

export {CacheModel}
