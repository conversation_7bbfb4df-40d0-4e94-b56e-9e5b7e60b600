import {CommonModel} from "@/model/CommonModel";
import {getPageList, addOrEdit, getList, getOne, deleteOne,getOneByName} from "@/api/CacheApi";

class CacheModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      gradeId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取一个-通过名称
  static async getOneByName(name) {
    let [res] = await getOneByName({
      name
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 获取某个课程最近的统计
  static async getCourseLastStatistic(courseId) {
    let query = {
      type: "courseStatistic",
      "info.courseId": courseId
    }
    let [list] = await this.getPageList(0, 1, "", query)
    if (list.length === 1) {
      return list[0]
    } else {
      return {}
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }
}

export {CacheModel}
