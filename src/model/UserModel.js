import {CommonModel} from "@/model/CommonModel";
import {
  getPageList,
  addOrEdit,
  getList,
  getOne,
  deleteOne,
  transferStudentToNewClazz,
  transferTeacherToNewCollege,
  getSearchTeacherList, getSearchStudentList, sendAccountEnableEmail
} from "@/api/UserApi";
import {msg_success} from "@/utils/ele_component";

class UserModel {
  // 获取一个
  static async getOne(id) {
    let [res] = await getOne({
      userId: id
    })
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 新增或修改
  static async addOrEdit(entity) {
    let [data] = await addOrEdit(entity);
    return data;
  }

  // 获取分页列表
  static async getPageList(page, size, sort, document) {
    let [data] = await getPageList(page, size, sort, document);
    return CommonModel.generateListMongo(data.data);
  }

  // 获取不分页列表
  static async getList(document) {
    let [data] = await getList(document);
    return data.data;
  }

  // 删除一个
  static async deleteOne(data) {
    let [res] = await deleteOne(data)
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 转移学生到新班级
  static async transferStudentToNewClazz(userId, clazzId) {
    let [res] = await transferStudentToNewClazz({
      userId,
      clazzId
    });
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 转移教师到新学院
  static async transferTeacherToNewCollege(userId, collegeId) {
    let [res] = await transferTeacherToNewCollege({
      userId,
      collegeId
    });
    if (res.code === "000000") {
      return res.data
    } else {
      return false
    }
  }

  // 搜索教师列表
  static async getSearchTeacherList(document) {
    let [data] = await getSearchTeacherList(document);
    return data.data;
  }

  // 搜索学生列表
  static async getSearchStudentList(document) {
    let [data] = await getSearchStudentList(document);
    return data.data;
  }

  // 发送账号启用邮件
  static async sendAccountEnableEmail(uid) {
    let [res] = await sendAccountEnableEmail(uid)
    if (res.code === "000000") {
      let result = res.data
      if (result) {
        msg_success(`账号启用通知邮件已发送！`)
        return true
      } else {
        msg_success("邮件发送失败！")
        return false
      }
    }
  }
}

export {UserModel}
