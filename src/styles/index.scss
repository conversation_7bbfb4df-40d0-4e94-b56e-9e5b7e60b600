@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 30px;
  text-align: right;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}


/** 自建 2020-11-13
 */

// 覆盖element ui dialog
.el-dialog__body {
  padding: 10px 15px 20px 15px;
}

// 解决element table表格对不齐问题
body .el-table th.gutter {
  display: table-cell !important;
}

body .el-table colgroup.gutter {
  display: table-cell !important;
}

// flex 布局
.flex {
  display: flex;
}

.flex-dr {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-start {
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  justify-content: flex-start;
  align-items: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

// 页面底部悬浮
.page-bottom-container {
  position: fixed;
  margin: auto;
  left: 0;
  right: 0;
  bottom: 40px;
  width: 200px;
  z-index: 9;
  text-align: center;
}

// 单个输入框的模糊搜索
.filter-container-ori {
  margin-bottom: 20px;

  .el-input {
    width: 250px;

    .el-input-group__append {
      background-color: #fff;
      border-left: none;
    }

    .el-input__inner {
      border-right: none;
      outline: none;
      border-color: #DCDFE6;
    }
  }
}

// 订单搜索和筛选
.header-container {
  .right {
    margin-bottom: 10px;
  }
}


// erp-上传-单文件类型
.erp-uploader-one .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  width: 200px;
  height: 100px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.erp-uploader-one .el-upload:hover {
  border-color: #409EFF;
}

.erp-uploader-one .uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.erp-uploader-one .img-show {
  width: 200px;
  height: 100px;
  display: block;
}
.erp-uploader-one .video-show {
  width: 200px;
  height: 100px;
  display: block;
}
.erp-uploader-one .buttons{
  text-align: left!important;
}
.erp-uploader-one .des{
 font-size: 13px;
  color:#666;
  div{
    line-height: 16px;
  }
}


// el-input 单独的错误提示
.el-input-error{
  color: #ff4949;
  font-size: 12px;
  line-height: 1;
  margin-top: 4px;
}

// 通用底部悬浮居中按钮组
.bottom-button-container {
  position: fixed;
  background-color: #fff;
  padding:10px 0px;
  border:1px solid #f2f2f2;
  bottom: 0;
  right: 0;
  width: 100%;
  margin-bottom: 0px;
  text-align: center;
}
/*省略号  */
.ellipsis {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/*英文数字等自动化换行 还需要设置width*/
.word-hr {
  word-wrap: break-word;
  white-space: pre-wrap;
  word-break: normal;
  text-align: center;
}
// 仿el-input
.fake-el-input{
  font-size: 14px;
  height: 36px;
  line-height: 36px;
  padding:5px 10px;
  outline: none;
  -webkit-appearance: none;
  background-color: #FFFFFF;
  background-image: none;
  border-radius: 4px;
  border: 1px solid #DCDFE6;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  color: #606266;
  display: inline-block;
  -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 100%;
}
