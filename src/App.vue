<template>
  <div id="app">
    <!--页首-->
    <page-header v-if="!isLoginPage"></page-header>
    <!--页中-->
    <div class="router-view-container">
      <router-view/>
    </div>
    <!--页尾-->
    <page-footer v-if="!isLoginPage"></page-footer>
  </div>
</template>
<script>
import pageHeader from "./views/components/pageHeader";
import pageFooter from "./views/components/pageFooter";
import {mapState} from "vuex";

export default {
  components: {pageHeader, pageFooter},
  computed: {
    ...mapState({
      webConfig: state => state.webConfig,
      isLoginPage: state => state.isLoginPage
    })
  },
}
</script>

<style lang="less">
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

// 为主内容区域添加底部padding，避免被固定页尾遮挡
// 添加顶部padding，避免被固定页眉遮挡
.router-view-container {
  flex: 1;
  padding-top: 72px; // 根据页眉高度调整
  padding-bottom: 80px; // 根据页尾高度调整
}
</style>
