server:
  port: 9999

# redis相关
redis:
  host: **************
  port: 63636
  db: 10
  password: '!ZyhdRedis'

spring:
  application:
    name: system-big_platform
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  # mongodb 相关
  data:
    mongodb:
      host: **************
      database: service_bigPlatform
      port: 27777
      username: service_bigPlatform
      password: service_bigPlatform_Mongo
      authentication-database: service_bigPlatform
      transactionEnabled: true

bigPlatform:
  #  文件存储文件夹路径
  storageFolder: "/Users/<USER>/Downloads/libAdmin/"
#  storageFolder: "/disk2/bigPlatform/"
  # api地址
#  apiUrl: "http://*************:9999/"
  apiUrl: "http://big.xhyjbj.com/api/"
  # 数据库备份文件夹
#  backupFolder: "/Users/<USER>/Downloads/0811/"
#  backupSh: "/Users/<USER>/Downloads/0811/1.sh"
  backupFolder: "/disk2/bigPlatform/backup/"
  backupSh: "/disk2/bigPlatform/sh/backup.sh"