package com.cdzyhd.big_platform.config;

import com.cdzyhd.base.common.config.RedisConfigBase;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedisConfig extends RedisConfigBase {
    public RedisConfig(@Value("${redis.host}") String host, @Value("${redis.port}") int port, @Value("${redis.db}") int db, @Value("${redis.password}") String password) {
        super.host = host;
        super.port = port;
        super.db = db;
        super.password = password;
    }
}