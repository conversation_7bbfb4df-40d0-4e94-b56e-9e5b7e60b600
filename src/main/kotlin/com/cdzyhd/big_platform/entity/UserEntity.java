package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.cdzyhd.base.common.util.PasswordUtil;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用户实体-学生和教师
 *
 * <AUTHOR>
 * @date 2022.7.8
 */
@Document(collection = "user")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UserEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String userId = new SnowflakeIdWorker(1, 0).nextId();

    // 角色 student或teacher 社会人士也是student
    public String role;

    // 姓名
    public String name;

    // 学号或工号或账号
    public String account;

    // 性别
    public String sex;

    // 绑定的邮箱
    public String email;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 学生-所属的行政班级id
    public String clazzId;

    // 学生-所属的教学班级数组
    public JSONArray teachingClazzIds = new JSONArray();

    // 教师-所属的学院
    public String collegeId;

    // 教师-是否拥有教秘权限
    public Boolean asSecretary;

    // 逻辑删除
    public Integer deleted = 0;

    // 密码 采用md5+salt验证
    @JsonIgnore
    public String password = PasswordUtil.generate("123456");

    // 是否已经登录过，修改密码，绑定邮箱过
    public Boolean hasLogin = false;

    // 评价过的课程id
    public JSONArray commentCourseId = new JSONArray();

    // 社会-是否是社会注册
    public Boolean asSocial = false;

    // 社会-身份
    public String identity;

    // 手机号
    public String phone;

    // 备注
    public String remark;

    // pojo 教师、学生-学院信息
    public JSONArray collegeEntity;

    // pojo 学生-行政班信息
    public JSONArray clazzEntity;

    // pojo 学生-专业信息
    public JSONArray majorEntity;

    // pojo 学生-年级信息
    public JSONArray gradeEntity;
}