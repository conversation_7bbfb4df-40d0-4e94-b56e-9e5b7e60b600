package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 教学班集合
 *
 * <AUTHOR>
 * @date 2022.7.8
 */
@Document(collection = "teachingClazz")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class TeachingClazzEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String teachingClazzId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 名称
    public String name;

    // 所属教师id
    public String teacherId;

    // 关联的行政班id
    public JSONArray clazzIds = new JSONArray();

    // pojo 行政班名称列表
    public JSONArray clazzNames = new JSONArray();

    // pojo 该教学班的学生人数
    public Integer studentNumber;

    // pojo 该教学班总课程完成人数
    public Integer courseCompletedNumber;

    // pojo 该教学班总报告填写人数
    public Integer reportCompletedNumber;
}