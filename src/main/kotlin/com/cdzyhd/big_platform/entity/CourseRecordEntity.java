package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程和任务记录
 *
 * <AUTHOR>
 * @date 2022.7.8
 */
@Document(collection = "course_record")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseRecordEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseRecordId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 更新时间
    public Long editTime = System.currentTimeMillis();

    // 关联的学生或教师用户id
    public String userId;

    // 关联的课程id
    public String courseId;

    // 关联的课程任务id id为open表示开放课程记录 一个用户只有一个课程记录 但是有多个任务记录
    public String courseTaskId = "open";

    // 关联的任务教师id
    public String teacherId;

    // 关联的任务教学班id
    public String teachingClazzId;

    // 是否逻辑删除
    public Integer deleted = 0;

    // 课程限制登录次数 todo 是否需要

    // 是否已成功完成课程实验
    public Boolean courseCompleted = false;

    // 是否已做过该实验课程
    public Boolean courseDone = false;

    // 课程实验分数
    public Double courseScore = 0.0;

    // 是否已填写报告
    public Boolean reportFilled = false;

    // 是否已批改报告
    public Boolean reportCorrected = false;

    // 报告分数
    public Double reportScore = 0.0;

    // 综合分数 总分
    public Double totalScore = 0.0;

    // 是否限制登录次数
    public Boolean limitLogin = false;

    // 限制登录次数
    public Integer limitNumber = 0;

    // 剩余登录次数
    public Integer leftLimitNumber = 0;

    // pojo userEntity
    public JSONArray userEntity;

    // pojo clazzEntity
    public JSONArray clazzEntity;

    // pojo majorEntity
    public JSONArray majorEntity;

    // pojo gradeEntity
    public JSONArray gradeEntity;

    // pojo courseEntity
    public JSONArray courseEntity;

    // pojo courseScoreEntity
    public JSONArray courseScoreEntity;

    // pojo courseTaskEntity
    public JSONArray courseTaskEntity;

    // pojo teacherEntity
    public JSONArray teacherEntity;
}