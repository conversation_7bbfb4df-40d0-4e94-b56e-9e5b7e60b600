package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * 班级实体
 *
 * <AUTHOR>
 * @date 2022.7.4
 */
@Document(collection = "clazz")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ClazzEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String clazzId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 所属的专业
    public String majorId;

    // 所属年级
    public String gradeId;

    // po 班级学生人数
    public Integer studentNumber;

    // po 关联的专业信息
    public JSONArray majorEntity;

    // po 关联的年级信息
    public JSONArray gradeEntity;
}