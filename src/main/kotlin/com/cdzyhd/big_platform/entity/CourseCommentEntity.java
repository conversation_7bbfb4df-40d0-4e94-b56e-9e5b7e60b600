package com.cdzyhd.big_platform.entity;

import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程评价
 *
 * <AUTHOR>
 * @date 2022.7.7
 */
@Document(collection = "course_comment")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseCommentEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseCommentId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 所属的课程id
    public String courseId;

    // 发表的用户id
    public String userId;

    // 评价内容
    public String content;

    // po 用户账号
    public String userAccount;

    // po 用户姓名
    public String userName;

    // po 用户所属学院
    public String userCollegeName;
}