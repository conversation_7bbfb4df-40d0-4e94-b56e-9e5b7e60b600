package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 缓存实体
 *
 * <AUTHOR>
 * @date 2022.7.4
 */
@Document(collection = "cache")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CacheEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String cacheId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 缓存类型
    public String type;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 缓存信息
    public JSONObject info = new JSONObject();
}