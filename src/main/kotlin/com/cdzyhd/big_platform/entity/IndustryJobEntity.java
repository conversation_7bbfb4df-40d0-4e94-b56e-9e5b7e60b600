package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 产教融合招聘信息实体类
 */
@Document(collection = "industry_job")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class IndustryJobEntity {
    
    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String jobId = String.valueOf(new SnowflakeIdWorker(1, 0).nextId());
    
    // 基础字段
    public String title;                    // 岗位名称
    public String companyId;                // 企业ID
    public String companyName;              // 企业名称
    public String companyLogo;              // 企业Logo URL
    public String category;                 // 岗位分类代码（development-技术开发，design-产品设计等）
    public String categoryName;             // 岗位分类名称
    public String location;                 // 工作地点
    public String salary;                   // 薪资范围
    public String experience;               // 经验要求
    public String education;                // 学历要求
    public String jobType;                  // 工作类型（全职-fulltime，兼职-parttime，实习-intern）
    public String description;              // 岗位简介
    public String fullDescription;          // 岗位详细描述（HTML格式）
    public JSONArray skills;                // 技能要求数组
    public JSONArray requirements;          // 任职要求数组
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 岗位标识
    public Boolean urgent = false;          // 是否急招
    public Boolean hot = false;             // 是否热门岗位
    public Boolean recommended = false;     // 是否推荐岗位
    
    // 发布和状态信息
    public Long publishTime = System.currentTimeMillis();  // 发布时间
    public String status = "active";        // 状态（active-招聘中，paused-暂停招聘，closed-结束招聘）
    public Long expiredTime;                // 截止时间
    public Integer applicantCount = 0;      // 申请人数
    
    // 联系信息
    public JSONObject contactInfo;          // 联系信息对象{contact, phone, email}
    
    // 管理字段
    public Integer sort = 1;                // 排序号，数字越小排序越前
    public String createBy;                 // 创建人ID
    public String updateBy;                 // 更新人ID
    public Long updateTime;                 // 更新时间
    
    // 统计字段（运行时计算，不存储）
    public Integer viewCount = 0;           // 浏览次数
    public Integer favoriteCount = 0;       // 收藏次数
    
    // 关联查询结果（POJO字段，不存储）
    public JSONObject companyEntity;        // 关联企业信息
}