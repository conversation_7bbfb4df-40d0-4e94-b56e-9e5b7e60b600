package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import org.bson.types.ObjectId;

@Document(collection = "resource_category")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ResourceCategoryEntity {
    
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String categoryId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 基础字段
    public String name;                     // 分类名称
    public String description;              // 分类描述
    public String icon;                     // 图标
    public String color;                    // 主题色
    public String parentId = "0";           // 父分类ID，0表示顶级分类
    public Integer sort = 0;                // 排序
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 统计字段
    public Integer resourceCount = 0;       // 该分类下的资源数量
    public Integer viewCount = 0;           // 该分类的浏览次数
    
    // 状态字段
    public String status = "active";        // 状态 (active-正常, hidden-隐藏, deleted-删除)
    
    // 关联查询结果（POJO字段，不存储）
    public JSONArray children;              // 子分类
    public JSONArray resources;             // 该分类下的资源
}