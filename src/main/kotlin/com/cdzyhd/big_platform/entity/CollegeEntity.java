package com.cdzyhd.big_platform.entity;

import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 学院实体
 *
 * <AUTHOR>
 * @date 2022.7.4
 */
@Document(collection = "college")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CollegeEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String collegeId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 是否显示到首页列表
    public Boolean showIndex = false;

    // 学院介绍文本
    public String desText;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // po 拥有的教师人数
    public Integer teacherNumber;
}