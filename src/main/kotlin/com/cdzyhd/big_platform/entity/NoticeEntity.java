package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 通知公告实体类
 * 用于存储基地通知公告、重要通知、教学通知、系统通知等信息
 */
@Document(collection = "notice")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class NoticeEntity {

    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;

    // 业务主键（雪花算法生成）
    public String noticeId = String.valueOf(new SnowflakeIdWorker(1, 0).nextId());

    // 基础字段
    public String title;                    // 通知标题
    public String summary;                  // 通知摘要
    public String content;                  // 通知详细内容（HTML格式）
    public String coverImage;               // 封面图片URL（可选）
    public String author;                   // 发布者
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Long publishTime;                // 发布时间
    public Long views = 0L;                 // 浏览量

    // 通知类型
    public String type;                     // 通知类型（important/teaching/system/activity）
    public String typeName;                 // 类型名称（重要通知/教学通知/系统通知/活动通知）

    // 状态字段
    public String status = "draft";         // 状态（draft-草稿/active-生效中/expired-已过期/archived-已归档）
    public Boolean isTop = false;           // 是否置顶
    public String urgency = "medium";       // 紧急程度（high-高/medium-中/low-低）
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）

    // 时效性字段
    public Long expireTime;                 // 过期时间（可选）
    public Boolean hasExpire = false;       // 是否设置过期时间

    // 通知范围
    public String targetScope = "all";      // 目标范围（all-全体/college-学院/major-专业/class-班级）
    public JSONArray targetIds;             // 目标ID数组（根据scope确定具体含义）

    // 扩展字段
    public JSONArray attachments=new JSONArray();           // 附件信息数组
    public String noticeLevel = "normal";   // 通知级别（urgent-紧急/important-重要/normal-普通）
    public Boolean needConfirm = false;     // 是否需要确认阅读
    public Long confirmCount = 0L;          // 确认阅读人数

    // 关联查询结果（POJO字段，不存储）
    public JSONArray relatedNotice;         // 相关通知数据

    // 审核相关（可选）
    public String auditStatus = "pending";  // 审核状态（pending-待审核/approved-已通过/rejected-已拒绝）
    public String auditUserId;              // 审核人ID
    public Long auditTime;                  // 审核时间
    public String auditRemark;              // 审核备注

    // 更新相关
    public String updateUserId;             // 最后更新人ID
    public Long updateTime;                 // 最后更新时间

    // 发送相关
    public Boolean isSent = false;          // 是否已发送
    public Long sentTime;                   // 发送时间
    public String sendMethod;               // 发送方式（web-网站/email-邮件/sms-短信）

    // 备用，额外信息
    public JSONObject extraInfo=new JSONObject();
}