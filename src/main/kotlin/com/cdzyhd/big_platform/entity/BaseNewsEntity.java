package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 基地新闻实体类
 * 用于存储基地新闻动态、成果展示、活动通知等信息
 */
@Document(collection = "base_news")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class BaseNewsEntity {

    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;

    // 业务主键（雪花算法生成）
    public String newsId = String.valueOf(new SnowflakeIdWorker(1, 0).nextId());

    // 基础字段
    public String title;                    // 新闻标题
    public String summary;                  // 新闻摘要
    public String content;                  // 新闻详细内容（HTML格式）
    public String coverImage;               // 封面图片URL
    public String author;                   // 作者
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Long publishTime;                // 发布时间
    public Long views = 0L;                 // 浏览量

    // 分类相关
    public String category;                 // 分类代码（base_news/activity/academic/achievement）
    public String categoryName;             // 分类名称（基地动态/活动通知/学术交流/成果展示）

    // 状态字段
    public String status = "draft";         // 状态（draft-草稿/published-已发布/archived-已归档）
    public Boolean isHot = false;           // 是否热门
    public Boolean isTop = false;           // 是否置顶
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）

    // 扩展字段
    public JSONArray tags;                  // 标签数组
    public String sourceUrl;                // 来源链接
    public String sourceFrom;               // 信息来源

    // 关联查询结果（POJO字段，不存储）
    public JSONArray relatedNews;           // 相关新闻数据

    // 审核相关（可选）
    public String auditStatus = "pending";  // 审核状态（pending-待审核/approved-已通过/rejected-已拒绝）
    public String auditUserId;              // 审核人ID
    public Long auditTime;                  // 审核时间
    public String auditRemark;              // 审核备注

    // 更新相关
    public String updateUserId;             // 最后更新人ID
    public Long updateTime;                 // 最后更新时间

    // 备用，额外信息
    public JSONObject extraInfo=new JSONObject();
}