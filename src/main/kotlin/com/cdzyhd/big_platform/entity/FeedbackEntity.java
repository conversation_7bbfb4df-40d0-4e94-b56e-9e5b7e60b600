package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 反馈实体
 *
 * <AUTHOR>
 * @date 2022.7.4
 */
@Document(collection = "feedback")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class FeedbackEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String feedbackId = new SnowflakeIdWorker(1, 0).nextId();

    // 用户id
    public String userId;

    // 反馈内容
    public String content;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // po userEntity
    public JSONArray userEntity;
}