package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程任务
 *
 * <AUTHOR>
 * @date 2022.7.8
 */
@Document(collection = "course_task")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseTaskEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseTaskId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 创建人id
    public String creatorId;

    // 属于哪个学院
    public String collegeId;

    // 任务开始时间
    public Long startTime;

    // 任务结束时间
    public Long endTime;

    // 课程id
    public String courseId;

    // 是否开放该任务
    public Boolean opened = true;

    // 是否需要填写报告
    public Boolean needFillReport = true;

    // 任务介绍文本
    public String desText;

    // 任务备注信息
    public String remarkText;

    // 教师和负责的教学班信息
    /**
     * {"teacherList":[{"teacherAccount":"1004","teacherId":"****************","teacherName":"张露","teachingClazzIdList":["****************","****************","****************"],"teachingClazzList":{"loaded":true,"list":[{"teachingClazzId":"****************","createTime":*************,"name":"1","teacherId":"****************","clazzIds":["****************"],"clazzNames":["毛概1班"],"studentNumber":5},{"teachingClazzId":"****************","createTime":*************,"name":"0712测试教学班2","teacherId":"****************","clazzIds":[],"clazzNames":[],"studentNumber":2},{"teachingClazzId":"****************","createTime":*************,"name":"测试","teacherId":"****************","clazzIds":["****************"],"clazzNames":["毛概1班"],"studentNumber":3}],"loading":true}},{"teacherAccount":"1005","teacherId":"****************","teacherName":"王璐","teachingClazzIdList":["****************"],"teachingClazzList":{"loaded":true,"list":[{"teacherId":"****************","createTime":*************,"studentNumber":6,"clazzNames":["毛概1班","毛概2班"],"name":"0712测试教学班1","clazzIds":["****************","****************"],"teachingClazzId":"****************"}],"loading":true}}],"calInfo":{"teacherNumber":2,"studentNumber":16}}
     */
    public JSONObject teachingClazzInfo = new JSONObject();

    // 所有教学班id列表
    public JSONArray teachingClazzIds = new JSONArray();

    // 所有教师id列表
    public JSONArray teacherIds = new JSONArray();

    // 分数占比-课程分数
    public Double courseScorePoint = 0.4;

    // 分数占比-报告分数
    public Double reportScorePoint = 0.6;

    // 是否限制使用次数
    public Boolean limitLogin = false;

    // 限制使用次数 保存后，所有任务记录都更改为这个值
    public Integer limitNumber = 0;

    // po 所属的学院名称
    public String collegeName;
}