package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 产教融合企业实体类
 */
@Document(collection = "industry_enterprise")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class IndustryEnterpriseEntity {
    
    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String enterpriseId = String.valueOf(new SnowflakeIdWorker(1, 0).nextId());
    
    // 基础字段
    public String name;                     // 企业名称
    public String logo;                     // 企业Logo URL
    public String type;                     // 企业类型代码（internet-互联网，finance-金融，manufacturing-制造业等）
    public String typeName;                 // 企业类型名称
    public String scale;                    // 企业规模代码（large-大型，medium-中型，small-小型）
    public String scaleName;                // 企业规模名称
    public String location;                 // 所在地区
    public String foundYear;                // 成立年份
    public String description;              // 企业简介
    public String fullDescription;          // 企业详细介绍（HTML格式）
    public JSONArray tags;                  // 企业标签数组
    public String website;                  // 企业官网
    public String address;                  // 详细地址
    public String employeeCount;            // 员工数量
    public String revenue;                  // 年营收
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 合作相关字段
    public Integer cooperationYears = 0;    // 合作年限
    public Integer studentCount = 0;        // 培养学生数量
    public Integer projectCount = 0;        // 合作项目数量
    public JSONArray cooperationProjects;  // 合作项目详情数组
    public String cooperationDetail;        // 合作详情（富文本HTML格式）
    
    // 联系信息
    public JSONObject contactInfo;          // 联系信息对象{contact, phone, email}
    
    // 状态和管理字段
    public String status = "active";        // 状态（active-激活，inactive-禁用）
    public Integer sort = 1;                // 排序号，数字越小排序越前
    public String createBy;                 // 创建人ID
    public String updateBy;                 // 更新人ID
    public Long updateTime;                 // 更新时间
    
    // 统计字段（运行时计算，不存储）
    public Integer viewCount = 0;           // 浏览次数
    public Integer favoriteCount = 0;       // 收藏次数
}