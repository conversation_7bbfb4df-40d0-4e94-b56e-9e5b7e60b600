package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程实体
 *
 * <AUTHOR>
 * @date 2022.7.6
 */
@Document(collection = "course")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 课程头图url
    public String avatarUrl;

    // 课程类型 web网页 client客户端
    public String courseType;

    // 属于哪个学科id
    public String courseSubjectId;

    // 属于哪个学院id
    public String collegeId;

    // 实验简介
    public String shortDesText;

    // 实验介绍
    public String desText;

    // 实验帮助文本
    public String helpText;

    // 联系方式文本
    public String contactText;

    // 软硬件要求文本
    public String softHardwareText;

    // 如果是网页类型 网页跳转地址
    public String webUrl;

    // 如果是客户端 客户端下载方式文本
    public String clientText;

    // appId todo 暂时和courseId一致 是否要考虑一个实验有多个端？
    public String appId;

    // appSecret
    @JsonIgnore
    public String appSecret;

    // 是否开放课程 可以访问和登录
    public Boolean opened = true;

    // 满分分数
    public Double fullScore = 100.0;

    // 分数占比-课程分数
    public Double courseScorePoint = 0.4;

    // 分数占比-报告分数
    public Double reportScorePoint = 0.6;

    // 是否显示在首页
    public Boolean showIndex = false;

    // 是否限制登录次数
    public Boolean limitLogin = false;

    // 限制登录次数
    public Integer limitNumber = 0;

    // 统计-总实验人数
    public Integer userNumber;

    // 统计-总评价人数
    public Integer commentNumber;

    // pojo 学科
    public JSONArray courseSubjectEntity = new JSONArray();

    // pojo 学院
    public JSONArray collegeEntity = new JSONArray();

    // pojo-学院名称
    public  String collegeName;
}