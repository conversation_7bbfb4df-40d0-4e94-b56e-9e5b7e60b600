package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import org.bson.types.ObjectId;

@Document(collection = "user_resource_favorite")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class UserResourceFavoriteEntity {
    
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String favoriteId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 关联字段
    public String userId;                   // 用户ID
    public String resourceId;               // 资源ID
    
    // 时间字段
    public Long createTime = System.currentTimeMillis();  // 收藏时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 关联查询结果（POJO字段，不存储）
    public JSONArray userEntity;           // 用户信息
    public JSONArray resourceEntity;       // 资源信息
}