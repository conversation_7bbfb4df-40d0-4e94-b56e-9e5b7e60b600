package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import org.bson.types.ObjectId;

@Document(collection = "resource")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ResourceEntity {
    
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String resourceId = new SnowflakeIdWorker(1, 0).nextId();
    
    // 基础字段
    public String title;                    // 资源标题
    public String description;              // 资源描述
    public String type;                     // 文件类型 (pdf, video, image, audio等)
    public String typeName;                 // 文件类型显示名称
    public String author;                   // 作者
    public String authorIntro;              // 作者介绍
    public String fileUrl;                  // 文件URL
    public String thumbnail;                // 缩略图URL
    public Long fileSize;                   // 文件大小（字节）
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Long uploadTime = System.currentTimeMillis();  // 上传时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 统计字段
    public Integer views = 0;               // 浏览量
    public Integer downloads = 0;           // 下载量
    public Integer favorites = 0;           // 收藏量
    
    // 分类相关
    public String categoryId;               // 分类ID
    public String categoryName;             // 分类名称
    
    // 课程关联
    public Boolean relatedToCourse = false; // 是否关联课程
    public String courseId;                 // 课程ID
    public String courseName;               // 课程名称
    
    // 权限控制
    public Boolean canDownload = true;      // 是否可下载
    public Boolean canPreview = true;       // 是否可预览
    
    // 扩展字段
    public JSONArray tags;                  // 标签数组
    public String language;                 // 语言
    public String difficulty;               // 难度
    public String estimatedReadTime;        // 预计阅读时间
    public Integer previewPages;            // PDF预览页数
    public String duration;                 // 视频/音频时长
    public String resolution;               // 视频分辨率
    public String format;                   // 文件格式
    
    // 状态字段
    public String status = "active";        // 状态 (active-正常, hidden-隐藏)
    
    // 关联查询结果（POJO字段，不存储）
    public JSONArray categoryEntity;       // 分类信息
    public JSONObject courseEntity;         // 课程信息
    public JSONArray relatedResources;      // 相关资源
}