package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 产教融合轮播图实体类
 */
@Document(collection = "industry_banner")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class IndustryBannerEntity {
    
    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String bannerId = String.valueOf(new SnowflakeIdWorker(1, 0).nextId());
    
    // 基础字段
    public String title;                    // 轮播图标题
    public String description;              // 轮播图描述
    public String image;                    // 轮播图图片URL
    public String link;                     // 点击跳转链接
    public String status = "active";        // 状态（active-激活，inactive-禁用）
    public Integer sort = 1;                // 排序号，数字越小排序越前
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 扩展字段
    public String target = "_self";         // 链接打开方式（_self-当前页面，_blank-新页面）
    public String imageAlt;                 // 图片Alt属性
    public Long startTime;                  // 开始显示时间
    public Long endTime;                    // 结束显示时间
    public String createBy;                 // 创建人ID
    public String updateBy;                 // 更新人ID
    public Long updateTime;                 // 更新时间
}