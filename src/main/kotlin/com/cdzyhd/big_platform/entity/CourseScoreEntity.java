package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程分数记录
 *
 * <AUTHOR>
 * @date 2022.7.14
 */
@Document(collection = "course_score")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseScoreEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseScoreId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 关联的课程记录id
    public String courseRecordId;

    // 关联的课程id
    public String courseId;

    // 成绩结果 0未完成 1已完成
    public Integer result = 1;

    // 满分分数 作为course的满分副本 课程满分可能会改变
    public Double fullScore;

    // 分数
    public Double score;

    // 开始时间
    public Long startTime;

    // 结束时间
    public Long endTime;

    // 用时 秒
    public Long usedTime;

    // 步骤记录信息
    public JSONArray stepInfo = new JSONArray();

    // 记录其他额外的信息
    public JSONObject extraInfo = new JSONObject();
}