package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 产教融合优秀学生实体类
 */
@Document(collection = "industry_student")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class IndustryStudentEntity {
    
    // MongoDB内部ID（不序列化）
    @Id
    @JsonIgnore
    public ObjectId id;
    
    // 业务主键（雪花算法生成）
    public String studentId = String.valueOf(new SnowflakeIdWorker(1, 0).nextId());
    
    // 基础字段
    public String name;                     // 学生姓名
    public String photo;                    // 学生照片URL
    public String major;                    // 专业
    public String graduationYear;           // 毕业年份
    public String company;                  // 就职企业
    public String position;                 // 职位
    public String category;                 // 分类代码（outstanding-优秀毕业生，scholarship-奖学金获得者等）
    public String categoryName;             // 分类名称
    public String gpa;                      // GPA成绩
    public Integer projectCount = 0;        // 项目数量
    public String quote;                    // 个人格言
    public String story;                    // 个人故事（HTML格式）
    public JSONArray achievements;          // 成就列表数组 [{id, title, type}]
    public JSONArray skills;                // 技能列表数组
    public JSONArray projects;              // 项目经历数组 [{id, name, description, role, tech}]
    public Long createTime = System.currentTimeMillis();  // 创建时间
    public Integer deleted = 0;             // 逻辑删除标识（0-正常，1-删除）
    
    // 联系信息
    public JSONObject contact;              // 联系方式对象{email, phone}
    
    // 学习和工作经历
    public String studentNumber;            // 学号
    public String clazzName;                // 班级名称
    public String collegeName;              // 学院名称
    public Long enrollmentTime;             // 入学时间
    public Long graduationTime;             // 毕业时间
    public Long employmentTime;             // 就职时间
    
    // 状态和管理字段
    public String status = "active";        // 状态（active-激活，inactive-禁用）
    public Boolean featured = false;        // 是否特色展示
    public Integer sort = 1;                // 排序号，数字越小排序越前
    public String createBy;                 // 创建人ID
    public String updateBy;                 // 更新人ID
    public Long updateTime;                 // 更新时间
    
    // 统计字段（运行时计算，不存储）
    public Integer viewCount = 0;           // 浏览次数
    public Integer favoriteCount = 0;       // 收藏次数
    
    // 扩展字段
    public JSONObject socialMedia;          // 社交媒体信息
    public String hometown;                 // 家乡
    public String personalWebsite;          // 个人网站
}