package com.cdzyhd.big_platform.entity;

import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 年级实体
 *
 * <AUTHOR>
 * @date 2022.7.4
 */
@Document(collection = "grade")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class GradeEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String gradeId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();
}