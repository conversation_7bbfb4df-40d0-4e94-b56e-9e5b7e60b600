package com.cdzyhd.big_platform.entity;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程记录
 *
 * <AUTHOR>
 * @date 2022.7.8
 */
@Document(collection = "course_report")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseReportEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseReportId = new SnowflakeIdWorker(1, 0).nextId();

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 关联的学生或教师用户id
    public String userId;

    // 管理的课程记录id
    public String courseRecordId;

    // 修改时间
    public Long editTime = System.currentTimeMillis();

    // 报告内容
    public String content;

    // 草稿内容
    public String draftContent;

    // 批改教师id
    public String correctTeacherId;

    // 批改教师姓名
    public String correctTeacherName;

    // 报告分数
    public Double score;

    // 评价内容
    public JSONObject commentInfo = new JSONObject();

    // 批改时间
    public Long correctDate;
}