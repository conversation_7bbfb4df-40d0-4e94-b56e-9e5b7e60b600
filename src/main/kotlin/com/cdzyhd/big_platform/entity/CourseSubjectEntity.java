package com.cdzyhd.big_platform.entity;

import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 课程学科
 *
 * <AUTHOR>
 * @date 2022.7.6
 */
@Document(collection = "course_subject")
@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class CourseSubjectEntity {
    @Id
    @JsonIgnore
    public ObjectId id;

    // id
    public String courseSubjectId = new SnowflakeIdWorker(1, 0).nextId();

    // 名称
    public String name;

    // 创建时间
    public Long createTime = System.currentTimeMillis();

    // 拥有的课程数量
    public Integer courseNumber;
}