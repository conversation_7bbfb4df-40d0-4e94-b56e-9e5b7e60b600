package com.cdzyhd.big_platform.a;

import com.cdzyhd.big_platform.config.CommonConfig;
import com.cdzyhd.big_platform.entity.FeedbackEntity;
import com.cdzyhd.big_platform.entity.SysLogEntity;
import com.cdzyhd.big_platform.repository.*;
import com.cdzyhd.big_platform.service.RedisService;
import org.springframework.data.mongodb.core.MongoTemplate;

public class StaticBean {
    // CommonConfig
    public static CommonConfig commonConfig = SpringUtil.getBeanByClass(CommonConfig.class);
    // Redis
    public static RedisService redisService = SpringUtil.getBeanByClass(RedisService.class);
    // mongoTemplate
    public static MongoTemplate mongoTemplate = SpringUtil.getBeanByClass(MongoTemplate.class);
    // 管理员用户Repository
    public static AdminUserRepository adminUserRepository = SpringUtil.getBeanByClass(AdminUserRepository.class);
    // Grade年级
    public static GradeRepository gradeRepository = SpringUtil.getBeanByClass(GradeRepository.class);
    // College学院
    public static CollegeRepository collegeRepository = SpringUtil.getBeanByClass(CollegeRepository.class);
    // Major专业
    public static MajorRepository majorRepository = SpringUtil.getBeanByClass(MajorRepository.class);
    // Clazz班级
    public static ClazzRepository clazzRepository = SpringUtil.getBeanByClass(ClazzRepository.class);
    // User学生和教师用户
    public static UserRepository userRepository = SpringUtil.getBeanByClass(UserRepository.class);
    // CourseSubject课程学科
    public static CourseSubjectRepository courseSubjectRepository = SpringUtil.getBeanByClass(CourseSubjectRepository.class);
    // Course课程
    public static CourseRepository courseRepository = SpringUtil.getBeanByClass(CourseRepository.class);
    // File文件
    public static FileRepository fileRepository = SpringUtil.getBeanByClass(FileRepository.class);
    // Help帮助
    public static HelpQuestionRepository helpQuestionRepository = SpringUtil.getBeanByClass(HelpQuestionRepository.class);
    // CourseComment课程评价
    public static CourseCommentRepository courseCommentRepository = SpringUtil.getBeanByClass(CourseCommentRepository.class);
    // CourseTask课程任务安排
    public static CourseTaskRepository courseTaskRepository = SpringUtil.getBeanByClass(CourseTaskRepository.class);
    // TeachingClazz教学班
    public static TeachingClazzRepository teachingClazzRepository = SpringUtil.getBeanByClass(TeachingClazzRepository.class);
    public static CourseRecordRepository courseRecordRepository = SpringUtil.getBeanByClass(CourseRecordRepository.class);
    public static CourseReportRepository courseReportRepository = SpringUtil.getBeanByClass(CourseReportRepository.class);
    public static CourseScoreRepository courseScoreRepository = SpringUtil.getBeanByClass(CourseScoreRepository.class);
    public static CacheRepository cacheRepository = SpringUtil.getBeanByClass(CacheRepository.class);
    public static SysLogRepository sysLogRepository = SpringUtil.getBeanByClass(SysLogRepository.class);
    public static FeedbackRepository feedbackRepository = SpringUtil.getBeanByClass(FeedbackRepository.class);
    // BaseNews基地新闻
    public static BaseNewsRepository baseNewsRepository = SpringUtil.getBeanByClass(BaseNewsRepository.class);
    // Notice通知公告
    public static NoticeRepository noticeRepository = SpringUtil.getBeanByClass(NoticeRepository.class);
    
    // 产教融合相关Repository
    // 产教融合轮播图
    public static IndustryBannerRepository industryBannerRepository = SpringUtil.getBeanByClass(IndustryBannerRepository.class);
    // 产教融合企业
    public static IndustryEnterpriseRepository industryEnterpriseRepository = SpringUtil.getBeanByClass(IndustryEnterpriseRepository.class);
    // 产教融合招聘信息
    public static IndustryJobRepository industryJobRepository = SpringUtil.getBeanByClass(IndustryJobRepository.class);
    // 产教融合优秀学生
    public static IndustryStudentRepository industryStudentRepository = SpringUtil.getBeanByClass(IndustryStudentRepository.class);
    
    // 资源中心相关Repository
    // 资源
    public static ResourceRepository resourceRepository = SpringUtil.getBeanByClass(ResourceRepository.class);
    // 资源分类
    public static ResourceCategoryRepository resourceCategoryRepository = SpringUtil.getBeanByClass(ResourceCategoryRepository.class);
    // 用户资源收藏
    public static UserResourceFavoriteRepository userResourceFavoriteRepository = SpringUtil.getBeanByClass(UserResourceFavoriteRepository.class);
}