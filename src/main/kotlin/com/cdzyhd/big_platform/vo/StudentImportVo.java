package com.cdzyhd.big_platform.vo;

import com.cdzyhd.big_platform.util.SnowflakeIdWorker;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 学生批量导入Vo
 *
 * <AUTHOR>
 * @date 2022.7.5
 */
@Data
public class StudentImportVo {
    // 姓名
    public String name;

    // 学号
    public String account;

    // 性别
    public String sex;

    // 绑定的邮箱
    public String email;

    // 所属的班级id
    public String clazzId;

    // 所属的班级名称
    public String clazzName;

    // 所属学院名称
    public String collegeName;

    // 所属专业名称
    public String majorName;

    // 所属年级名称
    public String gradeName;

    // 在表格第几行
    public Integer collumNumber;
}