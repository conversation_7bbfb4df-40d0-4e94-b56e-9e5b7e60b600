package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.CourseCommentEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/courseComment/"])
class CourseCommentController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseCommentRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseCommentId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.courseCommentRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        // 遍历获取每个用户信息
        for (comment in pageList.content) {
            val user = StaticBean.userRepository.findFirstByUserId(comment.userId)
            if (user != null) {
                comment.userAccount = user.account
                comment.userName = user.name
                // 获取学院
                val clazz = StaticBean.clazzRepository.findFirstByClazzId(user.clazzId)
                val major = StaticBean.majorRepository.findFirstByMajorId(clazz.majorId)
                val college = StaticBean.collegeRepository.findFirstByCollegeId(major.collegeId)
                comment.userCollegeName=college.name
            } else {
                comment.userAccount = "已删除"
                comment.userName = "已删除"
            }
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam courseCommentId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.courseCommentRepository, "courseCommentId", courseCommentId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam courseCommentId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.courseCommentRepository, "courseCommentId", courseCommentId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.courseCommentRepository,
                CourseCommentEntity(),
                "courseCommentId",
                infoObject
            )
        )
    }
}