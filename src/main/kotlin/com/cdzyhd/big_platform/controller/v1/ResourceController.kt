package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.entity.ResourceCategoryEntity
import com.cdzyhd.big_platform.model.ResourceModel
import com.cdzyhd.big_platform.model.ResourceCategoryModel
import com.cdzyhd.big_platform.entity.ResourceEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletRequest

@RestController
@RequestMapping(value = ["/v1/resource/"])
class ResourceController {

    // 获取资源列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.resourceRepository.getList(Document.parse(query))
        )
    }

    // 获取资源列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, 
        @PageableDefault(
            value = 20, 
            sort = ["uploadTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            ResourceModel.getResourcePageList(queryObject, pageable)
        )
    }

    // 获取资源详情
    @GetMapping("")
    fun getResourceDetail(@RequestParam resourceId: String,@RequestParam asAdmin: Boolean = false): OutResponse<Any> {
        val resource = ResourceModel.getResourceDetail(resourceId,asAdmin)
        return if (resource != null) {
            OutResponse("000000", "", resource)
        } else {
            OutResponse("000001", "资源不存在", null)
        }
    }

    // 删除资源
    @DeleteMapping("")
    @NeedToken
    @SysOperaLog(moduleName = "资源管理", methodName = "删除资源")
    fun deleteOne(@RequestParam resourceId: String): OutResponse<Any> {
        val resource = ResourceModel.getResourceDetail(resourceId)
        if (resource == null) {
            return OutResponse("000001", "资源不存在", null)
        }
        resource.deleted = 1
        StaticBean.resourceRepository.save(resource)
        return OutResponse(
            "000000",
            "",
            true
        )
    }

    // 新增或修改资源
    @PostMapping("")
    @NeedToken
    @SysOperaLog(moduleName = "资源管理", methodName = "新增或修改资源")
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.resourceRepository,
                ResourceEntity(),
                "resourceId",
                infoObject
            )
        )
    }

    // 获取资源分类列表-不分页
    @PostMapping("categories/list")
    fun getResourceCategories(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            ResourceCategoryModel.getResourceCategories(query)
        )
    }

    // 新增或修改资源分类
    @PostMapping("categories")
    @NeedToken
    @SysOperaLog(moduleName = "资源管理", methodName = "新增或修改资源分类")
    fun addOrEditCategory(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.resourceCategoryRepository,
                ResourceCategoryEntity(),
                "categoryId",
                infoObject
            )
        )
    }

    // 根据分类获取资源列表
    @PostMapping("category/{categoryId}")
    fun getResourcesByCategory(
        @PathVariable categoryId: String,
        @RequestBody query: String,
        @PageableDefault(
            value = 20, 
            sort = ["uploadTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        queryObject["categoryId"] = categoryId
        return OutResponse(
            "000000",
            "",
            ResourceModel.getResourcePageList(queryObject, pageable)
        )
    }

    // 删除资源分类
    @DeleteMapping("categories")
    @NeedToken
    @SysOperaLog(moduleName = "资源管理", methodName = "删除资源分类")
    fun deleteCategoryOne(@RequestParam categoryId: String): OutResponse<Any> {
        val category = StaticBean.resourceCategoryRepository.findFirstByCategoryId(categoryId)
        if (category == null) {
            return OutResponse("000001", "资源分类不存在", null)
        }
        category.deleted = 1
        StaticBean.resourceCategoryRepository.save(category)
        return OutResponse(
            "000000",
            "",
            true
        )
    }

    // 搜索资源
    @PostMapping("search")
    fun searchResources(
        @RequestBody query: String,
        @PageableDefault(
            value = 20, 
            sort = ["uploadTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            ResourceModel.getResourcePageList(queryObject, pageable)
        )
    }

    // 获取推荐资源
    @GetMapping("featured")
    fun getFeaturedResources(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            ResourceModel.getFeaturedResources()
        )
    }

    // 获取最新资源
    @GetMapping("latest")
    fun getLatestResources(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            ResourceModel.getLatestResources()
        )
    }

    // 获取资源统计数据
    @GetMapping("stats")
    fun getResourceStats(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            ResourceModel.getResourceStats()
        )
    }

    // 获取热门搜索标签
    @GetMapping("hotTags")
    fun getHotSearchTags(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            ResourceModel.getHotSearchTags()
        )
    }

    // 增加资源浏览量
    @PostMapping("views")
    fun increaseResourceViews(@RequestBody data: String): OutResponse<Any> {
        val dataObject = JSONObject.parseObject(data)
        val resourceId = dataObject.getString("resourceId")
        val success = ResourceModel.increaseResourceViews(resourceId)
        return OutResponse(
            "000000",
            if (success) "浏览量增加成功" else "资源不存在",
            success
        )
    }

    // 增加资源下载量
    @PostMapping("downloads")
    fun increaseResourceDownloads(@RequestBody data: String): OutResponse<Any> {
        val dataObject = JSONObject.parseObject(data)
        val resourceId = dataObject.getString("resourceId")
        val success = ResourceModel.increaseResourceDownloads(resourceId)
        return OutResponse(
            "000000",
            if (success) "下载量增加成功" else "资源不存在",
            success
        )
    }

    // 资源收藏/取消收藏
    @PostMapping("favorite")
    @NeedToken
    @SysOperaLog(moduleName = "资源管理", methodName = "收藏或取消收藏资源")
    fun toggleResourceFavorite(
        @RequestBody data: String,
        @RequestAttribute uid: String,
    ): OutResponse<Any> {
        val dataObject = JSONObject.parseObject(data)
        val resourceId = dataObject.getString("resourceId")
        
        val isFavorited = ResourceModel.toggleResourceFavorite(uid, resourceId)
        return OutResponse(
            "000000",
            if (isFavorited) "收藏成功" else "取消收藏成功",
            mapOf("isFavorited" to isFavorited)
        )
    }

    // 获取用户收藏的资源列表
    @PostMapping("favorites")
    @NeedToken
    fun getUserFavoriteResources(
        @RequestBody query: String,
        @PageableDefault(
            value = 20, 
            sort = ["createTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable,
        @RequestAttribute uid: String,
    ): OutResponse<Any> {
        
        return OutResponse(
            "000000",
            "",
            ResourceModel.getUserFavoriteResources(uid, pageable, query)
        )
    }

    // 获取相关资源
    @GetMapping("related")
    fun getRelatedResources(
        @RequestParam resourceId: String,
        @RequestParam categoryId: String
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            ResourceModel.getRelatedResources(resourceId, categoryId)
        )
    }

    // 下载资源
    @PostMapping("download")
    @SysOperaLog(moduleName = "资源管理", methodName = "下载资源")
    fun downloadResource(@RequestBody data: String): OutResponse<Any> {
        val dataObject = JSONObject.parseObject(data)
        val resourceId = dataObject.getString("resourceId")
        
        // 增加下载量
        ResourceModel.increaseResourceDownloads(resourceId)
        
        // 获取资源信息
        val resource = ResourceModel.getResourceDetail(resourceId)
        if (resource != null && resource.canDownload == true) {
            val downloadInfo = JSONObject()
            downloadInfo["success"] = true
            downloadInfo["downloadUrl"] = resource.fileUrl
            downloadInfo["message"] = "下载链接已生成"
            return OutResponse("000000", "下载成功", downloadInfo)
        } else {
            return OutResponse("000001", "资源不存在或不允许下载", null)
        }
    }

    // 检查用户是否收藏了某个资源
    @GetMapping("isFavorited")
    @NeedToken
    fun isResourceFavorited(
        @RequestParam resourceId: String,
        @RequestAttribute uid: String,
    ): OutResponse<Any> {
        
        val isFavorited = ResourceModel.isResourceFavorited(uid, resourceId)
        return OutResponse(
            "000000",
            "",
            mapOf("isFavorited" to isFavorited)
        )
    }
}