package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.entity.NoticeEntity
import com.cdzyhd.big_platform.model.NoticeModel
import com.cdzyhd.big_platform.a.StaticBean
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

/**
 * 通知公告控制器
 * 提供通知公告相关的API接口
 */
@RestController
@RequestMapping(value = ["/v1/notice/"])
class NoticeController {

    // 获取通知列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.noticeRepository.getList(Document.parse(query))
        )
    }

    // 获取通知列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 20,
            sort = ["publishTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.noticeRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 获取一个通知详情
    @GetMapping("")
    fun getOne(@RequestParam noticeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.noticeRepository,
                "noticeId",
                noticeId
            )
        )
    }

    // 删除一个通知
    @DeleteMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "通知公告", methodName = "删除通知")
    fun deleteOne(@RequestParam noticeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.noticeRepository,
                "noticeId",
                noticeId
            )
        )
    }

    // 新增或修改通知
    @PostMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "通知公告", methodName = "新增或修改通知")
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.noticeRepository,
                NoticeEntity(),
                "noticeId",
                infoObject
            )
        )
    }

    // 获取通知轮播列表
    @GetMapping("carousel")
    fun getCarouselList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取轮播通知成功",
            NoticeModel.getCarouselNoticeList()
        )
    }

    // 获取置顶通知列表
    @GetMapping("top")
    fun getTopList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取置顶通知成功",
            NoticeModel.getTopNoticeList()
        )
    }

    // 根据类型获取通知
    @GetMapping("type")
    fun getByType(
        @RequestParam type: String,
        @RequestParam(defaultValue = "10") limit: Int
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取分类通知成功",
            NoticeModel.getNoticeByType(type, limit)
        )
    }

    // 搜索通知
    @PostMapping("search")
    fun searchNotice(
        @RequestBody searchParams: String,
        @PageableDefault(
            value = 12,
            sort = ["publishTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val params = JSONObject.parseObject(searchParams)
        val keyword = params.getString("keyword") ?: ""
        return OutResponse(
            "000000",
            "搜索通知成功",
            NoticeModel.searchNotice(keyword, pageable)
        )
    }

    // 获取相关通知
    @GetMapping("related")
    fun getRelatedNotice(
        @RequestParam noticeId: String,
        @RequestParam type: String,
        @RequestParam(defaultValue = "5") limit: Int
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取相关通知成功",
            NoticeModel.getRelatedNotice(noticeId, type, limit)
        )
    }

    // 增加浏览量
    @PostMapping("view")
    fun increaseViews(@RequestParam noticeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "浏览量增加成功",
            NoticeModel.increaseViews(noticeId)
        )
    }

    // 获取有效期内的通知
    @GetMapping("active")
    fun getActiveNotices(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取有效通知成功",
            NoticeModel.getActiveNotices()
        )
    }

    // 获取即将过期的通知
    @GetMapping("expiring")
    @NeedAdminToken
    fun getExpiringNotices(@RequestParam(defaultValue = "7") days: Int): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取即将过期通知成功",
            NoticeModel.getExpiringNotices(days)
        )
    }

    // 发布通知
    @PostMapping("publish")
    @NeedAdminToken
    @SysOperaLog(moduleName = "通知公告", methodName = "发布通知")
    fun publishNotice(@RequestParam noticeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "通知发布成功",
            NoticeModel.publishNotice(noticeId)
        )
    }

    // 撤回通知
    @PostMapping("unpublish")
    @NeedAdminToken
    @SysOperaLog(moduleName = "通知公告", methodName = "撤回通知")
    fun unpublishNotice(@RequestParam noticeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "通知撤回成功",
            NoticeModel.unpublishNotice(noticeId)
        )
    }

    // 设置置顶通知
    @PostMapping("setTop")
    @NeedAdminToken
    @SysOperaLog(moduleName = "通知公告", methodName = "设置置顶通知")
    fun setTopNotice(
        @RequestParam noticeId: String,
        @RequestParam isTop: Boolean
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "设置置顶状态成功",
            NoticeModel.setTopNotice(noticeId, isTop)
        )
    }

    // 处理过期通知
    @PostMapping("processExpired")
    @NeedAdminToken
    @SysOperaLog(moduleName = "通知公告", methodName = "处理过期通知")
    fun processExpiredNotices(): OutResponse<Any> {
        val processedCount = NoticeModel.processExpiredNotices()
        return OutResponse(
            "000000",
            "处理过期通知成功，共处理${processedCount}条",
            processedCount
        )
    }

    // 确认阅读通知
    @PostMapping("confirmRead")
    @NeedToken
    @SysOperaLog(moduleName = "通知公告", methodName = "确认阅读通知")
    fun confirmRead(
        @RequestParam noticeId: String,
        @RequestParam userId: String
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "确认阅读成功",
            NoticeModel.confirmRead(noticeId, userId)
        )
    }

    // 获取通知统计信息
    @GetMapping("statistics")
    @NeedAdminToken
    fun getStatistics(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取统计信息成功",
            NoticeModel.getNoticeStatistics()
        )
    }
}