package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.TeachingClazzEntity
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.model.ClazzModel
import com.cdzyhd.big_platform.model.TeachingClazzModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.and
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping(value = ["/v1/teachingClazz/"])
class TeachingClazzController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.teachingClazzRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["teachingClazzId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.teachingClazzRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        // 遍历获取每个学院的教师人数 todo 更有效的不用遍历的查询方法
        for (teachingClazz in pageList.content) {
            val clazzNames = JSONArray()
            // 获取关联班级名称 todo 改为聚合lookup查询
            for (clazzId in teachingClazz.clazzIds) {
                val clazz = StaticBean.clazzRepository.findFirstByClazzId(clazzId as String)
                clazzNames.add(clazz.name)
            }
            // 获取该教学班学生总人数
            val studentNumber =
                StaticBean.userRepository.countByTeachingClazzIdsContainsAndRole(
                    teachingClazz.teachingClazzId,
                    "student"
                )
            teachingClazz.clazzNames = clazzNames
            teachingClazz.studentNumber = studentNumber
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam teachingClazzId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.teachingClazzRepository.findFirstByTeachingClazzId(teachingClazzId)
        )
    }

    // 获取某个教学班详情
    @GetMapping("oneDetail")
    fun getOneDetail(@RequestParam teachingClazzId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            TeachingClazzModel().getOneDetail(teachingClazzId)
        )
    }


    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam teachingClazzId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.teachingClazzRepository, "teachingClazzId", teachingClazzId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.teachingClazzRepository,
                TeachingClazzEntity(),
                "teachingClazzId",
                infoObject
            )
        )
    }

    // 新增一个教学班
    @PostMapping("addOne")
    @NeedAdminToken
    fun addOne(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            TeachingClazzModel().addOne(infoObject)
        )
    }

    // 编辑一个教学班
    @PostMapping("editOne")
    @NeedAdminToken
    fun editOne(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            TeachingClazzModel().editOne(infoObject)
        )
    }

    // 获取-某个教学班的学生列表
    @PostMapping("studentList")
    @NeedAdminToken
    fun getStudentList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["userId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val teachingClazzId = queryObject.getString("teachingClazzId")
        val account = queryObject.getString("account")
        val name = queryObject.getString("name")
        // 查询 学生教师且在教学班
        val criteria = Criteria("role").`is`("student").and("teachingClazzIds").all(teachingClazzId)
        if (name != null) {
            criteria.and("name").regex(name)
        }
        if (account != null) {
            criteria.and("account").regex(account)
        }

        val matchOperation = Aggregation.match(criteria)
        val countAggregation = Aggregation.count().`as`("count")
        var aggregation = Aggregation.newAggregation(matchOperation, countAggregation)
        val countResult =
            StaticBean.mongoTemplate.aggregate(aggregation, "user", HashMap::class.java).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // join
        val lookupClazz: LookupOperation =
            Aggregation.lookup("clazz", "clazzId", "clazzId", "clazzEntity")
        val lookupGrade =
            Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
        val lookupMajor =
            Aggregation.lookup("major", "clazzEntity.majorId", "majorId", "majorEntity")
        // 分页
        val skipOperation = Aggregation.skip(pageable.pageNumber * pageSize)
        val limitOperation = Aggregation.limit(pageSize.toLong())
        aggregation = Aggregation.newAggregation(
            lookupClazz,
            lookupGrade,
            lookupMajor,
            matchOperation,
            skipOperation,
            limitOperation
        )
        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(aggregation, "user", UserEntity::class.java).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }

    // 从教学班中删除一个学生
    @DeleteMapping("oneStudent")
    @NeedAdminToken
    fun deleteOneStudent(@RequestParam teachingClazzId: String, @RequestParam studentId: String): OutResponse<Any> {
        val student = StaticBean.userRepository.findFirstByUserId(studentId)
        return OutResponse(
            "000000",
            "",
            TeachingClazzModel().deleteOneStudent(teachingClazzId, student)
        )
    }

    // 从教学班中新增一个学生
    @PutMapping("oneStudent")
    @NeedAdminToken
    fun addOneStudent(@RequestParam teachingClazzId: String, @RequestParam studentId: String): OutResponse<Any> {
        val student = StaticBean.userRepository.findFirstByUserId(studentId)
        return OutResponse(
            "000000",
            "",
            TeachingClazzModel().addOneStudent(teachingClazzId, student)
        )
    }

    // 批量导入学生-班级内
    @PostMapping("/importStudentsByTeachingClazzId")
    @NeedAdminToken
    fun importStudentsByTeachingClazzId(
        @RequestParam multipartFile: MultipartFile,
        @RequestParam teachingClazzId: String
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "导入成功",
            TeachingClazzModel().importStudentsByTeachingClazzId(multipartFile, teachingClazzId)
        )
    }

    // 批量导入教学班
    @PostMapping("/importTeachingClazz")
    @NeedAdminToken
    fun importTeachingClazz(
        @RequestParam multipartFile: MultipartFile
    ): OutResponse<Any> {
        return OutResponse("000000", "导入成功", TeachingClazzModel().importTeachingClazz(multipartFile))
    }
}