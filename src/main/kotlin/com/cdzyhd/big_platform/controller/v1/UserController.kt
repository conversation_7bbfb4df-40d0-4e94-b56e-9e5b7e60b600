package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.exception.CommonException
import com.cdzyhd.big_platform.model.UserModel
import com.cdzyhd.big_platform.po.RegAccountPo
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/user/"])
class UserController {
    // 发送账号启用邮件
    @GetMapping("sendAccountEnableEmail")
    @NeedAdminToken
    fun sendAccountEnableEmail(@RequestParam uid: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().sendAccountEnableEmail(uid)
        )
    }

    // 判断用户信息是否重复
    @GetMapping("checkUserInfoRepeat")
    fun checkUserInfoRepeat(@RequestParam type: String, @RequestParam value: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().checkUserInfoRepeat(type, value)
        )
    }

    // 平台注册账号
    @PostMapping("regAccount")
    @SysOperaLog(moduleName = "用户模块", methodName = "注册账号")
    fun regAccount(@RequestBody regAccountPo: RegAccountPo): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().regAccount(regAccountPo)
        )
    }

    // 平台登录
    @GetMapping("loginInPlatform")
    @SysOperaLog(moduleName = "用户模块", methodName = "平台登录")
    fun loginInPlatform(@RequestParam account: String, @RequestParam password: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().loginInPlatform(account, password)
        )
    }

    // 通过token获取用户信息
    @GetMapping("infoByToken")
    @NeedToken
    fun getInfoByToken(@RequestAttribute uid: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().getInfoByUserId(uid)
        )
    }

    // 在首次登录时补充用户信息
    @PostMapping("supplementUserInfoOnFirstLogin")
    @NeedToken
    @SysOperaLog(moduleName = "用户模块", methodName = "首次登录补充信息")
    fun supplementUserInfoOnFirstLogin(@RequestAttribute uid: String, @RequestBody userInfo: String): OutResponse<Any> {
        val userInfoObject = JSONObject.parseObject(userInfo)
        val password = userInfoObject.getString("password")
        val email = userInfoObject.getString("email")
        val code = userInfoObject.getString("code")
        return OutResponse(
            "000000",
            "",
            UserModel().supplementUserInfoOnFirstLogin(uid, password, email, code)
        )
    }

    // 用户忘记密码,发送邮件验证码
    @GetMapping("forgetPasswordSendEmail")
    @SysOperaLog(moduleName = "用户模块", methodName = "发送忘记密码邮箱验证码")
    fun forgetPasswordSendEmail(@RequestParam account: String): OutResponse<Any> {
        val email = UserModel().forgetPasswordSendEmail(account)
        return OutResponse(
            "000000",
            "",
            email
        )
    }

    // 发送绑定邮箱验证码
    @GetMapping("sendBindEmail")
    @SysOperaLog(moduleName = "用户模块", methodName = "发送绑定邮箱验证码")
    fun sendBindEmail(@RequestParam email: String): OutResponse<Any> {
        UserModel().sendBindEmail(email)
        return OutResponse(
            "000000",
            "",
            email
        )
    }

    // 发送注册邮箱验证码
    @GetMapping("sendRegEmail")
    @SysOperaLog(moduleName = "用户模块", methodName = "发送注册邮箱验证码")
    fun sendRegEmail(@RequestParam email: String): OutResponse<Any> {
        UserModel().sendRegEmail(email)
        return OutResponse(
            "000000",
            "",
            email
        )
    }

    // 用户邮件重置密码
    @SysOperaLog(moduleName = "用户模块", methodName = "用户重置密码")
    @GetMapping("/resetPasswordByEmail/{uuid}", produces = ["text/html;charset=utf-8"])
    fun resetPasswordByEmail(@PathVariable("uuid") uuid: String): String {
        return UserModel().resetPasswordByEmail(uuid)
    }

    // 用户修改密码
    @GetMapping("changePassword")
    @NeedToken
    @SysOperaLog(moduleName = "用户模块", methodName = "修改密码")
    fun changePassword(
        @RequestAttribute uid: String,
        @RequestParam oldPassword: String,
        @RequestParam newPassword: String
    ): OutResponse<Any> {
        val user = StaticBean.userRepository.findFirstByUserId(uid)
        val passwordCheck = PasswordUtil.verify(oldPassword, user.password)
        if (passwordCheck) {
            user.password = PasswordUtil.generate(newPassword)
            StaticBean.userRepository.save(user)
            return OutResponse(
                "000000",
                "密码修改成功！",
                ""
            )
        } else {
            return OutResponse(
                "000001",
                "原密码输入错误！",
                ""
            )
        }
    }

    // 用户修改邮箱
    @GetMapping("changeEmail")
    @NeedToken
    @SysOperaLog(moduleName = "用户模块", methodName = "修改邮箱")
    fun changeEmail(
        @RequestAttribute uid: String,
        @RequestParam email: String,
        @RequestParam code: String,
    ): OutResponse<Any> {
        // 检测code是否正确
        if (!StaticBean.redisService.hasKey("bigPlatform_bindEmail_$email")) {
            throw CommonException("000001", "邮箱验证码已失效！")
        }
        val redisCode = StaticBean.redisService.get("bigPlatform_bindEmail_$email") as String
        if (redisCode != code) {
            throw CommonException("000001", "邮箱验证码不正确！")
        }
        StaticBean.redisService.set("bigPlatform_bindEmail_$email", "", 1)
        val user = StaticBean.userRepository.findFirstByUserId(uid)
        user.email = email // todo 是否同一学校不能重复
        StaticBean.userRepository.save(user)
        return OutResponse(
            "000000",
            "邮箱修改成功！",
            ""
        )
    }


    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.userRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["userId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.userRepository, queryObject.toJSONString(), pageable)
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam userId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.userRepository, "userId", userId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam userId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.userRepository, "userId", userId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.userRepository,
                UserEntity(),
                "userId",
                infoObject
            )
        )
    }

    // 转移学生到新班级
    @GetMapping("/transferStudentToNewClazz")
    @NeedAdminToken
    fun transferStudentToNewClazz(@RequestParam userId: String, @RequestParam clazzId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().transferStudentToNewClazz(userId, clazzId)
        )
    }

    // 转移教师到新学院
    @GetMapping("/transferTeacherToNewCollege")
    @NeedAdminToken
    fun transferTeacherToNewCollege(
        @RequestParam userId: String,
        @RequestParam collegeId: String
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            UserModel().transferTeacherToNewCollege(userId, collegeId)
        )
    }

    // 获取搜索和筛选用的教师列表
    @PostMapping("searchTeacherList")
    fun getSearchTeacherList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val name = queryObject.getString("name")
        val account = queryObject.getString("account")
        // 获取所有教师列表 聚合搜索
        var matchOperation = Aggregation.match(Criteria.where("role").`is`("teacher"))
        if (name != null && account == null) {
            matchOperation = Aggregation.match(
                Criteria.where("name").regex(name).and("role").`is`("teacher")
            )
        }
        if (name == null && account != null) {
            matchOperation = Aggregation.match(
                Criteria.where("account").regex(account).and("role").`is`("teacher")
            )
        }
        if (name != null && account != null) {
            val c = Criteria()
            c.orOperator(
                Criteria.where("name").regex(name).and("role").`is`("teacher"),
                Criteria.where("account").regex(name).and("role").`is`("teacher")
            )
            matchOperation = Aggregation.match(c)
        }
        // join
        val lookupCollege: LookupOperation =
            Aggregation.lookup("college", "collegeId", "collegeId", "collegeEntity")
        val aggregation = Aggregation.newAggregation(lookupCollege, matchOperation)
        val allList =
            StaticBean.mongoTemplate.aggregate(aggregation, "user", UserEntity::class.java).mappedResults
        return OutResponse(
            "000000",
            "",
            allList
        )
    }

    // 获取搜索和筛选用的学生列表
    @PostMapping("searchStudentList")
    fun getSearchStudentList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val name = queryObject.getString("name")
        val account = queryObject.getString("account")
        // 获取所有学生列表 聚合搜索
        var matchOperation = Aggregation.match(Criteria.where("role").`is`("student"))
        if (name != null && account == null) {
            matchOperation = Aggregation.match(
                Criteria.where("name").regex(name).and("role").`is`("student")
            )
        }
        if (name == null && account != null) {
            matchOperation = Aggregation.match(
                Criteria.where("account").regex(account).and("role").`is`("student")
            )
        }
        if (name != null && account != null) {
            val c = Criteria()
            c.orOperator(
                Criteria.where("name").regex(name).and("role").`is`("student"),
                Criteria.where("account").regex(name).and("role").`is`("student")
            )
            matchOperation = Aggregation.match(c)
        }
        // join
        val lookupClazz: LookupOperation =
            Aggregation.lookup("clazz", "clazzId", "clazzId", "clazzEntity")
        val lookupGrade: LookupOperation =
            Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
        val lookupMajor: LookupOperation =
            Aggregation.lookup("major", "clazzEntity.majorId", "majorId", "majorEntity")
        val aggregation = Aggregation.newAggregation(lookupClazz, lookupGrade, matchOperation, lookupMajor)
        println(aggregation.toString())
        val allList =
            StaticBean.mongoTemplate.aggregate(aggregation, "user", UserEntity::class.java).mappedResults
        return OutResponse(
            "000000",
            "",
            allList
        )
    }
}