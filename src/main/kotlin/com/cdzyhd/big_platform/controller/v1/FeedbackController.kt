package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.entity.CourseRecordEntity
import com.cdzyhd.big_platform.entity.FeedbackEntity
import com.cdzyhd.big_platform.exception.CommonException
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import java.util.*


@RestController
@RequestMapping(value = ["/v1/feedback/"])
class FeedbackController {
    // 用户提交反馈
    @PostMapping("post")
    @NeedToken
    fun postFeedback(@RequestAttribute uid: String, @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val content = infoObject.getString("content")
        if (content.length > 200) {
            throw CommonException("000001", "最多输入200字！")
        }
        val date24 = Date().time - 86400000
        val gte = "$" + "gte"
        // todo 字数检测、同一天只能反馈一次
        val checkQuery = """
            {
                "userId":"$uid",
                "createTime":{
                    "$gte":$date24
                }
            }
        """.trimIndent()
        val list = StaticBean.feedbackRepository.getList(Document.parse(checkQuery))
        if (list.size > 0) {
            throw CommonException("000001", "24小时内只能提交一次反馈信息，请稍后再试！")
        }
        val feedback = FeedbackEntity()
        feedback.userId = uid
        feedback.content = content
        StaticBean.feedbackRepository.save(feedback)
        return OutResponse(
            "000000",
            "提交成功",
            ""
        )
    }

    // 获取列表-不分页
    @PostMapping("list")
    @NeedAdminToken
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.feedbackRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    @NeedAdminToken
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["feedbackId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val account = queryObject.getString("account")
        val role = queryObject.getString("role")
        // 查询
        val criteria = Criteria("")
        if (account != null) {
            criteria.and("userEntity.account").regex(account)
        }
        if (role != null) {
            criteria.and("userEntity.role").`is`(role)
        }


        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")
        val countAggregation = Aggregation.count().`as`("count")
        val sortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        var aggregation = Aggregation.newAggregation(lookupUser, matchOperation, countAggregation)
        val countResult = StaticBean.mongoTemplate.aggregate(
            aggregation,
            "feedback",
            HashMap::class.java
        ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页 如果有传过来的就用
        var skipNumber = pageable.pageNumber * pageSize.toLong()
        var limitNumber = pageSize.toLong()
        if (queryObject.containsKey("skipNumber")) {
            skipNumber = queryObject.getLong("skipNumber")
        }
        if (queryObject.containsKey("limitNumber")) {
            limitNumber = queryObject.getLong("limitNumber")
        }
        val skipOperation = Aggregation.skip(skipNumber)
        val limitOperation = Aggregation.limit(limitNumber)
        aggregation = Aggregation.newAggregation(
            lookupUser,
            matchOperation,
            skipOperation,
            sortAggregation, limitOperation
        )

        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "feedback",
                FeedbackEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }


    // 获取一个
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam feedbackId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.feedbackRepository, "feedbackId", feedbackId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam feedbackId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.feedbackRepository, "feedbackId", feedbackId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.feedbackRepository,
                FeedbackEntity(),
                "feedbackId",
                infoObject
            )
        )
    }
}