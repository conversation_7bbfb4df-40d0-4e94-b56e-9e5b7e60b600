package com.cdzyhd.big_platform.controller.v1

import cn.hutool.crypto.SecureUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.model.CourseScoreModel
import com.cdzyhd.big_platform.model.OpenModel
import com.cdzyhd.big_platform.model.RedisConfigModel
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletResponse

@RestController
@RequestMapping("/v1/api/open")
class TestController {


    @GetMapping("/test")
    fun test1(httpServletResponse: HttpServletResponse, @RequestParam ticket: String): String {
        val signatureRight = SecureUtil.md5(ticket + "2395693058625536" + "b1d345b7173e428380f79e1bb1e715c6")
        return signatureRight
    }

    @GetMapping("/test2")
    fun test2(): OutResponse<Any> {
        val systemConfig = StaticBean.redisService.hget("config_bigPlatform", "systemConfig") as String
        val systemConfigObject = JSONObject.parseObject(systemConfig)
        return OutResponse("000000", "上传实验结果成功", systemConfigObject)
    }

    /**
     * 课程实验结果数据回传
     */
    @PostMapping("/dataupload")
    fun dataUpload(
        @RequestParam access_token: String,
        @RequestBody info: String,
    ): OutResponse<Any> {
        return OutResponse("000000", "上传实验结果成功", OpenModel().dataUpload(access_token, info))
    }

}