package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.CourseTaskEntity
import com.cdzyhd.big_platform.model.CourseTaskModel
import com.esotericsoftware.reflectasm.MethodAccess
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/courseTask/"])
class CourseTaskController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val result = StaticBean.courseTaskRepository.getList(Document.parse(query))
        for (li in result) {
            li.collegeName = StaticBean.collegeRepository.findFirstByCollegeId(li.collegeId).name
        }
        return OutResponse(
            "000000",
            "",
            result
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseTaskId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val result = StaticBean.courseTaskRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        val contentList = result.content
        for (li in contentList) {
            li.collegeName = StaticBean.collegeRepository.findFirstByCollegeId(li.collegeId).name
        }
        return OutResponse(
            "000000",
            "",
            result
        )
    }

    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam courseTaskId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseTaskRepository.findFirstByCourseTaskId(courseTaskId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam courseTaskId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseTaskRepository.deleteByCourseTaskId(courseTaskId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        if (infoObject.containsKey("courseTaskId")) {// 如果是编辑
            val courseTaskId = infoObject.getString("courseTaskId")
            val taskOld = StaticBean.courseTaskRepository.findFirstByCourseTaskId(courseTaskId)
            // 是否限制登录次数逻辑
            val limitLogin = infoObject.getBoolean("limitLogin")
            if (limitLogin == true) { // 如果要限制登录
                val limitNumber = infoObject.getInteger("limitNumber")
                if (taskOld.limitNumber != limitNumber) { // 如果限制登录次数发生了改变
                    // 所有关联的任务记录都改变
                    val limitUpdate = Update()
                    limitUpdate.set("limitLogin", true)
                    limitUpdate.set("limitNumber", limitNumber)
                    limitUpdate.set("leftLimitNumber", limitNumber)
                    val limitQuery =
                        Query(
                            Criteria("courseTaskId").`is`(courseTaskId)
                        )
                    StaticBean.mongoTemplate.updateMulti(limitQuery, limitUpdate, "course_record")
                } else { // 限制登录次数没有发生改变
                    if (taskOld.limitLogin == false) { // 只是由不限制变为限制
                        // 所有关联的任务记录都改变
                        val limitUpdate = Update()
                        limitUpdate.set("limitLogin", true) // 所有任务记录都标记为限制
                        val limitQuery =
                            Query(
                                Criteria("courseTaskId").`is`(courseTaskId)
                            )
                        StaticBean.mongoTemplate.updateMulti(limitQuery, limitUpdate, "course_record")
                    }
                }
            }
            if (taskOld.limitLogin == true && limitLogin == false) {// 如果由限制变为不限制
                infoObject["limitNumber"]=taskOld.limitNumber // 变为否时修改的数量不生效，改为原先的
                // 所有关联的任务记录都改变
                val limitUpdate = Update()
                limitUpdate.set("limitLogin", false) // 所有任务记录都标记为不限制
                val limitQuery =
                    Query(
                        Criteria("courseTaskId").`is`(courseTaskId)
                    )
                StaticBean.mongoTemplate.updateMulti(limitQuery, limitUpdate, "course_record")
            }
        }
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.courseTaskRepository,
                CourseTaskEntity(),
                "courseTaskId",
                infoObject
            )
        )
    }

    // 更新任务的教学班信息
    @PostMapping("teachingClazzInfo")
    @NeedAdminToken
    fun updateTeachingClazzInfo(@RequestParam courseTaskId: String, @RequestBody info: String): OutResponse<Any> {
        val teachingClazzInfo = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CourseTaskModel().updateTeachingClazzInfo(courseTaskId, teachingClazzInfo)
        )
    }
}