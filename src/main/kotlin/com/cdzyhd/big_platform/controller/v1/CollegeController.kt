package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.CollegeEntity
import com.cdzyhd.big_platform.model.CollegeModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping(value = ["/v1/college/"])
class CollegeController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.collegeRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["collegeId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList = StaticBean.collegeRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        // 遍历获取每个学院的教师人数
        for (college in pageList.content) {
            college.teacherNumber = StaticBean.userRepository.countByCollegeId(college.collegeId)
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam collegeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.collegeRepository, "collegeId", collegeId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam collegeId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.collegeRepository, "collegeId", collegeId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.collegeRepository,
                CollegeEntity(),
                "collegeId",
                infoObject
            )
        )
    }

    // 批量导入教师-学院内
    @PostMapping("/importTeachersByCollegeId")
    @NeedAdminToken
    fun importStudentsByCollegeId(@RequestParam multipartFile: MultipartFile, @RequestParam collegeId: String): OutResponse<Any> {
        return OutResponse("000000", "导入成功", CollegeModel().importTeachersByCollegeId(multipartFile,collegeId))
    }

    // 批量导入教师-学院外
    @PostMapping("/importTeachers")
    @NeedAdminToken
    fun importStudents(@RequestParam multipartFile: MultipartFile): OutResponse<Any> {
        return OutResponse("000000", "导入成功", CollegeModel().importTeachers(multipartFile))
    }
}