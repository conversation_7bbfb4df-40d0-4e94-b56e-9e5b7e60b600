package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.FileEntity
import com.cdzyhd.big_platform.model.FileModel
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping(value = ["/v1/file/"])
class FileController {
    // 上传单个文件
    @PostMapping("uploadOne")
    @NeedAdminToken
    fun uploadOne(
        @RequestParam(required = true) file: MultipartFile,
        @RequestParam(required = true) name: String,
        @RequestParam(required = true) byteSize: Long,
        @RequestParam(required = true) fileType: String,
        @RequestParam(required = true) lastModified: Long,
        @RequestParam(required = false) info: String
    ): OutResponse<Any> {
        val fileEntity = FileEntity()
        fileEntity.fileType = fileType
        fileEntity.name = name
        fileEntity.byteSize = byteSize
        fileEntity.lastModified = lastModified
        fileEntity.info = JSONObject.parseObject(info)
        FileModel().uploadOne(file, fileEntity)
        return OutResponse(
            "000000",
            "上传文件成功",
            fileEntity
        )
    }

    // 删除一个文件
    @DeleteMapping("/fileOne")
    @NeedAdminToken
    fun deleteFileOne(@RequestParam fileId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            FileModel().deleteFileOne(fileId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.fileRepository,
                FileEntity(),
                "fileId",
                infoObject
            )
        )
    }
}