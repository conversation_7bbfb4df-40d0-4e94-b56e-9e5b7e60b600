package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.CourseScoreEntity
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/courseScore/"])
class CourseScoreController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseScoreRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseScoreId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.courseScoreRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam courseScoreId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.courseScoreRepository, "courseScoreId", courseScoreId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam courseScoreId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.courseScoreRepository, "courseScoreId", courseScoreId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.courseScoreRepository,
                CourseScoreEntity(),
                "courseScoreId",
                infoObject
            )
        )
    }
}