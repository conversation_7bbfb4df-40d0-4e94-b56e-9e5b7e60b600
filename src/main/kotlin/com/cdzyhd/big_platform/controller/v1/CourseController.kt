package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.entity.CourseEntity
import com.cdzyhd.big_platform.model.CourseModel
import com.cdzyhd.big_platform.model.StatisticModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(value = ["/v1/course/"])
class CourseController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val list = StaticBean.courseRepository.getList(Document.parse(query))
        val statisticModel = StatisticModel()
        for (li in list) {
            li.commentNumber = statisticModel.getRedisValue(li.courseId, "commentNumber") as Int
            li.userNumber = statisticModel.getRedisValue(li.courseId, "userNumber") as Int
            li.collegeName = StaticBean.collegeRepository.findFirstByCollegeId(li.collegeId).name
        }
        return OutResponse(
            "000000",
            "",
            list
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.courseRepository, queryObject.toJSONString(), pageable)
        )
    }


    // 获取一个-后台
    @GetMapping("")
    @NeedAdminToken
    fun getOne(@RequestParam courseId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseRepository.findFirstByCourseId(courseId)
        )
    }

    // 获取课程appSecret
    @GetMapping("appSecret")
    @NeedAdminToken
    fun getOneCourseAppSecret(@RequestParam courseId: String): OutResponse<Any> {
        // todo 安全-请求记录
        return OutResponse(
            "000000",
            "",
            StaticBean.courseRepository.findFirstByCourseId(courseId).appSecret
        )
    }

    // 获取课程详情-前台
    @GetMapping("infoByFront")
    fun getOneInfoByFront(@RequestParam courseId: String): OutResponse<Any> {
        val course = StaticBean.courseRepository.findFirstByCourseId(courseId)
        // 获取关联信息
        course.courseSubjectEntity.add(StaticBean.courseSubjectRepository.findFirstByCourseSubjectId(course.courseSubjectId))
        course.collegeEntity.add(StaticBean.collegeRepository.findFirstByCollegeId(course.collegeId))
        course.commentNumber = StatisticModel().getRedisValue(courseId, "commentNumber") as Int
        course.userNumber = StatisticModel().getRedisValue(courseId, "userNumber") as Int
        if (course == null) {
            return OutResponse(
                "000001",
                "获取课程详情失败!",
                ""
            )
        }
        return OutResponse(
            "000000",
            "",
            course
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam courseId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseRepository.deleteByCourseId(courseId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        if (infoObject.containsKey("courseId")) {// 编辑
            return OutResponse(
                "000000",
                "",
                CommonMongoEntityModel.addOrEdit(
                    StaticBean.courseRepository,
                    CourseEntity(),
                    "courseId",
                    infoObject
                )
            )
        } else {// 新增
            return OutResponse(
                "000000",
                "",
                CourseModel().addOneCourse(infoObject)
            )
        }
    }
}