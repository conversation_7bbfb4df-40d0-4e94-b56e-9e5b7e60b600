package com.cdzyhd.big_platform.controller.v1

import cn.hutool.core.io.FileUtil
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.HelpQuestionEntity
import com.cdzyhd.big_platform.model.DatabaseModel
import org.bson.Document
import org.springframework.beans.factory.annotation.Value
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.scheduling.annotation.Async
import org.springframework.web.bind.annotation.*
import java.io.File
import java.io.IOException
import java.nio.file.Path
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.collections.HashMap


@RestController
@RequestMapping(value = ["/v1/database/"])
class DatabaseController {
    // 数据库备份文件夹
    @Value("\${bigPlatform.backupFolder}")
    var backupFolder: String? = null

    // 数据库备份sh
    @Value("\${bigPlatform.backupSh}")
    var backupSh: String? = null

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        val list = FileUtil.ls(backupFolder)
        val files = ArrayList<HashMap<String, Any>>()
        for (li in list) {
            val one = HashMap<String, Any>()
            one["name"] = li.name
            one["createTime"] = li.lastModified()
            files.add(one)
        }
        // 获取一个目录下所有文件列表
        return OutResponse(
            "000000",
            "",
            files
        )
    }

    // 新增备份
    @GetMapping("backupNow")
    @NeedAdminToken
    fun backupNow(): OutResponse<Any> {
        val result = DatabaseModel().backupNow(backupSh!!)
        if (result) {
            return OutResponse(
                "000000",
                "备份成功",
                ""
            )
        } else {
            return OutResponse(
                "000001",
                "备份失败",
                ""
            )
        }
    }
}