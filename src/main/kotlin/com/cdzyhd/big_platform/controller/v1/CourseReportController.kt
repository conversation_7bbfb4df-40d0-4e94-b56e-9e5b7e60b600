package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.entity.CourseRecordEntity
import com.cdzyhd.big_platform.entity.CourseReportEntity
import com.cdzyhd.big_platform.entity.TeachingClazzEntity
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.model.ClazzModel
import com.cdzyhd.big_platform.model.CourseRecordModel
import com.cdzyhd.big_platform.model.CourseReportModel
import com.cdzyhd.big_platform.model.TeachingClazzModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.and
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.util.*
import javax.servlet.http.HttpServletRequest


@RestController
@RequestMapping(value = ["/v1/courseReport/"])
class CourseReportController {
    // 学生填写实验报告
    @PostMapping("fillReport")
    @NeedToken
    fun fillReport(
        httpServletRequest: HttpServletRequest,
        @RequestAttribute uid: String,
        @RequestParam courseRecordId: String,
        @RequestBody content: String
    ): OutResponse<Any> {
        val courseRecord = StaticBean.courseRecordRepository.findFirstByCourseRecordId(courseRecordId)
        if (!courseRecord.userId.equals(uid)) {
            return OutResponse(
                "000001",
                "非法，不是自己的实验记录！",
                ""
            )
        }
        if (courseRecord.reportCorrected) {
            return OutResponse(
                "000001",
                "教师已批改报告，不能再次修改！",
                ""
            )
        }
        // 如果报告已经提交 就修改报告
        var report = StaticBean.courseReportRepository.findFirstByCourseRecordId(courseRecordId)
        if (courseRecord.reportFilled) {
            report.editTime = Date().time
            report.content = content
        } else {// 如果尚未提交
            report = CourseReportEntity()
            report.content = content
            report.courseRecordId = courseRecordId
            courseRecord.reportFilled = true
        }
        StaticBean.courseReportRepository.save(report)
        StaticBean.courseRecordRepository.save(courseRecord)
        return OutResponse(
            "000000",
            "",
            true
        )
    }

    // 获取某个记录的报告
    @GetMapping("oneCourseRecordReport")
    @NeedToken
    fun getOneCourseRecordReport(
        @RequestParam courseRecordId: String
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseReportRepository.findFirstByCourseRecordId(courseRecordId)
        )
    }

    // 教师批改某个实验报告
    @PostMapping("teacherCorrectReport")
    @NeedAdminToken
    fun teacherCorrectReport(
        httpServletRequest: HttpServletRequest,
        @RequestParam courseRecordId: String,
        @RequestParam score: Double,
        @RequestBody comment: String
    ): OutResponse<Any> {
        val teacherId = httpServletRequest.getAttribute("adminUserId") as String
        val commentObject = JSONObject.parseObject(comment)
        return OutResponse(
            "000000",
            "",
            CourseReportModel().teacherCorrectReport(courseRecordId, score, teacherId, commentObject)
        )
    }


    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseReportRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseReportId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.courseReportRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }

    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam courseReportId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseReportRepository.findFirstByCourseReportId(courseReportId)
        )
    }
}