package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.entity.BaseNewsEntity
import com.cdzyhd.big_platform.model.BaseNewsModel
import com.cdzyhd.big_platform.a.StaticBean
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

/**
 * 基地新闻控制器
 * 提供基地新闻相关的API接口
 */
@RestController
@RequestMapping(value = ["/v1/baseNews/"])
class BaseNewsController {

    // 获取新闻列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.baseNewsRepository.getList(Document.parse(query))
        )
    }

    // 获取新闻列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 20,
            sort = ["publishTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.baseNewsRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 获取一个新闻详情
    @GetMapping("")
    fun getOne(@RequestParam newsId: String): OutResponse<Any> {
        println(newsId)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.baseNewsRepository,
                "newsId",
                newsId
            )
        )
    }

    // 删除一个新闻
    @DeleteMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "基地新闻", methodName = "删除新闻")
    fun deleteOne(@RequestParam newsId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.baseNewsRepository,
                "newsId",
                newsId
            )
        )
    }

    // 新增或修改新闻
    @PostMapping("")
    @NeedAdminToken
    @SysOperaLog(moduleName = "基地新闻", methodName = "新增或修改新闻")
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.baseNewsRepository,
                BaseNewsEntity(),
                "newsId",
                infoObject
            )
        )
    }

    // 获取新闻轮播列表
    @GetMapping("carousel")
    fun getCarouselList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取轮播新闻成功",
            BaseNewsModel.getCarouselNewsList()
        )
    }

    // 获取热门新闻列表
    @GetMapping("hot")
    fun getHotList(@RequestParam(defaultValue = "10") limit: Int): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取热门新闻成功",
            BaseNewsModel.getHotNewsList(limit)
        )
    }

    // 获取置顶新闻列表
    @GetMapping("top")
    fun getTopList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取置顶新闻成功",
            BaseNewsModel.getTopNewsList()
        )
    }

    // 根据分类获取新闻
    @GetMapping("category")
    fun getByCategory(
        @RequestParam category: String,
        @RequestParam(defaultValue = "10") limit: Int
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取分类新闻成功",
            BaseNewsModel.getNewsByCategory(category, limit)
        )
    }

    // 搜索新闻
    @PostMapping("search")
    fun searchNews(
        @RequestBody searchParams: String,
        @PageableDefault(
            value = 12,
            sort = ["publishTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val params = JSONObject.parseObject(searchParams)
        val keyword = params.getString("keyword") ?: ""
        return OutResponse(
            "000000",
            "搜索新闻成功",
            BaseNewsModel.searchNews(keyword, pageable)
        )
    }

    // 获取相关新闻
    @GetMapping("related")
    fun getRelatedNews(
        @RequestParam newsId: String,
        @RequestParam category: String,
        @RequestParam(defaultValue = "5") limit: Int
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取相关新闻成功",
            BaseNewsModel.getRelatedNews(newsId, category, limit)
        )
    }

    // 增加浏览量
    @PostMapping("view")
    fun increaseViews(@RequestParam newsId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "浏览量增加成功",
            BaseNewsModel.increaseViews(newsId)
        )
    }

    // 发布新闻
    @PostMapping("publish")
    @NeedAdminToken
    @SysOperaLog(moduleName = "基地新闻", methodName = "发布新闻")
    fun publishNews(@RequestParam newsId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "新闻发布成功",
            BaseNewsModel.publishNews(newsId)
        )
    }

    // 撤回新闻
    @PostMapping("unpublish")
    @NeedAdminToken
    @SysOperaLog(moduleName = "基地新闻", methodName = "撤回新闻")
    fun unpublishNews(@RequestParam newsId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "新闻撤回成功",
            BaseNewsModel.unpublishNews(newsId)
        )
    }

    // 设置热门新闻
    @PostMapping("setHot")
    @NeedAdminToken
    @SysOperaLog(moduleName = "基地新闻", methodName = "设置热门新闻")
    fun setHotNews(
        @RequestParam newsId: String,
        @RequestParam isHot: Boolean
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "设置热门状态成功",
            BaseNewsModel.setHotNews(newsId, isHot)
        )
    }

    // 设置置顶新闻
    @PostMapping("setTop")
    @NeedAdminToken
    @SysOperaLog(moduleName = "基地新闻", methodName = "设置置顶新闻")
    fun setTopNews(
        @RequestParam newsId: String,
        @RequestParam isTop: Boolean
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "设置置顶状态成功",
            BaseNewsModel.setTopNews(newsId, isTop)
        )
    }

    // 获取新闻统计信息
    @GetMapping("statistics")
    @NeedAdminToken
    fun getStatistics(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "获取统计信息成功",
            BaseNewsModel.getNewsStatistics()
        )
    }
}