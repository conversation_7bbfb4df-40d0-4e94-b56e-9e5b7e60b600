package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.entity.*
import com.cdzyhd.big_platform.model.IndustryModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

/**
 * 产教融合接口控制器
 */
@RestController
@RequestMapping(value = ["/v1/industry/"])
class IndustryController {

    // ============== 轮播图相关接口 ==============

    // 获取轮播图列表-不分页
    @PostMapping("banner/list")
    fun getBannerList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.industryBannerRepository.getList(Document.parse(query))
        )
    }

    // 获取轮播图列表-分页
    @PostMapping("banner/pageList")
    fun getBannerPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 20,
            sort = ["sort", "createTime"],
            direction = Sort.Direction.ASC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(
                StaticBean.industryBannerRepository,
                queryObject.toJSONString(),
                pageable
            )
        )
    }

    // 获取激活状态的轮播图列表（前台使用）
    @GetMapping("banner/active")
    fun getActiveBannerList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getBannerList()
        )
    }

    // 获取一个轮播图
    @GetMapping("banner")
    fun getBannerOne(@RequestParam bannerId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(
                StaticBean.industryBannerRepository,
                "bannerId",
                bannerId
            )
        )
    }

    // 删除一个轮播图
    @DeleteMapping("banner")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "删除轮播图")
    fun deleteBannerOne(@RequestParam bannerId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.industryBannerRepository,
                "bannerId",
                bannerId
            )
        )
    }

    // 新增或修改轮播图
    @PostMapping("banner")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "新增或修改轮播图")
    fun addOrEditBanner(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.industryBannerRepository,
                IndustryBannerEntity(),
                "bannerId",
                infoObject
            )
        )
    }

    // ============== 企业相关接口 ==============

    // 获取企业列表-不分页
    @PostMapping("enterprise/list")
    fun getEnterpriseList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.industryEnterpriseRepository.getList(Document.parse(query))
        )
    }

    // 获取企业列表-分页
    @PostMapping("enterprise/pageList")
    fun getEnterprisePageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 12,
            sort = ["sort", "createTime"],
            direction = Sort.Direction.ASC
        ) pageable: Pageable
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getEnterprisePageList(query, pageable)
        )
    }

    // 获取企业简化列表（前台首页使用）
    @GetMapping("enterprise/simple")
    fun getSimpleEnterpriseList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getEnterpriseList()
        )
    }

    // 获取一个企业
    @GetMapping("enterprise")
    fun getEnterpriseOne(@RequestParam enterpriseId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getEnterpriseDetail(enterpriseId)
        )
    }

    // 删除一个企业
    @DeleteMapping("enterprise")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "删除企业")
    fun deleteEnterpriseOne(@RequestParam enterpriseId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.industryEnterpriseRepository,
                "enterpriseId",
                enterpriseId
            )
        )
    }

    // 新增或修改企业
    @PostMapping("enterprise")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "新增或修改企业")
    fun addOrEditEnterprise(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.industryEnterpriseRepository,
                IndustryEnterpriseEntity(),
                "enterpriseId",
                infoObject
            )
        )
    }

    // ============== 招聘信息相关接口 ==============

    // 获取招聘信息列表-不分页
    @PostMapping("job/list")
    fun getJobList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.industryJobRepository.getList(Document.parse(query))
        )
    }

    // 获取招聘信息列表-分页
    @PostMapping("job/pageList")
    fun getJobPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 12,
            sort = ["publishTime"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getJobPageList(query, pageable)
        )
    }

    // 获取招聘信息简化列表（前台首页使用）
    @GetMapping("job/simple")
    fun getSimpleJobList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getJobList()
        )
    }

    // 获取一个招聘信息
    @GetMapping("job")
    fun getJobOne(@RequestParam jobId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getJobDetail(jobId)
        )
    }

    // 删除一个招聘信息
    @DeleteMapping("job")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "删除招聘信息")
    fun deleteJobOne(@RequestParam jobId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.industryJobRepository,
                "jobId",
                jobId
            )
        )
    }

    // 新增或修改招聘信息
    @PostMapping("job")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "新增或修改招聘信息")
    fun addOrEditJob(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.industryJobRepository,
                IndustryJobEntity(),
                "jobId",
                infoObject
            )
        )
    }

    // ============== 学生相关接口 ==============

    // 获取学生列表-不分页
    @PostMapping("student/list")
    fun getStudentList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.industryStudentRepository.getList(Document.parse(query))
        )
    }

    // 获取学生列表-分页
    @PostMapping("student/pageList")
    fun getStudentPageList(
        @RequestBody query: String,
        @PageableDefault(
            value = 12,
            sort = ["sort", "createTime"],
            direction = Sort.Direction.ASC
        ) pageable: Pageable
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getStudentPageList(query, pageable)
        )
    }

    // 获取学生简化列表（前台首页使用）
    @GetMapping("student/simple")
    fun getSimpleStudentList(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getStudentList()
        )
    }

    // 获取一个学生
    @GetMapping("student")
    fun getStudentOne(@RequestParam studentId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getStudentDetail(studentId)
        )
    }

    // 删除一个学生
    @DeleteMapping("student")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "删除学生")
    fun deleteStudentOne(@RequestParam studentId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(
                StaticBean.industryStudentRepository,
                "studentId",
                studentId
            )
        )
    }

    // 新增或修改学生
    @PostMapping("student")
    @NeedAdminToken
    @SysOperaLog(moduleName = "产教融合", methodName = "新增或修改学生")
    fun addOrEditStudent(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.industryStudentRepository,
                IndustryStudentEntity(),
                "studentId",
                infoObject
            )
        )
    }

    // ============== 统计数据接口 ==============

    // 获取产教融合统计数据
    @GetMapping("stats")
    fun getIndustryStats(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getIndustryStats()
        )
    }

    // 获取分类统计数据
    @GetMapping("stats/category")
    fun getCategoryStats(): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            IndustryModel().getCategoryStats()
        )
    }
}