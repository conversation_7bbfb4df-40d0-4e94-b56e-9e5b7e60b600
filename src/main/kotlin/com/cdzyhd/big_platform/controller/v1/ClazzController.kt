package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.entity.ClazzEntity
import com.cdzyhd.big_platform.model.ClazzModel
import com.cdzyhd.big_platform.model.TeachingClazzModel
import org.bson.Document
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.MongoTemplate
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile


@RestController
@RequestMapping(value = ["/v1/clazz/"])
class ClazzController {

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.clazzRepository.getList(Document.parse(query))
        )
    }

    // 获取搜索和筛选用的行政班级列表 通过名称搜索
    @PostMapping("searchList")
    fun getSearchList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val name = queryObject.getString("name")
        val clazzIds = queryObject.getJSONArray("clazzIds")
        // 获取所有行政班级列表 聚合搜索
        var matchOperation = Aggregation.match(Criteria.where("name").ne(null))
        if (name !== null) {
            matchOperation = Aggregation.match(
                Criteria().orOperator(
                    Criteria.where("name").regex(name),
                    Criteria.where("gradeEntity.name").regex(name),
                    Criteria.where("majorEntity.name").regex(name),
                )
            )
        }
        if (clazzIds !== null) {
            matchOperation = Aggregation.match(
                Criteria.where("clazzId").`in`(clazzIds)
            )
        }
        // join
        val lookupMajor: LookupOperation =
            Aggregation.lookup("major", "majorId", "majorId", "majorEntity")
        val lookupGrade: LookupOperation =
            Aggregation.lookup("grade", "gradeId", "gradeId", "gradeEntity")
        val aggregation = Aggregation.newAggregation(lookupMajor, lookupGrade, matchOperation)
        val allList =
            StaticBean.mongoTemplate.aggregate(aggregation, "clazz", ClazzEntity::class.java).mappedResults
        return OutResponse(
            "000000",
            "",
            allList
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["clazzId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList = StaticBean.clazzRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        // 遍历获取每个班级的学生人数
        for (clazz in pageList.content) {
            clazz.studentNumber = StaticBean.userRepository.countByClazzId(clazz.clazzId)
        }
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam clazzId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getOneById(StaticBean.clazzRepository, "clazzId", clazzId)
        )
    }

    // 删除一个
    @DeleteMapping("")
    @NeedAdminToken
    fun deleteOne(@RequestParam clazzId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.clazzRepository, "clazzId", clazzId)
        )
    }

    // 新增或修改
    @PostMapping("")
    @NeedAdminToken
    fun addOrEdit(@RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.addOrEdit(
                StaticBean.clazzRepository,
                ClazzEntity(),
                "clazzId",
                infoObject
            )
        )
    }

    // 批量导入学生-班级内
    @PostMapping("/importStudentsByClazzId")
    @NeedAdminToken
    fun importStudentsByClazzId(
        @RequestParam multipartFile: MultipartFile,
        @RequestParam clazzId: String
    ): OutResponse<Any> {
        return OutResponse("000000", "导入成功", ClazzModel().importStudentsByClazzId(multipartFile, clazzId))
    }

    // 批量导入学生-班级外
    @PostMapping("/importStudents")
    @NeedAdminToken
    fun importStudents(@RequestParam multipartFile: MultipartFile): OutResponse<Any> {
        return OutResponse("000000", "导入成功", ClazzModel().importStudents(multipartFile))
    }
}