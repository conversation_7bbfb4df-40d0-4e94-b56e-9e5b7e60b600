package com.cdzyhd.big_platform.controller.v1

import cn.hutool.crypto.SecureUtil
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.model.CourseScoreModel
import com.cdzyhd.big_platform.model.OpenModel
import com.cdzyhd.big_platform.model.RedisConfigModel
import com.cdzyhd.big_platform.model.StatisticModel
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpServletResponse

/**
 * 统计控制器
 */
@RestController
@RequestMapping("/v1/statistic")
class StatisticController {
    /**
     * 获取课程实时统计数据
     */
    @GetMapping("/courseRealTimeValue")
    fun test1(@RequestParam courseId: String): OutResponse<Any> {
        return OutResponse("000000", "上传实验结果成功", StatisticModel().getCourseRealTimeValue(courseId))
    }

    /**
     * 获取课程实时统计数据
     */
    @GetMapping("/last30DayScoreNumber")
    @NeedAdminToken
    fun getLast30DayScoreNumber(@RequestParam courseId: String): OutResponse<Any> {
        return OutResponse("000000", "上传实验结果成功", StatisticModel().getLast30DayScoreNumber(courseId))
    }
}