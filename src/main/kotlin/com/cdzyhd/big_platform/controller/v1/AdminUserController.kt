package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.base.common.util.PasswordUtil
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.model.AdminUserModel
import com.cdzyhd.big_platform.model.TokenModel
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/v1/adminUser/"])
class AdminUserController {
    @GetMapping("test")
    fun test() {
        val roles = ArrayList<String>()
        roles.add("administrator")
        roles.add("teacher")
    }

    // 获取某个平台的管理员用户信息
    @GetMapping("user")
    @NeedAdminToken
    fun getUserInfo(@RequestAttribute adminUserId: String, @RequestParam loginRole: String): OutResponse<Any> {
        return OutResponse("000000", "获取该用户信息成功", AdminUserModel().getUserInfo(adminUserId, loginRole))
    }

    // 创建管理员用户
    @PostMapping("user")
    @NeedAdminToken
    fun createAdminUser(
        @RequestParam username: String,
        @RequestParam phoneNumber: String,
        @RequestParam password: String,
        @RequestBody userInfo: String
    ): OutResponse<Any> {
        val userInfoObject = JSONObject.parseObject(userInfo)
        val adminUser = AdminUserModel().createAdminUser(username, password, userInfoObject)
        return OutResponse("000000", "", adminUser)
    }


    // 管理员登录并获取token
    @GetMapping("token")
    @SysOperaLog(moduleName = "管理员用户模块", methodName = "用户登录")
    fun adminLogin(
        @RequestParam account: String,
        @RequestParam password: String,
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            AdminUserModel().adminLogin(account, password, 1)
        )
    }

    // 管理员-刷新token
    @GetMapping("token/refresh")
    fun tokenRefresh(
        @RequestParam token: String,
        @RequestParam expireDay: Int
    ): OutResponse<Any> {
        return OutResponse("000000", "", TokenModel().refreshAdminToken(token, expireDay))
    }

    // 管理员token验证
    @GetMapping("token/check")
    fun tokenCheck(
        @RequestParam token: String
    ): OutResponse<Any> {
        return OutResponse("000000", "token检测成功", TokenModel().checkAdminToken(token))
    }

    // 获取管理员列表-不分页
    @PostMapping("user/list")
    @NeedAdminToken
    fun getAdminUserList(@RequestBody query: String): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getList(StaticBean.adminUserRepository, queryObject.toJSONString())
        )
    }

    // 获取管理员列表-分页
    @PostMapping("user/pageList")
    @NeedAdminToken
    fun getAdminUserPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["adminUserId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.getPageList(StaticBean.adminUserRepository, queryObject.toJSONString(), pageable)
        )
    }

    // 修改管理员信息
    @PutMapping("user")
    @NeedAdminToken
    fun editAdminUser(@RequestBody userInfo: String): OutResponse<Any> {
        val userObject = JSONObject.parseObject(userInfo)
        return OutResponse(
            "000000",
            "",
            AdminUserModel().addOrEdit(userObject)
        )
    }

    // 删除管理员-单个
    @DeleteMapping("user")
    @NeedAdminToken
    fun deleteOneAdminUser(@RequestParam adminUserId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CommonMongoEntityModel.deleteOneById(StaticBean.adminUserRepository, "adminUserId", adminUserId)
        )
    }

    // 删除管理员-多个
    @PostMapping("user/delete/multiple")
    @NeedAdminToken
    fun deleteAdminUserMultiple(@RequestBody userIdList: String): OutResponse<Any> {
        val userListArr = JSONArray.parseArray(userIdList)
        CommonMongoEntityModel.deleteMultipleById(StaticBean.adminUserRepository, "adminUserId", userListArr)
        return OutResponse("000000", "", "")
    }

    // 删除token
    @DeleteMapping("token")
    @NeedAdminToken
    fun removeToken(@RequestParam token: String): OutResponse<Any> {
        return OutResponse("000000", "", TokenModel().removeAdminToken(token))
    }

    // 用户修改密码
    @PostMapping("changePassword")
    @NeedAdminToken
    @SysOperaLog(moduleName = "管理员用户模块", methodName = "修改密码")
    fun changePassword(
        @RequestAttribute adminUserId: String,
        @RequestBody info: String
    ): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val role = infoObject.getString("role")
        val newPassword = infoObject.getString("newPassword")
        if (role == "administrator") {
            val user = StaticBean.adminUserRepository.findFirstByAdminUserId(adminUserId)
            user.password = PasswordUtil.generate(newPassword)
            StaticBean.adminUserRepository.save(user)
        }
        if (role.startsWith("teacher")) {
            val user = StaticBean.userRepository.findFirstByUserId(adminUserId)
            user.password = PasswordUtil.generate(newPassword)
            StaticBean.userRepository.save(user)
        }
        return OutResponse(
            "000000",
            "密码修改成功！",
            ""
        )
    }
}