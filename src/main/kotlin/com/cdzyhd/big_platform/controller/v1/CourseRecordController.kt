package com.cdzyhd.big_platform.controller.v1

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedAdminToken
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.entity.CourseCommentEntity
import com.cdzyhd.big_platform.entity.CourseRecordEntity
import com.cdzyhd.big_platform.entity.TeachingClazzEntity
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.model.ClazzModel
import com.cdzyhd.big_platform.model.CourseRecordModel
import com.cdzyhd.big_platform.model.StatisticModel
import com.cdzyhd.big_platform.model.TeachingClazzModel
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.mongodb.core.query.and
import org.springframework.data.web.PageableDefault
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.util.*
import kotlin.collections.HashMap


@RestController
@RequestMapping(value = ["/v1/courseRecord/"])
class CourseRecordController {
    // 重置某个记录的剩余登录次数
    @GetMapping("resetRecordLeftLimitNumber")
    @NeedAdminToken
    fun resetRecordLeftLimitNumber(
        @RequestParam courseRecordId: String
    ): OutResponse<Any> {
        val courseRecord = StaticBean.courseRecordRepository.findFirstByCourseRecordId(courseRecordId)
        val course = StaticBean.courseRepository.findFirstByCourseId(courseRecord.courseId)
        if(courseRecord.courseTaskId=="open"){// 课程记录
            courseRecord.limitLogin = course.limitLogin
            courseRecord.limitNumber = course.limitNumber
            courseRecord.leftLimitNumber = course.limitNumber
        }else{// 任务记录
            val courseTask=StaticBean.courseTaskRepository.findFirstByCourseTaskId(courseRecord.courseTaskId)
            courseRecord.limitLogin = courseTask.limitLogin
            courseRecord.limitNumber = courseTask.limitNumber
            courseRecord.leftLimitNumber = courseTask.limitNumber
        }
        StaticBean.courseRecordRepository.save(courseRecord)
        return OutResponse(
            "000000",
            "重置登录次数成功",
            true
        )
    }

    // 用户填写课程评价
    @PostMapping("writeCourseComment")
    @NeedToken
    fun getList(@RequestAttribute uid: String, @RequestBody info: String): OutResponse<Any> {
        val infoObject = JSONObject.parseObject(info)
        val courseRecordId = infoObject.getString("courseRecordId")
        val content = infoObject.getString("content")
        val record = StaticBean.courseRecordRepository.findFirstByCourseRecordId(courseRecordId)
        if (record == null) {
            return OutResponse(
                "000001",
                "",
                "未找到该课程记录！"
            )
        }
        if (record.userId != uid) {
            return OutResponse(
                "000001",
                "",
                "非法请求，不是自己的课程记录！"
            )
        }
        val user = StaticBean.userRepository.findFirstByUserId(uid)
        if (user.commentCourseId.contains(record.courseId)) {
            return OutResponse(
                "000001",
                "",
                "已经填写过该课程的评价！"
            )
        }
        // 保存标记
        user.commentCourseId.add(record.courseId)
        val userUpdate = Update()
        userUpdate.set("commentCourseId", user.commentCourseId)
        val userQuery = Query(Criteria("userId").`is`(user.userId))
        StaticBean.mongoTemplate.updateMulti(userQuery, userUpdate, "user")
        // 新增评价
        val courseComment = CourseCommentEntity()
        courseComment.courseId = record.courseId
        courseComment.content = content
        courseComment.userId = uid
        StaticBean.courseCommentRepository.save(courseComment)
        // 统计-增加课程评价数量
        StatisticModel().addOneCommentNumber(record.courseId)
        return OutResponse(
            "000000",
            "提交评价成功",
            true
        )
    }

    // 获取列表-不分页
    @PostMapping("list")
    fun getList(@RequestBody query: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseRecordRepository.getList(Document.parse(query))
        )
    }

    // 获取列表-分页
    @PostMapping("pageList")
    fun getPageList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseRecordId"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val pageList =
            StaticBean.courseRecordRepository.getPageList(Document.parse(queryObject.toJSONString()), pageable)
        return OutResponse(
            "000000",
            "",
            pageList
        )
    }


    // 获取一个
    @GetMapping("")
    fun getOne(@RequestParam courseRecordId: String): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            StaticBean.courseRecordRepository.findFirstByCourseRecordId(courseRecordId)
        )
    }

    // 获取某个教学班详情
    @GetMapping("oneTeachingClazzDetail")
    @NeedAdminToken
    fun getOneTeachingClazzDetail(
        @RequestParam courseTaskId: String,
        @RequestParam teacherId: String,
        @RequestParam teachingClazzId: String
    ): OutResponse<Any> {
        return OutResponse(
            "000000",
            "",
            CourseRecordModel().getOneTeachingClazzDetail(courseTaskId, teacherId, teachingClazzId)
        )
    }


    // 获取-某个教学班的学生课程记录列表
    @PostMapping("teachingClazzStudentList")
    @NeedAdminToken
    fun getStudentList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseRecordList"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val courseTaskId = queryObject.getString("courseTaskId")
        val teacherId = queryObject.getString("teacherId")
        val teachingClazzId = queryObject.getString("teachingClazzId")
        val account = queryObject.getString("account")
        val name = queryObject.getString("name")
        // 查询 学生教师且在教学班
        val criteria =
            Criteria("courseTaskId").`is`(courseTaskId).and("teacherId").`is`(teacherId).and("teachingClazzId")
                .`is`(teachingClazzId).and("deleted").`is`(0)
        if (name != null) {
            criteria.and("userEntity.name").regex(name)
        }
        if (account != null) {
            criteria.and("userEntity.account").regex(account)
        }

        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")
        val lookupClazz =
            Aggregation.lookup("clazz", "userEntity.clazzId", "clazzId", "clazzEntity")
        val lookupGrade =
            Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
        val lookupMajor =
            Aggregation.lookup("major", "clazzEntity.majorId", "majorId", "majorEntity")
        val countAggregation = Aggregation.count().`as`("count")
        var aggregation = Aggregation.newAggregation(lookupUser, matchOperation, countAggregation)
        val countResult =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                HashMap::class.java
            ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页
        val skipOperation = Aggregation.skip(pageable.pageNumber * pageSize)
        val limitOperation = Aggregation.limit(pageSize.toLong())
        aggregation = Aggregation.newAggregation(
            lookupUser,
            lookupClazz,
            lookupGrade,
            lookupMajor,
            matchOperation,
            skipOperation,
            limitOperation
        )
        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                CourseRecordEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }

    // 获取某个课程的成绩记录列表
    @PostMapping("oneCourseScoreList")
    @NeedAdminToken
    fun getOneCourseScoreList(
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseRecordList"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val courseId = queryObject.getString("courseId")
        val account = queryObject.getString("account")
        val name = queryObject.getString("name")
        val recordType = queryObject.getString("recordType")
        // 查询
        val criteria =
            Criteria("courseId").`is`(courseId).and("deleted").`is`(0).and("courseTaskId").`is`("open")
        if (name != null) {
            criteria.and("userEntity.name").regex(name)
        }
        if (account != null) {
            criteria.and("userEntity.account").regex(account)
        }
        if (recordType !== null) {
            if (recordType == "open") {
                criteria.and("courseTaskId").`is`("open")
            }
            if (recordType == "task") {
                criteria.and("courseTaskId").ne("open")
            }
        }

        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")
        val lookupClazz =
            Aggregation.lookup("clazz", "userEntity.clazzId", "clazzId", "clazzEntity")
        val lookupGrade =
            Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
        val lookupMajor =
            Aggregation.lookup("major", "clazzEntity.majorId", "majorId", "majorEntity")
        val countAggregation = Aggregation.count().`as`("count")
        val sortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        var aggregation = Aggregation.newAggregation(lookupUser, matchOperation, countAggregation)
        val countResult =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                HashMap::class.java
            ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页 如果有传过来的就用
        var skipNumber = pageable.pageNumber * pageSize.toLong()
        var limitNumber = pageSize.toLong()
        if (queryObject.containsKey("skipNumber")) {
            skipNumber = queryObject.getLong("skipNumber")
        }
        if (queryObject.containsKey("limitNumber")) {
            limitNumber = queryObject.getLong("limitNumber")
        }
        val skipOperation = Aggregation.skip(skipNumber)
        val limitOperation = Aggregation.limit(limitNumber)
        aggregation = Aggregation.newAggregation(
            lookupUser,
            lookupClazz,
            lookupGrade,
            lookupMajor,
            matchOperation,
            skipOperation,
            sortAggregation, limitOperation,
        )
        println(aggregation.toString())
        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                CourseRecordEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }

    // 获取某个用户的课程记录列表-前台-我的课程
    @PostMapping("oneUserRecordList")
    @NeedToken
    fun getOneUserRecordList(
        @RequestAttribute uid: String,
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseRecordList"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val courseId = queryObject.getString("courseId")
        val account = queryObject.getString("account")
        val name = queryObject.getString("name")
        val userId = uid
        // 查询
        val criteria = Criteria("deleted").`is`(0).and("courseTaskId").`is`("open")
        if (courseId != null) {
            criteria.and("courseId").`is`(courseId)
        }
        if (name != null) {
            criteria.and("userEntity.name").regex(name)
        }
        if (account != null) {
            criteria.and("userEntity.account").regex(account)
        }
        criteria.and("userEntity.userId").`is`(userId)

        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupCourse =
            Aggregation.lookup("course", "courseId", "courseId", "courseEntity")
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")
        val lookupClazz =
            Aggregation.lookup("clazz", "userEntity.clazzId", "clazzId", "clazzEntity")
        val lookupGrade =
            Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
        val lookupMajor =
            Aggregation.lookup("major", "clazzEntity.majorId", "majorId", "majorEntity")
        val lookupScore =
            Aggregation.lookup("course_score", "courseRecordId", "courseRecordId", "courseScoreEntity")
        val countAggregation = Aggregation.count().`as`("count")
        val sortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        var aggregation = Aggregation.newAggregation(lookupUser, matchOperation, countAggregation)
        val countResult =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                HashMap::class.java
            ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页 如果有传过来的就用
        var skipNumber = pageable.pageNumber * pageSize.toLong()
        var limitNumber = pageSize.toLong()
        if (queryObject.containsKey("skipNumber")) {
            skipNumber = queryObject.getLong("skipNumber")
        }
        if (queryObject.containsKey("limitNumber")) {
            limitNumber = queryObject.getLong("limitNumber")
        }
        val skipOperation = Aggregation.skip(skipNumber)
        val limitOperation = Aggregation.limit(limitNumber)
        aggregation = Aggregation.newAggregation(
            lookupUser,
            lookupClazz,
            lookupGrade,
            lookupMajor,
            lookupCourse,
            lookupScore,
            matchOperation,
            skipOperation,
            sortAggregation, limitOperation
        )
        println(aggregation.toString())
        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                CourseRecordEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }

    // 获取某个用户的任务记录列表-前台-我的任务
    @PostMapping("oneUserTaskList")
    @NeedToken
    fun getOneUserTaskList(
        @RequestAttribute uid: String,
        @RequestBody query: String, @PageableDefault(
            value = 20, sort = ["courseRecordList"],
            direction = Sort.Direction.DESC
        ) pageable: Pageable
    ): OutResponse<Any> {
        val queryObject = JSONObject.parseObject(query)
        val courseId = queryObject.getString("courseId")
        val account = queryObject.getString("account")
        val name = queryObject.getString("name")
        val userId = uid
        val status = queryObject.getString("status")
        val dateNow = Date().time
        // 查询
        val criteria = Criteria("deleted").`is`(0).and("courseTaskId").ne("open")
        if (courseId != null) {
            criteria.and("courseId").`is`(courseId)
        }
        if (name != null) {
            criteria.and("userEntity.name").regex(name)
        }
        if (account != null) {
            criteria.and("userEntity.account").regex(account)
        }
        criteria.and("userEntity.userId").`is`(userId)
        if (status.equals("unStart")) {// 未开始的任务
            criteria.and("courseTaskEntity.startTime").gt(dateNow) // 当前时间<任务开始时间
        }
        if (status.equals("doing")) {
            criteria.andOperator(
                Criteria("courseTaskEntity.startTime").lte(dateNow),// 开始时间<=当前时间<=结束时间
                Criteria("courseTaskEntity.endTime").gte(dateNow)
            )
        }
        if (status.equals("done")) {
            criteria.and("courseTaskEntity.endTime").lt(dateNow) // 当前时间>结束时间
        }

        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupTeacher =
            Aggregation.lookup("user", "teacherId", "userId", "teacherEntity")
        val lookupCourseTask =
            Aggregation.lookup("course_task", "courseTaskId", "courseTaskId", "courseTaskEntity")
        val lookupCourse =
            Aggregation.lookup("course", "courseId", "courseId", "courseEntity")
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")
        val lookupClazz =
            Aggregation.lookup("clazz", "userEntity.clazzId", "clazzId", "clazzEntity")
        val lookupGrade =
            Aggregation.lookup("grade", "clazzEntity.gradeId", "gradeId", "gradeEntity")
        val lookupMajor =
            Aggregation.lookup("major", "clazzEntity.majorId", "majorId", "majorEntity")
        val lookupScore =
            Aggregation.lookup("course_score", "courseRecordId", "courseRecordId", "courseScoreEntity")
        val countAggregation = Aggregation.count().`as`("count")
        val sortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        var aggregation = Aggregation.newAggregation(lookupUser, lookupCourseTask, matchOperation, countAggregation)
        val countResult = StaticBean.mongoTemplate.aggregate(
            aggregation,
            "course_record",
            HashMap::class.java
        ).mappedResults
        val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
        val pageSize = pageable.pageSize
        val totalPages = totalElements / pageSize
        // 分页 如果有传过来的就用
        var skipNumber = pageable.pageNumber * pageSize.toLong()
        var limitNumber = pageSize.toLong()
        if (queryObject.containsKey("skipNumber")) {
            skipNumber = queryObject.getLong("skipNumber")
        }
        if (queryObject.containsKey("limitNumber")) {
            limitNumber = queryObject.getLong("limitNumber")
        }
        val skipOperation = Aggregation.skip(skipNumber)
        val limitOperation = Aggregation.limit(limitNumber)
        aggregation = Aggregation.newAggregation(
            lookupUser,
            lookupCourseTask,
            lookupClazz,
            lookupGrade,
            lookupMajor,
            lookupCourse,
            lookupScore,
            lookupTeacher,
            matchOperation,
            skipOperation,
            sortAggregation, limitOperation
        )

        // 输出
        val list =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                CourseRecordEntity::class.java
            ).mappedResults
        val returnObject = JSONObject.parseObject(
            """
            {
                "number":${pageable.pageNumber},
                "size":${pageable.pageSize},
                "totalElements":${totalElements},
                "totalPages":${totalPages}
            }
        """.trimIndent()
        )
        returnObject["content"] = list
        return OutResponse(
            "000000",
            "",
            returnObject
        )
    }
}