package com.cdzyhd.big_platform.controller.v1

import com.cdzyhd.base.common.util.OutResponse
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.annotation.SysOperaLog
import com.cdzyhd.big_platform.model.OpenModel
import com.cdzyhd.big_platform.model.StatisticModel
import org.springframework.web.bind.annotation.*


/**
 * 对外开放接口
 */
@RestController
@RequestMapping("/v1/open")
class OpenController {
    /**
     * 获取课程的ticket
     * 直接平台网页内获取
     */
    @GetMapping("/ticketByWeb")
    @NeedToken
    @SysOperaLog(moduleName = "开放模块", methodName = "网页获取Ticket")
    fun getTicketByWeb(@RequestAttribute uid: String, @RequestParam appId: String): OutResponse<Any> {
        return OutResponse("000000", "通过网页获取ticket成功", OpenModel().getTicket(uid, appId))
    }

    /**
     * 获取课程的ticket
     * 通过用户名密码登录
     */
    @GetMapping("/ticketByLogin")
    @SysOperaLog(moduleName = "开放模块", methodName = "用户名密码获取Ticket")
    fun getTicketByLogin(
        @RequestParam username: String,
        @RequestParam password: String,
        @RequestParam appId: String
    ): OutResponse<Any> {
        return OutResponse("000000", "通过登录获取ticket成功", OpenModel().getTicketByLogin(username, password, appId))
    }

    /**
     * 获取课程的access_token
     * 通过用户名密码登录
     */
    @GetMapping("/access_token")
    @SysOperaLog(moduleName = "开放模块", methodName = "获取access_token")
    fun getAccessToken(
        @RequestParam ticket: String,
        @RequestParam appId: String,
        @RequestParam signature: String
    ): OutResponse<Any> {
        // 获取token
        val tokenInfo = OpenModel().getCourseToken(ticket, appId, signature)
        // 增加课程访问量
        val courseInfo = tokenInfo["course_info"] as HashMap<*, *>
        val courseId = courseInfo["courseId"] as String
        StatisticModel().addOneViewNumber(courseId)
        return OutResponse("000000", "获取课程access_token成功", tokenInfo)
    }

    /**
     * 课程实验结果数据回传
     */
    @PostMapping("/data_upload")
    @SysOperaLog(moduleName = "开放模块", methodName = "实验结果数据回传")
    fun dataUpload(
        @RequestParam access_token: String,
        @RequestBody info: String,
    ): OutResponse<Any> {
        return OutResponse("000000", "上传实验结果成功", OpenModel().dataUpload(access_token, info))
    }
}