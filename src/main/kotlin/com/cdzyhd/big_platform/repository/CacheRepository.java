package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CacheEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CacheRepository extends MongoRepository<CacheEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCacheId(String id);

    // 通过id获取一个
    CacheEntity findFirstByCacheId(String id);

    // 通过id删除
    Integer deleteByCacheId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CacheEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CacheEntity> getList(Document d);

    @Query(value = "?0")
    CacheEntity getOne(Document d);

    CacheEntity findFirstByName(String name);
}
