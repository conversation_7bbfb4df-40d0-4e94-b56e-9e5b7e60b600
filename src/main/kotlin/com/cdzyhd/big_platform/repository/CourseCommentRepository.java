package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseCommentEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseCommentRepository extends MongoRepository<CourseCommentEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseCommentId(String id);

    // 通过id获取一个
    CourseCommentEntity findFirstByCourseCommentId(String id);

    // 通过id删除
    Integer deleteByCourseCommentId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseCommentEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseCommentEntity> getList(Document d);

    // 获取一个
    @Query(value = "?0")
    CourseCommentEntity getOne(Document d);

    // 通过用户id获取数量
    Integer countByUserId(String userid);


}
