package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.IndustryBannerEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 产教融合轮播图数据访问层
 */
public interface IndustryBannerRepository extends MongoRepository<IndustryBannerEntity, ObjectId> {
    
    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByBannerId(String bannerId);
    
    // 通过业务ID获取单个对象
    IndustryBannerEntity findFirstByBannerId(String bannerId);
    
    // 通过业务ID删除
    Integer deleteByBannerId(String bannerId);
    
    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<IndustryBannerEntity> getPageList(Document d, Pageable pageable);
    
    // 不分页查询
    @Query(value = "?0")
    List<IndustryBannerEntity> getList(Document d);
    
    // 获取单个对象
    @Query(value = "?0")
    IndustryBannerEntity getOne(Document d);
    
    // 业务特定查询方法
    // 获取激活状态的轮播图，按排序号升序
    List<IndustryBannerEntity> findByStatusAndDeletedOrderBySortAsc(String status, Integer deleted);
    
    // 获取指定状态的轮播图数量
    Integer countByStatusAndDeleted(String status, Integer deleted);
    
    // 根据排序号和状态查询
    List<IndustryBannerEntity> findByStatusAndDeletedAndSortBetweenOrderBySortAsc(
        String status, Integer deleted, Integer minSort, Integer maxSort);
    
    // 检查标题是否已存在（排除指定ID）
    IndustryBannerEntity findFirstByTitleAndDeletedAndBannerIdNot(String title, Integer deleted, String bannerId);
    
    // 获取最大排序号
    IndustryBannerEntity findFirstByDeletedOrderBySortDesc(Integer deleted);
}