package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.IndustryStudentEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 产教融合优秀学生数据访问层
 */
public interface IndustryStudentRepository extends MongoRepository<IndustryStudentEntity, ObjectId> {
    
    // 基础CRUD方法
    Boolean existsByStudentId(String studentId);
    IndustryStudentEntity findFirstByStudentId(String studentId);
    Integer deleteByStudentId(String studentId);
    
    // 通用查询方法
    @Query(value = "?0")
    Page<IndustryStudentEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0")
    List<IndustryStudentEntity> getList(Document d);
    
    @Query(value = "?0")
    IndustryStudentEntity getOne(Document d);
    
    // 业务特定查询方法
    IndustryStudentEntity findFirstByStudentNumberAndDeleted(String studentNumber, Integer deleted);
    IndustryStudentEntity findFirstByStudentNumberAndDeletedAndStudentIdNot(String studentNumber, Integer deleted, String studentId);
    Integer countByCategoryAndStatusAndDeleted(String category, String status, Integer deleted);
    Integer countByMajorContainingAndStatusAndDeleted(String major, String status, Integer deleted);
    Integer countByGraduationYearAndStatusAndDeleted(String graduationYear, String status, Integer deleted);
    List<IndustryStudentEntity> findByStatusAndDeletedOrderBySortAsc(String status, Integer deleted);
    List<IndustryStudentEntity> findByStatusAndDeletedOrderByCreateTimeDesc(String status, Integer deleted);
    List<IndustryStudentEntity> findByFeaturedAndStatusAndDeletedOrderBySortAsc(Boolean featured, String status, Integer deleted);
    List<IndustryStudentEntity> findByCategoryAndStatusAndDeletedOrderBySortAsc(String category, String status, Integer deleted);
    List<IndustryStudentEntity> findByMajorContainingAndStatusAndDeletedOrderBySortAsc(String major, String status, Integer deleted);
    List<IndustryStudentEntity> findByGraduationYearAndStatusAndDeletedOrderBySortAsc(String graduationYear, String status, Integer deleted);
    List<IndustryStudentEntity> findByCompanyContainingAndStatusAndDeletedOrderBySortAsc(String company, String status, Integer deleted);
    List<IndustryStudentEntity> findByNameContainingAndStatusAndDeletedOrderBySortAsc(String name, String status, Integer deleted);

    Integer countByStatusAndDeleted(String status, Integer deleted);
}