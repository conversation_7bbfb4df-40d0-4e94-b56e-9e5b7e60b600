package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.MajorEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface MajorRepository extends MongoRepository<MajorEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByMajorId(String id);

    // 通过id获取一个
    MajorEntity findFirstByMajorId(String id);

    // 通过id删除
    Integer deleteByMajorId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<MajorEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<MajorEntity> getList(Document d);

    @Query(value = "?0")
    MajorEntity getOne(Document d);

    // 通过名称查找
    MajorEntity findFirstByName(String name);

    // 通过名称和学院id查找
    MajorEntity findFirstByNameAndCollegeId(String name,String collegeId);
}
