package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseReportEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseReportRepository extends MongoRepository<CourseReportEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseReportId(String id);

    // 通过id获取一个
    CourseReportEntity findFirstByCourseReportId(String id);

    // 通过id删除
    Integer deleteByCourseReportId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseReportEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseReportEntity> getList(Document d);

    // 获取一个
    @Query(value = "?0")
    CourseReportEntity getOne(Document d);

    // 通过记录id获取一个
    CourseReportEntity findFirstByCourseRecordId(String courseRecordId);
}
