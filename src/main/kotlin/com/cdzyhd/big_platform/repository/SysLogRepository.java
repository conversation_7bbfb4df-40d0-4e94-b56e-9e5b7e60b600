package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.SysLogEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SysLogRepository extends MongoRepository<SysLogEntity, ObjectId> {
    // 是否存在
    boolean existsBySysLogId(String Id);

    // 获取第一个
    SysLogEntity findFirstBySysLogId(String Id);

    // 删除
    Integer deleteBySysLogId(String Id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<SysLogEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<SysLogEntity> getList(Document d);

    // 获取某一个-根据提供条件
    @Query(value = "?0")
    SysLogEntity getOne(Document d);
}
