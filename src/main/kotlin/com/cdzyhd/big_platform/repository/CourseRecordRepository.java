package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseRecordEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseRecordRepository extends MongoRepository<CourseRecordEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseRecordId(String id);

    // 通过id获取一个
    CourseRecordEntity findFirstByCourseRecordId(String id);

    // 通过id删除
    Integer deleteByCourseRecordId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseRecordEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseRecordEntity> getList(Document d);

    @Query(value = "?0")
    CourseRecordEntity getOne(Document d);

    CourseRecordEntity findFirstByCourseIdAndUserIdAndCourseTaskId(String courseId, String userId, String courseTaskId);
}
