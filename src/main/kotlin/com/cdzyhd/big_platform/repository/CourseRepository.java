package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseRepository extends MongoRepository<CourseEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseId(String id);

    // 通过id获取一个
    CourseEntity findFirstByCourseId(String id);

    // 通过id删除
    Integer deleteByCourseId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseEntity> getList(Document d);

    // 获取一个
    @Query(value = "?0")
    CourseEntity getOne(Document d);

    // 通过学科id获取数量
    Integer countByCourseSubjectId(String collegeId);

    // 通过appId获取一个
    CourseEntity findFirstByAppId(String appId);
}
