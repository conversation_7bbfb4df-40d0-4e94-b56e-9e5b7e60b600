package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.ResourceCategoryEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ResourceCategoryRepository extends MongoRepository<ResourceCategoryEntity, ObjectId> {
    
    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByCategoryId(String categoryId);
    
    // 通过业务ID获取单个对象
    ResourceCategoryEntity findFirstByCategoryId(String categoryId);
    
    // 通过业务ID删除
    Integer deleteByCategoryId(String categoryId);
    
    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<ResourceCategoryEntity> getPageList(Document d, Pageable pageable);
    
    // 不分页查询
    @Query(value = "?0")
    List<ResourceCategoryEntity> getList(Document d);
    
    // 获取单个对象
    @Query(value = "?0")
    ResourceCategoryEntity getOne(Document d);
    
    // 业务特定查询方法
    // 获取所有顶级分类（parentId为0）
    List<ResourceCategoryEntity> getAllByParentIdAndStatusAndDeletedOrderBySort(String parentId, String status, Integer deleted);
    
    // 获取指定父分类下的子分类
    List<ResourceCategoryEntity> getAllByParentIdAndStatusAndDeleted(String parentId, String status, Integer deleted);
    
    // 获取所有正常状态的分类（按排序）
    List<ResourceCategoryEntity> getAllByStatusAndDeletedOrderBySort(String status, Integer deleted);
    
    // 通过名称查找分类
    ResourceCategoryEntity findFirstByNameAndStatusAndDeleted(String name, String status, Integer deleted);
    
    // 统计分类总数
    Integer countByStatusAndDeleted(String status, Integer deleted);
}