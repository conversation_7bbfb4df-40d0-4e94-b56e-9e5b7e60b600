package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.IndustryJobEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 产教融合招聘信息数据访问层
 */
public interface IndustryJobRepository extends MongoRepository<IndustryJobEntity, ObjectId> {
    
    // 基础CRUD方法
    Boolean existsByJobId(String jobId);
    IndustryJobEntity findFirstByJobId(String jobId);
    Integer deleteByJobId(String jobId);
    
    // 通用查询方法
    @Query(value = "?0")
    Page<IndustryJobEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0")
    List<IndustryJobEntity> getList(Document d);
    
    @Query(value = "?0")
    IndustryJobEntity getOne(Document d);
    
    // 业务特定查询方法
    List<IndustryJobEntity> findByCompanyIdAndStatusAndDeletedOrderByPublishTimeDesc(String companyId, String status, Integer deleted);
    Integer countByCategoryAndStatusAndDeleted(String category, String status, Integer deleted);
    Integer countByLocationAndStatusAndDeleted(String location, String status, Integer deleted);
    List<IndustryJobEntity> findByStatusAndDeletedOrderByPublishTimeDesc(String status, Integer deleted);
    List<IndustryJobEntity> findByUrgentAndStatusAndDeletedOrderByPublishTimeDesc(Boolean urgent, String status, Integer deleted);
    List<IndustryJobEntity> findByHotAndStatusAndDeletedOrderByPublishTimeDesc(Boolean hot, String status, Integer deleted);
    List<IndustryJobEntity> findByCategoryAndStatusAndDeletedOrderByPublishTimeDesc(String category, String status, Integer deleted);
    List<IndustryJobEntity> findByLocationAndStatusAndDeletedOrderByPublishTimeDesc(String location, String status, Integer deleted);
    List<IndustryJobEntity> findByTitleContainingAndStatusAndDeletedOrderByPublishTimeDesc(String title, String status, Integer deleted);
    List<IndustryJobEntity> findByCompanyNameContainingAndStatusAndDeletedOrderByPublishTimeDesc(String companyName, String status, Integer deleted);

    Integer countByStatusAndDeleted(String status, Integer deleted);
}