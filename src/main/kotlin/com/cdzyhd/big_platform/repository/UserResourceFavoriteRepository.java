package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.UserResourceFavoriteEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface UserResourceFavoriteRepository extends MongoRepository<UserResourceFavoriteEntity, ObjectId> {
    
    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByFavoriteId(String favoriteId);
    
    // 通过业务ID获取单个对象
    UserResourceFavoriteEntity findFirstByFavoriteId(String favoriteId);
    
    // 通过业务ID删除
    Integer deleteByFavoriteId(String favoriteId);
    
    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<UserResourceFavoriteEntity> getPageList(Document d, Pageable pageable);
    
    // 不分页查询
    @Query(value = "?0")
    List<UserResourceFavoriteEntity> getList(Document d);
    
    // 获取单个对象
    @Query(value = "?0")
    UserResourceFavoriteEntity getOne(Document d);
    
    // 业务特定查询方法
    // 检查用户是否已收藏某个资源
    Boolean existsByUserIdAndResourceIdAndDeleted(String userId, String resourceId, Integer deleted);
    
    // 获取用户收藏的资源
    UserResourceFavoriteEntity findFirstByUserIdAndResourceIdAndDeleted(String userId, String resourceId, Integer deleted);
    
    // 获取用户的所有收藏（分页）
    Page<UserResourceFavoriteEntity> findAllByUserIdAndDeletedOrderByCreateTimeDesc(String userId, Integer deleted, Pageable pageable);
    
    // 获取用户的所有收藏（不分页）
    List<UserResourceFavoriteEntity> getAllByUserIdAndDeletedOrderByCreateTimeDesc(String userId, Integer deleted);
    
    // 统计用户收藏总数
    Integer countByUserIdAndDeleted(String userId, Integer deleted);
    
    // 统计资源被收藏次数
    Integer countByResourceIdAndDeleted(String resourceId, Integer deleted);
    
    // 删除用户对某个资源的收藏
    Integer deleteByUserIdAndResourceId(String userId, String resourceId);
}