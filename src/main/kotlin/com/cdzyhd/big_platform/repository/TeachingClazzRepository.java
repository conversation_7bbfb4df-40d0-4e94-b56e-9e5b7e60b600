package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.TeachingClazzEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface TeachingClazzRepository extends MongoRepository<TeachingClazzEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByTeachingClazzId(String id);

    // 通过id获取一个
    TeachingClazzEntity findFirstByTeachingClazzId(String id);

    // 通过id删除
    Integer deleteByTeachingClazzId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<TeachingClazzEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<TeachingClazzEntity> getList(Document d);

    @Query(value = "?0")
    TeachingClazzEntity getOne(Document d);

    // 通过名称查找
    TeachingClazzEntity findFirstByName(String name);

    // 通过名称和教师id查找
    TeachingClazzEntity findFirstByNameAndTeacherId(String name, String teacherId);
}
