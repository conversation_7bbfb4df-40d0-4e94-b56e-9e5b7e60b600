package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.GradeEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface GradeRepository extends MongoRepository<GradeEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByGradeId(String id);

    // 通过id获取一个
    GradeEntity findFirstByGradeId(String id);

    // 通过id删除
    Integer deleteByGradeId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<GradeEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<GradeEntity> getList(Document d);

    @Query(value = "?0")
    GradeEntity getOne(Document d);

    GradeEntity findFirstByName(String name);
}
