package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseSubjectEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseSubjectRepository extends MongoRepository<CourseSubjectEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseSubjectId(String id);

    // 通过id获取一个
    CourseSubjectEntity findFirstByCourseSubjectId(String id);

    // 通过id删除
    Integer deleteByCourseSubjectId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseSubjectEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseSubjectEntity> getList(Document d);

    @Query(value = "?0")
    CourseSubjectEntity getOne(Document d);

    CourseSubjectEntity findFirstByName(String name);
}
