package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.BaseNewsEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 基地新闻数据访问接口
 * 提供基地新闻的增删改查操作
 */
public interface BaseNewsRepository extends MongoRepository<BaseNewsEntity, ObjectId> {

    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByNewsId(String newsId);

    // 通过业务ID获取单个对象
    BaseNewsEntity findFirstByNewsId(String newsId);

    // 通过业务ID删除
    Integer deleteByNewsId(String newsId);

    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<BaseNewsEntity> getPageList(Document d, Pageable pageable);

    // 不分页查询
    @Query(value = "?0")
    List<BaseNewsEntity> getList(Document d);

    // 获取单个对象
    @Query(value = "?0")
    BaseNewsEntity getOne(Document d);

    // 业务特定查询方法
    // 通过分类获取新闻列表
    List<BaseNewsEntity> findAllByCategoryAndStatusAndDeletedOrderByPublishTimeDesc(
            String category, String status, Integer deleted);

    // 通过状态获取新闻列表
    List<BaseNewsEntity> findAllByStatusAndDeletedOrderByPublishTimeDesc(
            String status, Integer deleted);

    // 获取热门新闻
    List<BaseNewsEntity> findAllByIsHotAndStatusAndDeletedOrderByViewsDesc(
            Boolean isHot, String status, Integer deleted);

    // 获取置顶新闻
    List<BaseNewsEntity> findAllByIsTopAndStatusAndDeletedOrderByPublishTimeDesc(
            Boolean isTop, String status, Integer deleted);

    // 通过标题关键词搜索
    List<BaseNewsEntity> findAllByTitleContainingAndStatusAndDeletedOrderByPublishTimeDesc(
            String keyword, String status, Integer deleted);

    // 通过作者获取新闻
    List<BaseNewsEntity> findAllByAuthorAndStatusAndDeletedOrderByPublishTimeDesc(
            String author, String status, Integer deleted);

    // 统计相关方法
    // 统计某分类下的新闻数量
    Long countByCategoryAndStatusAndDeleted(String category, String status, Integer deleted);

    // 统计某作者的新闻数量
    Long countByAuthorAndStatusAndDeleted(String author, String status, Integer deleted);

    // 统计热门新闻数量
    Long countByIsHotAndStatusAndDeleted(Boolean isHot, String status, Integer deleted);

    // 获取最近发布的新闻（限制数量）
    List<BaseNewsEntity> findTop10ByStatusAndDeletedOrderByPublishTimeDesc(
            String status, Integer deleted);

    // 获取浏览量最高的新闻（限制数量）
    List<BaseNewsEntity> findTop10ByStatusAndDeletedOrderByViewsDesc(
            String status, Integer deleted);

    // 按时间范围查询新闻
    List<BaseNewsEntity> findAllByPublishTimeBetweenAndStatusAndDeletedOrderByPublishTimeDesc(
            Long startTime, Long endTime, String status, Integer deleted);

    // 查询相关新闻（排除当前新闻）
    List<BaseNewsEntity> findTop5ByCategoryAndStatusAndDeletedAndNewsIdNotOrderByPublishTimeDesc(
            String category, String status, Integer deleted, String excludeNewsId);

    Long countByStatusAndDeleted(String status,Integer deleted);

    Long countByIsTopAndStatusAndDeleted(Boolean isTop,String status,Integer deleted);
}