package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.UserEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface UserRepository extends MongoRepository<UserEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByUserId(String id);

    // 通过id获取一个
    UserEntity findFirstByUserId(String id);

    // 通过id删除
    Integer deleteByUserId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<UserEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<UserEntity> getList(Document d);

    // 获取一个
    @Query(value = "?0")
    UserEntity getOne(Document d);

    // 通过班级id获取学生人数
    Integer countByClazzId(String clazzId);

    // 通过账号判断是否已存在
    Boolean existsByAccount(String account);

    // 通过手机号判断是否已存在
    Boolean existsByPhone(String phone);

    // 通过邮箱判断是否已存在
    Boolean existsByEmail(String email);

    // 通过账号和角色判断是否已存在
    Boolean existsByAccountAndRole(String account,String role);

    // 通过学院id获取教师人数
    Integer countByCollegeId(String collegeId);

    // 通过教学班获取学生人数
    Integer countByTeachingClazzIdsContainsAndRole(String teachingClazzId, String role);

    // 通过行政班级id获取学生列表
    List<UserEntity> getAllByClazzIdAndRole(String clazzId, String role);

    // 通过账号获取
    UserEntity findFirstByAccount(String account);

    // 通过账号和角色获取
    UserEntity findFirstByAccountAndRole(String account,String role);

    // 获取学生列表-教学班不包含id、行政班、学生角色
    List<UserEntity> getAllByTeachingClazzIdsNotContainingAndClazzIdAndRole(String teachingClazzId, String clazzId, String role);

}
