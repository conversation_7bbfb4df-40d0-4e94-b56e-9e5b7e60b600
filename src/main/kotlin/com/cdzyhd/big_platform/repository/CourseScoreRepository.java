package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseScoreEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseScoreRepository extends MongoRepository<CourseScoreEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseScoreId(String id);

    // 通过id获取一个
    CourseScoreEntity findFirstByCourseScoreId(String id);

    // 通过id删除
    Integer deleteByCourseScoreId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseScoreEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseScoreEntity> getList(Document d);

    @Query(value = "?0")
    CourseScoreEntity getOne(Document d);

    // 获取某个课程最后的分数记录
    CourseScoreEntity findTopByCourseId(String courseId);
}
