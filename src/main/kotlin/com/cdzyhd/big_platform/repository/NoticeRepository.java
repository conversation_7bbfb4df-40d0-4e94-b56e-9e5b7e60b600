package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.NoticeEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 通知公告数据访问接口
 * 提供通知公告的增删改查操作
 */
public interface NoticeRepository extends MongoRepository<NoticeEntity, ObjectId> {

    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByNoticeId(String noticeId);

    // 通过业务ID获取单个对象
    NoticeEntity findFirstByNoticeId(String noticeId);

    // 通过业务ID删除
    Integer deleteByNoticeId(String noticeId);

    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<NoticeEntity> getPageList(Document d, Pageable pageable);

    // 不分页查询
    @Query(value = "?0")
    List<NoticeEntity> getList(Document d);

    // 获取单个对象
    @Query(value = "?0")
    NoticeEntity getOne(Document d);

    // 业务特定查询方法
    // 通过类型获取通知列表
    List<NoticeEntity> findAllByTypeAndStatusAndDeletedOrderByPublishTimeDesc(
            String type, String status, Integer deleted);

    // 通过状态获取通知列表
    List<NoticeEntity> findAllByStatusAndDeletedOrderByPublishTimeDesc(
            String status, Integer deleted);

    // 获取置顶通知
    List<NoticeEntity> findAllByIsTopAndStatusAndDeletedOrderByPublishTimeDesc(
            Boolean isTop, String status, Integer deleted);

    // 通过紧急程度获取通知
    List<NoticeEntity> findAllByUrgencyAndStatusAndDeletedOrderByPublishTimeDesc(
            String urgency, String status, Integer deleted);

    // 通过标题关键词搜索
    List<NoticeEntity> findAllByTitleContainingAndStatusAndDeletedOrderByPublishTimeDesc(
            String keyword, String status, Integer deleted);

    // 通过发布者获取通知
    List<NoticeEntity> findAllByAuthorAndStatusAndDeletedOrderByPublishTimeDesc(
            String author, String status, Integer deleted);

    // 获取有效期内的通知
    List<NoticeEntity> findAllByStatusAndDeletedAndExpireTimeGreaterThanOrderByPublishTimeDesc(
            String status, Integer deleted, Long currentTime);

    // 获取需要确认阅读的通知
    List<NoticeEntity> findAllByNeedConfirmAndStatusAndDeletedOrderByPublishTimeDesc(
            Boolean needConfirm, String status, Integer deleted);

    // 统计相关方法
    // 统计某类型下的通知数量
    Long countByTypeAndStatusAndDeleted(String type, String status, Integer deleted);

    // 统计某发布者的通知数量
    Long countByAuthorAndStatusAndDeleted(String author, String status, Integer deleted);

    // 统计置顶通知数量
    Long countByIsTopAndStatusAndDeleted(Boolean isTop, String status, Integer deleted);

    // 统计紧急通知数量
    Long countByUrgencyAndStatusAndDeleted(String urgency, String status, Integer deleted);

    // 获取最近发布的通知（限制数量）
    List<NoticeEntity> findTop10ByStatusAndDeletedOrderByPublishTimeDesc(
            String status, Integer deleted);

    // 获取浏览量最高的通知（限制数量）
    List<NoticeEntity> findTop10ByStatusAndDeletedOrderByViewsDesc(
            String status, Integer deleted);

    // 按时间范围查询通知
    List<NoticeEntity> findAllByPublishTimeBetweenAndStatusAndDeletedOrderByPublishTimeDesc(
            Long startTime, Long endTime, String status, Integer deleted);

    // 查询相关通知（排除当前通知）
    List<NoticeEntity> findTop5ByTypeAndStatusAndDeletedAndNoticeIdNotOrderByPublishTimeDesc(
            String type, String status, Integer deleted, String excludeNoticeId);

    // 查询即将过期的通知
    List<NoticeEntity> findAllByStatusAndDeletedAndExpireTimeBetweenOrderByExpireTimeAsc(
            String status, Integer deleted, Long startTime, Long endTime);

    // 查询已过期但状态仍为生效中的通知
    List<NoticeEntity> findAllByStatusAndDeletedAndExpireTimeLessThan(
            String status, Integer deleted, Long currentTime);

    Long countByStatusAndDeleted(String status,Integer deleted);
}