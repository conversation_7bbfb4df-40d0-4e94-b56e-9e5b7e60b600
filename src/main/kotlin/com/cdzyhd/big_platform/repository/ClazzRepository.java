package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.ClazzEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ClazzRepository extends MongoRepository<ClazzEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByClazzId(String id);

    // 通过id获取一个
    ClazzEntity findFirstByClazzId(String id);

    // 通过id删除
    Integer deleteByClazzId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<ClazzEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<ClazzEntity> getList(Document d);

    @Query(value = "?0")
    ClazzEntity getOne(Document d);

    ClazzEntity findFirstByName(String name);

    ClazzEntity findFirstByNameAndMajorIdAndGradeId(String name, String majorId, String gradeId);
}
