package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.HelpQuestionEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface HelpQuestionRepository extends MongoRepository<HelpQuestionEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByHelpQuestionId(String id);

    // 通过id获取一个
    HelpQuestionEntity findFirstByHelpQuestionId(String id);

    // 通过id删除
    Integer deleteByHelpQuestionId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<HelpQuestionEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<HelpQuestionEntity> getList(Document d);

    @Query(value = "?0")
    HelpQuestionEntity getOne(Document d);

    // 通过名称查找
    HelpQuestionEntity findFirstByName(String name);
}
