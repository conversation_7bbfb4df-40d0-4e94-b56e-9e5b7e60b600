package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.IndustryEnterpriseEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

/**
 * 产教融合企业数据访问层
 */
public interface IndustryEnterpriseRepository extends MongoRepository<IndustryEnterpriseEntity, ObjectId> {
    
    // 基础CRUD方法
    Boolean existsByEnterpriseId(String enterpriseId);
    IndustryEnterpriseEntity findFirstByEnterpriseId(String enterpriseId);
    Integer deleteByEnterpriseId(String enterpriseId);
    
    // 通用查询方法
    @Query(value = "?0")
    Page<IndustryEnterpriseEntity> getPageList(Document d, Pageable pageable);
    
    @Query(value = "?0")
    List<IndustryEnterpriseEntity> getList(Document d);
    
    @Query(value = "?0")
    IndustryEnterpriseEntity getOne(Document d);
    
    // 业务特定查询方法
    IndustryEnterpriseEntity findFirstByNameAndDeleted(String name, Integer deleted);
    IndustryEnterpriseEntity findFirstByNameAndDeletedAndEnterpriseIdNot(String name, Integer deleted, String enterpriseId);
    Integer countByTypeAndStatusAndDeleted(String type, String status, Integer deleted);
    Integer countByScaleAndStatusAndDeleted(String scale, String status, Integer deleted);
    Integer countByLocationAndStatusAndDeleted(String location, String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByStatusAndDeletedOrderBySortAsc(String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByStatusAndDeletedOrderByCreateTimeDesc(String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByTypeAndStatusAndDeletedOrderBySortAsc(String type, String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByScaleAndStatusAndDeletedOrderBySortAsc(String scale, String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByLocationAndStatusAndDeletedOrderBySortAsc(String location, String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByNameContainingAndStatusAndDeletedOrderBySortAsc(String name, String status, Integer deleted);
    List<IndustryEnterpriseEntity> findByCooperationYearsGreaterThanEqualAndStatusAndDeletedOrderBySortAsc(Integer years, String status, Integer deleted);
    
    Integer countByStatusAndDeleted(String status, Integer deleted);
}