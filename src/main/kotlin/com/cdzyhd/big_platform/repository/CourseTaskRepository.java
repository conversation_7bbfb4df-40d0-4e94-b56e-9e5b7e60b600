package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.CourseTaskEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface CourseTaskRepository extends MongoRepository<CourseTaskEntity, ObjectId> {
    // 通过id判断是是否存在
    Boolean existsByCourseTaskId(String id);

    // 通过id获取一个
    CourseTaskEntity findFirstByCourseTaskId(String id);

    // 通过id删除
    Integer deleteByCourseTaskId(String id);

    // 获取分页列表-根据提供条件
    @Query(value = "?0")
    Page<CourseTaskEntity> getPageList(Document d, Pageable pageable);

    // 获取不分页列表-根据提供条件
    @Query(value = "?0")
    List<CourseTaskEntity> getList(Document d);

    @Query(value = "?0")
    CourseTaskEntity getOne(Document d);

    // 通过名称查找
    CourseTaskEntity findFirstByName(String name);
}
