package com.cdzyhd.big_platform.repository;

import com.cdzyhd.big_platform.entity.ResourceEntity;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;

import java.util.List;

public interface ResourceRepository extends MongoRepository<ResourceEntity, ObjectId> {
    
    // 基础CRUD方法
    // 通过业务ID判断是否存在
    Boolean existsByResourceId(String resourceId);
    
    // 通过业务ID获取单个对象
    ResourceEntity findFirstByResourceId(String resourceId);
    
    // 通过业务ID删除
    Integer deleteByResourceId(String resourceId);
    
    // 通用查询方法
    // 分页查询
    @Query(value = "?0")
    Page<ResourceEntity> getPageList(Document d, Pageable pageable);
    
    // 不分页查询
    @Query(value = "?0")
    List<ResourceEntity> getList(Document d);
    
    // 获取单个对象
    @Query(value = "?0")
    ResourceEntity getOne(Document d);
    
    // 业务特定查询方法
    // 通过分类ID获取资源列表
    List<ResourceEntity> getAllByCategoryIdAndStatusAndDeleted(String categoryId, String status, Integer deleted);
    
    // 通过课程ID获取资源列表
    List<ResourceEntity> getAllByCourseIdAndStatusAndDeleted(String courseId, String status, Integer deleted);
    
    // 根据状态获取资源列表（用于首页推荐等）
    List<ResourceEntity> getAllByStatusAndDeletedOrderByCreateTimeDesc(String status, Integer deleted);
    
    // 统计分类下的资源数量
    Integer countByCategoryIdAndStatusAndDeleted(String categoryId, String status, Integer deleted);
    
    // 统计总资源数量
    Integer countByStatusAndDeleted(String status, Integer deleted);
    
    // 获取热门资源（按浏览量排序）
    List<ResourceEntity> findTop10ByStatusAndDeletedOrderByViewsDesc(String status, Integer deleted);
    
    // 获取最新资源（按上传时间排序）
    List<ResourceEntity> findTop10ByStatusAndDeletedOrderByUploadTimeDesc(String status, Integer deleted);
    
    // 获取推荐资源（按收藏量排序）
    List<ResourceEntity> findTop10ByStatusAndDeletedOrderByFavoritesDesc(String status, Integer deleted);
    
    // 根据类型统计资源数量
    Integer countByTypeAndStatusAndDeleted(String type, String status, Integer deleted);
    
    // 搜索相关方法（基于标题和描述）
    List<ResourceEntity> findByTitleContainingIgnoreCaseOrDescriptionContainingIgnoreCaseAndStatusAndDeleted(
        String titleKeyword, String descKeyword, String status, Integer deleted
    );
}