package com.cdzyhd.big_platform.util;

import com.cdzyhd.big_platform.annotation.SysOperaLog;
import org.aspectj.lang.JoinPoint;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Method;
import java.util.HashMap;

/**
 */
public class LogUtil {

    /***
     * 获取操作信息
     * @param point
     * @return
     */
    public static HashMap<String, Object> getControllerMethodDescription(JoinPoint point) throws Exception {
        // 获取连接点目标类名
        String targetName = point.getTarget().getClass().getName();
        // 获取连接点签名的方法名
        String methodName = point.getSignature().getName();
        //获取连接点参数
        Object[] args = point.getArgs();
        //根据连接点类的名字获取指定类
        Class targetClass = Class.forName(targetName);
        //获取类里面的方法
        Method[] methods = targetClass.getMethods();
        HashMap<String, Object> result = new HashMap<>();
        for (Method method : methods) {
            if (method.getName().equals(methodName)) {
                Class[] clazzs = method.getParameterTypes();
                if (clazzs.length == args.length) {
                    result.put("description", method.getAnnotation(SysOperaLog.class).descrption());
                    result.put("moduleName", method.getAnnotation(SysOperaLog.class).moduleName());
                    result.put("methodName", method.getAnnotation(SysOperaLog.class).methodName());
                    break;
                }
            }
        }
        return result;
    }


    /**
     * 获取堆栈信息
     *
     * @param throwable
     * @return
     */
    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }
}
