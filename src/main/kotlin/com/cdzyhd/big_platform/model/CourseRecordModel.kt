package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.*
import com.cdzyhd.big_platform.exception.CommonException
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import java.util.*

class CourseRecordModel {
    // 获取某个教学班的详情
    fun getOneTeachingClazzDetail(
        courseTaskId: String,
        teacherId: String,
        teachingClazzId: String
    ): TeachingClazzEntity {
        val teachingClazz = StaticBean.teachingClazzRepository.findFirstByTeachingClazzId(teachingClazzId)
        if (teachingClazz != null) {
            teachingClazz.clazzNames = ClazzModel().multiGetClazzNamesByIds(teachingClazz.clazzIds)
            // 获取该教学班学生列表
            var match = Aggregation.match(
                Criteria("courseTaskId").`is`(courseTaskId).and("teacherId").`is`(teacherId).and("teachingClazzId")
                    .`is`(teachingClazzId).and("deleted").`is`(0)
            )
            var aggregation = Aggregation.newAggregation(match, Aggregation.project("userId"))
            val studentList =
                StaticBean.mongoTemplate.aggregate(
                    aggregation,
                    "course_record",
                    CourseRecordEntity::class.java
                ).mappedResults
            teachingClazz.studentNumber = studentList.size // 总学生人数
            // 获取总课程完成人数
            match = Aggregation.match(
                Criteria("courseTaskId").`is`(courseTaskId).and("teacherId").`is`(teacherId).and("teachingClazzId")
                    .`is`(teachingClazzId).and("courseCompleted").`is`(true).and("deleted").`is`(0)
            )
            aggregation = Aggregation.newAggregation(match, Aggregation.project("userId"))
            val courseCompletedList =
                StaticBean.mongoTemplate.aggregate(
                    aggregation,
                    "course_record",
                    CourseRecordEntity::class.java
                ).mappedResults
            teachingClazz.courseCompletedNumber = courseCompletedList.size
            // 获取总报告填写人数
            match = Aggregation.match(
                Criteria("courseTaskId").`is`(courseTaskId).and("teacherId").`is`(teacherId).and("teachingClazzId")
                    .`is`(teachingClazzId).and("reportFilled").`is`(true).and("deleted").`is`(0)
            )
            aggregation = Aggregation.newAggregation(match, Aggregation.project("userId"))
            val reportCompletedList =
                StaticBean.mongoTemplate.aggregate(
                    aggregation,
                    "course_record",
                    CourseRecordEntity::class.java
                ).mappedResults
            teachingClazz.reportCompletedNumber = reportCompletedList.size
            return teachingClazz
        } else {
            throw CommonException("000001", "未找到该教学班")
        }
    }

    // 创建一个课程记录
    fun addOneRecord(courseRecord: CourseRecordEntity) {
        StaticBean.courseRecordRepository.save(courseRecord)
    }

    // 获取某个用户某个课程的开放记录
    fun getOneUserOneCourseOpenRecord(userId: String, courseId: String): CourseRecordEntity {
        // 查找是否已存在开放记录
        var openRecord = StaticBean.courseRecordRepository.findFirstByCourseIdAndUserIdAndCourseTaskId(
            courseId,
            userId,
            "open"
        )
        val course = StaticBean.courseRepository.findFirstByCourseId(courseId)
        if (openRecord == null) {// 如果不存在,就创建一个开放记录
            openRecord = CourseRecordEntity()
            openRecord.userId = userId
            openRecord.courseTaskId = "open"
            openRecord.courseId = courseId
            // 继承限制登录次数
            openRecord.limitLogin = course.limitLogin
            openRecord.limitNumber = course.limitNumber
            openRecord.leftLimitNumber = course.limitNumber
            CourseRecordModel().addOneRecord(openRecord)
        } else {// 如果存在
            /**  判断课程是否限制次数，进行修改---start--- **/
            if (course.limitLogin == true) { // 如果记录课程限制登录次数
                var needSave = false // 标记是否需要保存修改
                if (openRecord.limitLogin == false) {// 如果记录不限制
                    openRecord.limitLogin = true
                    needSave = true
                }
                if (openRecord.limitNumber != course.limitNumber) {// 如果记录限制次数不等于课程限制次数
                    needSave = true
//                    if (openRecord.leftLimitNumber > course.limitNumber) {// 选择1：剩余次数着改变，如果剩余次数比课程限制次数还大，就置为课程限制次数。
//                        openRecord.leftLimitNumber = course.limitNumber
//                    }
                    openRecord.leftLimitNumber = course.limitNumber // 选择2：剩余次数跟着改变，剩余次数直接改为课程限制次数。使用最便利
//                    openRecord.leftLimitNumber=openRecord.leftLimitNumber // 选择3：剩余次数不跟着改变
                    openRecord.limitNumber = course.limitNumber
                }
                if (needSave) {
                    StaticBean.courseRecordRepository.save(openRecord)
                }
            }
            if (openRecord.limitLogin == true && course.limitLogin == false) { // 如果记录限制，课程不限制
                openRecord.limitLogin = false
                StaticBean.courseRecordRepository.save(openRecord)
            }
            /**  判断课程是否限制次数，进行修改---end--- **/
        }
        return openRecord
    }

    // 获取某个用户某个课程所有的任务记录列表
    fun getOneUserOneCourseTaskList(courseId: String, userId: String, status: String): MutableList<CourseRecordEntity> {
        val dateNow = Date().time
        // 查询
        val criteria = Criteria("deleted").`is`(0).and("courseTaskId").ne("open")
        criteria.and("courseId").`is`(courseId)
        criteria.and("userEntity.userId").`is`(userId)
        if (status == "unStart") {// 未开始的任务
            criteria.and("courseTaskEntity.startTime").gt(dateNow) // 当前时间<任务开始时间
        }
        if (status == "doing") {
            criteria.andOperator(
                Criteria("courseTaskEntity.startTime").lte(dateNow),// 开始时间<=当前时间<=结束时间
                Criteria("courseTaskEntity.endTime").gte(dateNow)
            )
        }
        if (status == "done") {
            criteria.and("courseTaskEntity.endTime").lt(dateNow) // 当前时间>结束时间
        }
        val matchOperation = Aggregation.match(criteria)
        // join
        val lookupCourseTask =
            Aggregation.lookup("course_task", "courseTaskId", "courseTaskId", "courseTaskEntity")
        val lookupUser =
            Aggregation.lookup("user", "userId", "userId", "userEntity")

        val aggregation = Aggregation.newAggregation(lookupUser, lookupCourseTask, matchOperation)
        val allList =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_record",
                CourseRecordEntity::class.java
            ).mappedResults
        return allList
    }

    // 更新课程记录分数
    fun updateRecordAndAddScore(
        courseRecord: CourseRecordEntity,
        courseRecordId: String,
        courseId: String,
        status: Int,
        fullScore: Double,
        score: Double,
        startTime: Long,
        endTime: Long,
        usedTime: Long,
        stepInfo: JSONArray,
        extraInfo: JSONObject
    ): CourseScoreEntity {
        // 新增一个分数记录
        val newScoreEntity = CourseScoreModel().addOneScore(
            courseRecordId, courseId, status, fullScore, score, startTime,
            endTime, usedTime, stepInfo, extraInfo
        )
        // 更新开放课程记录
        courseRecord.editTime = Date().time
        if (status == 1) {// 如果完成了课程
            courseRecord.courseCompleted = true
            // 获取多个分数中的最高分
            val maxScore = CourseScoreModel().getOneRecordMaxScore(courseRecord.courseRecordId)
            courseRecord.courseScore = maxScore
            // 计算综合分数 todo 是否有报告分后 就不更新最高分
            if (courseRecord.courseTaskId == "open") { // 开放记录总分就是实验分
                courseRecord.totalScore = courseRecord.courseScore
            } else {
                val courseTask = StaticBean.courseTaskRepository.findFirstByCourseTaskId(courseRecord.courseTaskId)
                if (courseTask.needFillReport) {// 如果需要填写实验报告，就计算分数占比
                    val totalScore =
                        courseRecord.courseScore * courseTask.courseScorePoint + courseRecord.reportScore * courseTask.reportScorePoint
                    courseRecord.totalScore = totalScore
                } else {// 如果不需要填写实验报告，总分就是实验分
                    courseRecord.totalScore = courseRecord.courseScore
                }
            }
        }
        // 删除不必要entity
        courseRecord.userEntity = null
        courseRecord.courseTaskEntity = null
        // 统计-如果原来没有做过课程，就增加一个实验人数
        if (courseRecord.courseDone == false) {
            StatisticModel().addOneUserNumber(courseId)
            courseRecord.courseDone = true
        }
        StaticBean.courseRecordRepository.save(courseRecord)
        return newScoreEntity
    }
}