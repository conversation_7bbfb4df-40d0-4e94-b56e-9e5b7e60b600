package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.CourseRecordEntity
import com.cdzyhd.big_platform.entity.CourseScoreEntity
import com.cdzyhd.big_platform.exception.CommonException
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria

// 课程分数
class CourseScoreModel {
    // 新增一个课程分数
    fun addOneScore(
        courseRecordId: String, courseId: String, result: Int, fullScore: Double, score: Double, startTime: Long,
        endTime: Long, usedTime: Long, stepInfo: JSONArray, extraInfo: JSONObject
    ): CourseScoreEntity {
        val newScore = CourseScoreEntity()
        newScore.courseRecordId = courseRecordId
        newScore.courseId = courseId
        newScore.result = result
        newScore.fullScore = fullScore
        newScore.score = score
        newScore.startTime = startTime
        newScore.endTime = endTime
        if (usedTime < 0) {
            throw CommonException("timeUsed_error", "timeUsed不能小于0")
        }
        newScore.usedTime = usedTime
        newScore.stepInfo = stepInfo
        newScore.extraInfo=extraInfo
        StaticBean.courseScoreRepository.save(newScore)

        return newScore
    }

    // 获取某个实验记录所有分数中的最大值
    fun getOneRecordMaxScore(courseRecordId: String): Double {
        val criteria = Criteria("courseRecordId").`is`(courseRecordId).and("result").`is`(1)
        val matchOperation = Aggregation.match(criteria)
        val maxOperation = Aggregation.group("maxScore").max("$" + "score").`as`("maxScore")
        val aggregation = Aggregation.newAggregation(matchOperation, maxOperation)
        //println(aggregation.toString())
        val allList =
            StaticBean.mongoTemplate.aggregate(
                aggregation,
                "course_score",
                HashMap::class.java
            ).mappedResults
        return if (allList.size == 1) {
            allList[0]["maxScore"] as Double
        } else {
            0.0
        }
    }
}