package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.BaseNewsEntity
import com.cdzyhd.big_platform.exception.CommonException
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria

/**
 * 基地新闻业务模型
 * 处理基地新闻相关的业务逻辑
 */
class BaseNewsModel {

    companion object {

        /**
         * 获取新闻分页列表
         */
        fun getPageList(query: String, pageable: Pageable): JSONObject {
            return CommonMongoEntityModel.getPageList(
                StaticBean.baseNewsRepository,
                query,
                pageable
            ) as JSONObject
        }

        /**
         * 获取新闻列表（不分页）
         */
        fun getList(query: String): List<BaseNewsEntity> {
            return StaticBean.baseNewsRepository.getList(Document.parse(query))
        }

        /**
         * 根据ID获取新闻详情
         */
        fun getOneById(newsId: String): BaseNewsEntity? {
            return CommonMongoEntityModel.getOneById(
                StaticBean.baseNewsRepository,
                "newsId",
                newsId
            ) as BaseNewsEntity
        }

        /**
         * 根据ID删除新闻
         */
        fun deleteOneById(newsId: String): Boolean {
            return CommonMongoEntityModel.deleteOneById(
                StaticBean.baseNewsRepository,
                "newsId",
                newsId
            ) as Boolean
        }

        /**
         * 新增或编辑新闻
         */
        fun addOrEdit(infoObject: JSONObject): BaseNewsEntity {
            return CommonMongoEntityModel.addOrEdit(
                StaticBean.baseNewsRepository,
                BaseNewsEntity(),
                "newsId",
                infoObject
            ) as BaseNewsEntity
        }

        /**
         * 增加新闻浏览量
         */
        fun increaseViews(newsId: String): Boolean {
            try {
                val news = getOneById(newsId)
                if (news != null) {
                    news.views = (news.views ?: 0L) + 1
                    StaticBean.baseNewsRepository.save(news)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "增加浏览量失败: ${e.message}")
            }
        }

        /**
         * 获取热门新闻列表
         */
        fun getHotNewsList(limit: Int = 10): List<BaseNewsEntity> {
            return StaticBean.baseNewsRepository.findTop10ByStatusAndDeletedOrderByViewsDesc(
                "published", 0
            ).take(limit)
        }

        /**
         * 获取置顶新闻列表
         */
        fun getTopNewsList(): List<BaseNewsEntity> {
            return StaticBean.baseNewsRepository.findAllByIsTopAndStatusAndDeletedOrderByPublishTimeDesc(
                true, "published", 0
            )
        }

        /**
         * 获取轮播新闻列表
         */
        fun getCarouselNewsList(): List<BaseNewsEntity> {
            // 获取置顶且热门的新闻作为轮播内容
            val criteria = Criteria()
                .and("status").`is`("published")
                .and("deleted").`is`(0)
                .and("isHot").`is`(true)

            val matchOperation = Aggregation.match(criteria)
            val sortOperation = Aggregation.sort(Sort.Direction.DESC, "publishTime")
            val limitOperation = Aggregation.limit(5)

            val pipeline = Aggregation.newAggregation(
                matchOperation,
                sortOperation,
                limitOperation
            )

            return StaticBean.mongoTemplate.aggregate(
                pipeline,
                "base_news",
                BaseNewsEntity::class.java
            ).mappedResults
        }

        /**
         * 根据分类获取新闻列表
         */
        fun getNewsByCategory(category: String, limit: Int = 10): List<BaseNewsEntity> {
            return StaticBean.baseNewsRepository.findAllByCategoryAndStatusAndDeletedOrderByPublishTimeDesc(
                category, "published", 0
            ).take(limit)
        }

        /**
         * 搜索新闻
         */
        fun searchNews(keyword: String, pageable: Pageable): JSONObject {
            val criteria = Criteria()
                .and("status").`is`("published")
                .and("deleted").`is`(0)

            if (keyword.isNotBlank()) {
                criteria.orOperator(
                    Criteria.where("title").regex(keyword, "i"),
                    Criteria.where("summary").regex(keyword, "i"),
                    Criteria.where("content").regex(keyword, "i")
                )
            }

            // 构建聚合查询
            val matchOperation = Aggregation.match(criteria)
            val sortOperation = Aggregation.sort(Sort.Direction.DESC, "publishTime")

            // 统计总数
            val countAggregation = Aggregation.count().`as`("count")
            val countPipeline = Aggregation.newAggregation(matchOperation, countAggregation)
            val countResult = StaticBean.mongoTemplate.aggregate(
                countPipeline,
                "base_news",
                HashMap::class.java
            ).mappedResults
            val totalElements = if (countResult.isEmpty()) 0 else countResult[0]["count"] as Int

            // 分页查询
            val skipOperation = Aggregation.skip(pageable.pageNumber * pageable.pageSize.toLong())
            val limitOperation = Aggregation.limit(pageable.pageSize.toLong())
            val dataPipeline = Aggregation.newAggregation(
                matchOperation,
                sortOperation,
                skipOperation,
                limitOperation
            )

            val resultList = StaticBean.mongoTemplate.aggregate(
                dataPipeline,
                "base_news",
                BaseNewsEntity::class.java
            ).mappedResults

            // 构建返回对象
            val totalPages = (totalElements + pageable.pageSize - 1) / pageable.pageSize
            val returnObject = JSONObject.parseObject("""
                {
                    "number":${pageable.pageNumber},
                    "size":${pageable.pageSize},
                    "totalElements":${totalElements},
                    "totalPages":${totalPages}
                }
            """.trimIndent())
            returnObject["content"] = resultList

            return returnObject
        }

        /**
         * 获取相关新闻
         */
        fun getRelatedNews(newsId: String, category: String, limit: Int = 5): List<BaseNewsEntity> {
            return StaticBean.baseNewsRepository.findTop5ByCategoryAndStatusAndDeletedAndNewsIdNotOrderByPublishTimeDesc(
                category, "published", 0, newsId
            ).take(limit)
        }

        /**
         * 发布新闻
         */
        fun publishNews(newsId: String): Boolean {
            try {
                val news = getOneById(newsId)
                if (news != null) {
                    news.status = "published"
                    news.publishTime = System.currentTimeMillis()
                    news.updateTime = System.currentTimeMillis()
                    StaticBean.baseNewsRepository.save(news)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "发布新闻失败: ${e.message}")
            }
        }

        /**
         * 撤回新闻
         */
        fun unpublishNews(newsId: String): Boolean {
            try {
                val news = getOneById(newsId)
                if (news != null) {
                    news.status = "draft"
                    news.updateTime = System.currentTimeMillis()
                    StaticBean.baseNewsRepository.save(news)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "撤回新闻失败: ${e.message}")
            }
        }

        /**
         * 设置新闻为热门
         */
        fun setHotNews(newsId: String, isHot: Boolean): Boolean {
            try {
                val news = getOneById(newsId)
                if (news != null) {
                    news.isHot = isHot
                    news.updateTime = System.currentTimeMillis()
                    StaticBean.baseNewsRepository.save(news)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "设置热门状态失败: ${e.message}")
            }
        }

        /**
         * 设置新闻置顶
         */
        fun setTopNews(newsId: String, isTop: Boolean): Boolean {
            try {
                val news = getOneById(newsId)
                if (news != null) {
                    news.isTop = isTop
                    news.updateTime = System.currentTimeMillis()
                    StaticBean.baseNewsRepository.save(news)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "设置置顶状态失败: ${e.message}")
            }
        }

        /**
         * 获取新闻统计信息
         */
        fun getNewsStatistics(): JSONObject {
            val totalCount = StaticBean.baseNewsRepository.countByStatusAndDeleted("published", 0)
            val hotCount = StaticBean.baseNewsRepository.countByIsHotAndStatusAndDeleted(true, "published", 0)
            val topCount = StaticBean.baseNewsRepository.countByIsTopAndStatusAndDeleted(true, "published", 0)

            val statistics = JSONObject()
            statistics["totalCount"] = totalCount
            statistics["hotCount"] = hotCount
            statistics["topCount"] = topCount

            // 按分类统计
            val categoryStats = JSONObject()
            categoryStats["base_news"] = StaticBean.baseNewsRepository.countByCategoryAndStatusAndDeleted("base_news", "published", 0)
            categoryStats["activity"] = StaticBean.baseNewsRepository.countByCategoryAndStatusAndDeleted("activity", "published", 0)
            categoryStats["academic"] = StaticBean.baseNewsRepository.countByCategoryAndStatusAndDeleted("academic", "published", 0)
            categoryStats["achievement"] = StaticBean.baseNewsRepository.countByCategoryAndStatusAndDeleted("achievement", "published", 0)
            statistics["categoryStats"] = categoryStats

            return statistics
        }
    }
}