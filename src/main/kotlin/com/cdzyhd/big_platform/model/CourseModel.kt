package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.CourseEntity
import java.util.*

class CourseModel {

    // 新增一门课程
    fun addOneCourse(entity: JSONObject): CourseEntity {
        val courseEntity = CourseEntity()
        val course = CommonMongoEntityModel.add(
            StaticBean.courseRepository,
            courseEntity,
            entity
        )
        course as CourseEntity
        // 生成appId和appSecret
        val appSecret = UUID.randomUUID().toString().replace("-", "")
        course.appId = course.courseId
        course.appSecret = appSecret
        StaticBean.courseRepository.save(course)
        // 初始化统计
        StatisticModel().initRedisValue(course.courseId)
        return course
    }
}