package com.cdzyhd.big_platform.model

import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.annotation.NeedToken
import com.cdzyhd.big_platform.exception.CommonException
import io.jsonwebtoken.Claims


class TokenModel {

    // 生成token存储key
    private fun genTokenKey(roleName: String, token: String): String {
        return "bigPlatform~$roleName~$token"
    }

    /**
     * 普通用户-生成课程token并保存到redis
     * role为course，存在audience中
     * courseId存在subject中
     */
    fun genCourseTokenAndSave(
        userId: String,
        courseId: String,
        expireDay: Int
    ): String {
        try {
            // 生成token
            val jwtModel = JWTModel()
            jwtModel.expireDays = expireDay
            val token = jwtModel.gen_token("bigPlatform", userId, courseId, "course")
            // 存入redis 记录格式
            val tokenName = genTokenKey("course", token)
            StaticBean.redisService.set(tokenName, userId, jwtModel.expireDays * 24 * 3600L)
            return token
        } catch (e: Exception) {
            throw CommonException("000001", "生成token失败")
        }
    }

    // 普通用户-课程token检测
    fun checkCourseToken(token: String): Boolean {
        val jwtModel = JWTModel()
        val claims: Claims = jwtModel.parse_token(token)
        return if (claims.isEmpty()) {
            false
        } else {
            StaticBean.redisService.hasKey(genTokenKey("course", token))
        }
    }

    // 普通用户-生成token并保存到redis
    fun genTokenAndSave(
        userId: String,
        roleName: String,
        expireDay: Int
    ): String {
        try {
            // 生成token
            val jwtModel = JWTModel()
            jwtModel.expireDays = expireDay
            val token = jwtModel.gen_token("bigPlatform", userId, userId, roleName)
            // 存入redis 记录格式
            val tokenName = genTokenKey(roleName, token)
            StaticBean.redisService.set(tokenName, userId, jwtModel.expireDays * 24 * 3600L)
            return token
        } catch (e: Exception) {
            throw CommonException("000001", "生成token失败")
        }
    }

    // 普通用户-token检测
    fun checkToken(token: String, roleName: String): Boolean {
        val jwtModel = JWTModel()
        val claims: Claims = jwtModel.parse_token(token)
        return if (claims.isEmpty()) {
            false
        } else {
            StaticBean.redisService.hasKey(genTokenKey(roleName, token))
        }
    }

    // 普通用户-更新token
    fun refreshToken(
        token: String,
        roleName: String,
        expireDay: Int
    ): String {
        // 先检测token是否合法
        val check = checkToken(token, roleName)
        val claims: Claims = JWTModel().parse_token(token)
        val unionUserId = claims.id
        val userId = claims.subject
        if (check) {
            // 生成新token
            return genTokenAndSave(userId, roleName, expireDay)
        } else {
            throw CommonException("000001", "token不正确或已失效")
        }
    }

    // 普通用户-删除token
    fun removeToken(token: String, roleName: String): Boolean {
        val tokenName = genTokenKey(roleName, token)
        StaticBean.redisService.set(tokenName, "", 1)
        return true
    }

    // 管理员-生成token并保存到redis
    fun genAdminTokenAndSave(adminUserId: String, expireDay: Int): String {
        try {
            // 生成token
            val roleName = "administrator"
            val jwtModel = JWTModel()
            jwtModel.expireDays = expireDay
            val token = jwtModel.gen_token("bigPlatform", adminUserId, adminUserId, roleName)
            // 存入redis 记录格式
            val tokenName = genTokenKey("administrator", token)
            StaticBean.redisService.set(tokenName, adminUserId, jwtModel.expireDays * 24 * 3600L)
            return token
        } catch (e: Exception) {
            throw CommonException("000001", "生成token失败")
        }
    }

    // 管理员-token检测
    fun checkAdminToken(token: String): Boolean {
        val roleName = "administrator"
        val jwtModel = JWTModel()
        val claims: Claims = jwtModel.parse_token(token)
        return if (claims.isEmpty()) {
            false
        } else {
            StaticBean.redisService.hasKey("$roleName~$token")
        }
    }

    // 管理员用户-删除token
    fun removeAdminToken(token: String): Boolean {
        val tokenName = genTokenKey("administrator", token)
        StaticBean.redisService.set(tokenName, "", 1)
        return true
    }

    // 普通用户-更新token
    fun refreshAdminToken(token: String, expireDay: Int): String {
        // 先检测token是否合法
        val check = checkAdminToken(token)
        if (check) {
            val claims: Claims = JWTModel().parse_token(token)
            val adminUserId = claims.id
            // 生成新token
            return genAdminTokenAndSave(adminUserId, expireDay)
        } else {
            throw CommonException("000001", "token不正确或已失效")
        }
    }
}