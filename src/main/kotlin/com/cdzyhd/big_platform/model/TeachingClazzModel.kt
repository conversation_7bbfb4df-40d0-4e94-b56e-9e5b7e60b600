package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.ClazzEntity
import com.cdzyhd.big_platform.entity.TeachingClazzEntity
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.exception.CommonException
import com.cdzyhd.big_platform.util.ImportExcelUtil
import com.cdzyhd.big_platform.vo.StudentImportVo
import com.cdzyhd.big_platform.vo.TeachingClazzImportVo
import org.apache.commons.collections4.CollectionUtils
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.mongodb.core.query.UpdateDefinition
import org.springframework.web.multipart.MultipartFile

// 教学班详情
class TeachingClazzModel {
    // 获取单个教学班详情
    fun getOneDetail(teachingClazzId: String): TeachingClazzEntity {
        val teachingClazz = StaticBean.teachingClazzRepository.findFirstByTeachingClazzId(teachingClazzId)
        if (teachingClazz != null) {
            // 获取该教学班学生总人数
            val studentNumber =
                StaticBean.userRepository.countByTeachingClazzIdsContainsAndRole(teachingClazzId, "student")
            teachingClazz.clazzNames = ClazzModel().multiGetClazzNamesByIds(teachingClazz.clazzIds)
            teachingClazz.studentNumber = studentNumber
            return teachingClazz
        } else {
            throw CommonException("000001", "未找到该教学班")
        }
    }

    // 新增一个教学班
    fun addOne(info: JSONObject): TeachingClazzEntity {
        val teachingClazz = TeachingClazzEntity()
        teachingClazz.clazzIds = info.getJSONArray("clazzIds")
        teachingClazz.name = info.getString("name")
        teachingClazz.teacherId = info.getString("teacherId")
        // 给对应的学生加上教学班
        // 找到对应行政班的所有学生
        for (clazzId in teachingClazz.clazzIds) {
            clazzId as String
            this.editUserTeachingClazzIdsWhenAdd(clazzId, teachingClazz.teachingClazzId)
        }
        StaticBean.teachingClazzRepository.save(teachingClazz)
        return teachingClazz
    }

    // 当教学班新增某个行政班时，修改行政班所有学生的教学班ids
    private fun editUserTeachingClazzIdsWhenAdd(clazzId: String, teachingClazzId: String) {
        val oneClazzStudentList = StaticBean.userRepository.getAllByClazzIdAndRole(clazzId, "student")
        // 循环遍历学生，设置教学班
        for (student in oneClazzStudentList) {
            this.addOneStudent(teachingClazzId, student)
        }
    }

    // 当教学班删除某个行政班时，修改行政班所有学生的教学班ids
    private fun editUserTeachingClazzIdsWhenDel(clazzId: String, teachingClazzId: String) {
        val oneClazzStudentList = StaticBean.userRepository.getAllByClazzIdAndRole(clazzId, "student")
        // 循环遍历学生，设置教学班
        for (student in oneClazzStudentList) {
            this.deleteOneStudent(teachingClazzId, student)
        }
    }

    // 保存学生的教学班信息 todo 和saveAll的效率测试
    private fun saveStudentTeachingClazzIds(student: UserEntity) {
        val update = Update()
        update.set("teachingClazzIds", student.teachingClazzIds)
        val query = Query(Criteria.where("userId").`is`(student.userId))
        StaticBean.mongoTemplate.updateFirst(query, update, UserEntity::class.java)
    }

    // 编辑一个教学班
    fun editOne(info: JSONObject): TeachingClazzEntity {
        val teachingClazz =
            StaticBean.teachingClazzRepository.findFirstByTeachingClazzId(info.getString("teachingClazzId"))
        teachingClazz.name = info.getString("name")
        val clazzIds = info.getJSONArray("clazzIds")
        // 给对应的学生加上教学班

        for (clazzId in clazzIds) {
            clazzId as String
            // 新增了行政班
            if (!teachingClazz.clazzIds.contains(clazzId)) {
                this.editUserTeachingClazzIdsWhenAdd(clazzId, teachingClazz.teachingClazzId)
            }
        }
        // 删除行政班级
        for (clazzId in teachingClazz.clazzIds) {
            clazzId as String
            if (!clazzIds.contains(clazzId)) {
                this.editUserTeachingClazzIdsWhenDel(clazzId, teachingClazz.teachingClazzId)
            }
        }
        teachingClazz.clazzIds = clazzIds
        StaticBean.teachingClazzRepository.save(teachingClazz)
        return teachingClazz
    }

    // 教学班中删除一个学生
    fun deleteOneStudent(teachingClazzId: String, student: UserEntity) {
        if (student.teachingClazzIds.contains(teachingClazzId)) {
            student.teachingClazzIds.remove(teachingClazzId)
            // 保存学生的教学班id信息
            this.saveStudentTeachingClazzIds(student)
        }
    }

    // 教学班中新增一个学生
    fun addOneStudent(teachingClazzId: String, student: UserEntity) {
        if (!student.teachingClazzIds.contains(teachingClazzId)) {
            student.teachingClazzIds.add(teachingClazzId)
            // 保存学生的教学班id信息
            this.saveStudentTeachingClazzIds(student)
        }
    }

    // 批量导入学生-班级内
    fun importStudentsByTeachingClazzId(multipartFile: MultipartFile, teachingClazzId: String) {
        val arrayLists: ArrayList<ArrayList<Any>> = ImportExcelUtil.readExcel(multipartFile)
        val studentList = ArrayList<UserEntity>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    if ("学生姓名" == fieldText) {
                        fieldNames[i] = "name"
                        fieldIndex["name"] = i
                    } else if ("*学生学号" == fieldText) {
                        fieldNames[i] = "account"
                        fieldIndex["account"] = i
                    }
                    i++
                }
            }
            for (i in 1 until arrayLists.size) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects.size != 1) {
                    if (objects.isNotEmpty()) {
                        if (!fieldIndex.containsKey("account")) {
                            throw CommonException("000001", "导入失败。未找到学生学号列")
                        }
                        val accountIndex = fieldIndex["account"]
                        val account = objects[accountIndex!!] as String
                        if ("" == account.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号未填写或为空格")
                        }

                        // 判断学号是否已存在
                        if (!userModel.isUserExisted(account)) {
                            throw CommonException(
                                "000001",
                                "导入失败。您的表格第" + num + "行学生学号" + account + "在系统中不存在，请先在行政班级中添加学生！"
                            )
                        }
                        val student = StaticBean.userRepository.findFirstByAccount(account)
                        studentList.add(student)
                    }
                }
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (studentList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            for (student in studentList) {
                // 执行在班级中新增学生
                this.addOneStudent(teachingClazzId, student)
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }

    // 批量导入教学班
    fun importTeachingClazz(multipartFile: MultipartFile): HashMap<String, HashSet<String>> {
        val arrayLists = ImportExcelUtil.readExcel(multipartFile)
        val clazzList = ArrayList<TeachingClazzImportVo>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList!!.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    if ("*教师工号" == fieldText) {
                        fieldNames[i] = "teacherAccount"
                        fieldIndex["teacherAccount"] = i
                    } else if ("*学生学号" == fieldText) {
                        fieldNames[i] = "studentAccount"
                        fieldIndex["studentAccount"] = i
                    } else if ("*教学班名称" == fieldText) {
                        fieldNames[i] = "teachingClazzName"
                        fieldIndex["teachingClazzName"] = i
                    }
                    i++
                }
            }
            val totalNum = arrayLists.size
            for (i in 1 until totalNum) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects!!.size != 1) {
                    if (objects.isNotEmpty()) {
                        if (!fieldIndex.containsKey("teacherAccount")) {
                            throw CommonException("000001", "导入失败。未找到教师工号列")
                        }
                        val teacherAccountIndex = fieldIndex["teacherAccount"]!!
                        val teacherAccount = objects[teacherAccountIndex] as String
                        if ("" == teacherAccount.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号未填写或为空格")
                        }
                        if (!fieldIndex.containsKey("studentAccount")) {
                            throw CommonException("000001", "导入失败。未找到学生学号列")
                        }
                        val studentAccountIndex = fieldIndex["studentAccount"]!!
                        val studentAccount = objects[studentAccountIndex] as String
                        if ("" == studentAccount.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号未填写或为空格")
                        }
                        if (!fieldIndex.containsKey("teachingClazzName")) {
                            throw CommonException("000001", "导入失败。未找到教学班名称列")
                        }
                        val teachingClazzNameIndex = fieldIndex["teachingClazzName"]!!
                        val teachingClazzName = objects[teachingClazzNameIndex] as String
                        if ("" == teacherAccount.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教学班名称未填写或为空格")
                        }

                        // 判断教师工号是否存在
                        if (!userModel.isUserExisted(teacherAccount)) {
                            throw CommonException(
                                "000001",
                                "导入失败。您的表格第" + num + "行教师工号" + teacherAccount + "在系统中不存在，请先在教师管理中添加教师！"
                            )
                        }
                        // 判断学号是否已存在
                        if (!userModel.isUserExisted(studentAccount)) {
                            throw CommonException(
                                "000001",
                                "导入失败。您的表格第" + num + "行学生学号" + studentAccount + "在系统中不存在，请先在行政班级中添加学生！"
                            )
                        }
                        val teacher = StaticBean.userRepository.findFirstByAccount(teacherAccount)
                        val student = StaticBean.userRepository.findFirstByAccount(studentAccount)
                        val teachingClazzImportVo = TeachingClazzImportVo()
                        teachingClazzImportVo.name = teachingClazzName
                        teachingClazzImportVo.teacherId = teacher.userId
                        teachingClazzImportVo.student = student
                        clazzList.add(teachingClazzImportVo)
                    }
                }
                println("$num/$totalNum")
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (clazzList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            val teacherObject = HashMap<String, HashSet<String>>()
            for (clazz in clazzList) {
                // 判断教学班是否存在
                var teachingClazz = StaticBean.teachingClazzRepository.findFirstByNameAndTeacherId(
                    clazz.name,
                    clazz.teacherId
                )
                if (teachingClazz == null) {// 如果教学班不存在 就创建教学班
                    teachingClazz = TeachingClazzEntity()
                    teachingClazz.teacherId = clazz.teacherId
                    teachingClazz.name = clazz.name
                    StaticBean.teachingClazzRepository.save(teachingClazz)
                }
                // 在教学班中新增一个学生
                this.addOneStudent(teachingClazz.teachingClazzId, clazz.student)
                // 生成返回信息 教师和教学班id表
                if (teacherObject.containsKey(clazz.teacherId)) {
                    teacherObject[clazz.teacherId]?.add(teachingClazz.teachingClazzId)
                } else {
                    val clazzIds = HashSet<String>()
                    clazzIds.add(teachingClazz.teachingClazzId)
                    teacherObject[clazz.teacherId] = clazzIds
                }
            }
            // 返回信息
            return teacherObject
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }
}