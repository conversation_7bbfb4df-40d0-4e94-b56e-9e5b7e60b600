package com.cdzyhd.big_platform.model

import cn.hutool.core.date.DateUtil
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.service.RedisService
import org.apache.poi.ss.formula.functions.T
import java.util.*
import kotlin.collections.HashMap


// 统计model
/**
"viewNumber":1,// 浏览量 获取一次token增加一次浏览量
"userNumber":1,// 课程总参与人数
"scoreNumber":1,// 总完成次数
"commentNumber":50,// 总评价次数
"averageScore":90,// 平均分
"maxScore":100,// 最高分
"minScore":50,// 最小分
"averageUsedTime":102,// 平均用时 秒
"maxUsedTime":102,// 最大用时 秒
"minUsedTime":102,// 最低用时 秒
"completePoint":0.6,// 完成率
"passPoint":0.4,// 分数超过60的比率
"excellentNumber":500,// 优秀人次 分数记录中分数大于85
"standardNumber":500,// 达标人次 分数记录中分数小于等于85 大于等于60
"failNumber":500,// 不达标人次 分数记录中分数小于60
 */
/**
 * 统计存储和计算方法
 * 频繁的统计 放redis
 * 不频繁的统计 放数据库
 * 大量的统计 每日夜间进行
 */
class StatisticModel {
    private val redisService: RedisService = StaticBean.redisService
    private val redisKey = "bigPlatform_statistic_course_"

    // 设置某个课程redis统计参数
    private fun setRedisValue(courseId: String, key: String, value: Any) {
        redisService.hset(redisKey + courseId, "updateTime", Date().time) // 记录更新时间
        redisService.hset(redisKey + courseId, key, value)
    }

    // 获取某个课程redis统计参数
    fun getRedisValue(courseId: String, key: String): Any {
        return redisService.hget(redisKey + courseId, key)
    }

    // 获取某个课程实时统计数据
    fun getCourseRealTimeValue(courseId: String): HashMap<String, Any> {
        val resultObject = HashMap<String, Any>()
        resultObject["viewNumber"] = getRedisValue(courseId, "viewNumber") as Int
        resultObject["scoreNumber"] = getRedisValue(courseId, "scoreNumber") as Int
        resultObject["userNumber"] = getRedisValue(courseId, "userNumber") as Int
        resultObject["averageUsedTime"] = getRedisValue(courseId, "averageUsedTime") as Int
        resultObject["commentNumber"] = getRedisValue(courseId, "commentNumber") as Int
        resultObject["completePoint"] = getRedisValue(courseId, "completePoint") as Double
        resultObject["passPoint"] = getRedisValue(courseId, "passPoint") as Double
        resultObject["excellentNumber"] = getRedisValue(courseId, "excellentNumber") as Int
        resultObject["standardNumber"] = getRedisValue(courseId, "standardNumber") as Int
        resultObject["failNumber"] = getRedisValue(courseId, "failNumber") as Int
        resultObject["updateTime"] = getRedisValue(courseId, "updateTime") as Long
        resultObject["maxScore"] = getRedisValue(courseId, "maxScore") as Double
        resultObject["minScore"] = getRedisValue(courseId, "minScore") as Double
        resultObject["averageScore"] = getRedisValue(courseId, "averageScore") as Double
        resultObject["maxUsedTime"] = getRedisValue(courseId, "maxUsedTime") as Int
        resultObject["minUsedTime"] = getRedisValue(courseId, "minUsedTime") as Int
        resultObject["averageUsedTime"] = getRedisValue(courseId, "averageUsedTime") as Int

        return resultObject
    }

    /**
     * 初始化课程统计redis统计
     * 在创建课程时调用
     */
    fun initRedisValue(courseId: String) {
        val initMap = HashMap<String, Any>()
        // 浏览量
        initMap["viewNumber"] = 0
        // 实验人次
        initMap["scoreNumber"] = 0
        // 实验人数
        initMap["userNumber"] = 0
        // 所有分数记录的用时和，用于计算平均用时
        initMap["totalUsedTime"] = 0L
        // 平均用时
        initMap["averageUsedTime"] = 0
        // 最多用时
        initMap["maxUsedTime"] = 0
        // 最少用时
        initMap["minUsedTime"] = 0
        // 所有分数记录的分数和，用于计算平均分
        initMap["totalScore"] = 0.0
        // 最高分
        initMap["maxScore"] = 0.0
        // 最低分
        initMap["minScore"] = 0.0
        // 平均分
        initMap["averageScore"] = 0.0
        // 评价数量
        initMap["commentNumber"] = 0
        // 总完成数量 传分参数status为1
        initMap["doneNumber"] = 0
        // 完成率
        initMap["completePoint"] = 0.0
        // 总通过数量 实验分数超过60分
        initMap["passNumber"] = 0
        // 完成率
        initMap["passPoint"] = 0.0
        // 优秀人次
        initMap["excellentNumber"] = 0
        // 达标人次
        initMap["standardNumber"] = 0
        // 不达标人次
        initMap["failNumber"] = 0

        initMap["updateTime"] = Date().time
        redisService.hsetAll(redisKey + courseId, initMap)
    }

    /**
     * 增加一个评价数量
     * 在获取access_token的时候增加浏览量，表明此时用户已经进入了课程实验
     * openController.getAccessToken
     */
    fun addOneCommentNumber(courseId: String) {
        var commentNumber = getRedisValue(courseId, "commentNumber") as Int
        commentNumber++
        setRedisValue(courseId, "commentNumber", commentNumber)
    }

    /**
     * 增加一次课程浏览量
     * 在获取access_token的时候增加浏览量，表明此时用户已经进入了课程实验
     * openController.getAccessToken
     */
    fun addOneViewNumber(courseId: String) {
        var viewNumber = getRedisValue(courseId, "viewNumber") as Int
        viewNumber++
        setRedisValue(courseId, "viewNumber", viewNumber)
    }

    /**
     * 增加一个实验人数
     * 同一个实验，一个用户算一个人数
     * 开放课程完成时算一个
     * courseRecordModel.updateRecordAndAddScore
     */
    fun addOneUserNumber(courseId: String) {
        var userNumber = getRedisValue(courseId, "userNumber") as Int
        userNumber++
        setRedisValue(courseId, "userNumber", userNumber)
    }

    /**
     * 增加一次当日实验人次
     */
    private fun addOneDayScoreNumber(courseId: String) {
        val date = DateUtil.format(Date(), "yyyyMMdd")
        val field = "scoreNumber_$date"
        var scoreNumber = 1
        if (redisService.hasHashKey(redisKey + courseId, field)) {
            scoreNumber = getRedisValue(courseId, field) as Int
            scoreNumber++
        }
        setRedisValue(courseId, field, scoreNumber)
    }

    // 获取近30日实验人次
    fun getLast30DayScoreNumber(courseId: String): LinkedHashMap<String, Int> {
        val today = Date()
        val resultMap = LinkedHashMap<String, Int>()
        for (i in 0..29) {
            val date = DateUtil.offsetDay(today, -i)
            val dateString = DateUtil.format(date, "yyyyMMdd")
            val field = "scoreNumber_$dateString"
            if (redisService.hasHashKey(redisKey + courseId, field)) {
                val scoreNumber = getRedisValue(courseId, field) as Int
                resultMap[dateString] = scoreNumber
            } else {
                resultMap[dateString] = 0
            }
        }
        return resultMap
    }


    /**
     * 当新增一个分数时计算各个统计数据
     * 只计算开放记录的，因为开放记录在任务记录时也有一个分数
     */
    fun calWhenAddOneScore(courseId: String, usedTime: Long, score: Double, status: Int) {
        val calMap = HashMap<String, Any>()
        // 课程增加一次实验次数
        var scoreNumber = getRedisValue(courseId, "scoreNumber") as Int
        scoreNumber++
        calMap["scoreNumber"] = scoreNumber
        // 增加一次当日实验人次
        addOneDayScoreNumber(courseId)
        // 计算总用时 单位秒
        val totalUsedTimeGet = getRedisValue(courseId, "totalUsedTime") // 取出来可能是int也可能是long，需要处理
        var totalUsedTime = 0L
        if (totalUsedTimeGet.javaClass == Long::class.java) {
            totalUsedTimeGet as Long
            totalUsedTime = totalUsedTimeGet
        } else {
            totalUsedTimeGet as Int
            totalUsedTime = totalUsedTimeGet.toLong()
        }
        totalUsedTime += usedTime
        calMap["totalUsedTime"] = totalUsedTime
        // 计算平均用时 单位秒
        val averageUsedTime = totalUsedTime / scoreNumber
        calMap["averageUsedTime"] = (averageUsedTime)
        // 计算最多用时
        val maxUsedTime = getRedisValue(courseId, "maxUsedTime") as Int
        if (usedTime > maxUsedTime) {
            calMap["maxUsedTime"] = usedTime
        }
        // 计算最少用时
        val minUsedTime = getRedisValue(courseId, "minUsedTime") as Int
        if (usedTime < minUsedTime || minUsedTime == 0) {
            calMap["minUsedTime"] = usedTime
        }
        // 计算最高分
        val maxScore = getRedisValue(courseId, "maxScore") as Double
        if (score > maxScore) {
            calMap["maxScore"] = score
        }
        // 计算最低分
        val minScore = getRedisValue(courseId, "minScore") as Double
        if (score < minScore || minScore == 0.0) {
            calMap["minScore"] = score
        }
        // 计算分数和
        var totalScore = getRedisValue(courseId, "totalScore") as Double
        totalScore += score
        calMap["totalScore"] = totalScore
        // 计算平均分
        val averageScore = totalScore / scoreNumber
        calMap["averageScore"] = (averageScore)
        // 计算完成率
        var doneNumber = getRedisValue(courseId, "doneNumber") as Int
        if (status == 1) {// 此次已完成
            doneNumber += 1
            calMap["doneNumber"] = doneNumber
            val completePoint = doneNumber.toDouble() / scoreNumber
            calMap["completePoint"] = completePoint
        }
        val completePoint = doneNumber.toDouble() / scoreNumber
        calMap["completePoint"] = completePoint
        // 计算通过率
        var passNumber = getRedisValue(courseId, "passNumber") as Int
        if (score >= 60.0) {// 此次已通过 todo 自定义通过分数
            passNumber += 1
            calMap["passNumber"] = passNumber
        }
        val passPoint = passNumber.toDouble() / scoreNumber
        calMap["passPoint"] = passPoint
        // 不达标人次
        if (score < 60) {
            var failNumber = getRedisValue(courseId, "failNumber") as Int
            failNumber += 1
            calMap["failNumber"] = failNumber
        }
        // 达标人次
        if (score >= 60 && score < 85) {
            var standardNumber = getRedisValue(courseId, "standardNumber") as Int
            standardNumber += 1
            calMap["standardNumber"] = standardNumber
        }
        // 优秀人次
        if (score > 85) {
            var excellentNumber = getRedisValue(courseId, "excellentNumber") as Int
            excellentNumber += 1
            calMap["excellentNumber"] = excellentNumber
        }
        // 更新统计时间
        calMap["updateTime"] = Date().time
        // 存储到redis
        redisService.hsetAll(redisKey + courseId, calMap)
    }
}