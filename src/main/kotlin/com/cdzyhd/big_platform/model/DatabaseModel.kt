package com.cdzyhd.big_platform.model

import com.cdzyhd.base.common.util.OutResponse
import org.springframework.scheduling.annotation.Async
import java.io.IOException

open class DatabaseModel {
    @Async
    open fun backupNow(backupSh: String): Bo<PERSON>an {
        val r = Runtime.getRuntime()
        //执行命令
        try {
            r.exec(backupSh)
            return true
        } catch (e: IOException) {
            e.printStackTrace()
            return false
        }
    }
}