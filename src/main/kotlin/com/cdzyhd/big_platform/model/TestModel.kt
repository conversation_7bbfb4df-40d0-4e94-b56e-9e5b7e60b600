package com.cdzyhd.big_platform.model

class TestModel {
    fun main(args: Array<String>) {
        test()
    }

    val a = 13
    var b = 3

    fun test(): Any {
        val a = A("c","d")
        return a
    }

    // data class
    data class Customer(val name: String, val email: String, val company: String)
    // 过滤list
    // listOf mapOf

    class A(val a: String,val b:String) {

    }

}