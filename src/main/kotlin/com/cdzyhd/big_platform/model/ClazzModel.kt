package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.ClazzEntity
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.exception.CommonException
import com.cdzyhd.big_platform.util.ImportExcelUtil
import com.cdzyhd.big_platform.vo.StudentImportVo
import org.apache.commons.collections4.CollectionUtils
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.web.multipart.MultipartFile

// 班级model
class ClazzModel {
    // 批量获取行政班完整名称
    fun multiGetClazzNamesByIds(clazzIds: JSONArray): JSONArray {
        // 聚合查询-获取关联班级名称
        val clazzNames = JSONArray()
        val matchOperation = Aggregation.match(Criteria.where("clazzId").`in`(clazzIds))
        val lookupMajor: LookupOperation =
            Aggregation.lookup("major", "majorId", "majorId", "majorEntity")
        val lookupGrade: LookupOperation =
            Aggregation.lookup("grade", "gradeId", "gradeId", "gradeEntity")
        var aggregation = Aggregation.newAggregation(lookupMajor, lookupGrade, matchOperation)
        val allList =
            StaticBean.mongoTemplate.aggregate(aggregation, "clazz", ClazzEntity::class.java).mappedResults
        for (clazz in allList) {
            val gradeEntity = clazz.gradeEntity.getJSONObject(0)
            val majorEntity = clazz.majorEntity.getJSONObject(0)
            clazzNames.add("${gradeEntity.getString("name")}-${majorEntity.getString("name")}-${clazz.name}")
        }
        return clazzNames
    }

    // 批量导入学生-班级内
    fun importStudentsByClazzId(multipartFile: MultipartFile, clazzId: String) {
        val arrayLists: ArrayList<ArrayList<Any>> = ImportExcelUtil.readExcel(multipartFile)
        val studentList = ArrayList<UserEntity>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    if ("*学生姓名" == fieldText) {
                        fieldNames[i] = "name"
                        fieldIndex["name"] = i
                    } else if ("*学生学号" == fieldText) {
                        fieldNames[i] = "account"
                        fieldIndex["account"] = i
                    } else if ("*学生性别" == fieldText) {
                        fieldNames[i] = "sex"
                        fieldIndex["sex"] = i
                    }
                    i++
                }
            }
            for (i in 1 until arrayLists.size) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects.size != 1) {
                    if (objects.isNotEmpty()) {
                        if (!fieldIndex.containsKey("name")) {
                            throw CommonException("000001", "导入失败。未找到学生姓名列")
                        }
                        val nameIndex = fieldIndex["name"]!!
                        val name = objects[nameIndex] as String
                        if ("" == name.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生姓名未填写或为空格")
                        }
                        if (name.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生姓名，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("account")) {
                            throw CommonException("000001", "导入失败。未找到学生学号列")
                        }
                        val accountIndex = fieldIndex["account"]
                        val account = objects[accountIndex!!] as String
                        if ("" == account.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号未填写或为空格")
                        }
                        if (account.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("sex")) {
                            throw CommonException("000001", "导入失败。未找到学生性别列")
                        }
                        val sexIndex = fieldIndex["sex"]
                        if (sexIndex!! >= objects.size) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教学生性别未填写。")
                        }
                        val sex = objects[sexIndex] as String
                        if ("" == sex.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生性别未填写或为空格")
                        }
                        if (!("男" == sex || "女" == sex)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生性别输入错误，仅支持输入男或女")
                        }

                        // 判断学号是否已存在
                        if (userModel.isUserExisted(account)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号" + account + "已存在！")
                        }

                        // 加入待添加列表
                        val student = UserEntity()
                        student.account = account
                        student.name = name
                        student.sex = sex
                        studentList.add(student)
                    }
                }
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (studentList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            for (student in studentList) {
                // 执行在班级中新增学生
                userModel.addStudentToOneClazz(student.account, student.name, student.sex, clazzId)
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }

    // 批量导入学生-班级外
    fun importStudents(multipartFile: MultipartFile) {
        val arrayLists = ImportExcelUtil.readExcel(multipartFile)
        val studentList = ArrayList<StudentImportVo>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList!!.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    if ("*学生姓名" == fieldText) {
                        fieldNames[i] = "name"
                        fieldIndex["name"] = i
                    } else if ("*学生学号" == fieldText) {
                        fieldNames[i] = "account"
                        fieldIndex["account"] = i
                    } else if ("*学生性别" == fieldText) {
                        fieldNames[i] = "sex"
                        fieldIndex["sex"] = i
                    } else if ("*学院名称" == fieldText) {
                        fieldNames[i] = "college"
                        fieldIndex["college"] = i
                    } else if ("*专业名称" == fieldText) {
                        fieldNames[i] = "major"
                        fieldIndex["major"] = i
                    } else if ("*年级名称" == fieldText) {
                        fieldNames[i] = "grade"
                        fieldIndex["grade"] = i
                    } else if ("*班级名称" == fieldText) {
                        fieldNames[i] = "clazz"
                        fieldIndex["clazz"] = i
                    }
                    i++
                }
            }
            val totalNum = arrayLists.size
            for (i in 1 until totalNum) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects!!.size != 1) {
                    if (objects.isNotEmpty()) {
                        if (!fieldIndex.containsKey("college")) {
                            throw CommonException("000001", "导入失败。未找到学院名称列")
                        }
                        val collegeIndex = fieldIndex["college"]!!
                        val college = objects[collegeIndex] as String
                        if ("" == college.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学院名称未填写或为空格")
                        }
                        if (college.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学院名称，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("major")) {
                            throw CommonException("000001", "导入失败。未找到专业名称列")
                        }
                        val majorIndex = fieldIndex["major"]!!
                        val major = objects[majorIndex] as String
                        if ("" == major.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行专业名称未填写或为空格")
                        }
                        if (major.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行专业名称，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("grade")) {
                            throw CommonException("000001", "导入失败。未找到年级名称列")
                        }
                        val gradeIndex = fieldIndex["grade"]!!
                        val grade = objects[gradeIndex] as String
                        if ("" == grade.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行年级名称未填写或为空格")
                        }
                        if (grade.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行年级名称，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("clazz")) {
                            throw CommonException("000001", "导入失败。未找到行政班级名称列")
                        }
                        val clazzIndex = fieldIndex["clazz"]!!
                        val clazz = objects[clazzIndex] as String
                        if ("" == clazz.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行班级名称未填写或为空格")
                        }
                        if (clazz.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行班级名称，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("name")) {
                            throw CommonException("000001", "导入失败。未找到学生姓名列")
                        }
                        val nameIndex = fieldIndex["name"]!!
                        val name = objects[nameIndex] as String
                        if ("" == name.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生姓名未填写或为空格")
                        }
                        if (name.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生姓名，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("account")) {
                            throw CommonException("000001", "导入失败。未找到学生学号列")
                        }
                        val accountIndex = fieldIndex["account"]
                        val account = objects[accountIndex!!] as String
                        if ("" == account.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号未填写或为空格")
                        }
                        if (account.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("sex")) {
                            throw CommonException("000001", "导入失败。未找到学生性别列")
                        }
                        val sexIndex = fieldIndex["sex"]
                        if (sexIndex!! >= objects.size) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教学生性别方式未填写。")
                        }
                        val sex = objects[sexIndex] as String
                        if ("" == sex.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生性别未填写或为空格")
                        }
                        if (!("男" == sex || "女" == sex)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生性别输入错误，仅支持输入男或女")
                        }
                        // 判断学号是否已存在
                        if (userModel.isUserExisted(account)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学生学号" + account + "已存在！")
                        }
                        val student = StudentImportVo()
                        student.clazzName = clazz
                        student.majorName = major
                        student.gradeName = grade
                        student.collegeName = college
                        student.account = account
                        student.sex = sex
                        student.name = name
                        student.collumNumber = num
                        // 预处理年级、学院、专业、班级信息
                        val studentTreated = importStudentPreAction(student)
                        // 加入待添加列表
                        studentList.add(studentTreated)
                    }
                }
                println("$num/$totalNum")
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (studentList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            for (student in studentList) {
                // 执行在班级中新增学生
                userModel.addStudentToOneClazz(
                    student.account,
                    student.name,
                    student.sex,
                    student.clazzId
                )
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }

    // 预处理年级、学院、专业、班级信息
    private fun importStudentPreAction(student: StudentImportVo): StudentImportVo {
        // 查找年级
        val grade = StaticBean.gradeRepository.findFirstByName(student.gradeName)
            ?: throw CommonException("000001", "导入失败。您的表格第" + student.collumNumber + "行年级名称不存在，请先在年级管理中创建后再进行导入！")
        // 查找学院
        val college = StaticBean.collegeRepository.findFirstByName(student.collegeName)
            ?: throw CommonException("000001", "导入失败。您的表格第" + student.collumNumber + "行学院不存在，请先在学院管理中创建后再进行导入！")
        // 查找专业
        val major = StaticBean.majorRepository.findFirstByNameAndCollegeId(student.majorName, college.collegeId)
            ?: throw CommonException(
                "000001",
                "导入失败。您的表格第" + student.collumNumber + "行专业在" + college.name + "学院中不存在，请先在专业管理中创建后再进行导入！"
            )
        // 查找班级
        var clazz = StaticBean.clazzRepository.findFirstByNameAndMajorIdAndGradeId(
            student.clazzName,
            major.majorId,
            grade.gradeId
        )
        if (clazz == null) {// 不存在时创建
            clazz = ClazzEntity()
            clazz.gradeId = grade.gradeId
            clazz.majorId = major.majorId
            clazz.name = student.clazzName
            StaticBean.clazzRepository.save(clazz)
        }
        student.clazzId = clazz.clazzId
        return student
    }
}