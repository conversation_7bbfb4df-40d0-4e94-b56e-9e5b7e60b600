package com.cdzyhd.big_platform.model

import cn.hutool.core.util.RandomUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.*
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.exception.CommonException
import com.cdzyhd.big_platform.po.RegAccountPo
import com.cdzyhd.big_platform.util.EmailTools
import org.springframework.scheduling.annotation.Async
import java.util.*
import javax.mail.Message
import javax.mail.Session
import javax.mail.Transport
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeUtility


// 用户model
class UserModel {
    // 判断用户信息是否重复
    fun checkUserInfoRepeat(type: String, value: String): Boolean? {
        when (type) {
            "account" -> {
                return StaticBean.userRepository.existsByAccount(value)
            }
            "phone" -> {
                return StaticBean.userRepository.existsByPhone(value)
            }
            "email" -> {
                return StaticBean.userRepository.existsByEmail(value)
            }
        }
        return true
    }

    // 账号注册
    fun regAccount(regAccountPo: RegAccountPo): UserEntity {
        // todo-安全 注册频率限制
        val systemConfig = StaticBean.redisService.hget("config_bigPlatform", "systemConfig") as String
        val systemConfigObject = JSONObject.parseObject(systemConfig)
        if (systemConfigObject.getString("regFunction") != "open") {
            throw CommonException("000001", "注册功能已关闭！")
        }
        // 检测邮箱验证码是否正确
        if (!StaticBean.redisService.hasKey("bigPlatform_regEmail_${regAccountPo.email}")) {
            throw CommonException("000001", "邮箱验证码已失效或不存在！")
        }
        val redisCode = StaticBean.redisService.get("bigPlatform_regEmail_${regAccountPo.email}") as String
        if (redisCode != regAccountPo.emailCode) {
            throw CommonException("000001", "邮箱验证码不正确！")
        }
        StaticBean.redisService.set("bigPlatform_bindEmail_${regAccountPo.email}", "", 1) // 立即过期
        // todo 安全-后端进行各项验证 用户名、手机号、邮箱规则
        if (StaticBean.userRepository.existsByAccount(regAccountPo.account)) {
            throw CommonException("000001", "该账号已被注册！")
        }
        if (StaticBean.userRepository.existsByPhone(regAccountPo.phone)) {
            throw CommonException("000001", "该手机号已被绑定！")
        }
        if (StaticBean.userRepository.existsByEmail(regAccountPo.email)) {
            throw CommonException("000001", "该邮箱已被绑定！")
        }
        val user = UserEntity()
        user.role = "student"
        user.account = regAccountPo.account
        user.name = regAccountPo.name
        user.password = PasswordUtil.generate(regAccountPo.password);
        user.email = regAccountPo.email
        user.phone = regAccountPo.phone
        user.sex = regAccountPo.sex
        user.identity = regAccountPo.identity
        user.asSocial = true
        user.hasLogin = true // 因为已经设置邮箱手机号，标记已登录
        user.clazzId = "1001" // 加入校外人员专用行政班级
        if (systemConfigObject.getString("regUserEnable") == "true") {// 注册用户是否默认启用
            user.deleted = 0
        } else {
            user.deleted = 1
        }
        StaticBean.userRepository.save(user)
        return user
    }

    // 账号密码登录验证
    fun loginCheckByAccount(account: String, password: String): UserEntity {
        // 查找用户名
        val userFind = StaticBean.userRepository.findFirstByAccount(account)
        if (userFind == null) {
            throw CommonException("username_not_exist", "该用户名不存在！")
        }
        if (userFind.deleted == 1) {
            if (!userFind.asSocial) {
                throw CommonException("username_disabled", "该账号已被禁用！")
            }
            if (userFind.asSocial) {
                throw CommonException("username_disabled", "该账号尚未启用！")
            }
        }
        // 检测密码是否正确
        val passwordCheck = PasswordUtil.verify(password, userFind.password)
        if (passwordCheck) {
            return userFind
        } else {
            throw CommonException("username_or_password_error", "账户或密码错误")
        }
    }

    // 登录平台 todo 安全-登录频率限制
    fun loginInPlatform(account: String, password: String): HashMap<String, Any> {
        val result = HashMap<String, Any>()
        val userFind = this.loginCheckByAccount(account, password)
        if (userFind != null) {// 登录成功
            // 生成token
            result["token"] = TokenModel().genTokenAndSave(userFind.userId, userFind.role, 1)
            // todo 隐藏不必要信息
            result["userInfo"] = userFind

        }
        return result
    }

    // 通过userId获取用户信息
    fun getInfoByUserId(userId: String): UserEntity {
        val userFind = StaticBean.userRepository.findFirstByUserId(userId)
        // 获取班级等信息
        if (userFind.role == "student") {
            val clazz = StaticBean.clazzRepository.findFirstByClazzId(userFind.clazzId)
            userFind.clazzEntity = JSONArray()
            userFind.clazzEntity.add(clazz)
            val grade = StaticBean.gradeRepository.findFirstByGradeId(clazz.gradeId)
            userFind.gradeEntity = JSONArray()
            userFind.gradeEntity.add(grade)
            val major = StaticBean.majorRepository.findFirstByMajorId(clazz.majorId)
            userFind.majorEntity = JSONArray()
            userFind.majorEntity.add(major)
            val college = StaticBean.collegeRepository.findFirstByCollegeId(major.collegeId)
            userFind.collegeEntity = JSONArray()
            userFind.collegeEntity.add(college)
        }
        if (userFind.role == "teacher") {
            val college = StaticBean.collegeRepository.findFirstByCollegeId(userFind.collegeId)
            userFind.collegeEntity = JSONArray()
            userFind.collegeEntity.add(college)
        }
        return userFind
    }

    // 用户忘记密码,发送邮件验证码 todo 安全-发送邮件频率限制
    fun forgetPasswordSendEmail(account: String): String {
        val user = StaticBean.userRepository.findFirstByAccount(account)
        if (user == null) {
            throw CommonException("000001", "此用户名不存在！")
        }
        if (user.email.isNullOrBlank()) {
            throw CommonException("000001", "此用户没有绑定邮箱！")
        }
        if (this.sendResetPasswordEmail(account, user.email)) {
            return user.email
        } else {
            return ""
        }
    }

    // 发送重置密码邮件
    private fun sendResetPasswordEmail(account: String, email: String): Boolean {
        try {
            val properties = Properties()
            properties["mail.transport.protocol"] = "smtp" // 连接协议
            properties["mail.smtp.host"] = "smtp.qq.com" // 主机名
            properties["mail.smtp.port"] = 465 // 端口号
            properties["mail.smtp.auth"] = "true"
            properties["mail.smtp.ssl.enable"] = "true" // 设置是否使用ssl安全连接 ---一般都使用
            properties["mail.debug"] = "true" // 设置是否显示debug信息 true 会在控制台显示相关信息
            // 得到回话对象
            val session: Session = Session.getInstance(properties)
            // 获取邮件对象
            val message = MimeMessage(session)
            // 设置发件人邮箱地址
            message.setFrom(InternetAddress("<EMAIL>"))
            // 设置收件人邮箱地址
            message.setRecipients(Message.RecipientType.TO, arrayOf<InternetAddress>(InternetAddress(email)))

            // 设置邮件标题 todo 平台名称
            val subject = "思政虚拟仿真实验平台密码重置"
            val encodedSubject: String = MimeUtility.encodeText(subject, MimeUtility.mimeCharset("gb2312"), null)
            message.setSubject(encodedSubject)
            // 生成redis标记
            val uuid = UUID.randomUUID().toString()
            RedisConfigModel().setConfig("bigPlatform_forgetPasswordEmail_$uuid", account, 1 * 24 * 3600L)
            // 设置邮件内容
            val dateTime: String = DateTimeTools.dateFormat(Date(), "YYYY-MM-dd HH:mm")
            //  判断是学生还是教师
            var text = ""
            text =
                "您正在进行重置密码操作，点击<a style='color:#fff;text-decoration:underline;padding: 0px 4px;' href='${StaticBean.commonConfig.apiUrl}/v1/user//resetPasswordByEmail/$uuid'>重置密码</a>，您的密码将重置为初始密码：<span style='color:#fff'>123456</span>。该链接24小时内有效，请及时处理。重置后，请尽快登录系统更改密码。"
            text += "<div style='margin-left:20px;'></div>"
            val messageText: String = EmailTools().buildEmailHtml(text, dateTime)
            message.setContent(messageText, "text/html;charset=utf-8")
            // 得到邮差对象
            val transport: Transport = session.getTransport()
            // 连接自己的邮箱账户
            transport.connect("<EMAIL>", "whojncmgdqqxiceg") // 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients())
            transport.close()
        } catch (e: Exception) {
            throw CommonException("000001", "重置密码邮件发送失败！")
        }
        return true
    }

    // 发送绑定邮箱邮件
    fun sendBindEmail(email: String): Boolean {
        // todo 安全 发送限制
        try {
            val properties = Properties()
            properties["mail.transport.protocol"] = "smtp" // 连接协议
            properties["mail.smtp.host"] = "smtp.qq.com" // 主机名
            properties["mail.smtp.port"] = 465 // 端口号
            properties["mail.smtp.auth"] = "true"
            properties["mail.smtp.ssl.enable"] = "true" // 设置是否使用ssl安全连接 ---一般都使用
            properties["mail.debug"] = "true" // 设置是否显示debug信息 true 会在控制台显示相关信息
            // 得到回话对象
            val session: Session = Session.getInstance(properties)
            // 获取邮件对象
            val message = MimeMessage(session)
            // 设置发件人邮箱地址
            message.setFrom(InternetAddress("<EMAIL>"))
            // 设置收件人邮箱地址
            message.setRecipients(Message.RecipientType.TO, arrayOf<InternetAddress>(InternetAddress(email)))

            // 设置邮件标题 todo 平台名称
            val subject = "思政虚拟仿真实验平台绑定邮箱验证码"
            val encodedSubject: String = MimeUtility.encodeText(subject, MimeUtility.mimeCharset("gb2312"), null)
            message.setSubject(encodedSubject)
            // 生成redis标记
            val code = RandomUtil.randomNumbers(6)
            RedisConfigModel().setConfig("bigPlatform_bindEmail_$email", code, 1 * 24 * 3600L)
            // 设置邮件内容
            val dateTime: String = DateTimeTools.dateFormat(Date(), "YYYY-MM-dd HH:mm")
            var text = ""
            text =
                "您正在进行绑定邮箱操作，您的验证码是：<span style='color:#fff'>$code</span>。该验证码24小时内有效，请及时处理。"
            text += "<div style='margin-left:20px;'></div>"
            val messageText: String = EmailTools().buildEmailHtml(text, dateTime)
            message.setContent(messageText, "text/html;charset=utf-8")
            // 得到邮差对象
            val transport: Transport = session.getTransport()
            // 连接自己的邮箱账户
            transport.connect("<EMAIL>", "whojncmgdqqxiceg") // 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients())
            transport.close()
        } catch (e: Exception) {
            throw CommonException("000001", "邮件发送失败！")
        }
        return true
    }

    // 发送注册邮箱邮件
    fun sendRegEmail(email: String): Boolean {
        // todo 安全 发送限制
        try {
            val properties = Properties()
            // todo 邮件文件存储到配置文件
            properties["mail.transport.protocol"] = "smtp" // 连接协议
            properties["mail.smtp.host"] = "smtp.qq.com" // 主机名
            properties["mail.smtp.port"] = 465 // 端口号
            properties["mail.smtp.auth"] = "true"
            properties["mail.smtp.ssl.enable"] = "true" // 设置是否使用ssl安全连接 ---一般都使用
            properties["mail.debug"] = "true" // 设置是否显示debug信息 true 会在控制台显示相关信息
            // 得到回话对象
            val session: Session = Session.getInstance(properties)
            // 获取邮件对象
            val message = MimeMessage(session)
            // 设置发件人邮箱地址
            message.setFrom(InternetAddress("<EMAIL>"))
            // 设置收件人邮箱地址
            message.setRecipients(Message.RecipientType.TO, arrayOf<InternetAddress>(InternetAddress(email)))

            // 设置邮件标题 todo 平台名称 写到配置文件
            val subject = "思政虚拟仿真实验平台注册验证码"
            val encodedSubject: String = MimeUtility.encodeText(subject, MimeUtility.mimeCharset("gb2312"), null)
            message.setSubject(encodedSubject)
            // 生成redis标记
            val code = RandomUtil.randomNumbers(6)
            RedisConfigModel().setConfig("bigPlatform_regEmail_${email}", code, 1 * 24 * 3600L)
            // 设置邮件内容
            val dateTime: String = DateTimeTools.dateFormat(Date(), "YYYY-MM-dd HH:mm")
            var text = ""
            text =
                "您正在进行注册账号操作，您的验证码是：<span style='color:#fff'>$code</span>。该验证码24小时内有效，请及时处理。"
            text += "<div style='margin-left:20px;'></div>"
            val messageText: String = EmailTools().buildEmailHtml(text, dateTime)
            message.setContent(messageText, "text/html;charset=utf-8")
            // 得到邮差对象
            val transport: Transport = session.getTransport()
            // 连接自己的邮箱账户
            transport.connect("<EMAIL>", "whojncmgdqqxiceg") // 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients())
            transport.close()
        } catch (e: Exception) {
            throw CommonException("000001", "邮件发送失败！")
        }
        return true
    }

    // 发送账号启用邮件
    fun sendAccountEnableEmail(uid: String): Boolean {
        val user = StaticBean.userRepository.findFirstByUserId(uid)
        // todo 安全 发送限制
        try {
            val properties = Properties()
            properties["mail.transport.protocol"] = "smtp" // 连接协议
            properties["mail.smtp.host"] = "smtp.qq.com" // 主机名
            properties["mail.smtp.port"] = 465 // 端口号
            properties["mail.smtp.auth"] = "true"
            properties["mail.smtp.ssl.enable"] = "true" // 设置是否使用ssl安全连接 ---一般都使用
            properties["mail.debug"] = "true" // 设置是否显示debug信息 true 会在控制台显示相关信息
            // 得到回话对象
            val session: Session = Session.getInstance(properties)
            // 获取邮件对象
            val message = MimeMessage(session)
            // 设置发件人邮箱地址
            message.setFrom(InternetAddress("<EMAIL>"))
            // 设置收件人邮箱地址
            message.setRecipients(Message.RecipientType.TO, arrayOf<InternetAddress>(InternetAddress(user.email)))

            // 设置邮件标题 todo 平台名称
            val subject = "思政虚拟仿真实验平台-账号启用通知"
            val encodedSubject: String = MimeUtility.encodeText(subject, MimeUtility.mimeCharset("gb2312"), null)
            message.setSubject(encodedSubject)
            // 设置邮件内容
            val dateTime: String = DateTimeTools.dateFormat(Date(), "YYYY-MM-dd HH:mm")
            var text = ""
            text =
                "您的账号：<span style='color:#fff'>${user.account}</span> 已启用，现在可以正常登录系统！"
            text += "<div style='margin-left:20px;'></div>"
            val messageText: String = EmailTools().buildEmailHtml(text, dateTime)
            message.setContent(messageText, "text/html;charset=utf-8")
            // 得到邮差对象
            val transport: Transport = session.getTransport()
            // 连接自己的邮箱账户
            transport.connect("<EMAIL>", "whojncmgdqqxiceg") // 密码为QQ邮箱开通的stmp服务后得到的客户端授权码
            // 发送邮件
            transport.sendMessage(message, message.getAllRecipients())
            transport.close()
        } catch (e: Exception) {
            throw CommonException("000001", "邮件发送失败！")
        }
        return true
    }

    // 通过邮件重置密码
    fun resetPasswordByEmail(uuid: String): String {
        if (RedisConfigModel().hasKey("bigPlatform_forgetPasswordEmail_$uuid")) {
            val account = RedisConfigModel().getConfig("bigPlatform_forgetPasswordEmail_$uuid") as String
            val user = StaticBean.userRepository.findFirstByAccount(account)
            user.password = PasswordUtil.generate("123456")
            user.hasLogin = false
            StaticBean.userRepository.save(user)
            return "您的密码已重置为123456，请尽快登录修改默认密码！"
        } else {
            return "重置链接已失效！"
        }
    }

    // 平台用户首次登录时补充用户信息
    fun supplementUserInfoOnFirstLogin(userId: String, password: String, email: String, code: String): Boolean {
        // 检测code是否正确
        if (!StaticBean.redisService.hasKey("bigPlatform_bindEmail_$email")) {
            throw CommonException("000001", "邮箱验证码已失效！")
        }
        val redisCode = StaticBean.redisService.get("bigPlatform_bindEmail_$email") as String
        if (redisCode != code) {
            throw CommonException("000001", "邮箱验证码不正确！")
        }
        StaticBean.redisService.set("bigPlatform_bindEmail_$email", "", 1)
        val user = StaticBean.userRepository.findFirstByUserId(userId)
        user.email = email
        user.password = PasswordUtil.generate(password)
        user.hasLogin = true
        StaticBean.userRepository.save(user)
        return true
    }

    // 在某个班级中新增一个学生
    fun addStudentToOneClazz(account: String, name: String, sex: String, clazzId: String) {
        val student = UserEntity()
        student.account = account
        student.name = name
        student.sex = sex
        student.clazzId = clazzId
        student.role = "student"
        StaticBean.userRepository.save(student)
    }

    // 转移学生到新的班级
    fun transferStudentToNewClazz(studentId: String, clazzId: String) {
        val student = StaticBean.userRepository.findFirstByUserId(studentId)
        student.clazzId = clazzId
        StaticBean.userRepository.save(student)
    }

    // 判断用户是否已存在
    fun isUserExisted(account: String): Boolean {
        return StaticBean.userRepository.existsByAccount(account)
    }

    // 在某个学院中新增一个教师
    fun addTeacherToOneCollege(account: String, name: String, sex: String, collegeId: String) {
        val teacher = UserEntity()
        teacher.account = account
        teacher.name = name
        teacher.sex = sex
        teacher.collegeId = collegeId
        teacher.role = "teacher"
        StaticBean.userRepository.save(teacher)
    }

    // 转移教师到新的学院
    fun transferTeacherToNewCollege(teacherId: String, collegeId: String) {
        val teacher = StaticBean.userRepository.findFirstByUserId(teacherId)
        teacher.collegeId = collegeId
        StaticBean.userRepository.save(teacher)
    }
}