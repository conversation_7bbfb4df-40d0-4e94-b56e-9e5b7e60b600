package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.*
import com.cdzyhd.big_platform.exception.CommonException
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria

/**
 * 产教融合业务模型
 */
class IndustryModel {
    
    // ============== 轮播图相关方法 ==============
    
    /**
     * 获取激活状态的轮播图列表
     */
    fun getBannerList(): List<IndustryBannerEntity> {
        return StaticBean.industryBannerRepository.findByStatusAndDeletedOrderBySortAsc("active", 0)
    }
    
    /**
     * 获取轮播图详情
     */
    fun getBannerDetail(bannerId: String): IndustryBannerEntity? {
        return StaticBean.industryBannerRepository.findFirstByBannerId(bannerId)
    }
    
    /**
     * 验证轮播图标题唯一性
     */
    fun checkBannerTitleRepeat(title: String, bannerId: String?): Boolean {
        return if (bannerId.isNullOrBlank()) {
            // 新增时检查
            StaticBean.industryBannerRepository.findFirstByTitleAndDeletedAndBannerIdNot(title, 0, "") != null
        } else {
            // 编辑时检查（排除自己）
            StaticBean.industryBannerRepository.findFirstByTitleAndDeletedAndBannerIdNot(title, 0, bannerId) != null
        }
    }
    
    // ============== 企业相关方法 ==============
    
    /**
     * 获取企业分页列表
     */
    fun getEnterprisePageList(query: String, pageable: Pageable): Any {
        return CommonMongoEntityModel.getPageList(StaticBean.industryEnterpriseRepository, query, pageable)
    }
    
    /**
     * 获取企业详情
     */
    fun getEnterpriseDetail(enterpriseId: String): IndustryEnterpriseEntity? {
        return StaticBean.industryEnterpriseRepository.findFirstByEnterpriseId(enterpriseId)
    }
    
    /**
     * 获取企业简化列表（用于主页展示）
     */
    fun getEnterpriseList(limit: Int = 6): List<IndustryEnterpriseEntity> {
        val enterprises = StaticBean.industryEnterpriseRepository.findByStatusAndDeletedOrderBySortAsc("active", 0)
        return if (enterprises.size > limit) enterprises.subList(0, limit) else enterprises
    }
    
    /**
     * 验证企业名称唯一性
     */
    fun checkEnterpriseNameRepeat(name: String, enterpriseId: String?): Boolean {
        return if (enterpriseId.isNullOrBlank()) {
            // 新增时检查
            StaticBean.industryEnterpriseRepository.findFirstByNameAndDeleted(name, 0) != null
        } else {
            // 编辑时检查（排除自己）
            StaticBean.industryEnterpriseRepository.findFirstByNameAndDeletedAndEnterpriseIdNot(name, 0, enterpriseId) != null
        }
    }
    
    // ============== 招聘信息相关方法 ==============
    
    /**
     * 获取招聘信息分页列表
     */
    fun getJobPageList(query: String, pageable: Pageable): Any {
        return CommonMongoEntityModel.getPageList(StaticBean.industryJobRepository, query, pageable)
    }
    
    /**
     * 获取招聘信息详情
     */
    fun getJobDetail(jobId: String): IndustryJobEntity? {
        // 查询招聘信息
        val job = StaticBean.industryJobRepository.findFirstByJobId(jobId)
        if (job != null && !job.companyId.isNullOrBlank()) {
            // 关联查询企业信息
            val enterprise = StaticBean.industryEnterpriseRepository.findFirstByEnterpriseId(job.companyId)
            if (enterprise != null) {
                val companyObject = JSONObject()
                companyObject["enterpriseId"] = enterprise.enterpriseId
                companyObject["name"] = enterprise.name
                companyObject["logo"] = enterprise.logo
                companyObject["type"] = enterprise.type
                companyObject["typeName"] = enterprise.typeName
                companyObject["location"] = enterprise.location
                companyObject["description"] = enterprise.description
                job.companyEntity = companyObject
            }
        }
        return job
    }
    
    /**
     * 获取招聘信息简化列表（用于主页展示）
     */
    fun getJobList(limit: Int = 4): List<IndustryJobEntity> {
        val jobs = StaticBean.industryJobRepository.findByStatusAndDeletedOrderByPublishTimeDesc("active", 0)
        return if (jobs.size > limit) jobs.subList(0, limit) else jobs
    }
    
    // ============== 学生相关方法 ==============
    
    /**
     * 获取学生分页列表
     */
    fun getStudentPageList(query: String, pageable: Pageable): Any {
        return CommonMongoEntityModel.getPageList(StaticBean.industryStudentRepository, query, pageable)
    }
    
    /**
     * 获取学生详情
     */
    fun getStudentDetail(studentId: String): IndustryStudentEntity? {
        return StaticBean.industryStudentRepository.findFirstByStudentId(studentId)
    }
    
    /**
     * 获取学生简化列表（用于主页展示）
     */
    fun getStudentList(limit: Int = 4): List<IndustryStudentEntity> {
        val students = StaticBean.industryStudentRepository.findByStatusAndDeletedOrderBySortAsc("active", 0)
        return if (students.size > limit) students.subList(0, limit) else students
    }
    
    /**
     * 验证学生学号唯一性
     */
    fun checkStudentNumberRepeat(studentNumber: String, studentId: String?): Boolean {
        return if (studentId.isNullOrBlank()) {
            // 新增时检查
            StaticBean.industryStudentRepository.findFirstByStudentNumberAndDeleted(studentNumber, 0) != null
        } else {
            // 编辑时检查（排除自己）
            StaticBean.industryStudentRepository.findFirstByStudentNumberAndDeletedAndStudentIdNot(studentNumber, 0, studentId) != null
        }
    }
    
    // ============== 统计数据相关方法 ==============
    
    /**
     * 获取产教融合统计数据
     */
    fun getIndustryStats(): JSONObject {
        val stats = JSONObject()
        
        // 统计合作企业数量
        val enterpriseCount = StaticBean.industryEnterpriseRepository.countByStatusAndDeleted("active", 0)
        stats["enterpriseCount"] = enterpriseCount
        
        // 统计优秀学生数量
        val studentCount = StaticBean.industryStudentRepository.countByStatusAndDeleted("active", 0)
        stats["studentCount"] = studentCount
        
        // 统计招聘岗位数量
        val jobCount = StaticBean.industryJobRepository.countByStatusAndDeleted("active", 0)
        stats["jobCount"] = jobCount
        
        // 统计合作项目数量（从企业表中汇总）
        val enterprises = StaticBean.industryEnterpriseRepository.findByStatusAndDeletedOrderBySortAsc("active", 0)
        val projectCount = enterprises.sumOf { it.projectCount ?: 0 }
        stats["projectCount"] = projectCount
        
        return stats
    }
    
    /**
     * 获取分类统计数据
     */
    fun getCategoryStats(): JSONObject {
        val stats = JSONObject()
        
        // 企业类型统计
        val enterpriseTypes = JSONObject()
        val enterprises = StaticBean.industryEnterpriseRepository.findByStatusAndDeletedOrderBySortAsc("active", 0)
        enterprises.groupBy { it.type }.forEach { (type, list) ->
            enterpriseTypes[type ?: "unknown"] = list.size
        }
        stats["enterpriseTypes"] = enterpriseTypes
        
        // 岗位分类统计
        val jobCategories = JSONObject()
        val jobs = StaticBean.industryJobRepository.findByStatusAndDeletedOrderByPublishTimeDesc("active", 0)
        jobs.groupBy { it.category }.forEach { (category, list) ->
            jobCategories[category ?: "unknown"] = list.size
        }
        stats["jobCategories"] = jobCategories
        
        // 学生分类统计
        val studentCategories = JSONObject()
        val students = StaticBean.industryStudentRepository.findByStatusAndDeletedOrderBySortAsc("active", 0)
        students.groupBy { it.category }.forEach { (category, list) ->
            studentCategories[category ?: "unknown"] = list.size
        }
        stats["studentCategories"] = studentCategories
        
        return stats
    }
}