package com.cdzyhd.big_platform.model

import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.CollegeEntity
import com.cdzyhd.big_platform.entity.UserEntity
import com.cdzyhd.big_platform.exception.CommonException
import com.cdzyhd.big_platform.util.ImportExcelUtil
import com.cdzyhd.big_platform.vo.TeacherImportVo
import org.apache.commons.collections4.CollectionUtils
import org.springframework.web.multipart.MultipartFile

// 学院model
class CollegeModel {

    // 批量导入教师-学院内
    fun importTeachersByCollegeId(multipartFile: MultipartFile, collegeId: String) {
        val arrayLists: ArrayList<ArrayList<Any>> = ImportExcelUtil.readExcel(multipartFile)
        val teacherList = ArrayList<UserEntity>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    if ("*教师姓名" == fieldText) {
                        fieldNames[i] = "name"
                        fieldIndex["name"] = i
                    } else if ("*教师工号" == fieldText) {
                        fieldNames[i] = "account"
                        fieldIndex["account"] = i
                    } else if ("*教师性别" == fieldText) {
                        fieldNames[i] = "sex"
                        fieldIndex["sex"] = i
                    }
                    i++
                }
            }
            for (i in 1 until arrayLists.size) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects.size != 1) {
                    if (objects.isNotEmpty()) {
                        if (!fieldIndex.containsKey("name")) {
                            throw CommonException("000001", "导入失败。未找到教师姓名列")
                        }
                        val nameIndex = fieldIndex["name"]!!
                        val name = objects[nameIndex] as String
                        if ("" == name.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师姓名未填写或为空格")
                        }
                        if (name.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师姓名，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("account")) {
                            throw CommonException("000001", "导入失败。未找到教师工号列")
                        }
                        val accountIndex = fieldIndex["account"]
                        val account = objects[accountIndex!!] as String
                        if ("" == account.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号未填写或为空格")
                        }
                        if (account.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("sex")) {
                            throw CommonException("000001", "导入失败。未找到教师性别列")
                        }
                        val sexIndex = fieldIndex["sex"]
                        if (sexIndex!! >= objects.size) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教教师性别未填写。")
                        }
                        val sex = objects[sexIndex] as String
                        if ("" == sex.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师性别未填写或为空格")
                        }
                        if (!("男" == sex || "女" == sex)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师性别输入错误，仅支持输入男或女")
                        }

                        // 判断工号是否已存在
                        if (userModel.isUserExisted(account)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号" + account + "已存在或与已存在的学生学号相同！")
                        }

                        // 加入待添加列表
                        val teacher = UserEntity()
                        teacher.account = account
                        teacher.name = name
                        teacher.sex = sex
                        teacherList.add(teacher)
                    }
                }
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (teacherList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            for (teacher in teacherList) {
                // 执行在学院中新增教师
                userModel.addTeacherToOneCollege(teacher.account, teacher.name, teacher.sex, collegeId)
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }

    // 批量导入教师-学院外
    fun importTeachers(multipartFile: MultipartFile) {
        val arrayLists = ImportExcelUtil.readExcel(multipartFile)
        val teacherList = ArrayList<TeacherImportVo>()
        val userModel = UserModel()
        if (CollectionUtils.isNotEmpty(arrayLists)) {
            val headList = arrayLists[0]
            val fieldNames: MutableMap<Int, String> = HashMap()
            val fieldIndex: MutableMap<String, Int> = HashMap()
            run {
                var i = 0
                val length = headList!!.size
                while (i < length) {
                    val fieldText = headList[i] as String
                    if ("*教师姓名" == fieldText) {
                        fieldNames[i] = "name"
                        fieldIndex["name"] = i
                    } else if ("*教师工号" == fieldText) {
                        fieldNames[i] = "account"
                        fieldIndex["account"] = i
                    } else if ("*教师性别" == fieldText) {
                        fieldNames[i] = "sex"
                        fieldIndex["sex"] = i
                    } else if ("*学院名称" == fieldText) {
                        fieldNames[i] = "college"
                        fieldIndex["college"] = i
                    }
                    i++
                }
            }
            val totalNum = arrayLists.size
            for (i in 1 until totalNum) {
                val num = i + 1
                val objects = arrayLists[i]
                if (objects!!.size != 1) {
                    if (objects.isNotEmpty()) {
                        if (!fieldIndex.containsKey("college")) {
                            throw CommonException("000001", "导入失败。未找到学院名称列")
                        }
                        val collegeIndex = fieldIndex["college"]!!
                        val college = objects[collegeIndex] as String
                        if ("" == college.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学院名称未填写或为空格")
                        }
                        if (college.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行学院名称，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("name")) {
                            throw CommonException("000001", "导入失败。未找到教师姓名列")
                        }
                        val nameIndex = fieldIndex["name"]!!
                        val name = objects[nameIndex] as String
                        if ("" == name.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师姓名未填写或为空格")
                        }
                        if (name.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师姓名，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("account")) {
                            throw CommonException("000001", "导入失败。未找到教师工号列")
                        }
                        val accountIndex = fieldIndex["account"]
                        val account = objects[accountIndex!!] as String
                        if ("" == account.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号未填写或为空格")
                        }
                        if (account.length > 30) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号，字数不应超过30字")
                        }
                        if (!fieldIndex.containsKey("sex")) {
                            throw CommonException("000001", "导入失败。未找到教师性别列")
                        }
                        val sexIndex = fieldIndex["sex"]
                        if (sexIndex!! >= objects.size) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教教师性别方式未填写。")
                        }
                        val sex = objects[sexIndex] as String
                        if ("" == sex.trim { it <= ' ' }) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师性别未填写或为空格")
                        }
                        if (!("男" == sex || "女" == sex)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师性别输入错误，仅支持输入男或女")
                        }
                        // 判断工号是否已存在
                        if (userModel.isUserExisted(account)) {
                            throw CommonException("000001", "导入失败。您的表格第" + num + "行教师工号" + account + "已存在或与已存在的学生学号相同！")
                        }
                        val teacher = TeacherImportVo()
                        teacher.collegeName = college
                        teacher.account = account
                        teacher.sex = sex
                        teacher.name = name
                        teacher.collumNumber = num
                        // 预处理学院信息
                        val teacherTreated = importTeacherPreAction(teacher)
                        // 加入待添加列表
                        teacherList.add(teacherTreated)
                    }
                }
                println("$num/$totalNum")
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
        if (teacherList.isNotEmpty()) {
            // 已完成首次表格检查，进行添加
            for (teacher in teacherList) {
                // 执行在学院中新增教师
                userModel.addTeacherToOneCollege(
                    teacher.account,
                    teacher.name,
                    teacher.sex,
                    teacher.collegeId
                )
            }
        } else {
            throw CommonException("000001", "数据表为空")
        }
    }

    // 预处理学院信息
    private fun importTeacherPreAction(teacher: TeacherImportVo): TeacherImportVo {
        // 查找学院
        val college = StaticBean.collegeRepository.findFirstByName(teacher.collegeName)
            ?: throw CommonException("000001", "导入失败。您的表格第" + teacher.collumNumber + "行学院不存在，请先在学院管理中创建后再进行导入！")
        teacher.collegeId = college.collegeId
        return teacher
    }
}