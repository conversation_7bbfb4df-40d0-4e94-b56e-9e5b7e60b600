package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.ClazzEntity
import com.cdzyhd.big_platform.entity.CourseRecordEntity
import com.cdzyhd.big_platform.entity.CourseTaskEntity
import com.cdzyhd.big_platform.entity.UserEntity
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.LookupOperation
import org.springframework.data.mongodb.core.aggregation.ProjectionOperation
import org.springframework.data.mongodb.core.query.Criteria
import org.springframework.data.mongodb.core.query.Query
import org.springframework.data.mongodb.core.query.Update
import org.springframework.data.mongodb.core.query.and

// 任务安排
class CourseTaskModel {
    // 更新任务安排的教学班信息
    /**
     * 逻辑：
     *      多个教学班存在多一个学生，只生成一个记录
     *      从教学班删除一个学生后，如果原来有记录，deleted就标记为1
     *      如果原来deleted为1，再次加入教学班后，deleted改为0
     *      todo 是否要改为教学班里删除添加人时，主动触发
     *      todo 行政班添加人时主动触发？
     */
    fun updateTeachingClazzInfo(courseTaskId: String, teachingClazzInfo: JSONObject): Boolean {
        val courseRecordModel=CourseRecordModel()
        // 保存教学班信息
        val courseTask = StaticBean.courseTaskRepository.findFirstByCourseTaskId(courseTaskId)
        courseTask.teachingClazzInfo = teachingClazzInfo
        // 保存教学班id集合信息
        courseTask.teachingClazzIds = teachingClazzInfo.getJSONArray("teachingClazzIds")
        // 保存教师id集合信息
        courseTask.teacherIds = teachingClazzInfo.getJSONArray("teacherIds")
        StaticBean.courseTaskRepository.save(courseTask)
        // 遍历所有教师、教学班和学生，生成课程记录 todo 优化，一次查询就得到结果

        for (teacher in teachingClazzInfo.getJSONArray("teacherList")) {
            teacher as JSONObject
            val teacherId = teacher.getString("teacherId") // 教师id
            val teachingClazzIdList = teacher.getJSONArray("teachingClazzIdList")
            for (teachingClazzId in teachingClazzIdList) {
                teachingClazzId as String // 教学班id
                // 获取教学班内的所有学生id列表
                var match =
                    Aggregation.match(Criteria("role").`is`("student").and("teachingClazzIds").all(teachingClazzId))
                var aggregation = Aggregation.newAggregation(match, Aggregation.project("userId"))
                val studentList =
                    StaticBean.mongoTemplate.aggregate(aggregation, "user", UserEntity::class.java).mappedResults
                val studentIdList = HashSet<String>()
                for (student in studentList) {
                    studentIdList.add(student.userId)
                }
                val studentIdListBack = studentIdList.clone() as HashSet<String>
                // 找到所有已创建该任务记录的学生列表
                match = Aggregation.match(
                    Criteria("courseTaskId").`is`(courseTaskId).and("teacherId").`is`(teacherId).and("teachingClazzId")
                        .`is`(teachingClazzId)
                )
                aggregation = Aggregation.newAggregation(match, Aggregation.project("userId"))
                val studentListAllRecord =
                    StaticBean.mongoTemplate.aggregate(
                        aggregation,
                        "course_record",
                        CourseRecordEntity::class.java
                    ).mappedResults
                val allRecordStudentIdList = HashSet<String>()
                for (student in studentListAllRecord) {
                    allRecordStudentIdList.add(student.userId)
                }
                // 原来逻辑删除的学生，标记为0
                var deleteUpdate = Update()
                deleteUpdate.set("deleted", 0)
                var deleteQuery =
                    Query(
                        Criteria("userId").`in`(studentIdList).and("courseTaskId").`is`(courseTask.courseTaskId)
                            .and("teacherId").`is`(teacherId).and("teachingClazzId").`is`(teachingClazzId)
                    )
                StaticBean.mongoTemplate.updateMulti(deleteQuery, deleteUpdate, "course_record")
                // 未创建的学生创建记录 所有学生-已创建学生=未创建学生
                studentIdList.removeAll(allRecordStudentIdList)
                // todo 优化-是否可以一次创建多个
                for (studentId in studentIdList) {
                    val courseRecord = CourseRecordEntity()
                    courseRecord.userId = studentId
                    courseRecord.courseTaskId = courseTask.courseTaskId
                    courseRecord.courseId = courseTask.courseId
                    // 记录教师id和教学班id
                    courseRecord.teacherId = teacherId
                    courseRecord.teachingClazzId = teachingClazzId
                    courseRecordModel.addOneRecord(courseRecord)

                }
                // 未在教学班里的学生记录标记逻辑删除 已创建学生-所有学生=删除的学生
                allRecordStudentIdList.removeAll(studentIdListBack)
                deleteUpdate = Update()
                deleteUpdate.set("deleted", 1)
                deleteQuery =
                    Query(
                        Criteria("userId").`in`(allRecordStudentIdList).and("courseTaskId")
                            .`is`(courseTask.courseTaskId).and("teacherId").`is`(teacherId).and("teachingClazzId")
                            .`is`(teachingClazzId)
                    )
                StaticBean.mongoTemplate.updateMulti(deleteQuery, deleteUpdate, "course_record")
            }
        }


        return true
    }
}