package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.ResourceCategoryEntity
import org.bson.Document
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria

class ResourceCategoryModel {
    
    companion object {
        
        /**
         * 获取所有资源分类
         */
        fun getResourceCategories(query: String): List<ResourceCategoryEntity> {
            val queryObject = JSONObject.parseObject(query)
            queryObject["deleted"] = 0 // 只查询未删除的分类
            val list= StaticBean.resourceCategoryRepository.getList(Document.parse(queryObject.toString()))

            // 为每个分类更新资源统计数据
            list.forEach { category ->
                val resourceCount = StaticBean.resourceRepository
                    .countByCategoryIdAndStatusAndDeleted(category.categoryId, "active", 0)
                category.resourceCount = resourceCount
            }
            
            return list
        }
        
        /**
         * 获取分类详情
         */
        fun getCategoryDetail(categoryId: String): ResourceCategoryEntity? {
            val category = StaticBean.resourceCategoryRepository.findFirstByCategoryId(categoryId)
            if (category != null && category.status == "active" && category.deleted == 0) {
                // 更新资源统计数据
                val resourceCount = StaticBean.resourceRepository
                    .countByCategoryIdAndStatusAndDeleted(categoryId, "active", 0)
                category.resourceCount = resourceCount
                return category
            }
            return null
        }
        
        /**
         * 更新分类的资源统计数据
         */
        fun updateCategoryResourceCount(categoryId: String) {
            val category = StaticBean.resourceCategoryRepository.findFirstByCategoryId(categoryId)
            if (category != null) {
                val resourceCount = StaticBean.resourceRepository
                    .countByCategoryIdAndStatusAndDeleted(categoryId, "active", 0)
                category.resourceCount = resourceCount
                StaticBean.resourceCategoryRepository.save(category)
            }
        }
        
        /**
         * 获取分类统计信息
         */
        fun getCategoryStats(): JSONObject {
            val totalCategories = StaticBean.resourceCategoryRepository.countByStatusAndDeleted("active", 0)
            
            val stats = JSONObject()
            stats["totalCategories"] = totalCategories
            
            return stats
        }
    }
}