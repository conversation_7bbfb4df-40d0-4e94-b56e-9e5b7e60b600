package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.model.CommonMongoEntityModel
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.NoticeEntity
import com.cdzyhd.big_platform.exception.CommonException
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria

/**
 * 通知公告业务模型
 * 处理通知公告相关的业务逻辑
 */
class NoticeModel {

    companion object {

        /**
         * 获取通知分页列表
         */
        fun getPageList(query: String, pageable: Pageable): JSONObject {
            return CommonMongoEntityModel.getPageList(
                StaticBean.noticeRepository,
                query,
                pageable
            ) as JSONObject
        }

        /**
         * 获取通知列表（不分页）
         */
        fun getList(query: String): List<NoticeEntity> {
            return StaticBean.noticeRepository.getList(Document.parse(query))
        }

        /**
         * 根据ID获取通知详情
         */
        fun getOneById(noticeId: String): NoticeEntity? {
            return CommonMongoEntityModel.getOneById(
                StaticBean.noticeRepository,
                "noticeId",
                noticeId
            ) as NoticeEntity
        }

        /**
         * 根据ID删除通知
         */
        fun deleteOneById(noticeId: String): Boolean {
            return CommonMongoEntityModel.deleteOneById(
                StaticBean.noticeRepository,
                "noticeId",
                noticeId
            ) as Boolean
        }

        /**
         * 新增或编辑通知
         */
        fun addOrEdit(infoObject: JSONObject): NoticeEntity {
            return CommonMongoEntityModel.addOrEdit(
                StaticBean.noticeRepository,
                NoticeEntity(),
                "noticeId",
                infoObject
            ) as NoticeEntity
        }

        /**
         * 增加通知浏览量
         */
        fun increaseViews(noticeId: String): Boolean {
            try {
                val notice = getOneById(noticeId)
                if (notice != null) {
                    notice.views = (notice.views ?: 0L) + 1
                    StaticBean.noticeRepository.save(notice)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "增加浏览量失败: ${e.message}")
            }
        }

        /**
         * 获取置顶通知列表
         */
        fun getTopNoticeList(): List<NoticeEntity> {
            return StaticBean.noticeRepository.findAllByIsTopAndStatusAndDeletedOrderByPublishTimeDesc(
                true, "active", 0
            )
        }

        /**
         * 获取轮播通知列表
         */
        fun getCarouselNoticeList(): List<NoticeEntity> {
            // 获取置顶且重要的通知作为轮播内容
            val criteria = Criteria()
                .and("status").`is`("active")
                .and("deleted").`is`(0)
                .and("isTop").`is`(true)

            val currentTime = System.currentTimeMillis()
            criteria.orOperator(
                Criteria.where("expireTime").exists(false),
                Criteria.where("expireTime").`is`(null),
                Criteria.where("expireTime").gte(currentTime)
            )

            val matchOperation = Aggregation.match(criteria)
            val sortOperation = Aggregation.sort(Sort.Direction.DESC, "publishTime")
            val limitOperation = Aggregation.limit(5)

            val pipeline = Aggregation.newAggregation(
                matchOperation,
                sortOperation,
                limitOperation
            )

            return StaticBean.mongoTemplate.aggregate(
                pipeline,
                "notice",
                NoticeEntity::class.java
            ).mappedResults
        }

        /**
         * 根据类型获取通知列表
         */
        fun getNoticeByType(type: String, limit: Int = 10): List<NoticeEntity> {
            val currentTime = System.currentTimeMillis()
            return StaticBean.noticeRepository.findAllByTypeAndStatusAndDeletedOrderByPublishTimeDesc(
                type, "active", 0
            ).filter { notice ->
                notice.expireTime == null || notice.expireTime!! > currentTime
            }.take(limit)
        }

        /**
         * 搜索通知
         */
        fun searchNotice(keyword: String, pageable: Pageable): JSONObject {
            val criteria = Criteria()
                .and("status").`is`("active")
                .and("deleted").`is`(0)

            // 添加过期时间筛选
            val currentTime = System.currentTimeMillis()
            criteria.orOperator(
                Criteria.where("expireTime").exists(false),
                Criteria.where("expireTime").`is`(null),
                Criteria.where("expireTime").gte(currentTime)
            )

            if (keyword.isNotBlank()) {
                criteria.andOperator(
                    Criteria().orOperator(
                        Criteria.where("title").regex(keyword, "i"),
                        Criteria.where("summary").regex(keyword, "i"),
                        Criteria.where("content").regex(keyword, "i")
                    )
                )
            }

            // 构建聚合查询
            val matchOperation = Aggregation.match(criteria)
            val sortOperation = Aggregation.sort(Sort.Direction.DESC, "publishTime")

            // 统计总数
            val countAggregation = Aggregation.count().`as`("count")
            val countPipeline = Aggregation.newAggregation(matchOperation, countAggregation)
            val countResult = StaticBean.mongoTemplate.aggregate(
                countPipeline,
                "notice",
                HashMap::class.java
            ).mappedResults
            val totalElements = if (countResult.isEmpty()) 0 else countResult[0]["count"] as Int

            // 分页查询
            val skipOperation = Aggregation.skip(pageable.pageNumber * pageable.pageSize.toLong())
            val limitOperation = Aggregation.limit(pageable.pageSize.toLong())
            val dataPipeline = Aggregation.newAggregation(
                matchOperation,
                sortOperation,
                skipOperation,
                limitOperation
            )

            val resultList = StaticBean.mongoTemplate.aggregate(
                dataPipeline,
                "notice",
                NoticeEntity::class.java
            ).mappedResults

            // 构建返回对象
            val totalPages = (totalElements + pageable.pageSize - 1) / pageable.pageSize
            val returnObject = JSONObject.parseObject("""
                {
                    "number":${pageable.pageNumber},
                    "size":${pageable.pageSize},
                    "totalElements":${totalElements},
                    "totalPages":${totalPages}
                }
            """.trimIndent())
            returnObject["content"] = resultList

            return returnObject
        }

        /**
         * 获取相关通知
         */
        fun getRelatedNotice(noticeId: String, type: String, limit: Int = 5): List<NoticeEntity> {
            val currentTime = System.currentTimeMillis()
            return StaticBean.noticeRepository.findTop5ByTypeAndStatusAndDeletedAndNoticeIdNotOrderByPublishTimeDesc(
                type, "active", 0, noticeId
            ).filter { notice ->
                notice.expireTime == null || notice.expireTime!! > currentTime
            }.take(limit)
        }

        /**
         * 发布通知
         */
        fun publishNotice(noticeId: String): Boolean {
            try {
                val notice = getOneById(noticeId)
                if (notice != null) {
                    notice.status = "active"
                    notice.publishTime = System.currentTimeMillis()
                    notice.updateTime = System.currentTimeMillis()
                    notice.isSent = true
                    notice.sentTime = System.currentTimeMillis()
                    StaticBean.noticeRepository.save(notice)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "发布通知失败: ${e.message}")
            }
        }

        /**
         * 撤回通知
         */
        fun unpublishNotice(noticeId: String): Boolean {
            try {
                val notice = getOneById(noticeId)
                if (notice != null) {
                    notice.status = "draft"
                    notice.updateTime = System.currentTimeMillis()
                    StaticBean.noticeRepository.save(notice)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "撤回通知失败: ${e.message}")
            }
        }

        /**
         * 设置通知置顶
         */
        fun setTopNotice(noticeId: String, isTop: Boolean): Boolean {
            try {
                val notice = getOneById(noticeId)
                if (notice != null) {
                    notice.isTop = isTop
                    notice.updateTime = System.currentTimeMillis()
                    StaticBean.noticeRepository.save(notice)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "设置置顶状态失败: ${e.message}")
            }
        }

        /**
         * 获取有效期内的通知
         */
        fun getActiveNotices(): List<NoticeEntity> {
            val currentTime = System.currentTimeMillis()
            return StaticBean.noticeRepository.findAllByStatusAndDeletedAndExpireTimeGreaterThanOrderByPublishTimeDesc(
                "active", 0, currentTime
            )
        }

        /**
         * 获取即将过期的通知
         */
        fun getExpiringNotices(days: Int = 7): List<NoticeEntity> {
            val currentTime = System.currentTimeMillis()
            val futureTime = currentTime + (days * 24 * 60 * 60 * 1000L) // days天后的时间戳
            return StaticBean.noticeRepository.findAllByStatusAndDeletedAndExpireTimeBetweenOrderByExpireTimeAsc(
                "active", 0, currentTime, futureTime
            )
        }

        /**
         * 处理过期通知
         */
        fun processExpiredNotices(): Int {
            val currentTime = System.currentTimeMillis()
            val expiredNotices = StaticBean.noticeRepository.findAllByStatusAndDeletedAndExpireTimeLessThan(
                "active", 0, currentTime
            )

            var processedCount = 0
            expiredNotices.forEach { notice ->
                notice.status = "expired"
                notice.updateTime = currentTime
                StaticBean.noticeRepository.save(notice)
                processedCount++
            }

            return processedCount
        }

        /**
         * 获取通知统计信息
         */
        fun getNoticeStatistics(): JSONObject {
            val currentTime = System.currentTimeMillis()
            val totalCount = StaticBean.noticeRepository.countByStatusAndDeleted("active", 0)
            val topCount = StaticBean.noticeRepository.countByIsTopAndStatusAndDeleted(true, "active", 0)
            val urgentCount = StaticBean.noticeRepository.countByUrgencyAndStatusAndDeleted("high", "active", 0)

            val statistics = JSONObject()
            statistics["totalCount"] = totalCount
            statistics["topCount"] = topCount
            statistics["urgentCount"] = urgentCount

            // 按类型统计
            val typeStats = JSONObject()
            typeStats["important"] = StaticBean.noticeRepository.countByTypeAndStatusAndDeleted("important", "active", 0)
            typeStats["teaching"] = StaticBean.noticeRepository.countByTypeAndStatusAndDeleted("teaching", "active", 0)
            typeStats["system"] = StaticBean.noticeRepository.countByTypeAndStatusAndDeleted("system", "active", 0)
            typeStats["activity"] = StaticBean.noticeRepository.countByTypeAndStatusAndDeleted("activity", "active", 0)
            statistics["typeStats"] = typeStats

            // 即将过期通知数量
            val expiringCount = getExpiringNotices(7).size
            statistics["expiringCount"] = expiringCount

            return statistics
        }

        /**
         * 确认阅读通知
         */
        fun confirmRead(noticeId: String, userId: String): Boolean {
            try {
                val notice = getOneById(noticeId)
                if (notice != null && notice.needConfirm == true) {
                    // 这里可以记录具体的用户阅读记录，暂时只增加计数
                    notice.confirmCount = (notice.confirmCount ?: 0L) + 1
                    StaticBean.noticeRepository.save(notice)
                    return true
                }
                return false
            } catch (e: Exception) {
                throw CommonException("000001", "确认阅读失败: ${e.message}")
            }
        }
    }
}