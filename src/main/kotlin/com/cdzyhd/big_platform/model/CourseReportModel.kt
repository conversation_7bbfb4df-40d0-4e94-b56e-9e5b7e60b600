package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.CourseRecordEntity
import java.util.*

class CourseReportModel {

    // 教师批改某个报告分数
    /**
     * commentObject 暂不使用，后续可以填充教师评语等
     */
    fun teacherCorrectReport(
        courseRecordId: String,
        score: Double,
        teacherId: String,
        commentObject: JSONObject
    ): CourseRecordEntity {
        // 获取报告信息
        val courseReport = StaticBean.courseReportRepository.findFirstByCourseReportId(courseRecordId)
        courseReport.score = score
        courseReport.correctTeacherId = teacherId
        val correctTeacher = StaticBean.userRepository.findFirstByUserId(teacherId)
        courseReport.correctTeacherName = correctTeacher.name
        courseReport.correctDate = Date().time
        // 获取对应的实验记录信息
        val courseRecord = StaticBean.courseRecordRepository.findFirstByCourseRecordId(courseReport.courseRecordId)
        courseRecord.reportCorrected = true
        courseRecord.reportScore = score
        // 计算综合分数
        val courseTask = StaticBean.courseTaskRepository.findFirstByCourseTaskId(courseRecord.courseTaskId)
        if (courseTask.needFillReport) {// 如果需要填写实验报告，就计算分数占比
            val totalScore =
                courseRecord.courseScore * courseTask.courseScorePoint + courseRecord.reportScore * courseTask.reportScorePoint
            courseRecord.totalScore = totalScore
        } else {// 如果不需要填写实验报告，总分就是实验分
            courseRecord.totalScore = courseRecord.courseScore
        }
        // 保存
        StaticBean.courseReportRepository.save(courseReport)
        StaticBean.courseRecordRepository.save(courseRecord)
        return courseRecord
    }
}