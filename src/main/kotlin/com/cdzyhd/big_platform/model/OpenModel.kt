package com.cdzyhd.big_platform.model

import cn.hutool.crypto.SecureUtil
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.base.common.util.JsonWebTokenManagerBase
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.CourseScoreEntity
import com.cdzyhd.big_platform.exception.CommonException
import com.cdzyhd.big_platform.util.SnowflakeIdWorker
import io.jsonwebtoken.Claims
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.query.Criteria
import java.util.*
import kotlin.collections.ArrayList

class OpenModel {
    // ticket默认过期时间 10分钟
    private val ticketExpireSecond = 10 * 60L

    // ticket Redis存储的key前缀
    private val ticketKey = "bigPlatform_ticket_"

    // token过期时间 默认1天
    private val tokenExpireDay = 1

    // token Redis存储的key
    private val tokenKey = "bigPlatform~course~"

    /**
     * 通过账号密码获取课程ticket
     */
    fun getTicketByLogin(username: String, password: String, appId: String): String {
        val course =
            StaticBean.courseRepository.findFirstByAppId(appId) ?: throw CommonException("appId_error", "appId不正确！")
        // 如果课程已下线
        if (!course.opened) {
            throw CommonException("course_offLine", "该课程已下线！")
        }
        val findUser = UserModel().loginCheckByAccount(username, password)
        val ticket = this.getTicket(findUser.userId, appId)
        return ticket
    }

    /**
     * 获取课程的ticket
     */
    fun getTicket(userId: String, appId: String): String {
        val user = StaticBean.userRepository.findFirstByUserId(userId)
        if (user.deleted == 1) {
            if (!user.asSocial) {
                throw CommonException("username_disabled", "该账号已被禁用！")
            }
            if (user.asSocial) {
                throw CommonException("username_disabled", "该账号尚未启用！")
            }
        }

        val course =
            StaticBean.courseRepository.findFirstByAppId(appId) ?: throw CommonException("appId_error", "appId不正确！")
        // 如果课程已下线
        if (!course.opened) {
            throw CommonException("course_offLine", "该课程已下线！")
        }
        // ticket规则 直接snowflakeId
        val ticket = SnowflakeIdWorker(2, 1).nextId()
        // 存入Redis bigPlatform_ticket
        RedisConfigModel().setConfig(ticketKey + ticket, userId, ticketExpireSecond)
        // 如果不存在开放课程记录，就创建一个

        val openRecord = CourseRecordModel().getOneUserOneCourseOpenRecord(userId, course.courseId)
        // todo 记录课程统计
        return ticket
    }

    /**
     *  获取课程访问token
     *  signature规则 MD5 (ticket+appid+secret)
     */
    fun getCourseToken(ticket: String, appId: String, signature: String): HashMap<String, Any> {
        val redisConfigModel = RedisConfigModel()
        // 验证ticket是否存在
        if (!redisConfigModel.hasKey(ticketKey + ticket)) {
            throw CommonException("ticket_error", "该ticket已过期或不存在！")
        }
        val userId = redisConfigModel.getConfig(ticketKey + ticket) as String
        // 判断用户是否已禁用
        val userFind = StaticBean.userRepository.findFirstByUserId(userId)
        if (userFind == null) {
            throw CommonException("username_not_exist", "未找到该用户！")
        }
        if (userFind.deleted == 1) {
            if (!userFind.asSocial) {
                throw CommonException("username_disabled", "该账号已被禁用！")
            }
            if (userFind.asSocial) {
                throw CommonException("username_disabled", "该账号尚未启用！")
            }
        }
        // 让ticket失效
        redisConfigModel.setConfig(ticketKey + ticket, "", 1)
        // 获取课程详情
        val course =
            StaticBean.courseRepository.findFirstByAppId(appId) ?: throw CommonException("appId_error", "appId不正确！")
        // 计算和验证signature
        val signatureRight = SecureUtil.md5(ticket + appId + course.appSecret)
        if (!signatureRight.equals(signature)) {
            throw CommonException("signature_error", "signature不正确！")
        }

        /**判断登录剩余次数-start-**/
        /**
         * 22.08.21
         * 任务限制优先级最高，如果有进行中的任务限制就限制。
         * 如果任务中有一个不限制就都不限制
         * 如果没有任务限制，课程限制才限制
         */
        var leftLimitNumberTotal = 0 // 总剩余次数是剩余限制次数中最大的那个数字
        var limitLogin = false
        val limitLoginRecordIdList = ArrayList<String>(0)
        val taskRecordList = CourseRecordModel().getOneUserOneCourseTaskList(
            course.courseId,
            userId,
            "doing"
        ) // 获取正在进行中的任务列表 todo 优化算法 不需要其他内容，只需要是否限制 project优化
        var limitLoginTask = false
        if (taskRecordList.size == 0) {// 如果没有正在进行中的任务，就不限制
            limitLoginTask = false
        }
        for (taskRecord in taskRecordList) { // 如果有进行中的任务
            if (taskRecord.limitLogin) {// 任务限制登录
                limitLoginTask = true
                if (taskRecord.leftLimitNumber > 0) {
                    if (taskRecord.leftLimitNumber > leftLimitNumberTotal) {
                        leftLimitNumberTotal = taskRecord.leftLimitNumber
                    }
                    limitLoginRecordIdList.add(taskRecord.courseRecordId)
                }
            } else {
                limitLoginTask = false
                break // 只要有一个任务是开放的，就不限制
            }
        }
        if (limitLoginTask) {// 如果有进行中的任务限制,就最终限制
            limitLogin = true
        } else {
            if (course.limitLogin) {// 如果课程限制登录
                limitLogin = true
                val courseRecord = StaticBean.courseRecordRepository.findFirstByCourseIdAndUserIdAndCourseTaskId(
                    course.courseId,
                    userId,
                    "open"
                )
                val leftLimitNumberOpen = courseRecord.leftLimitNumber
                if (leftLimitNumberOpen > 0) {
                    if (leftLimitNumberOpen > leftLimitNumberTotal) {
                        leftLimitNumberTotal = leftLimitNumberOpen
                    }
                    limitLoginRecordIdList.add(courseRecord.courseRecordId)
                }
            }
        }
        if (limitLogin) {// 如果最终限制登录
            if (leftLimitNumberTotal == 0) {
                throw CommonException("leftLimitNumber_notEnough", "没有登录权限，剩余实验次数不足！")
            }
            // 给每个限制记录减1剩余次数
            for (recordId in limitLoginRecordIdList) {
                val record = StaticBean.courseRecordRepository.findFirstByCourseRecordId(recordId)
                record.leftLimitNumber -= 1
                StaticBean.courseRecordRepository.save(record)
            }
        }
        /**判断登录剩余次数-end-**/

        // 生成 jwt token
        val token = TokenModel().genCourseTokenAndSave(userId, course.courseId, tokenExpireDay)
        // 返回用户和token信息
        val user = UserModel().getInfoByUserId(userId)
        val userInfo = HashMap<String, Any>()
        userInfo["userId"] = user.userId
        userInfo["username"] = user.account
        userInfo["realName"] = user.name
        userInfo["sex"] = user.sex
        userInfo["role"] = user.role
        if (user.role == "student") {
            userInfo["classId"] = user.clazzId
            userInfo["className"] = user.gradeEntity.getJSONObject(0).getString("name") + "-" +
                    user.majorEntity.getJSONObject(0).getString("name") + "-" +
                    user.clazzEntity.getJSONObject(0).getString("name")
        }
        if (user.role == "teacher") {
            userInfo["collegeName"] = user.collegeEntity.getJSONObject(0).getString("name")
            userInfo["collegeId"] = user.collegeId
        }
        val result = HashMap<String, Any>()
        result["access_token"] = token
        result["token_create_time"] = Date().time // token创建时间
        result["token_expire_time"] = Date().time + tokenExpireDay * 24 * 3600000L // token过期时间
        result["user_info"] = userInfo
        val courseInfo = HashMap<String, Any>()
        courseInfo["courseId"] = course.courseId
        courseInfo["name"] = course.name
        // 标记剩余登录次数
        if (limitLogin) {
            courseInfo["limitLogin"] = true
            courseInfo["leftLimitNumber"] = leftLimitNumberTotal
        } else {
            courseInfo["limitLogin"] = false
        }
        result["course_info"] = courseInfo
        // 获取该用户最后一次实验分数情况
        val scoreCriteria = Criteria("courseId").`is`(course.courseId)
        val scoreMatchOperation = Aggregation.match(scoreCriteria)
        val scoreSortAggregation = Aggregation.sort(Sort.Direction.DESC, "_id")
        val scoreLimitOperation = Aggregation.limit(1)
        val scoreAggregation =
            Aggregation.newAggregation(scoreMatchOperation, scoreSortAggregation, scoreLimitOperation)
        val scoreList =
            StaticBean.mongoTemplate.aggregate(
                scoreAggregation,
                "course_score",
                CourseScoreEntity::class.java
            ).mappedResults
        if (scoreList.size == 1) {
            result["score_info"] = scoreList[0]
        } else {
            result["score_info"] = JSONObject()
        }

        return result
    }

    /**
     * 实验结果回传
     */
    fun dataUpload(token: String, data: String): String {
        val redisConfigModel = RedisConfigModel()
        val courseRecordModel = CourseRecordModel()
        // 检测token合法性
        if (!redisConfigModel.hasKey(tokenKey + token)) { // 验证token是否存在
            throw CommonException("token_expire_or_error", "access_token已过期或不存在！")
        }
        val claims: Claims // 格式检验
        try {
            claims = JsonWebTokenManagerBase().parse_token(token)
        } catch (e: Exception) {
            throw CommonException("token_format_error", "access_token格式不正确！")
        }
        val userId = claims.id
        val courseId = claims.subject
        val course = StaticBean.courseRepository.findFirstByCourseId(courseId)
        // 数据检测
        val dataObject: JSONObject
        try {
            dataObject = JSONObject.parseObject(data)
        } catch (e: Exception) {
            throw CommonException("data_format_error", "数据格式不正确，需要jsonObject！")
        }
        // 完成状态 1-完成 2-未完成
        val status: Int
        if (!dataObject.containsKey("status")) {
            throw CommonException("data_field_error", "数据格式不正确，需要status(Int)！")
        }
        try {
            status = dataObject.getInteger("status")
        } catch (e: Exception) {
            throw CommonException("data_field_error", "数据格式不正确，需要status(Int)！")
        }
        // 本次实验成绩分数 double
        val score: Double
        if (!dataObject.containsKey("score")) {
            throw CommonException("data_field_error", "数据格式不正确，需要score(Double)！")
        }
        try {
            score = dataObject.getDouble("score")
        } catch (e: Exception) {
            throw CommonException("data_field_error", "数据格式不正确，需要score(Double)！")
        }
        // 步骤开始时间 startTime
        if (!dataObject.containsKey("startTime")) {
            throw CommonException("data_field_error", "数据格式不正确，需要startTime(Long)！")
        }
        val startTime: Long
        try {
            startTime = dataObject.getLong("startTime")
        } catch (e: Exception) {
            throw CommonException("data_field_error", "数据格式不正确，需要startTime(Long)！")
        }
        // 步骤结束时间 endTime
        if (!dataObject.containsKey("endTime")) {
            throw CommonException("data_field_error", "数据格式不正确，需要endTime(Long)！")
        }
        val endTime: Long
        try {
            endTime = dataObject.getLong("endTime")
        } catch (e: Exception) {
            throw CommonException("data_field_error", "数据格式不正确，需要endTime(Long)！")
        }
        // 步骤用时 timeUsed
        if (!dataObject.containsKey("timeUsed")) {
            throw CommonException("data_field_error", "数据格式不正确，需要timeUsed(Long)！")
        }
        val timeUsed: Long
        try {
            timeUsed = dataObject.getLong("timeUsed")
        } catch (e: Exception) {
            throw CommonException("data_field_error", "数据格式不正确，需要timeUsed(Long)！")
        }
        // 实验步骤 Array
        if (!dataObject.containsKey("steps")) {
            throw CommonException("data_field_error", "数据格式不正确，需要steps(jsonArray)！")
        }
        var steps = JSONArray()
        try {
            steps = dataObject.getJSONArray("steps")
            this.stepCheck(steps)
        } catch (e: Exception) {
            throw CommonException("data_field_error", "数据格式不正确，需要steps(jsonArray)！")
        }
        var extraInfo = JSONObject()
        try {
            extraInfo = dataObject.getJSONObject("extraInfo")
        } catch (e: Exception) {
            extraInfo = JSONObject()
        }
        // 数据检测完成
        /**
         * 更新开放课程记录
         */
        val openRecord =
            StaticBean.courseRecordRepository.findFirstByCourseIdAndUserIdAndCourseTaskId(courseId, userId, "open")
                ?: throw CommonException("record_not_found", "未找到对应的课程记录！")
        // 新增一个分数、更新课程记录
        var newScoreEntity = courseRecordModel.updateRecordAndAddScore(
            openRecord, openRecord.courseRecordId, courseId, status, course.fullScore, score, startTime,
            endTime, timeUsed, steps, extraInfo
        )
        //
        /**
         * 更新安排任务 找到符合条件的任务安排列表 正在进行中、开放的安排
         *
         * 这儿是更新该学生正在进行中的所有任务记录，所以登录时不用选择教师和任务记录id
         */
        val taskRecordList = courseRecordModel.getOneUserOneCourseTaskList(courseId, userId, "doing")
        for (taskRecord in taskRecordList) {
            // 新增一个分数、更新课程记录
            newScoreEntity = courseRecordModel.updateRecordAndAddScore(
                taskRecord, taskRecord.courseRecordId, courseId, status, course.fullScore, score, startTime,
                endTime, timeUsed, steps, extraInfo
            )
        }
        // 实时统计-当增加一个分数时
        StatisticModel().calWhenAddOneScore(courseId, timeUsed, score, status)
        // 返回分数的最新id
        return newScoreEntity.courseScoreId
    }

    // 实验步骤检测
    private fun stepCheck(steps: JSONArray) {
        for ((index, step) in steps.withIndex()) {
            step as JSONObject
            // 步骤序号 Int
            if (!step.containsKey("seq")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要seq(Int)！")
            }
            val seq: Int
            try {
                seq = step.getInteger("seq")
                step["seq"] = seq
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要seq(Int)！")
            }
            // 步骤名称 title
            if (!step.containsKey("title")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要title(String)！")
            }
            val title: String
            try {
                title = step.getString("title")
                step["title"] = title
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要title(String)！")
            }
            // 步骤开始时间 startTime
            if (!step.containsKey("startTime")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要startTime(Long)！")
            }
            val startTime: Long
            try {
                startTime = step.getLong("startTime")
                step["startTime"] = startTime
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要startTime(Long)！")
            }
            // 步骤结束时间 endTime
            if (!step.containsKey("endTime")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要endTime(Long)！")
            }
            val endTime: Long
            try {
                endTime = step.getLong("endTime")
                step["endTime"] = endTime
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要endTime(Long)！")
            }
            // 步骤用时 timeUsed
            if (!step.containsKey("timeUsed")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要timeUsed(Long)！")
            }
            val timeUsed: Long
            try {
                timeUsed = step.getLong("timeUsed")
                step["timeUsed"] = timeUsed
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要timeUsed(Long)！")
            }
            // 合理预期用时 expectTime
            if (!step.containsKey("expectTime")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要expectTime(Long)！")
            }
            val expectTime: Long
            try {
                expectTime = step.getLong("expectTime")
                step["expectTime"] = expectTime
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要expectTime(Long)！")
            }
            // 步骤满分 maxScore
            if (!step.containsKey("maxScore")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要maxScore(Double)！")
            }
            val maxScore: Double
            try {
                maxScore = step.getDouble("maxScore")
                step["maxScore"]=maxScore
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要maxScore(Double)！")
            }
            // 步骤得分 score
            if (!step.containsKey("score")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要score(Double)！")
            }
            val score: Double
            try {
                score = step.getDouble("score")
                step["score"] = score
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要score(Double)！")
            }
            // 操作重复次数 repeatCount
            if (!step.containsKey("repeatCount")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要repeatCount(Int)！")
            }
            val repeatCount: Int
            try {
                repeatCount = step.getInteger("repeatCount")
                step["repeatCount"] = repeatCount
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要repeatCount(Int)！")
            }
            // 步骤评价 evaluation todo 200字以内
            if (!step.containsKey("evaluation")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要evaluation(String)！")
            }
            val evaluation: String
            try {
                evaluation = step.getString("evaluation")
                step["evaluation"] = evaluation
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要evaluation(String)！")
            }
            // 赋分模型 scoringModel todo 200字以内
            if (!step.containsKey("scoringModel")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要scoringModel(String)！")
            }
            val scoringModel: String
            try {
                scoringModel = step.getString("scoringModel")
                step["scoringModel"] = scoringModel
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要scoringModel(String)！")
            }
            // 备注 remarks todo 200字以内
            if (!step.containsKey("remarks")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要remarks(String)！")
            }
            val remarks: String
            try {
                remarks = step.getString("remarks")
                step["remarks"] = remarks
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要remarks(String)！")
            }
            // 是否已完成 done 0未完成 1已完成
            if (!step.containsKey("done")) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要done(Int)！")
            }
            val done: Int
            try {
                done = step.getInteger("done")
                step["done"] = done
            } catch (e: Exception) {
                throw CommonException("data_field_error", "steps第${index + 1}行数据格式不正确，需要done(Int)！")
            }
        }
    }
}