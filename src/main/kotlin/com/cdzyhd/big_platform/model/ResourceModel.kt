package com.cdzyhd.big_platform.model

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.cdzyhd.big_platform.a.StaticBean
import com.cdzyhd.big_platform.entity.ResourceEntity
import com.cdzyhd.big_platform.entity.UserResourceFavoriteEntity
import com.cdzyhd.big_platform.exception.CommonException
import org.bson.Document
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.data.mongodb.core.aggregation.Aggregation
import org.springframework.data.mongodb.core.aggregation.AggregationOperation
import org.springframework.data.mongodb.core.query.Criteria

class ResourceModel {
    
    companion object {
        
        /**
         * 获取资源分页列表
         */
        fun getResourcePageList(queryObject: JSONObject, pageable: Pageable): JSONObject {
            // 构建查询条件
            val criteria = Criteria()
            criteria.and("deleted").`is`(0)
            
            // 关键词搜索（标题、描述、作者、课程名称）
            if (queryObject.containsKey("keyword") && queryObject.getString("keyword").isNotEmpty()) {
                val keyword = queryObject.getString("keyword")
                val orCriteria = Criteria()
                orCriteria.orOperator(
                    Criteria.where("title").regex(keyword, "i"),
                    Criteria.where("description").regex(keyword, "i"),
                    Criteria.where("author").regex(keyword, "i"),
                    Criteria.where("courseName").regex(keyword, "i")
                )
                criteria.andOperator(orCriteria)
            }

            // 状态筛选
            if (queryObject.containsKey("status") && queryObject.getString("status").isNotEmpty()) {
                criteria.and("status").`is`(queryObject.getString("status"))
            }

            // 分类筛选
            if (queryObject.containsKey("categoryId") && queryObject.getString("categoryId").isNotEmpty()) {
                criteria.and("categoryId").`is`(queryObject.getString("categoryId"))
            }
            
            // 文件类型筛选
            if (queryObject.containsKey("type") && queryObject.getString("type").isNotEmpty()) {
                criteria.and("type").`is`(queryObject.getString("type"))
            }
            
            // 标题筛选
            if (queryObject.containsKey("title") && queryObject.getString("title").isNotEmpty()) {
                criteria.and("title").regex(queryObject.getString("title"), "i")
            }
            
            // 作者筛选
            if (queryObject.containsKey("author") && queryObject.getString("author").isNotEmpty()) {
                criteria.and("author").regex(queryObject.getString("author"), "i")
            }
            
            // 课程名称筛选
            if (queryObject.containsKey("courseName") && queryObject.getString("courseName").isNotEmpty()) {
                criteria.and("courseName").regex(queryObject.getString("courseName"), "i")
            }
            
            // 构建聚合管道
            val matchOperation = Aggregation.match(criteria)
            
            // 关联分类信息
            val lookupCategory = Aggregation.lookup("resource_category", "categoryId", "categoryId", "categoryEntity")
            
            // 排序
            var sortOperation = Aggregation.sort(Sort.Direction.DESC, "uploadTime")
            if (queryObject.containsKey("sortBy")) {
                when (queryObject.getString("sortBy")) {
                    "time" -> sortOperation = Aggregation.sort(Sort.Direction.DESC, "uploadTime")
                    "views" -> sortOperation = Aggregation.sort(Sort.Direction.DESC, "views")
                    "downloads" -> sortOperation = Aggregation.sort(Sort.Direction.DESC, "downloads")
                    "favorites" -> sortOperation = Aggregation.sort(Sort.Direction.DESC, "favorites")
                }
            }
            
            // 统计总数
            val countAggregation = Aggregation.count().`as`("count")
            val countPipeline = Aggregation.newAggregation(lookupCategory, matchOperation, countAggregation)
            val countResult = StaticBean.mongoTemplate.aggregate(
                countPipeline,
                "resource",
                HashMap::class.java
            ).mappedResults
            val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
            
            // 分页查询
            val skipOperation = Aggregation.skip(pageable.pageNumber * pageable.pageSize.toLong())
            val limitOperation = Aggregation.limit(pageable.pageSize.toLong())
            val dataPipeline = Aggregation.newAggregation(
                lookupCategory,
                matchOperation,
                sortOperation,
                skipOperation,
                limitOperation
            )
            
            val resultList = StaticBean.mongoTemplate.aggregate(
                dataPipeline,
                "resource",
                ResourceEntity::class.java
            ).mappedResults
            
            // 构建返回对象
            val totalPages = (totalElements + pageable.pageSize - 1) / pageable.pageSize
            val returnObject = JSONObject.parseObject("""
                {
                    "number":${pageable.pageNumber},
                    "size":${pageable.pageSize},
                    "totalElements":${totalElements},
                    "totalPages":${totalPages}
                }
            """.trimIndent())
            returnObject["content"] = resultList
            
            return returnObject
        }
        
        /**
         * 获取资源详情
         */
        fun getResourceDetail(resourceId: String, asAdmin: Boolean = false): ResourceEntity? {
            val criteria = Criteria()
            criteria.and("resourceId").`is`(resourceId)
            if (!asAdmin) {// 如果是前台才不能显示隐藏的
                criteria.and("status").`is`("active")
            }
            criteria.and("deleted").`is`(0)
            
            // 关联分类和课程信息
            val matchOperation = Aggregation.match(criteria)
            val lookupCategory = Aggregation.lookup("resource_category", "categoryId", "categoryId", "categoryEntity")
            
            val pipeline = Aggregation.newAggregation(lookupCategory, matchOperation)
            val result = StaticBean.mongoTemplate.aggregate(
                pipeline,
                "resource",
                ResourceEntity::class.java
            ).mappedResults
            
            return if (result.isNotEmpty()) result[0] else null
        }
        
        /**
         * 增加资源浏览量
         */
        fun increaseResourceViews(resourceId: String): Boolean {
            val resource = StaticBean.resourceRepository.findFirstByResourceId(resourceId)
            if (resource != null) {
                resource.views = (resource.views ?: 0) + 1
                StaticBean.resourceRepository.save(resource)
                return true
            }
            return false
        }
        
        /**
         * 增加资源下载量
         */
        fun increaseResourceDownloads(resourceId: String): Boolean {
            val resource = StaticBean.resourceRepository.findFirstByResourceId(resourceId)
            if (resource != null) {
                resource.downloads = (resource.downloads ?: 0) + 1
                StaticBean.resourceRepository.save(resource)
                return true
            }
            return false
        }
        
        /**
         * 资源收藏/取消收藏
         */
        fun toggleResourceFavorite(userId: String, resourceId: String): Boolean {
            // 检查资源是否存在
            val resource = StaticBean.resourceRepository.findFirstByResourceId(resourceId)
                ?: throw CommonException("000001", "资源不存在")
            
            // 检查是否已收藏
            val existingFavorite = StaticBean.userResourceFavoriteRepository
                .findFirstByUserIdAndResourceIdAndDeleted(userId, resourceId, 0)
            
            return if (existingFavorite != null) {
                // 已收藏，执行取消收藏
                StaticBean.userResourceFavoriteRepository.deleteByUserIdAndResourceId(userId, resourceId)
                // 更新资源收藏数
                resource.favorites = maxOf((resource.favorites ?: 0) - 1, 0)
                StaticBean.resourceRepository.save(resource)
                false // 返回false表示已取消收藏
            } else {
                // 未收藏，执行收藏
                val favorite = UserResourceFavoriteEntity()
                favorite.userId = userId
                favorite.resourceId = resourceId
                StaticBean.userResourceFavoriteRepository.save(favorite)
                // 更新资源收藏数
                resource.favorites = (resource.favorites ?: 0) + 1
                StaticBean.resourceRepository.save(resource)
                true // 返回true表示已收藏
            }
        }
        
        /**
         * 获取用户收藏的资源列表
         */
        fun getUserFavoriteResources(userId: String, pageable: Pageable, query: String = ""): JSONObject {
            val criteria = Criteria()
            criteria.and("userId").`is`(userId)
            criteria.and("deleted").`is`(0)
            
            // 解析查询参数
            var keyword = ""
            if (query.isNotEmpty()) {
                try {
                    val queryJson = JSONObject.parseObject(query)
                    keyword = queryJson.getString("keyword") ?: ""
                } catch (e: Exception) {
                    // 忽略解析错误
                }
            }
            
            // 关联资源信息
            val matchOperation = Aggregation.match(criteria)
            val lookupResource = Aggregation.lookup("resource", "resourceId", "resourceId", "resourceEntity")
            
            // 构建聚合管道操作列表
            val pipelineOperations = mutableListOf<AggregationOperation>()
            pipelineOperations.add(lookupResource)
            pipelineOperations.add(matchOperation)
            
            // 如果有关键词搜索，添加资源标题过滤
            if (keyword.isNotEmpty()) {
                val searchCriteria = Criteria("resourceEntity.title").regex(keyword, "i")
                val searchMatchOperation = Aggregation.match(searchCriteria)
                pipelineOperations.add(searchMatchOperation)
            }
            
            val sortOperation = Aggregation.sort(Sort.Direction.DESC, "createTime")
            pipelineOperations.add(sortOperation)
            
            // 统计总数
            val countAggregation = Aggregation.count().`as`("count")
            val countPipeline = Aggregation.newAggregation(pipelineOperations + listOf(countAggregation))
            val countResult = StaticBean.mongoTemplate.aggregate(
                countPipeline,
                "user_resource_favorite",
                HashMap::class.java
            ).mappedResults
            val totalElements = if (countResult.size == 0) 0 else countResult[0]["count"] as Int
            
            // 分页查询
            val skipOperation = Aggregation.skip(pageable.pageNumber * pageable.pageSize.toLong())
            val limitOperation = Aggregation.limit(pageable.pageSize.toLong())
            val dataPipeline = Aggregation.newAggregation(
                pipelineOperations + listOf(skipOperation, limitOperation)
            )
            
            val resultList = StaticBean.mongoTemplate.aggregate(
                dataPipeline,
                "user_resource_favorite",
                UserResourceFavoriteEntity::class.java
            ).mappedResults
            
            // 构建返回对象
            val totalPages = (totalElements + pageable.pageSize - 1) / pageable.pageSize
            val returnObject = JSONObject.parseObject("""
                {
                    "number":${pageable.pageNumber},
                    "size":${pageable.pageSize},
                    "totalElements":${totalElements},
                    "totalPages":${totalPages}
                }
            """.trimIndent())
            returnObject["content"] = resultList
            
            return returnObject
        }
        
        /**
         * 获取推荐资源
         */
        fun getFeaturedResources(): List<ResourceEntity> {
            return StaticBean.resourceRepository
                .findTop10ByStatusAndDeletedOrderByFavoritesDesc("active", 0)
        }
        
        /**
         * 获取最新资源
         */
        fun getLatestResources(): List<ResourceEntity> {
            return StaticBean.resourceRepository
                .findTop10ByStatusAndDeletedOrderByUploadTimeDesc("active", 0)
        }
        
        /**
         * 获取资源统计数据
         */
        fun getResourceStats(): JSONObject {
            val totalResources = StaticBean.resourceRepository.countByStatusAndDeleted("active", 0)
            
            // 统计总浏览量、下载量、收藏量
            val criteria = Criteria()
            criteria.and("status").`is`("active")
            criteria.and("deleted").`is`(0)
            
            val matchOperation = Aggregation.match(criteria)
            val groupOperation = Aggregation.group()
                .sum("views").`as`("totalViews")
                .sum("downloads").`as`("totalDownloads")
                .sum("favorites").`as`("totalFavorites")
            
            val pipeline = Aggregation.newAggregation(matchOperation, groupOperation)
            val result = StaticBean.mongoTemplate.aggregate(
                pipeline,
                "resource",
                HashMap::class.java
            ).mappedResults
            
            val stats = JSONObject()
            stats["totalResources"] = totalResources
            if (result.isNotEmpty()) {
                val statsData = result[0]
                stats["totalViews"] = statsData["totalViews"] ?: 0
                stats["totalDownloads"] = statsData["totalDownloads"] ?: 0
                stats["totalFavorites"] = statsData["totalFavorites"] ?: 0
            } else {
                stats["totalViews"] = 0
                stats["totalDownloads"] = 0
                stats["totalFavorites"] = 0
            }
            
            return stats
        }
        
        /**
         * 获取热门搜索标签
         */
        fun getHotSearchTags(): JSONArray {
            // 模拟热门标签数据，实际可以从搜索日志或资源标签中统计
            val hotTags = JSONArray()
            hotTags.addAll(listOf(
                "数据结构", "算法", "机械制图", "虚拟仿真", 
                "实验指导", "毕业设计", "CAD", "编程",
                "电路分析", "化学", "英语听力", "软件工具"
            ))
            return hotTags
        }
        
        /**
         * 获取相关资源
         */
        fun getRelatedResources(resourceId: String, categoryId: String): List<ResourceEntity> {
            val criteria = Criteria()
            criteria.and("resourceId").ne(resourceId) // 排除当前资源
            criteria.and("categoryId").`is`(categoryId)
            criteria.and("status").`is`("active")
            criteria.and("deleted").`is`(0)
            
            val matchOperation = Aggregation.match(criteria)
            val sortOperation = Aggregation.sort(Sort.Direction.DESC, "views")
            val limitOperation = Aggregation.limit(6) // 限制6个相关资源
            
            val pipeline = Aggregation.newAggregation(matchOperation, sortOperation, limitOperation)
            return StaticBean.mongoTemplate.aggregate(
                pipeline,
                "resource",
                ResourceEntity::class.java
            ).mappedResults
        }
        
        /**
         * 检查用户是否收藏了某个资源
         */
        fun isResourceFavorited(userId: String, resourceId: String): Boolean {
            return StaticBean.userResourceFavoriteRepository
                .existsByUserIdAndResourceIdAndDeleted(userId, resourceId, 0)
        }
    }
}