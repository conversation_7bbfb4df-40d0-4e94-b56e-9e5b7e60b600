package com.cdzyhd.big_platform.event;

import com.cdzyhd.big_platform.a.StaticBean;
import com.cdzyhd.big_platform.entity.SysLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@EnableAsync
public class SysLogListener {
    @Async
    @Order
    @EventListener(SysLogEvent.class)
    public void saveSysLog(SysLogEvent event) {
        SysLogEntity sysLogEntity = (SysLogEntity) event.getSource();
        //  保存日志
        try {
            StaticBean.sysLogRepository.save(sysLogEntity);
        } catch (Exception ignored) {

        }
    }
}
