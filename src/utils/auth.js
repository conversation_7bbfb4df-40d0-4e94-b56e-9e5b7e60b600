import Cookies from 'js-cookie'

const TokenKey = 'BigPlatform-Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function getLoginRole() {
  return Cookies.get("loginRole")
}

export function setToken(token, loginRole) {
  Cookies.set("loginRole", loginRole)
  return Cookies.set(Token<PERSON><PERSON>, token)
}

export function removeToken() {
  Cookies.remove("loginRole")
  return Cookies.remove(Token<PERSON>ey)
}
