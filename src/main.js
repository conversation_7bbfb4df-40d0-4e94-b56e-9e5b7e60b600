import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
// normalize
import 'normalize.css/normalize.css'
// 初始化和通用css
import '@/style/common.css';
// element
import 'element-ui/lib/theme-chalk/index.css';
import Element from 'element-ui'
Vue.use(Element);
// fontawesome
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { fas } from '@fortawesome/free-solid-svg-icons'
import { far } from '@fortawesome/free-regular-svg-icons'

// 添加所有图标
library.add(fas, far)

Vue.component('font-awesome-icon', FontAwesomeIcon)

// 本应用通用css
import '@/style/app.less';
// 路由钩子
import './router/hook'

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
