import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// ============== 轮播图相关接口 ==============

// 获取轮播图列表-分页
export async function getBannerPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/industry/banner/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取轮播图列表-不分页
export async function getBannerList(data) {
  return request_async(API_URL + `/v1/industry/banner/list`, "post_body", data);
}

// 获取一个轮播图
export async function getBannerOne(data) {
  return request_async(API_URL + "/v1/industry/banner/", "get", data);
}

// 新增或修改轮播图
export async function addOrEditBanner(data) {
  return request_async(API_URL + "/v1/industry/banner/", "post_body", data);
}

// 删除一个轮播图
export async function deleteBannerOne(data) {
  return request_async(API_URL + "/v1/industry/banner/", "delete", data);
}

// ============== 企业相关接口 ==============

// 获取企业列表-分页
export async function getEnterprisePageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/industry/enterprise/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取企业列表-不分页
export async function getEnterpriseList(data) {
  return request_async(API_URL + `/v1/industry/enterprise/list`, "post_body", data);
}

// 获取一个企业
export async function getEnterpriseOne(data) {
  return request_async(API_URL + "/v1/industry/enterprise/", "get", data);
}

// 新增或修改企业
export async function addOrEditEnterprise(data) {
  return request_async(API_URL + "/v1/industry/enterprise/", "post_body", data);
}

// 删除一个企业
export async function deleteEnterpriseOne(data) {
  return request_async(API_URL + "/v1/industry/enterprise/", "delete", data);
}

// ============== 招聘信息相关接口 ==============

// 获取招聘信息列表-分页
export async function getJobPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/industry/job/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取招聘信息列表-不分页
export async function getJobList(data) {
  return request_async(API_URL + `/v1/industry/job/list`, "post_body", data);
}

// 获取一个招聘信息
export async function getJobOne(data) {
  return request_async(API_URL + "/v1/industry/job/", "get", data);
}

// 新增或修改招聘信息
export async function addOrEditJob(data) {
  return request_async(API_URL + "/v1/industry/job/", "post_body", data);
}

// 删除一个招聘信息
export async function deleteJobOne(data) {
  return request_async(API_URL + "/v1/industry/job/", "delete", data);
}

// ============== 优秀学生相关接口 ==============

// 获取学生列表-分页
export async function getStudentPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/industry/student/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取学生列表-不分页
export async function getStudentList(data) {
  return request_async(API_URL + `/v1/industry/student/list`, "post_body", data);
}

// 获取一个学生
export async function getStudentOne(data) {
  return request_async(API_URL + "/v1/industry/student/", "get", data);
}

// 新增或修改学生
export async function addOrEditStudent(data) {
  return request_async(API_URL + "/v1/industry/student/", "post_body", data);
}

// 删除一个学生
export async function deleteStudentOne(data) {
  return request_async(API_URL + "/v1/industry/student/", "delete", data);
}

// ============== 统计数据相关接口 ==============

// 获取产教融合统计数据
export async function getIndustryStats() {
  return request_async(API_URL + "/v1/industry/stats", "get");
}

// 获取分类统计数据
export async function getCategoryStats() {
  return request_async(API_URL + "/v1/industry/stats/category", "get");
}