import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/courseRecord/", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/courseRecord/", "post_body", data);
}

// 新增修改
export async function updateTeachingClazzInfo(courseRecordId, info) {
  return request_async(API_URL + "/v1/courseRecord/teachingClazzInfo?courseRecordId=" + courseRecordId, "post_body", info);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/courseRecord/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/courseRecord/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/courseRecord/", "delete", data);
}

// 获取一个教学班的详情
export async function getOneTeachingClazzDetail(data) {
  return request_async(API_URL + "/v1/courseRecord/oneTeachingClazzDetail", "get", data);
}

// 获取教学班的学生记录列表-分页
export async function getTeachingClazzStudentList(page, size, sort, data) {
  return request_async(API_URL + `/v1/courseRecord/teachingClazzStudentList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取某个课程的记录列表-分页
export async function getOneCourseScoreList(page, size, sort, data) {
  return request_async(API_URL + `/v1/courseRecord/oneCourseScoreList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 重置登录次数
export async function resetRecordLeftLimitNumber(courseRecordId) {
  return request_async(API_URL + "/v1/courseRecord/resetRecordLeftLimitNumber", "get", {courseRecordId});
}
