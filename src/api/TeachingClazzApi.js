import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/teachingClazz/", "get", data);
}

// 获取一个教学班的详情
export async function getOneDetail(data) {
  return request_async(API_URL + "/v1/teachingClazz/oneDetail", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/teachingClazz/", "post_body", data);
}

// 新增一个教学班
export async function addOne(data) {
  return request_async(API_URL + "/v1/teachingClazz/addOne", "post_body", data);
}

// 编辑一个教学班
export async function editOne(data) {
  return request_async(API_URL + "/v1/teachingClazz/editOne", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/teachingClazz/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/teachingClazz/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/teachingClazz/", "delete", data);
}

// 获取学生列表-分页
export async function getStudentList(page, size, sort, data) {
  return request_async(API_URL + `/v1/teachingClazz/studentList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 删除一个学生
export async function deleteOneStudent(data) {
  return request_async(API_URL + "/v1/teachingClazz/oneStudent", "delete", data);
}

// 新增一个学生
export async function addOneStudent(data) {
  return request_async(API_URL + `/v1/teachingClazz/oneStudent?studentId=${data.studentId}&teachingClazzId=${data.teachingClazzId}`, "put", {});
}
