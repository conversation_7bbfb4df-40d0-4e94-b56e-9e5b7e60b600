import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// ======================= 基地新闻接口 =======================

// 获取新闻列表-分页
export async function getNewsPageList(page, size, data) {
  return request_async(API_URL + `/v1/baseNews/pageList?page=${page}&size=${size}`, "post_body", data);
}

// 获取一条新闻详情
export async function getNewsDetail(newsId) {
  return request_async(API_URL + `/v1/baseNews/?newsId=${newsId}`, "get");
}

// 获取相关新闻
export async function getRelatedNews(newsId, category, limit = 5) {
  return request_async(API_URL + `/v1/baseNews/related?newsId=${newsId}&category=${category}&limit=${limit}`, "get");
}

// 增加新闻浏览量
export async function increaseNewsViews(newsId) {
  return request_async(API_URL + `/v1/baseNews/view?newsId=${newsId}`, "post");
}

// 获取新闻轮播列表
export async function getNewsCarouselList() {
  return request_async(API_URL + "/v1/baseNews/carousel", "get");
}

// 获取热门新闻列表
export async function getHotNewsList(limit = 10) {
  return request_async(API_URL + `/v1/baseNews/hot?limit=${limit}`, "get");
}

// 获取置顶新闻列表
export async function getTopNewsList() {
  return request_async(API_URL + "/v1/baseNews/top", "get");
}

// 根据分类获取新闻
export async function getNewsByCategory(category, limit = 10) {
  return request_async(API_URL + `/v1/baseNews/category?category=${category}&limit=${limit}`, "get");
}

// 搜索新闻
export async function searchNews(page, size, data) {
  return request_async(API_URL + `/v1/baseNews/search?page=${page}&size=${size}`, "post_body", data);
}

// ======================= 通知公告接口 =======================

// 获取通知公告列表-分页
export async function getNoticePageList(page, size, data) {
  return request_async(API_URL + `/v1/notice/pageList?page=${page}&size=${size}`, "post_body", data);
}

// 获取一条通知公告详情
export async function getNoticeDetail(noticeId) {
  return request_async(API_URL + `/v1/notice/?noticeId=${noticeId}`, "get");
}

// 获取相关通知公告
export async function getRelatedNotice(noticeId, type, limit = 5) {
  return request_async(API_URL + `/v1/notice/related?noticeId=${noticeId}&type=${type}&limit=${limit}`, "get");
}

// 增加通知公告浏览量
export async function increaseNoticeViews(noticeId) {
  return request_async(API_URL + `/v1/notice/view?noticeId=${noticeId}`, "post");
}

// 获取通知公告轮播列表
export async function getNoticeCarouselList() {
  return request_async(API_URL + "/v1/notice/carousel", "get");
}

// 获取置顶通知列表
export async function getTopNoticeList() {
  return request_async(API_URL + "/v1/notice/top", "get");
}

// 根据类型获取通知
export async function getNoticeByType(type, limit = 10) {
  return request_async(API_URL + `/v1/notice/type?type=${type}&limit=${limit}`, "get");
}

// 搜索通知
export async function searchNotice(page, size, data) {
  return request_async(API_URL + `/v1/notice/search?page=${page}&size=${size}`, "post_body", data);
}

// 获取有效期内的通知
export async function getActiveNotices() {
  return request_async(API_URL + "/v1/notice/active", "get");
}