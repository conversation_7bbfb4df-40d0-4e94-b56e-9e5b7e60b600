import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取某个课程的实时统计数据
export async function getCourseRealTimeValue(data) {
  return request_async(API_URL + "/v1/statistic/courseRealTimeValue", "get", data);
}

// 获取最近30天实验人次数据
export async function getLast30DayScoreNumber(data) {
  return request_async(API_URL + "/v1/statistic/last30DayScoreNumber", "get", data);
}
