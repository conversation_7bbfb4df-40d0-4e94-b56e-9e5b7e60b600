import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// ============== 资源分类相关接口 ==============

// 获取资源分类列表-分页
export async function getCategoryPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/resource/categories/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取资源分类列表-不分页
export async function getCategoryList(data) {
  return request_async(API_URL + `/v1/resource/categories/list`, "post_body", data);
}

// 获取一个资源分类
export async function getCategoryOne(data) {
  return request_async(API_URL + "/v1/resource/categories/", "get", data);
}

// 新增或修改资源分类
export async function addOrEditCategory(data) {
  return request_async(API_URL + "/v1/resource/categories/", "post_body", data);
}

// 删除一个资源分类
export async function deleteCategoryOne(data) {
  return request_async(API_URL + "/v1/resource/categories/", "delete", data);
}

// ============== 资源相关接口 ==============

// 获取资源列表-分页
export async function getResourcePageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/resource/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取资源列表-不分页
export async function getResourceList(data) {
  return request_async(API_URL + `/v1/resource/list`, "post_body", data);
}

// 获取一个资源
export async function getResourceOne(data) {
  return request_async(API_URL + "/v1/resource/?resourceId=" + data.resourceId+"&asAdmin=true", "get", {});
}

// 新增或修改资源
export async function addOrEditResource(data) {
  return request_async(API_URL + "/v1/resource/", "post_body", data);
}

// 删除一个资源
export async function deleteResourceOne(data) {
  return request_async(API_URL + "/v1/resource/", "delete", data);
}
