import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// ============== 产教融合主页 ==============

// 获取激活状态的轮播图列表
export async function getBannerList() {
  return request_async(API_URL + "/v1/industry/banner/active", "get");
}

// 获取产教融合统计数据
export async function getIndustryStats() {
  return request_async(API_URL + "/v1/industry/stats", "get");
}

// ============== 合作企业 ==============

// 获取企业列表-分页
export async function getEnterprisePageList(page, size, data) {
  return request_async(API_URL + `/v1/industry/enterprise/pageList?page=${page}&size=${size}`, "post_body", data);
}

// 获取企业详情
export async function getEnterpriseDetail(enterpriseId) {
  return request_async(API_URL + `/v1/industry/enterprise?enterpriseId=${enterpriseId}`, "get");
}

// 获取企业列表(简化版)
export async function getEnterpriseList() {
  return request_async(API_URL + "/v1/industry/enterprise/simple", "get");
}

// ============== 招聘信息 ==============

// 获取招聘信息列表-分页
export async function getJobPageList(page, size, data) {
  return request_async(API_URL + `/v1/industry/job/pageList?page=${page}&size=${size}`, "post_body", data);
}

// 获取招聘信息详情
export async function getJobDetail(jobId) {
  return request_async(API_URL + `/v1/industry/job?jobId=${jobId}`, "get");
}

// 获取招聘信息列表(简化版)
export async function getJobList() {
  return request_async(API_URL + "/v1/industry/job/simple", "get");
}

// ============== 优秀学生 ==============

// 获取学生列表-分页
export async function getStudentPageList(page, size, data) {
  return request_async(API_URL + `/v1/industry/student/pageList?page=${page}&size=${size}`, "post_body", data);
}

// 获取学生详情
export async function getStudentDetail(studentId) {
  return request_async(API_URL + `/v1/industry/student?studentId=${studentId}`, "get");
}

// 获取学生列表(简化版)
export async function getStudentList() {
  return request_async(API_URL + "/v1/industry/student/simple", "get");
}