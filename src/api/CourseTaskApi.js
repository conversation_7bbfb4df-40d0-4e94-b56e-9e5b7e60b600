import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/courseTask/", "get", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/courseTask/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/courseTask/list`, "post_body", data);
}
