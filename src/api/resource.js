import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取资源列表-分页
export async function getResourcePageList(page, size, data) {
  return request_async(API_URL + `/v1/resource/pageList?page=${page}&size=${size}`, "post_body", data);
}

// 获取一条资源详情
export async function getResourceDetail(resourceId) {
  return request_async(API_URL + `/v1/resource/?resourceId=${resourceId}`, "get", {});
}

// 获取相关资源
export async function getRelatedResources(resourceId, categoryId) {
  return request_async(API_URL + `/v1/resource/related?resourceId=${resourceId}&categoryId=${categoryId}`, "get", {});
}

// 增加资源浏览量
export async function increaseResourceViews(resourceId) {
  return request_async(API_URL + "/v1/resource/views", "post_body", { resourceId });
}

// 增加资源下载量
export async function increaseResourceDownloads(resourceId) {
  return request_async(API_URL + "/v1/resource/downloads", "post_body", { resourceId });
}

// 资源收藏/取消收藏
export async function toggleResourceFavorite(resourceId) {
  return request_async(API_URL + "/v1/resource/favorite", "post_body", { resourceId });
}

// 获取用户收藏的资源列表
export async function getUserFavoriteResources(page, size, data) {
  return request_async(API_URL + `/v1/resource/favorites?page=${page}&size=${size}`, "post_body", data);
}

// 获取资源分类列表
export async function getResourceCategories() {
  return request_async(API_URL + "/v1/resource/categories/list", "post_body", {});
}

// 根据分类获取资源列表
export async function getResourcesByCategory(categoryId, page, size, data) {
  return request_async(API_URL + `/v1/resource/category/${categoryId}?page=${page}&size=${size}`, "post_body", data);
}

// 搜索资源
export async function searchResources(page, size, data) {
  return request_async(API_URL + `/v1/resource/search?page=${page}&size=${size}`, "post_body", data);
}

// 获取推荐资源
export async function getFeaturedResources() {
  return request_async(API_URL + "/v1/resource/featured", "get", {});
}

// 获取最新资源
export async function getLatestResources() {
  return request_async(API_URL + "/v1/resource/latest", "get", {});
}

// 获取资源统计数据
export async function getResourceStats() {
  return request_async(API_URL + "/v1/resource/stats", "get", {});
}

// 获取热门搜索标签
export async function getHotSearchTags() {
  return request_async(API_URL + "/v1/resource/hotTags", "get", {});
}

// 下载资源
export async function downloadResource(resourceId) {
  return request_async(API_URL + "/v1/resource/download", "post_body", { resourceId });
}

// 检查用户是否收藏了某个资源
export async function isResourceFavorited(resourceId) {
  return request_async(API_URL + `/v1/resource/isFavorited?resourceId=${resourceId}`, "get", {});
}