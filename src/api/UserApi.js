import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/user/", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/user/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/user/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/user/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/user/", "delete", data);
}

// 转移学生到新班级
export async function transferStudentToNewClazz(data) {
  return request_async(API_URL + "/v1/user/transferStudentToNewClazz", "get", data);
}

// 转移教师到新学院
export async function transferTeacherToNewCollege(data) {
  return request_async(API_URL + "/v1/user/transferTeacherToNewCollege", "get", data);
}

// 搜索教师列表
export async function getSearchTeacherList(data) {
  return request_async(API_URL + `/v1/user/searchTeacherList`, "post_body", data);
}

// 搜索学生列表
export async function getSearchStudentList(data) {
  return request_async(API_URL + `/v1/user/searchStudentList`, "post_body", data);
}

// 发送账号启用邮件
export async function sendAccountEnableEmail(uid) {
  return request_async(API_URL + "/v1/user/sendAccountEnableEmail?uid=" + uid, "get", {});
}
