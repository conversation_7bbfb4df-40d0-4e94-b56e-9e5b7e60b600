import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

/**
 * 通知公告API接口
 */

// 获取一个通知公告
export async function getOne(data) {
  return request_async(API_URL + "/v1/notice/", "get", data);
}

// 新增或修改通知公告
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/notice/", "post_body", data);
}

// 获取通知公告列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/notice/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取通知公告列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/notice/list`, "post_body", data);
}

// 删除一个通知公告
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/notice/", "delete", data);
}

// 获取通知轮播列表
export async function getCarouselList() {
  return request_async(API_URL + "/v1/notice/carousel", "get");
}

// 获取置顶通知列表
export async function getTopList() {
  return request_async(API_URL + "/v1/notice/top", "get");
}

// 根据类型获取通知
export async function getByType(type, limit = 10) {
  return request_async(API_URL + `/v1/notice/type?type=${type}&limit=${limit}`, "get");
}

// 搜索通知
export async function searchNotice(page, size, data) {
  return request_async(API_URL + `/v1/notice/search?page=${page}&size=${size}`, "post_body", data);
}

// 获取相关通知
export async function getRelatedNotice(noticeId, type, limit = 5) {
  return request_async(API_URL + `/v1/notice/related?noticeId=${noticeId}&type=${type}&limit=${limit}`, "get");
}

// 增加浏览量
export async function increaseViews(noticeId) {
  return request_async(API_URL + `/v1/notice/view?noticeId=${noticeId}`, "post");
}

// 获取有效期内的通知
export async function getActiveNotices() {
  return request_async(API_URL + "/v1/notice/active", "get");
}

// 获取即将过期的通知
export async function getExpiringNotices(days = 7) {
  return request_async(API_URL + `/v1/notice/expiring?days=${days}`, "get");
}

// 发布通知
export async function publishNotice(noticeId) {
  return request_async(API_URL + `/v1/notice/publish?noticeId=${noticeId}`, "post");
}

// 撤回通知
export async function unpublishNotice(noticeId) {
  return request_async(API_URL + `/v1/notice/unpublish?noticeId=${noticeId}`, "post");
}

// 设置置顶通知
export async function setTopNotice(noticeId, isTop) {
  return request_async(API_URL + `/v1/notice/setTop?noticeId=${noticeId}&isTop=${isTop}`, "post");
}

// 处理过期通知
export async function processExpiredNotices() {
  return request_async(API_URL + "/v1/notice/processExpired", "post");
}

// 确认阅读通知
export async function confirmRead(noticeId, userId) {
  return request_async(API_URL + `/v1/notice/confirmRead?noticeId=${noticeId}&userId=${userId}`, "post");
}

// 获取通知统计信息
export async function getStatistics() {
  return request_async(API_URL + "/v1/notice/statistics", "get");
}