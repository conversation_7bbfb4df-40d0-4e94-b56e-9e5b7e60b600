import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
    return request_async(API_URL + "/v1/courseReport/", "get", data);
}

// 获取某个记录的报告情况
export async function getOneCourseRecordReport(data) {
    return request_async(API_URL + "/v1/courseReport/oneCourseRecordReport", "get", data);
}

// 填写和修改实验报告
export async function fillReportApi(courseRecordId, content) {
    return request_async(API_URL + "/v1/courseReport/fillReport?courseRecordId=" + courseRecordId, "post_body", content);
}
