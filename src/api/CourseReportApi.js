import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

// 获取一个
export async function getOne(data) {
  return request_async(API_URL + "/v1/courseReport/", "get", data);
}

// 新增修改
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/courseReport/", "post_body", data);
}

// 获取列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/courseReport/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/courseReport/list`, "post_body", data);
}

// 删除一个
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/courseReport/", "delete", data);
}

// 获取某个记录的报告情况
export async function getOneCourseRecordReport(data) {
  return request_async(API_URL + "/v1/courseReport/oneCourseRecordReport", "get", data);
}

// 教师批改实验报告
export async function teacherCorrectReport(courseRecordId, score, comment) {
  return request_async(API_URL + `/v1/courseReport/teacherCorrectReport?courseRecordId=${courseRecordId}&score=${score}`, "post_body", comment);
}
