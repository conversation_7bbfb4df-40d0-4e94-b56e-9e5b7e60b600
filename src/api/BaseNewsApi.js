import {request_async} from "@/utils/requestAsync";
import {API_URL} from "@/config/main";

/**
 * 基地新闻API接口
 */

// 获取一个基地新闻
export async function getOne(data) {
  return request_async(API_URL + "/v1/baseNews/", "get", data);
}

// 新增或修改基地新闻
export async function addOrEdit(data) {
  return request_async(API_URL + "/v1/baseNews/", "post_body", data);
}

// 获取基地新闻列表-分页
export async function getPageList(page, size, sort, data) {
  return request_async(API_URL + `/v1/baseNews/pageList?page=${page}&size=${size}&sort=${sort}`, "post_body", data);
}

// 获取基地新闻列表-不分页
export async function getList(data) {
  return request_async(API_URL + `/v1/baseNews/list`, "post_body", data);
}

// 删除一个基地新闻
export async function deleteOne(data) {
  return request_async(API_URL + "/v1/baseNews/", "delete", data);
}

// 获取新闻轮播列表
export async function getCarouselList() {
  return request_async(API_URL + "/v1/baseNews/carousel", "get");
}

// 获取热门新闻列表
export async function getHotList(limit = 10) {
  return request_async(API_URL + `/v1/baseNews/hot?limit=${limit}`, "get");
}

// 获取置顶新闻列表
export async function getTopList() {
  return request_async(API_URL + "/v1/baseNews/top", "get");
}

// 根据分类获取新闻
export async function getByCategory(category, limit = 10) {
  return request_async(API_URL + `/v1/baseNews/category?category=${category}&limit=${limit}`, "get");
}

// 搜索新闻
export async function searchNews(page, size, data) {
  return request_async(API_URL + `/v1/baseNews/search?page=${page}&size=${size}`, "post_body", data);
}

// 获取相关新闻
export async function getRelatedNews(newsId, category, limit = 5) {
  return request_async(API_URL + `/v1/baseNews/related?newsId=${newsId}&category=${category}&limit=${limit}`, "get");
}

// 增加浏览量
export async function increaseViews(newsId) {
  return request_async(API_URL + `/v1/baseNews/view?newsId=${newsId}`, "post");
}

// 发布新闻
export async function publishNews(newsId) {
  return request_async(API_URL + `/v1/baseNews/publish?newsId=${newsId}`, "post");
}

// 撤回新闻
export async function unpublishNews(newsId) {
  return request_async(API_URL + `/v1/baseNews/unpublish?newsId=${newsId}`, "post");
}

// 设置热门新闻
export async function setHotNews(newsId, isHot) {
  return request_async(API_URL + `/v1/baseNews/setHot?newsId=${newsId}&isHot=${isHot}`, "post");
}

// 设置置顶新闻
export async function setTopNews(newsId, isTop) {
  return request_async(API_URL + `/v1/baseNews/setTop?newsId=${newsId}&isTop=${isTop}`, "post");
}

// 获取新闻统计信息
export async function getStatistics() {
  return request_async(API_URL + "/v1/baseNews/statistics", "get");
}