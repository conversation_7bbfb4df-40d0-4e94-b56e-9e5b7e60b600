<template>
  <div class="icon-selector">
    <el-popover
      placement="bottom-start"
      width="800"
      trigger="click"
      v-model="visible">
      <div class="icon-selector-content">
        <div class="icon-selector-header">
          <el-input
            v-model="searchText"
            size="small"
            placeholder="搜索图标"
            prefix-icon="el-icon-search"
            clearable>
          </el-input>
        </div>
        <div class="icon-list">
          <div
            v-for="icon in filteredIcons"
            :key="icon"
            class="icon-item"
            :class="{ active: value === icon }"
            @click="selectIcon(icon)">
            <i :class="icon" style="font-size: 18px;"></i>
            <span class="icon-name">{{ icon }}</span>
          </div>
        </div>
      </div>
      <el-input
        slot="reference"
        :value="value"
        readonly
        placeholder="请选择图标">
        <template slot="prepend">
          <i :class="value || 'el-icon-document'" style="width: 20px;"></i>
        </template>
      </el-input>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'IconSelector',
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      searchText: '',
      icons: [
        'el-icon-document',
        'el-icon-video-camera',
        'el-icon-picture',
        'el-icon-setting',
        'el-icon-edit-outline',
        'el-icon-star-on',
        'el-icon-star-off',
        'el-icon-location',
        'el-icon-date',
        'el-icon-time',
        'el-icon-phone',
        'el-icon-mobile-phone',
        'el-icon-message',
        'el-icon-bell',
        'el-icon-user',
        'el-icon-user-solid',
        'el-icon-delete',
        'el-icon-search',
        'el-icon-upload',
        'el-icon-download',
        'el-icon-share',
        'el-icon-link',
        'el-icon-view',
        'el-icon-view-off',
        'el-icon-more',
        'el-icon-more-outline',
        'el-icon-menu',
        'el-icon-s-home',
        'el-icon-s-promotion',
        'el-icon-s-flag',
        'el-icon-s-data',
        'el-icon-s-check',
        'el-icon-s-grid',
        'el-icon-s-tools',
        'el-icon-s-operation',
        'el-icon-s-order',
        'el-icon-s-fold',
        'el-icon-s-unfold',
        'el-icon-folder',
        'el-icon-folder-opened',
        'el-icon-folder-add',
        'el-icon-folder-remove',
        'el-icon-folder-delete',
        'el-icon-collection',
        'el-icon-collection-tag',
        'el-icon-notebook-1',
        'el-icon-notebook-2',
        'el-icon-edit',
        'el-icon-edit-outline',
        'el-icon-reading',
        'el-icon-picture',
        'el-icon-picture-outline',
        'el-icon-picture-outline-round',
        'el-icon-camera',
        'el-icon-camera-solid',
        'el-icon-video-camera',
        'el-icon-video-camera-solid',
        'el-icon-video-play',
        'el-icon-video-pause',
        'el-icon-monitor',
        'el-icon-headset',
        'el-icon-s-platform',
        'el-icon-s-cooperation',
        'el-icon-s-management',
        'el-icon-s-shop',
        'el-icon-s-marketing',
        'el-icon-s-finance',
        'el-icon-s-claim',
        'el-icon-s-custom',
        'el-icon-s-opportunity',
        'el-icon-s-data'
      ]
    }
  },
  computed: {
    filteredIcons() {
      if (!this.searchText) {
        return this.icons
      }
      const search = this.searchText.toLowerCase()
      return this.icons.filter(icon =>
        icon.toLowerCase().includes(search)
      )
    }
  },
  methods: {
    selectIcon(icon) {
      this.$emit('input', icon)
      this.$emit('change', icon)
      this.visible = false
      this.searchText = ''
    }
  }
}
</script>

<style scoped>
.icon-selector {
  display: inline-block;
  width: 100%;
}

.icon-selector-content {
  max-height: 400px;
  overflow-y: auto;
}

.icon-selector-header {
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.icon-list {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 10px;
  padding: 10px;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-item:hover {
  background-color: #f5f7fa;
  border-color: #409eff;
}

.icon-item.active {
  background-color: #ecf5ff;
  border-color: #409eff;
  color: #409eff;
}

.icon-name {
  font-size: 10px;
  margin-top: 4px;
  text-align: center;
  color: #606266;
}

@media (max-width: 768px) {
  .icon-list {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
