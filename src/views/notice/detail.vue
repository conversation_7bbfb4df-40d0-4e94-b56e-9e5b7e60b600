<template>
  <div class="notice-detail-page">
    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回列表</el-button>
      </div>

      <!-- 通知公告详情 -->
      <div class="notice-detail" v-if="noticeDetail" v-loading="loading">
        <div class="notice-header">
          <!-- 标识标签 -->
          <div class="notice-flags">
            <el-tag v-if="noticeDetail.isTop" type="danger" size="small">置顶</el-tag>
            <el-tag v-if="noticeDetail.type === 'important'" type="warning" size="small">重要</el-tag>
            <el-tag v-if="noticeDetail.urgency === 'high'" type="danger" size="small">紧急</el-tag>
            <el-tag :type="getStatusTagType(noticeDetail.status)" size="small">
              {{ getStatusText(noticeDetail.status) }}
            </el-tag>
          </div>
          
          <h1 class="notice-title">{{ noticeDetail.title }}</h1>
          
          <div class="notice-meta">
            <span class="meta-item">
              <i class="el-icon-time"></i>
              发布时间：{{ formatDate(noticeDetail.publishTime, 'YYYY-MM-DD HH:mm') }}
            </span>
            <span class="meta-item">
              <i class="el-icon-user"></i>
              发布部门：{{ noticeDetail.author }}
            </span>
            <span class="meta-item">
              <i class="el-icon-view"></i>
              浏览量：{{ noticeDetail.views }}
            </span>
            <span class="meta-item">
              <i class="el-icon-folder"></i>
              类型：{{ getTypeName(noticeDetail.type) }}
            </span>
            <span v-if="noticeDetail.expireTime" class="meta-item expire-info" :class="getExpireClass(noticeDetail.expireTime)">
              <i class="el-icon-warning"></i>
              {{ getExpireText(noticeDetail.expireTime) }}
            </span>
          </div>
        </div>

        <!-- 重要提示框 -->
        <div v-if="noticeDetail.type === 'important' || noticeDetail.urgency === 'high'" class="important-alert">
          <el-alert
            :title="getAlertTitle(noticeDetail)"
            :type="getAlertType(noticeDetail)"
            :description="getAlertDescription(noticeDetail)"
            show-icon
            :closable="false"
          />
        </div>

        <div class="notice-content">
          <div class="content-wrapper html-view" v-html="noticeDetail.content"></div>
        </div>

        <!-- 附件列表 -->
        <div class="notice-attachments" v-if="noticeDetail.attachments && noticeDetail.attachments.length > 0">
          <h3>相关附件</h3>
          <div class="attachment-list">
            <div 
              class="attachment-item" 
              v-for="attachment in noticeDetail.attachments" 
              :key="attachment.fileId"
              @click="downloadAttachment(attachment)"
            >
              <div class="attachment-icon">
                <i :class="getFileIcon(attachment.name)"></i>
              </div>
              <div class="attachment-info">
                <div class="attachment-name">{{ attachment.name }}</div>
                <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
              </div>
              <div class="attachment-action">
                <el-button type="text" size="small" icon="el-icon-download">下载</el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分享和操作 -->
        <div class="notice-actions" v-if="false">
          <div class="action-buttons">
            <el-button type="primary" @click="shareNotice">
              <i class="el-icon-share"></i>
              分享
            </el-button>
            <el-button @click="collectNotice" :type="isCollected ? 'warning' : 'default'">
              <i :class="isCollected ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
              {{ isCollected ? '已收藏' : '收藏' }}
            </el-button>
            <el-button @click="downloadNotice" v-if="hasAttachment">
              <i class="el-icon-download"></i>
              下载附件
            </el-button>
          </div>
        </div>

        <!-- 相关通知 -->
        <div class="related-notices" v-if="relatedNotices.length > 0">
          <h3>相关通知</h3>
          <div class="related-list">
            <div class="related-item" v-for="item in relatedNotices" :key="item.id" @click="goToDetail(item.id)">
              <img :src="item.coverImage" :alt="item.title" />
              <div class="related-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.summary }}</p>
                <span class="related-date">{{ formatDate(item.publishTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-else-if="!loading">
        <i class="el-icon-warning"></i>
        <p>未找到相关通知</p>
        <el-button type="primary" @click="goBack">返回列表</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {NewsModel} from '@/model/NewsModel'
import {ConfigModel} from '@/model/ConfigModel'

export default {
  name: 'NoticeDetail',
  data() {
    return {
      noticeDetail: null,
      relatedNotices: [],
      loading: false,
      isCollected: false,
      hasAttachment: false,
      noticeTypes: []
    }
  },
  
  async created() {
    await this.loadNoticeTypes()
    this.fetchNoticeDetail()
  },
  
  watch: {
    '$route'(to, from) {
      if (to.path !== from.path) {
        this.fetchNoticeDetail()
      }
    }
  },
  
  methods: {
    // 加载通知类型配置
    async loadNoticeTypes() {
      try {
        const config = JSON.parse(await ConfigModel.getConfig("noticeConfig"))
        if (config && config.types) {
          this.noticeTypes = JSON.parse(config.types)
        } else {
          // 默认类型
          this.noticeTypes = [
            {label: '重要通知', value: 'important'},
            {label: '教学通知', value: 'teaching'},
            {label: '系统通知', value: 'system'},
            {label: '活动通知', value: 'activity'}
          ]
        }
      } catch (error) {
        console.error('加载通知类型配置失败:', error)
        this.noticeTypes = [
          {label: '重要通知', value: 'important'},
          {label: '教学通知', value: 'teaching'},
          {label: '系统通知', value: 'system'},
          {label: '活动通知', value: 'activity'}
        ]
      }
    },
    
    // 根据类型值获取类型名称
    getTypeName(typeValue) {
      if (!typeValue || !this.noticeTypes.length) return typeValue
      const type = this.noticeTypes.find(t => t.value === typeValue)
      return type ? type.label : typeValue
    },
    
    // 获取通知公告详情
    async fetchNoticeDetail() {
      const id = this.$route.params.id
      if (!id) {
        this.$message.error('通知ID无效')
        this.goBack()
        return
      }
      
      this.loading = true
      try {
        const detail = await NewsModel.getNoticeDetail(id)
        if (detail) {
          this.noticeDetail = detail
          // 增加浏览量
          await NewsModel.increaseNoticeViews(id)
          // 获取相关通知
          await this.fetchRelatedNotices(id, detail.type)
          // 检查是否有附件（模拟）
          this.hasAttachment = Math.random() > 0.7
        } else {
          this.$message.error('通知不存在')
          this.goBack()
        }
      } catch (error) {
        this.$message.error('获取通知详情失败')
        console.error('获取通知详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 获取相关通知
    async fetchRelatedNotices(id, type) {
      try {
        const related = await NewsModel.getRelatedNotice(id, type)
        this.relatedNotices = related || []
      } catch (error) {
        console.error('获取相关通知失败:', error)
      }
    },
    
    // 返回列表
    goBack() {
      this.$router.push('/notice')
    },
    
    // 跳转到另一个详情
    goToDetail(id) {
      if (id === parseInt(this.$route.params.id)) return
      this.$router.push(`/notice/${id}`)
    },
    
    // 分享通知
    shareNotice() {
      const url = window.location.href
      const title = this.noticeDetail.title
      
      if (navigator.share) {
        navigator.share({
          title: title,
          url: url
        }).catch(err => {
          console.log('分享失败:', err)
          this.fallbackShare(url, title)
        })
      } else {
        this.fallbackShare(url, title)
      }
    },
    
    // 备用分享方式
    fallbackShare(url, title) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.$message.error('复制链接失败')
        })
      } else {
        this.$message.info('请手动复制页面链接进行分享')
      }
    },
    
    // 收藏通知
    collectNotice() {
      this.isCollected = !this.isCollected
      if (this.isCollected) {
        this.$message.success('收藏成功')
      } else {
        this.$message.success('取消收藏')
      }
    },
    
    // 下载附件
    downloadNotice() {
      this.$message.success('附件下载已开始')
      // 这里可以实现真实的文件下载逻辑
    },
    
    // 获取状态标签类型
    getStatusTagType(status) {
      const types = {
        active: 'success',
        expiring: 'warning', 
        expired: 'danger'
      }
      return types[status] || 'info'
    },
    
    // 获取状态文本
    getStatusText(status) {
      const texts = {
        active: '有效',
        expiring: '即将过期',
        expired: '已过期'
      }
      return texts[status] || '未知'
    },
    
    // 获取过期状态类名
    getExpireClass(expireTime) {
      const now = new Date().getTime()
      const expire = new Date(expireTime).getTime()
      const diff = expire - now
      const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
      
      if (days < 0) return 'expired'
      if (days <= 3) return 'expiring'
      return 'valid'
    },
    
    // 获取过期状态文本
    getExpireText(expireTime) {
      const now = new Date().getTime()
      const expire = new Date(expireTime).getTime()
      const diff = expire - now
      const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
      
      if (days < 0) return '已过期'
      if (days === 0) return '今日过期'
      if (days <= 3) return `${days}天后过期`
      return `有效期至 ${this.formatDate(expireTime, 'YYYY年MM月DD日')}`
    },
    
    // 获取提示框标题
    getAlertTitle(notice) {
      if (notice.urgency === 'high') return '紧急通知'
      if (notice.type === 'important') return '重要通知'
      return '特别提醒'
    },
    
    // 获取提示框类型
    getAlertType(notice) {
      if (notice.urgency === 'high') return 'error'
      if (notice.type === 'important') return 'warning'
      return 'info'
    },
    
    // 获取提示框描述
    getAlertDescription(notice) {
      if (notice.urgency === 'high') return '此通知为紧急通知，请务必仔细阅读并及时处理相关事宜。'
      if (notice.type === 'important') return '此通知非常重要，请务必认真阅读并按要求执行。'
      return '请仔细阅读以下内容。'
    },
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return ''
      return NewsModel.formatDate(date, format)
    },
    
    // 获取文件图标
    getFileIcon(fileName) {
      if (!fileName) return 'el-icon-document'
      
      const ext = fileName.toLowerCase().split('.').pop()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        ppt: 'el-icon-present',
        pptx: 'el-icon-present',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        zip: 'el-icon-folder',
        rar: 'el-icon-folder',
        txt: 'el-icon-document',
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mov: 'el-icon-video-camera'
      }
      
      return iconMap[ext] || 'el-icon-document'
    },
    
    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'
      
      const units = ['B', 'KB', 'MB', 'GB']
      let unitIndex = 0
      let fileSize = size
      
      while (fileSize >= 1024 && unitIndex < units.length - 1) {
        fileSize /= 1024
        unitIndex++
      }
      
      return `${fileSize.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
    },
    
    // 下载附件
    downloadAttachment(attachment) {
      if (!attachment.url) {
        this.$message.error('附件链接无效')
        return
      }
      
      try {
        // 使用 fetch 获取文件数据
        fetch(attachment.url)
          .then(response => {
            if (!response.ok) {
              throw new Error('下载失败')
            }
            return response.blob()
          })
          .then(blob => {
            // 创建下载链接
            const url = window.URL.createObjectURL(blob)
            const link = document.createElement('a')
            link.href = url
            link.download = attachment.name || '附件'
            link.style.display = 'none'
            
            // 触发下载
            document.body.appendChild(link)
            link.click()
            
            // 清理
            document.body.removeChild(link)
            window.URL.revokeObjectURL(url)
            
            this.$message.success(`开始下载 ${attachment.name}`)
          })
          .catch(error => {
            console.error('下载失败:', error)
            // 如果 fetch 失败，尝试直接下载方式
            this.directDownload(attachment)
          })
      } catch (error) {
        console.error('下载出错:', error)
        this.directDownload(attachment)
      }
    },
    
    // 直接下载方式（备用方案）
    directDownload(attachment) {
      const link = document.createElement('a')
      link.href = attachment.url
      link.download = attachment.name || '附件'
      link.setAttribute('download', attachment.name || '附件')
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      this.$message.success(`开始下载 ${attachment.name}`)
    }
  }
}
</script>

<style lang="less" scoped>

.notice-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-button {
    margin-bottom: 20px;
  }
  
  .notice-detail {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .notice-header {
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 20px;
      margin-bottom: 30px;
      
      .notice-flags {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;
        flex-wrap: wrap;
        
        .el-tag {
          margin: 0;
        }
      }
      
      .notice-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        line-height: 1.4;
        margin-bottom: 15px;
      }
      
      .notice-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        color: #666;
        font-size: 14px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 5px;
          
          i {
            color: #4093f9;
          }
          
          &.expire-info {
            font-weight: 500;
            
            &.valid {
              color: #67c23a;
            }
            
            &.expiring {
              color: #e6a23c;
            }
            
            &.expired {
              color: #f56c6c;
            }
          }
        }
      }
    }
    
    .important-alert {
      margin-bottom: 30px;
      
      ::v-deep .el-alert {
        border-radius: 8px;
      }
    }
    
    .notice-content {
      .content-wrapper {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        
        // 富文本内容样式
        ::v-deep {
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          
          p {
            margin-bottom: 15px;
            text-align: justify;
            text-indent: 2em;
          }
          
          h1, h2, h3, h4, h5, h6 {
            margin: 25px 0 15px 0;
            color: #333;
            font-weight: bold;
          }
          
          h3 {
            font-size: 20px;
            color: #4093f9;
            border-left: 4px solid #4093f9;
            padding-left: 12px;
          }
          
          ul, ol {
            margin: 15px 0;
            padding-left: 30px;
            
            li {
              margin-bottom: 8px;
              line-height: 1.6;
            }
          }
          
          strong {
            color: #4093f9;
            font-weight: bold;
          }
          
          // 特殊的通知格式
          .notice-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #4093f9;
          }
          
          .signature {
            text-align: right;
            margin-top: 30px;
            color: #666;
            font-style: italic;
          }
        }
      }
    }
    
    .notice-actions {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
      
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
        flex-wrap: wrap;
      }
    }
    
    .notice-attachments {
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      
      h3 {
        font-size: 18px;
        color: #333;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        
        &:before {
          content: '';
          width: 4px;
          height: 18px;
          background: #4093f9;
          margin-right: 10px;
        }
      }
      
      .attachment-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        
        .attachment-item {
          display: flex;
          align-items: center;
          padding: 15px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e9ecef;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: #fff;
            border-color: #4093f9;
            box-shadow: 0 2px 8px rgba(64, 147, 249, 0.1);
            transform: translateY(-1px);
          }
          
          .attachment-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #4093f9;
            border-radius: 6px;
            margin-right: 15px;
            flex-shrink: 0;
            
            i {
              font-size: 20px;
              color: white;
            }
          }
          
          .attachment-info {
            flex: 1;
            min-width: 0;
            
            .attachment-name {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              margin-bottom: 4px;
              word-break: break-all;
              line-height: 1.4;
            }
            
            .attachment-size {
              font-size: 12px;
              color: #666;
            }
          }
          
          .attachment-action {
            margin-left: 15px;
            flex-shrink: 0;
            
            .el-button {
              color: #4093f9;
              
              &:hover {
                color: #2c6fdb;
              }
            }
          }
        }
      }
    }
    
    .related-notices {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        
        &:before {
          content: '';
          width: 4px;
          height: 20px;
          background: #4093f9;
          margin-right: 10px;
        }
      }
      
      .related-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        
        .related-item {
          display: flex;
          background: #f8f9fa;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background: #fff;
          }
          
          img {
            width: 100px;
            height: 80px;
            object-fit: cover;
            flex-shrink: 0;
          }
          
          .related-content {
            padding: 15px;
            flex: 1;
            
            h4 {
              font-size: 14px;
              color: #333;
              margin-bottom: 8px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.4;
              font-weight: 500;
            }
            
            p {
              font-size: 12px;
              color: #666;
              margin-bottom: 5px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.4;
            }
            
            .related-date {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }
  }
  
  .empty-state {
    background: white;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    color: #999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    i {
      font-size: 64px;
      margin-bottom: 16px;
      color: #ddd;
    }
    
    p {
      font-size: 16px;
      margin: 0 0 20px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notice-detail-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
    
    .notice-detail {
      padding: 20px;
      
      .notice-header {
        .notice-title {
          font-size: 24px;
        }
        
        .notice-meta {
          flex-direction: column;
          gap: 10px;
        }
      }
      
      .notice-content {
        .content-wrapper {
          font-size: 15px;
          
          ::v-deep {
            p {
              text-indent: 1.5em;
            }
            
            h3 {
              font-size: 18px;
            }
          }
        }
      }
      
      .notice-actions {
        .action-buttons {
          flex-direction: column;
          align-items: center;
          
          .el-button {
            width: 150px;
          }
        }
      }
      
      .notice-attachments {
        .attachment-list {
          .attachment-item {
            padding: 12px;
            
            .attachment-icon {
              width: 36px;
              height: 36px;
              margin-right: 12px;
              
              i {
                font-size: 18px;
              }
            }
            
            .attachment-info {
              .attachment-name {
                font-size: 13px;
              }
              
              .attachment-size {
                font-size: 11px;
              }
            }
            
            .attachment-action {
              margin-left: 10px;
              
              .el-button {
                font-size: 12px;
                padding: 4px 8px;
              }
            }
          }
        }
      }
      
      .related-notices {
        .related-list {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .notice-detail-page {
    .notice-detail {
      padding: 15px;
      
      .notice-header {
        .notice-title {
          font-size: 20px;
        }
        
        .notice-flags {
          gap: 5px;
        }
      }
      
      .notice-content {
        .content-wrapper {
          font-size: 14px;
          
          ::v-deep {
            p {
              text-indent: 1em;
            }
            
            h3 {
              font-size: 16px;
            }
          }
        }
      }
      
      .notice-attachments {
        .attachment-list {
          .attachment-item {
            padding: 10px;
            
            .attachment-icon {
              width: 32px;
              height: 32px;
              margin-right: 10px;
              
              i {
                font-size: 16px;
              }
            }
            
            .attachment-info {
              .attachment-name {
                font-size: 12px;
              }
              
              .attachment-size {
                font-size: 10px;
              }
            }
            
            .attachment-action {
              margin-left: 8px;
              
              .el-button {
                font-size: 11px;
                padding: 3px 6px;
              }
            }
          }
        }
      }
    }
  }
}
</style>