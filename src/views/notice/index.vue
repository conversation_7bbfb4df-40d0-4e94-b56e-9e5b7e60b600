<template>
  <div class="notice-page">
    <!-- 页面头部 - 全宽 -->
    <div class="page-header">
      <div class="header-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
      </div>
      <div class="header-content">
        <div class="title-wrapper">
          <h1 class="page-title">
            <i class="el-icon-bell title-icon"></i>
            <span class="title-text">通知公告</span>
          </h1>
          <div class="page-subtitle">
            <span class="subtitle-text">基地重要通知及公告信息</span>
            <div class="subtitle-decoration"></div>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ total || 0 }}</div>
            <div class="stat-label">公告总数</div>
          </div>
        </div>
      </div>
      
      <!-- 轮播区域 - 全宽 -->
      <div class="carousel-section">
        <el-carousel 
          :interval="5000" 
          :height="carouselHeight"
          indicator-position="outside"
          arrow="hover"
          v-loading="carouselLoading"
        >
          <el-carousel-item v-for="item in carouselList" :key="item.noticeId">
            <div class="carousel-item" @click="goToDetail(item.noticeId)">
              <div class="carousel-image">
                <img :src="item.coverImage" :alt="item.title" />
                <div class="carousel-overlay"></div>
              </div>
              <div class="carousel-content">
                <div class="carousel-badges">
                  <span v-if="item.isTop" class="badge-top">置顶</span>
                  <span class="badge-type" :class="`type-${item.type}`">{{ item.typeName }}</span>
                  <span v-if="item.urgency === 'high'" class="badge-urgent">紧急</span>
                </div>
                <h3 class="carousel-title">{{ item.title }}</h3>
                <p class="carousel-summary">{{ item.summary }}</p>
                <div class="carousel-meta">
                  <span class="carousel-date">
                    <i class="el-icon-time"></i>
                    {{ formatDate(item.publishTime, 'YYYY-MM-DD') }}
                  </span>
                  <span class="carousel-views">
                    <i class="el-icon-view"></i>
                    {{ item.views }}
                  </span>
                  <span v-if="item.expireTime" class="expire-info" :class="getExpireClass(item.expireTime)">
                    <i class="el-icon-warning"></i>
                    {{ getExpireText(item.expireTime) }}
                  </span>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>

    <!-- 内容区域 - 居中 -->
    <div class="content-container">
      <!-- 搜索和筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入搜索关键词"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-area">
          <el-select v-model="selectedType" placeholder="选择类型" @change="handleTypeChange">
            <el-option label="全部" value=""></el-option>
            <el-option 
              v-for="type in noticeTypes" 
              :key="type.value" 
              :label="type.label" 
              :value="type.value">
            </el-option>
          </el-select>
          
          <el-select v-model="selectedStatus" placeholder="选择状态" @change="handleStatusChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="有效" value="active"></el-option>
            <el-option label="即将过期" value="expiring"></el-option>
            <el-option label="已过期" value="expired"></el-option>
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          >
          </el-date-picker>
        </div>
      </div>

      <!-- 通知公告列表区域 -->
      <div class="notice-list-section" v-loading="loading">
        <div class="notice-list" v-if="noticeList.length > 0">
          <div 
            class="notice-item" 
            v-for="item in noticeList" 
            :key="item.noticeId"
            :class="{ 'is-top': item.isTop, 'is-important': item.type === 'important' }"
            @click="goToDetail(item.noticeId)"
          >
            <!-- 置顶和重要标识 -->
            <div class="notice-flags">
              <span v-if="item.isTop" class="flag-top">置顶</span>
              <span v-if="item.type === 'important'" class="flag-important">重要</span>
              <span v-if="item.urgency === 'high'" class="flag-urgent">紧急</span>
            </div>
            
            <div class="notice-content">
              <h3 class="notice-title">{{ item.title }}</h3>
              <p class="notice-summary">{{ item.summary }}</p>
              
              <div class="notice-meta">
                <div class="meta-left">
                  <span class="notice-type">
                    <i class="el-icon-folder"></i>
                    {{ item.typeName }}
                  </span>
                  <span class="notice-author">
                    <i class="el-icon-user"></i>
                    {{ item.author }}
                  </span>
                  <span class="notice-views">
                    <i class="el-icon-view"></i>
                    {{ item.views }}
                  </span>
                </div>
                
                <div class="meta-right">
                  <span class="notice-date">
                    <i class="el-icon-time"></i>
                    {{ formatDate(item.publishTime, 'YYYY-MM-DD') }}
                  </span>
                  <span v-if="item.expireTime" class="notice-expire" :class="getExpireClass(item.expireTime)">
                    <i class="el-icon-warning"></i>
                    {{ getExpireText(item.expireTime) }}
                  </span>
                </div>
              </div>
            </div>
            
            <!-- 状态指示器 -->
            <div class="notice-status" :class="`status-${item.status}`">
              <i :class="getStatusIcon(item.status)"></i>
            </div>
          </div>
        </div>
        
        <!-- 空数据状态 -->
        <div class="empty-state" v-else-if="!loading">
          <i class="el-icon-bell"></i>
          <p>暂无通知公告</p>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {NewsModel} from '@/model/NewsModel'
import {ConfigModel} from '@/model/ConfigModel'

export default {
  name: 'NoticePage',
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedType: '',
      selectedStatus: '',
      dateRange: null,
      
      // 分页
      currentPage: 1,
      pageSize: 12,
      total: 0,
      
      // 数据
      noticeList: [],
      loading: false,
      
      // 轮播
      carouselList: [],
      carouselLoading: false,
      carouselHeight: '300px',
      
      // 通知类型配置
      noticeTypes: []
    }
  },
  
  async created() {
    await this.loadNoticeTypes()
    this.fetchCarouselList()
    this.fetchNoticeList()
  },
  
  methods: {
    // 获取通知类型配置
    async loadNoticeTypes() {
      try {
        const config = JSON.parse(await ConfigModel.getConfig("noticeConfig"))
        if (config && config.types) {
          this.noticeTypes = JSON.parse(config.types)
        } else {
          // 如果配置不存在，使用默认分类
          this.noticeTypes = [
            { label: "重要通知", value: "important" },
            { label: "教学通知", value: "teaching" },
            { label: "活动通知", value: "activity" },
            { label: "系统通知", value: "system" }
          ]
        }
      } catch (error) {
        console.error('获取通知类型配置失败:', error)
        // 错误情况下使用默认分类
        this.noticeTypes = [
          { label: "重要通知", value: "important" },
          { label: "教学通知", value: "teaching" },
          { label: "活动通知", value: "activity" },
          { label: "系统通知", value: "system" }
        ]
      }
    },
    
    // 获取轮播数据
    async fetchCarouselList() {
      this.carouselLoading = true
      try {
        const carouselData = await NewsModel.getNoticeCarouselList()
        this.carouselList = carouselData
      } catch (error) {
        this.$message.error('获取轮播数据失败')
        console.error('获取轮播数据失败:', error)
      } finally {
        this.carouselLoading = false
      }
    },
    
    // 获取通知公告列表
    async fetchNoticeList() {
      this.loading = true
      try {
        const params = {
          keyword: this.searchKeyword?{'$regex': `.*${this.searchKeyword}.*`}:undefined,
          type: this.selectedType?this.selectedType:undefined,
          status: this.selectedStatus?this.selectedStatus:undefined,
          publishTime: this.dateRange ? {
            $gte: new Date(this.dateRange[0]).getTime(),
            $lte: (() => {
              const endDate = new Date(this.dateRange[1]);
              endDate.setHours(23, 59, 59, 999);
              return endDate.getTime();
            })()
          } : undefined

        }
        
        const response = await NewsModel.getNoticePageList(this.currentPage-1, this.pageSize, params)
        
        this.noticeList = response.list
        this.total = response.total
      } catch (error) {
        this.$message.error('获取通知公告列表失败')
        console.error('获取通知公告列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchNoticeList()
    },
    
    // 类型筛选
    handleTypeChange() {
      this.currentPage = 1
      this.fetchNoticeList()
    },
    
    // 状态筛选
    handleStatusChange() {
      this.currentPage = 1
      this.fetchNoticeList()
    },
    
    // 日期筛选
    handleDateChange() {
      this.currentPage = 1
      this.fetchNoticeList()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchNoticeList()
      // 滚动到顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    
    // 跳转详情
    goToDetail(id) {
      this.$router.push(`/notice/${id}`)
    },
    
    // 获取过期状态类名
    getExpireClass(expireTime) {
      const now = new Date().getTime()
      const expire = new Date(expireTime).getTime()
      const diff = expire - now
      const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
      
      if (days < 0) return 'expired'
      if (days <= 3) return 'expiring'
      return 'valid'
    },
    
    // 获取过期状态文本
    getExpireText(expireTime) {
      const now = new Date().getTime()
      const expire = new Date(expireTime).getTime()
      const diff = expire - now
      const days = Math.ceil(diff / (1000 * 60 * 60 * 24))
      
      if (days < 0) return '已过期'
      if (days === 0) return '今日过期'
      if (days <= 3) return `${days}天后过期`
      return `有效期至 ${this.formatDate(expireTime, 'MM-DD')}`
    },
    
    // 获取状态图标
    getStatusIcon(status) {
      const icons = {
        active: 'el-icon-success',
        expiring: 'el-icon-warning',
        expired: 'el-icon-error'
      }
      return icons[status] || 'el-icon-info'
    },
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return ''
      return NewsModel.formatDate(date, format)
    }
  }
}
</script>

<style lang="less" scoped>

.notice-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  
  .page-header {
    position: relative;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(64, 147, 249, 0.15);
    margin-bottom: 40px;
    
    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(64, 147, 249, 0.85) 0%, rgba(44, 90, 160, 0.9) 100%);
      
      .bg-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
          radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.04) 0%, transparent 50%);
        animation: patternMove 20s ease-in-out infinite;
      }
      
      .bg-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.08) 50%, transparent 70%);
        animation: gradientShine 3s ease-in-out infinite;
      }
    }
    
    .header-content {
      position: relative;
      z-index: 2;
      padding: 40px 0;
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title-wrapper {
        flex: 1;
        
        .page-title {
          display: flex;
          align-items: center;
          font-size: 36px;
          font-weight: bold;
          color: white;
          margin-bottom: 15px;
          animation: titleSlideIn 1s ease-out;
          
          .title-icon {
            font-size: 40px;
            margin-right: 15px;
            animation: iconBell 2s ease-in-out infinite;
          }
          
          .title-text {
            position: relative;
            
            &::after {
              content: '';
              position: absolute;
              bottom: -5px;
              left: 0;
              width: 0;
              height: 3px;
              background: rgba(255, 255, 255, 0.8);
              animation: underlineExpand 1.5s ease-out 0.5s forwards;
            }
          }
        }
        
        .page-subtitle {
          position: relative;
          
          .subtitle-text {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            animation: subtitleFadeIn 1s ease-out 0.3s both;
          }
          
          .subtitle-decoration {
            width: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.6);
            margin-top: 10px;
            border-radius: 1px;
            animation: decorationSlide 1s ease-out 0.8s both;
          }
        }
      }
      
      .header-stats {
        .stat-item {
          text-align: center;
          background: rgba(255, 255, 255, 0.15);
          padding: 20px 30px;
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          animation: statsSlideIn 1s ease-out 0.6s both;
          
          .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
            line-height: 1;
            margin-bottom: 5px;
            animation: numberCount 2s ease-out 1s both;
          }
          
          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }
  
  // 动画定义
  @keyframes patternMove {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(10px, -10px) rotate(2deg); }
  }
  
  @keyframes gradientShine {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
  }
  
  @keyframes titleSlideIn {
    0% { 
      opacity: 0; 
      transform: translateY(30px); 
    }
    100% { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes iconBell {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-10deg); }
    75% { transform: rotate(10deg); }
  }
  
  @keyframes underlineExpand {
    0% { width: 0; }
    100% { width: 100px; }
  }
  
  @keyframes subtitleFadeIn {
    0% { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    100% { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes decorationSlide {
    0% { 
      width: 0; 
      opacity: 0; 
    }
    100% { 
      width: 60px; 
      opacity: 1; 
    }
  }
  
  @keyframes statsSlideIn {
    0% { 
      opacity: 0; 
      transform: translateX(30px); 
    }
    100% { 
      opacity: 1; 
      transform: translateX(0); 
    }
  }
  
  @keyframes numberCount {
    0% { 
      transform: scale(0.5); 
      opacity: 0; 
    }
    50% { 
      transform: scale(1.1); 
    }
    100% { 
      transform: scale(1); 
      opacity: 1; 
    }
  }
  
  .carousel-section {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px 30px;
    
    ::v-deep .el-carousel {
      border-radius: 12px;
      
      .el-carousel__container {
        border-radius: 12px;
      }
      
      .el-carousel__indicators--outside {
        margin-top: 20px;
        
        .el-carousel__indicator {
          .el-carousel__button {
            background-color: rgba(255, 255, 255, 0.5);
            
            &.is-active {
              background-color: rgba(255, 255, 255, 0.9);
            }
          }
        }
      }
      
      .el-carousel__arrow {
        background-color: rgba(255, 255, 255, 0.8);
        color: #333;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.9);
        }
      }
    }
    
    .carousel-item {
      position: relative;
      height: 100%;
      cursor: pointer;
      border-radius: 12px;
      overflow: hidden;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.02);
        
        .carousel-overlay {
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.3) 0%,
            rgba(0, 0, 0, 0.7) 100%
          );
        }
        
        .carousel-title {
          color: #fff;
        }
      }
      
      .carousel-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .carousel-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.2) 0%,
            rgba(0, 0, 0, 0.6) 100%
          );
          transition: background 0.3s ease;
        }
      }
      
      .carousel-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 30px;
        color: white;
        z-index: 2;
        
        .carousel-badges {
          display: flex;
          gap: 8px;
          margin-bottom: 10px;
          flex-wrap: wrap;
          
          .badge-top, .badge-type, .badge-urgent {
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            color: white;
          }
          
          .badge-top {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            animation: topPulse 2s ease-in-out infinite;
          }
          
          .badge-urgent {
            background: linear-gradient(45deg, #f44336, #ff6b6b);
            animation: urgentBlink 1.5s ease-in-out infinite;
          }
          
          .badge-type {
            &.type-important {
              background: rgba(255, 167, 38, 0.9);
            }
            
            &.type-teaching {
              background: rgba(103, 194, 58, 0.9);
            }
            
            &.type-system {
              background: rgba(144, 147, 153, 0.9);
            }
            
            &.type-activity {
              background: rgba(64, 147, 249, 0.9);
            }
          }
        }
        
        .carousel-title {
          font-size: 22px;
          font-weight: bold;
          margin-bottom: 10px;
          line-height: 1.3;
          color: rgba(255, 255, 255, 0.95);
          transition: color 0.3s ease;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .carousel-summary {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.5;
          margin-bottom: 15px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .carousel-meta {
          display: flex;
          align-items: center;
          gap: 15px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          flex-wrap: wrap;
          
          .carousel-date, .carousel-views, .expire-info {
            display: flex;
            align-items: center;
            gap: 4px;
          }
          
          .expire-info {
            &.valid {
              color: rgba(103, 194, 58, 0.9);
            }
            
            &.expiring {
              color: rgba(255, 167, 38, 0.9);
              animation: expireWarning 2s ease-in-out infinite;
            }
            
            &.expired {
              color: rgba(245, 108, 108, 0.9);
            }
          }
        }
      }
    }
  }
  
  @keyframes topPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
  
  @keyframes urgentBlink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }
  
  @keyframes expireWarning {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
  }
  
  .content-container {
    width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }
  
  .search-filter-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(64, 147, 249, 0.1);
    
    .search-area {
      margin-bottom: 25px;
      text-align: center;
      
      .search-input {
        max-width: 600px;
        
        ::v-deep .el-input__inner {
          border-radius: 30px 0 0 30px;
          height: 50px;
          line-height: 46px; // 调整行高以适应边框
          border: 2px solid rgba(64, 147, 249, 0.2);
          font-size: 15px;
          padding-left: 20px;
          transition: all 0.3s ease;
          box-sizing: border-box;
          
          &:focus {
            border-color: #4093f9;
            box-shadow: 0 0 0 3px rgba(64, 147, 249, 0.1);
          }
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 30px 30px 0;
          border: 2px solid rgba(64, 147, 249, 0.2);
          border-left: none;
          height: 50px;
          
          .el-button {
            border-radius: 0 30px 30px 0;
            height: 46px; // 调整按钮高度以适应容器
            padding: 0 25px;
            background: linear-gradient(135deg, #4093f9 0%, darken(#4093f9, 10%) 100%);
            border: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              background: linear-gradient(135deg, darken(#4093f9, 5%) 0%, darken(#4093f9, 15%) 100%);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(64, 147, 249, 0.3);
            }
          }
        }
      }
    }
    
    .filter-area {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .el-select, .el-date-picker {
        min-width: 160px;
        
        ::v-deep .el-input__inner {
          border-radius: 12px;
          height: 42px;
          border: 2px solid rgba(64, 147, 249, 0.15);
          transition: all 0.3s ease;
          
          &:focus {
            border-color: #4093f9;
            box-shadow: 0 0 0 3px rgba(64, 147, 249, 0.1);
          }
        }
      }
      
      ::v-deep .el-select:hover .el-input__inner,
      ::v-deep .el-date-editor:hover .el-input__inner {
        border-color: #4093f9;
      }
    }
  }
  
  .notice-list-section {
    min-height: 400px;
    
    .notice-list {
      display: flex;
      flex-direction: column;
      gap: 15px;
      
      .notice-item {
        background: white;
        border-radius: 8px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;
        border-left: 4px solid transparent;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        &.is-top {
          border-left-color: #ff6b6b;
          background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
        }
        
        &.is-important {
          border-left-color: #ffa726;
          background: linear-gradient(135deg, #fff8e1 0%, #ffffff 100%);
        }
        
        .notice-flags {
          position: absolute;
          top: 15px;
          right: 15px;
          display: flex;
          gap: 5px;
          
          .flag-top, .flag-important, .flag-urgent {
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
            color: white;
          }
          
          .flag-top {
            background: #ff6b6b;
          }
          
          .flag-important {
            background: #ffa726;
          }
          
          .flag-urgent {
            background: #f44336;
          }
        }
        
        .notice-content {
          padding-right: 100px;
          
          .notice-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            
            &:hover {
              color: #4093f9;
            }
          }
          
          .notice-summary {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .notice-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
            flex-wrap: wrap;
            gap: 10px;
            
            .meta-left, .meta-right {
              display: flex;
              gap: 15px;
              flex-wrap: wrap;
            }
            
            .notice-type, .notice-author, .notice-views, .notice-date, .notice-expire {
              display: flex;
              align-items: center;
              gap: 4px;
            }
            
            .notice-expire {
              &.valid {
                color: #67c23a;
              }
              
              &.expiring {
                color: #e6a23c;
              }
              
              &.expired {
                color: #f56c6c;
              }
            }
          }
        }
        
        .notice-status {
          position: absolute;
          bottom: 15px;
          right: 15px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          
          &.status-active {
            background: #67c23a;
            color: white;
          }
          
          &.status-expiring {
            background: #e6a23c;
            color: white;
          }
          
          &.status-expired {
            background: #f56c6c;
            color: white;
          }
          
          i {
            font-size: 12px;
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      
      i {
        font-size: 64px;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0 60px;
    padding: 20px 20px 15px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    ::v-deep .el-pagination {
      .el-pagination__total {
        font-size: 14px;
        color: #666;
        margin-right: 20px;
      }

      .el-pager {
        li {
          min-width: 36px;
          height: 36px;
          line-height: 36px;
          font-size: 14px;
          margin: -5px 2px;
          border-radius: 6px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          color: #495057;
          transition: all 0.2s ease;

          &:hover {
            background: #e9ecef;
            border-color: #dee2e6;
          }

          &.active {
            background: #4093f9;
            color: white;
            border-color: #4093f9;
          }
        }
      }


      .btn-prev, .btn-next {
        min-width: 36px;
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        margin: -5px 8px;
        border-radius: 6px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        color: #495057;
        transition: all 0.2s ease;

        &:hover {
          background: #4093f9;
          color: white;
          border-color: #4093f9;
        }

        &:disabled {
          background: #f8f9fa;
          color: #adb5bd;
          border-color: #e9ecef;
          cursor: not-allowed;

          &:hover {
            background: #f8f9fa;
            color: #adb5bd;
            border-color: #e9ecef;
          }
        }
      }

      .el-pagination__jump {
        margin-left: 20px;

        .el-pagination__editor {
          width: 50px;
          height: 36px;
          border-radius: 6px;

          text-align: center;
          font-size: 14px;

          &:focus {
            border-color: #4093f9;
            outline: none;
          }
        }

        .el-pagination__goto {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .notice-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
    
    .page-header {
      .header-content {
        padding: 40px 20px;
        flex-direction: column;
        text-align: center;
        gap: 30px;
        
        .title-wrapper {
          .page-title {
            font-size: 28px;
            justify-content: center;
            
            .title-icon {
              font-size: 32px;
            }
          }
          
          .page-subtitle {
            .subtitle-text {
              font-size: 16px;
            }
          }
        }
        
        .header-stats {
          .stat-item {
            padding: 15px 20px;
            
            .stat-number {
              font-size: 24px;
            }
          }
        }
      }
    }
    
    .search-filter-section {
      padding: 15px;
      
      .search-area {
        .search-input {
          max-width: 100%;
        }
      }
      
      .filter-area {
        flex-direction: column;
        align-items: center;
        
        .el-select, .el-date-picker {
          width: 100%;
          max-width: 300px;
        }
      }
    }
    
    .notice-list-section {
      .notice-list {
        .notice-item {
          padding: 15px;
          
          .notice-flags {
            position: static;
            margin-bottom: 10px;
            justify-content: flex-start;
          }
          
          .notice-content {
            padding-right: 0;
            
            .notice-meta {
              flex-direction: column;
              align-items: flex-start;
              
              .meta-left, .meta-right {
                width: 100%;
                justify-content: space-between;
              }
            }
          }
          
          .notice-status {
            position: static;
            align-self: flex-end;
            margin-top: 10px;
          }
        }
      }
    }
    
    .pagination-container {
      margin: 30px 0 40px;
      padding: 15px;
      
      ::v-deep .el-pagination {
        .el-pager {
          li {
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            font-size: 13px;
            margin: 0 1px;
          }
        }
        
        .btn-prev, .btn-next {
          min-width: 32px;
          height: 32px;
          line-height: 32px;
          font-size: 13px;
          margin: 0 4px;
        }
        
        .el-pagination__jump {
          margin-left: 10px;
          
          .el-pagination__editor {
            width: 45px;
            height: 32px;
            font-size: 13px;
          }
          
          .el-pagination__goto {
            font-size: 13px;
          }
        }
        
        .el-pagination__total {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .notice-page {
    .page-header {
      .header-content {
        padding: 30px 15px;
        
        .title-wrapper {
          .page-title {
            font-size: 24px;
            
            .title-icon {
              font-size: 28px;
              margin-right: 10px;
            }
          }
          
          .page-subtitle {
            .subtitle-text {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .notice-list-section {
      .notice-list {
        .notice-item {
          .notice-content {
            .notice-title {
              font-size: 16px;
            }
            
            .notice-summary {
              font-size: 13px;
            }
            
            .notice-meta {
              font-size: 11px;
              
              .meta-left, .meta-right {
                gap: 8px;
              }
            }
          }
        }
      }
    }
    
    .pagination-container {
      padding: 10px;
      
      ::v-deep .el-pagination {
        .el-pager {
          li {
            min-width: 30px;
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            margin: 0 1px;
          }
        }
        
        .btn-prev, .btn-next {
          min-width: 30px;
          height: 30px;
          line-height: 30px;
          font-size: 12px;
          margin: 0 3px;
        }
        
        .el-pagination__jump {
          display: none; // 在小屏幕隐藏跳转功能
        }
        
        .el-pagination__total {
          font-size: 12px;
        }
      }
    }
  }
}
</style>