<template>
  <div class="statistics-page">
    <!-- 全屏切换按钮 -->
    <div class="fullscreen-toggle" @click="toggleFullscreen">
      <i :class="isFullscreen ? 'el-icon-aim' : 'el-icon-full-screen'"></i>
    </div>

    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">虚仿基地数据展示</h1>
      <div class="update-time">
        <i class="el-icon-time"></i>
        最后更新：{{ lastUpdateTime }}
      </div>
    </div>

    <!-- Tab切换 -->
    <div class="tab-container" v-loading="loading" element-loading-text="加载数据中...">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="statistics-tabs">
        <el-tab-pane label="整体概览" name="overview">
          <div class="tab-content">
            <!-- 整体概览内容 -->
            <div class="overview-section">
              <div class="stats-grid">
                <div class="stat-card" v-for="(item, index) in overviewStats" :key="index">
                  <div class="stat-icon">
                    <i :class="item.icon" :style="{ color: item.color }"></i>
                  </div>
                  <div class="stat-content">
                    <div class="stat-number">{{ item.value }}</div>
                    <div class="stat-label">{{ item.label }}</div>
                    <div class="stat-change" :class="item.trend">
                      <i :class="item.trend === 'up' ? 'el-icon-top' : 'el-icon-bottom'"></i>
                      {{ item.change }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- 整体趋势图表 -->
              <div class="chart-container">
                <div class="chart-card">
                  <div class="chart-header">
                    <h3>用户活跃度趋势</h3>
                    <el-radio-group v-model="userActivityType" size="small" @change="updateUserActivityChart">
                      <el-radio-button label="daily">日活跃</el-radio-button>
                      <el-radio-button label="weekly">周活跃</el-radio-button>
                    </el-radio-group>
                  </div>
                  <div id="userActivityChart" class="chart"></div>
                </div>

                <div class="chart-card">
                  <div class="chart-header">
                    <h3>模块使用分布</h3>
                  </div>
                  <div id="moduleUsageChart" class="chart"></div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="实训课程" name="course">
          <div class="tab-content">
            <!-- 课程统计内容 -->
            <div class="course-section">
              <div class="stats-summary">
                <div class="summary-card" v-for="(item, index) in courseStats" :key="index">
                  <div class="summary-icon">
                    <i :class="item.icon"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-number">{{ item.value }}</div>
                    <div class="summary-label">{{ item.label }}</div>
                  </div>
                </div>
              </div>

              <div class="chart-container">
                <div class="chart-card">
                  <div class="chart-header">
                    <h3>实训数据趋势</h3>
                    <el-radio-group v-model="courseTrendType" size="small" @change="updateCourseTrendChart">
                      <el-radio-button label="week">本周</el-radio-button>
                      <el-radio-button label="month">本年</el-radio-button>
                    </el-radio-group>
                  </div>
                  <div id="courseTrendChart" class="chart"></div>
                </div>

                <div class="chart-card">
                  <div class="chart-header">
                    <h3>课程分类分布</h3>
                  </div>
                  <div id="courseCategoryChart" class="chart"></div>
                </div>
              </div>

              <!-- 热门课程排行 -->
              <div class="ranking-section">
                <div class="ranking-card">
                  <div class="ranking-header">
                    <h3>热门课程排行</h3>
                  </div>
                  <div class="ranking-list">
                    <div class="ranking-item" v-for="(course, index) in popularCourses" :key="course.id">
                      <div class="ranking-number" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
                      <div class="ranking-content">
                        <div class="ranking-title">{{ course.name }}</div>
                        <div class="ranking-stats">
                          <span>浏览量: {{ course.views }}</span>
                          <span>参与人数: {{ course.participants }}</span>
                          <span>评分: {{ course.rating }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="产教融合" name="industry">
          <div class="tab-content">
            <!-- 产教融合统计内容 -->
            <div class="industry-section">
              <div class="stats-summary">
                <div class="summary-card" v-for="(item, index) in industryStats" :key="index">
                  <div class="summary-icon">
                    <i :class="item.icon"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-number">{{ item.value }}</div>
                    <div class="summary-label">{{ item.label }}</div>
                  </div>
                </div>
              </div>

              <div class="chart-container">
                <div class="chart-card">
                  <div class="chart-header">
                    <h3>产教融合发展趋势</h3>
                    <el-radio-group v-model="industryTrendType" size="small" @change="updateIndustryTrendChart">
                      <el-radio-button label="enterprise">企业合作</el-radio-button>
                      <el-radio-button label="student">学生就业</el-radio-button>
                    </el-radio-group>
                  </div>
                  <div id="industryTrendChart" class="chart"></div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="资源中心" name="resource">
          <div class="tab-content">
            <!-- 资源中心统计内容 -->
            <div class="resource-section">
              <div class="stats-summary">
                <div class="summary-card" v-for="(item, index) in resourceStats" :key="index">
                  <div class="summary-icon">
                    <i :class="item.icon"></i>
                  </div>
                  <div class="summary-content">
                    <div class="summary-number">{{ item.value }}</div>
                    <div class="summary-label">{{ item.label }}</div>
                  </div>
                </div>
              </div>

              <div class="chart-container">
                <div class="chart-card">
                  <div class="chart-header">
                    <h3>资源分类分布</h3>
                  </div>
                  <div id="resourceCategoryChart" class="chart"></div>
                </div>

                <div class="chart-card">
                  <div class="chart-header">
                    <h3>资源使用趋势</h3>
                    <el-radio-group v-model="resourceTrendType" size="small" @change="updateResourceTrendChart">
                      <el-radio-button label="views">浏览量</el-radio-button>
                      <el-radio-button label="downloads">下载量</el-radio-button>
                    </el-radio-group>
                  </div>
                  <div id="resourceTrendChart" class="chart"></div>
                </div>
              </div>

              <!-- 热门资源排行 -->
              <div class="ranking-section">
                <div class="ranking-card">
                  <div class="ranking-header">
                    <h3>热门资源排行</h3>
                  </div>
                  <div class="ranking-list">
                    <div class="ranking-item" v-for="(resource, index) in popularResources" :key="resource.id">
                      <div class="ranking-number" :class="'rank-' + (index + 1)">{{ index + 1 }}</div>
                      <div class="ranking-content">
                        <div class="ranking-title">{{ resource.title }}</div>
                        <div class="ranking-stats">
                          <span>浏览量: {{ resource.views }}</span>
                          <span>下载量: {{ resource.downloads }}</span>
                          <span>评分: {{ resource.rating }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { StatisticModel } from '@/model/StatisticModel'
import { date_format } from '@/utils/common'

export default {
  name: 'StatisticsPage',
  
  data() {
    return {
      // 页面状态
      isFullscreen: false,
      activeTab: 'overview',
      lastUpdateTime: '',
      
      // 图表实例
      userActivityChart: null,
      moduleUsageChart: null,
      courseTrendChart: null,
      courseCategoryChart: null,
      industryTrendChart: null,
      resourceCategoryChart: null,
      resourceTrendChart: null,
      
      // 图表配置
      userActivityType: 'daily',
      courseTrendType: 'week',
      industryTrendType: 'enterprise',
      resourceTrendType: 'views',
      
      // 数据
      overviewStats: [],
      courseStats: [],
      industryStats: [],
      resourceStats: [],
      popularCourses: [],
      popularResources: [],

      // 工具变量
      resizeTimer: null,
      loading: false
    }
  },
  
  mounted() {
    this.initPage()
    // 监听窗口大小变化，重新调整图表大小
    window.addEventListener('resize', this.handleResize)
    // 监听全屏状态变化
    document.addEventListener('fullscreenchange', this.handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },

  beforeDestroy() {
    this.destroyCharts()
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
    document.removeEventListener('fullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange)
    document.removeEventListener('MSFullscreenChange', this.handleFullscreenChange)
  },
  
  methods: {
    // 初始化页面
    async initPage() {
      this.lastUpdateTime = date_format(new Date(), 'yyyy-MM-dd hh:mm:ss')
      await this.loadData()
      this.$nextTick(() => {
        this.initCharts()
      })
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        // 并行加载所有数据
        const [
          baseOverview,
          courseModule,
          industryModule,
          resourceModule,
          popularCourses,
          popularResources
        ] = await Promise.all([
          StatisticModel.getBaseOverviewStats(),
          StatisticModel.getCourseModuleStats(),
          StatisticModel.getIndustryModuleStats(),
          StatisticModel.getResourceModuleStats(),
          StatisticModel.getPopularCourses({ limit: 8 }),
          StatisticModel.getPopularResources({ limit: 8 })
        ])

        // 处理整体概览数据
        this.overviewStats = [
          {
            icon: 'el-icon-notebook-1',
            color: '#4093f9',
            value: baseOverview.totalCourses,
            label: '总课程数',
            change: '+12%',
            trend: 'up'
          },
          {
            icon: 'el-icon-user',
            color: '#67c23a',
            value: baseOverview.totalUsers,
            label: '总用户数',
            change: '+8%',
            trend: 'up'
          },
          {
            icon: 'el-icon-document',
            color: '#e6a23c',
            value: baseOverview.totalResources,
            label: '总资源数',
            change: '+15%',
            trend: 'up'
          },
          {
            icon: 'el-icon-office-building',
            color: '#f56c6c',
            value: baseOverview.totalEnterprises,
            label: '合作企业',
            change: '+5%',
            trend: 'up'
          },
          {
            icon: 'el-icon-view',
            color: '#909399',
            value: baseOverview.totalViews,
            label: '总浏览量',
            change: '+25%',
            trend: 'up'
          },
          {
            icon: 'el-icon-download',
            color: '#606266',
            value: baseOverview.totalDownloads,
            label: '总下载量',
            change: '+18%',
            trend: 'up'
          }
        ]

        // 处理课程统计数据
        this.courseStats = [
          {
            icon: 'el-icon-notebook-1',
            value: courseModule.totalCourses,
            label: '总课程数'
          },
          {
            icon: 'el-icon-user',
            value: courseModule.uniqueUsers,
            label: '实训人数'
          },
          {
            icon: 'el-icon-data-line',
            value: courseModule.totalParticipants,
            label: '实训人次'
          },
          {
            icon: 'el-icon-time',
            value: courseModule.avgDuration + '分钟',
            label: '平均用时'
          },
          {
            icon: 'el-icon-circle-check',
            value: courseModule.completionRate + '%',
            label: '完成率'
          },
          {
            icon: 'el-icon-trophy',
            value: courseModule.passRate + '%',
            label: '通过率'
          }
        ]

        // 处理产教融合统计数据
        this.industryStats = [
          {
            icon: 'el-icon-office-building',
            value: industryModule.enterpriseCount,
            label: '合作企业'
          },
          {
            icon: 'el-icon-user',
            value: industryModule.studentCount,
            label: '优秀学生'
          },
          {
            icon: 'el-icon-suitcase',
            value: industryModule.jobCount,
            label: '招聘岗位'
          },
          {
            icon: 'el-icon-connection',
            value: industryModule.projectCount,
            label: '合作项目'
          },
          {
            icon: 'el-icon-trophy',
            value: industryModule.employmentRate + '%',
            label: '就业率'
          },
          {
            icon: 'el-icon-coin',
            value: industryModule.avgSalary,
            label: '平均薪资'
          }
        ]

        // 处理资源中心统计数据
        this.resourceStats = [
          {
            icon: 'el-icon-document',
            value: resourceModule.totalResources,
            label: '总资源数'
          },
          {
            icon: 'el-icon-view',
            value: resourceModule.totalViews,
            label: '总浏览量'
          },
          {
            icon: 'el-icon-download',
            value: resourceModule.totalDownloads,
            label: '总下载量'
          },
          {
            icon: 'el-icon-star-on',
            value: resourceModule.totalFavorites,
            label: '总收藏量'
          },
          {
            icon: 'el-icon-medal',
            value: resourceModule.avgRating,
            label: '平均评分'
          },
          {
            icon: 'el-icon-refresh',
            value: resourceModule.updateFrequency + '个/月',
            label: '更新频率'
          }
        ]

        this.popularCourses = popularCourses
        this.popularResources = popularResources

      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载数据失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 初始化图表
    initCharts() {
      try {
        // 根据当前激活的tab初始化对应的图表
        if (this.activeTab === 'overview') {
          this.initUserActivityChart()
          this.initModuleUsageChart()
        } else if (this.activeTab === 'course') {
          this.initCourseTrendChart()
          this.initCourseCategoryChart()
        } else if (this.activeTab === 'industry') {
          this.initIndustryTrendChart()
        } else if (this.activeTab === 'resource') {
          this.initResourceCategoryChart()
          this.initResourceTrendChart()
        }
      } catch (error) {
        console.error('初始化图表失败:', error)
        this.$message.error('图表初始化失败，请刷新页面重试')
      }
    },

    // 销毁图表
    destroyCharts() {
      const charts = [
        'userActivityChart',
        'moduleUsageChart',
        'courseTrendChart',
        'courseCategoryChart',
        'industryTrendChart',
        'resourceCategoryChart',
        'resourceTrendChart'
      ]

      charts.forEach(chartName => {
        if (this[chartName]) {
          this[chartName].dispose()
          this[chartName] = null
        }
      })
    },

    // 初始化用户活跃度图表
    async initUserActivityChart() {
      const chartDom = document.getElementById('userActivityChart')
      if (!chartDom) return

      this.userActivityChart = echarts.init(chartDom)
      await this.updateUserActivityChart()
    },

    // 更新用户活跃度图表
    async updateUserActivityChart() {
      if (!this.userActivityChart) return

      try {
        const data = await StatisticModel.getUserActivityStats({ type: this.userActivityType })

        const option = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: { color: '#fff' }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.labels,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' }
          },
          yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' },
            splitLine: { lineStyle: { color: '#eee' } }
          },
          series: [{
            name: data.datasets[0].name,
            type: 'line',
            data: data.datasets[0].data,
            smooth: true,
            lineStyle: { color: '#4093f9', width: 3 },
            itemStyle: { color: '#4093f9' },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 147, 249, 0.3)' },
                  { offset: 1, color: 'rgba(64, 147, 249, 0.1)' }
                ]
              }
            }
          }]
        }

        this.userActivityChart.setOption(option)
      } catch (error) {
        console.error('更新用户活跃度图表失败:', error)
      }
    },

    // 初始化模块使用分布图表
    initModuleUsageChart() {
      const chartDom = document.getElementById('moduleUsageChart')
      if (!chartDom) return

      this.moduleUsageChart = echarts.init(chartDom)

      const option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          textStyle: { color: '#fff' }
        },
        series: [{
          name: '模块使用分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: [
            { value: 35, name: '实训课程', itemStyle: { color: '#4093f9' } },
            { value: 28, name: '资源中心', itemStyle: { color: '#67c23a' } },
            { value: 22, name: '产教融合', itemStyle: { color: '#e6a23c' } },
            { value: 15, name: '其他模块', itemStyle: { color: '#f56c6c' } }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          label: {
            show: true,
            formatter: '{b}: {c}%'
          }
        }]
      }

      this.moduleUsageChart.setOption(option)
    },

    // 初始化课程趋势图表
    async initCourseTrendChart() {
      const chartDom = document.getElementById('courseTrendChart')
      if (!chartDom) return

      this.courseTrendChart = echarts.init(chartDom)
      await this.updateCourseTrendChart()
    },

    // 更新课程趋势图表
    async updateCourseTrendChart() {
      if (!this.courseTrendChart) return

      try {
        const data = await StatisticModel.getCourseTrendStats({ type: this.courseTrendType })

        const option = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: { color: '#fff' }
          },
          legend: {
            data: data.datasets.map(d => d.name),
            textStyle: { color: '#666' }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.labels,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' }
          },
          yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' },
            splitLine: { lineStyle: { color: '#eee' } }
          },
          series: data.datasets.map((dataset, index) => ({
            name: dataset.name,
            type: 'line',
            data: dataset.data,
            smooth: true,
            lineStyle: {
              color: index === 0 ? '#4093f9' : '#67c23a',
              width: 3
            },
            itemStyle: {
              color: index === 0 ? '#4093f9' : '#67c23a'
            }
          }))
        }

        this.courseTrendChart.setOption(option)
      } catch (error) {
        console.error('更新课程趋势图表失败:', error)
      }
    },

    // 初始化课程分类图表
    async initCourseCategoryChart() {
      const chartDom = document.getElementById('courseCategoryChart')
      if (!chartDom) return

      this.courseCategoryChart = echarts.init(chartDom)

      try {
        const data = await StatisticModel.getCourseCategoryStats()

        const option = {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: { color: '#fff' }
          },
          series: [{
            name: '课程分类',
            type: 'pie',
            radius: '70%',
            center: ['50%', '50%'],
            data: data.map(item => ({
              value: item.value,
              name: item.name,
              itemStyle: { color: item.color }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              formatter: '{b}: {c}门'
            }
          }]
        }

        this.courseCategoryChart.setOption(option)
      } catch (error) {
        console.error('初始化课程分类图表失败:', error)
      }
    },

    // 初始化产教融合趋势图表
    async initIndustryTrendChart() {
      const chartDom = document.getElementById('industryTrendChart')
      if (!chartDom) return

      this.industryTrendChart = echarts.init(chartDom)
      await this.updateIndustryTrendChart()
    },

    // 更新产教融合趋势图表
    async updateIndustryTrendChart() {
      if (!this.industryTrendChart) return

      try {
        const data = await StatisticModel.getIndustryTrendStats({ type: this.industryTrendType })

        const option = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: { color: '#fff' }
          },
          legend: {
            data: data.datasets.map(d => d.name),
            textStyle: { color: '#666' }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.labels,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' }
          },
          yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' },
            splitLine: { lineStyle: { color: '#eee' } }
          },
          series: data.datasets.map((dataset, index) => ({
            name: dataset.name,
            type: 'bar',
            data: dataset.data,
            itemStyle: {
              color: index === 0 ? '#4093f9' : '#67c23a'
            }
          }))
        }

        this.industryTrendChart.setOption(option)
      } catch (error) {
        console.error('更新产教融合趋势图表失败:', error)
      }
    },

    // 初始化资源分类图表
    async initResourceCategoryChart() {
      const chartDom = document.getElementById('resourceCategoryChart')
      if (!chartDom) return

      this.resourceCategoryChart = echarts.init(chartDom)

      try {
        const data = await StatisticModel.getResourceCategoryStats()

        const option = {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: { color: '#fff' },
            formatter: '{b}<br/>资源数: {c}<br/>浏览量: {d0}<br/>下载量: {d1}'
          },
          series: [{
            name: '资源分类',
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['50%', '50%'],
            data: data.map(item => ({
              value: item.value,
              name: item.name,
              itemStyle: { color: item.color },
              views: item.views,
              downloads: item.downloads
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            label: {
              show: true,
              formatter: '{b}: {c}个'
            }
          }]
        }

        this.resourceCategoryChart.setOption(option)
      } catch (error) {
        console.error('初始化资源分类图表失败:', error)
      }
    },

    // 初始化资源趋势图表
    async initResourceTrendChart() {
      const chartDom = document.getElementById('resourceTrendChart')
      if (!chartDom) return

      this.resourceTrendChart = echarts.init(chartDom)
      await this.updateResourceTrendChart()
    },

    // 更新资源趋势图表
    async updateResourceTrendChart() {
      if (!this.resourceTrendChart) return

      try {
        const data = await StatisticModel.getResourceTrendStats({ type: this.resourceTrendType })

        const option = {
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            textStyle: { color: '#fff' }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.labels,
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' }
          },
          yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#ccc' } },
            axisLabel: { color: '#666' },
            splitLine: { lineStyle: { color: '#eee' } }
          },
          series: [{
            name: data.datasets[0].name,
            type: 'bar',
            data: data.datasets[0].data,
            itemStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: '#4093f9' },
                  { offset: 1, color: '#67c23a' }
                ]
              }
            }
          }]
        }

        this.resourceTrendChart.setOption(option)
      } catch (error) {
        console.error('更新资源趋势图表失败:', error)
      }
    },

    // Tab切换事件
    handleTabClick(tab) {
      this.activeTab = tab.name
      // 延迟初始化图表，确保DOM已渲染
      this.$nextTick(() => {
        try {
          if (tab.name === 'overview') {
            if (!this.userActivityChart) this.initUserActivityChart()
            if (!this.moduleUsageChart) this.initModuleUsageChart()
          } else if (tab.name === 'course') {
            if (!this.courseTrendChart) this.initCourseTrendChart()
            if (!this.courseCategoryChart) this.initCourseCategoryChart()
          } else if (tab.name === 'industry') {
            if (!this.industryTrendChart) this.initIndustryTrendChart()
          } else if (tab.name === 'resource') {
            if (!this.resourceCategoryChart) this.initResourceCategoryChart()
            if (!this.resourceTrendChart) this.initResourceTrendChart()
          }
        } catch (error) {
          console.error('Tab切换时初始化图表失败:', error)
        }
      })
    },

    // 全屏切换
    toggleFullscreen() {
      if (!this.isFullscreen) {
        // 进入全屏
        const element = document.documentElement
        if (element.requestFullscreen) {
          element.requestFullscreen()
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen()
        } else if (element.webkitRequestFullscreen) {
          element.webkitRequestFullscreen()
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen()
        }
      } else {
        // 退出全屏
        if (document.exitFullscreen) {
          document.exitFullscreen()
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen()
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen()
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen()
        }
      }
      this.isFullscreen = !this.isFullscreen
    },

    // 处理窗口大小变化
    handleResize() {
      // 防抖处理，避免频繁调用
      clearTimeout(this.resizeTimer)
      this.resizeTimer = setTimeout(() => {
        this.resizeCharts()
      }, 300)
    },

    // 调整所有图表大小
    resizeCharts() {
      const charts = [
        this.userActivityChart,
        this.moduleUsageChart,
        this.courseTrendChart,
        this.courseCategoryChart,
        this.industryTrendChart,
        this.resourceCategoryChart,
        this.resourceTrendChart
      ]

      charts.forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    },

    // 处理全屏状态变化
    handleFullscreenChange() {
      this.isFullscreen = !!(
        document.fullscreenElement ||
        document.webkitFullscreenElement ||
        document.mozFullScreenElement ||
        document.msFullscreenElement
      )

      // 全屏状态变化后，延迟调整图表大小
      setTimeout(() => {
        this.resizeCharts()
      }, 100)
    }
  }
}
</script>

<style lang="less" scoped>
.statistics-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;

  .fullscreen-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: white;
      transform: scale(1.1);
    }

    i {
      font-size: 18px;
      color: #4093f9;
    }
  }

  .page-header {
    text-align: center;
    margin-bottom: 30px;

    .page-title {
      font-size: 36px;
      font-weight: bold;
      color: white;
      margin: 0 0 10px 0;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .update-time {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;

      i {
        margin-right: 5px;
      }
    }
  }

  .tab-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }
}

// Tab样式
::v-deep .statistics-tabs {
  .el-tabs__header {
    background: #f8f9fa;
    margin: 0;

    .el-tabs__nav-wrap {
      padding: 0 20px;
    }

    .el-tabs__item {
      font-size: 16px;
      font-weight: 500;
      height: 60px;
      line-height: 60px;
      color: #666;

      &.is-active {
        color: #4093f9;
        font-weight: bold;
      }
    }

    .el-tabs__active-bar {
      background-color: #4093f9;
      height: 3px;
    }
  }

  .el-tabs__content {
    padding: 0;
  }
}

.tab-content {
  padding: 30px;
  min-height: 600px;
}

// 整体概览样式
.overview-section {
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 40px;

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        background: rgba(64, 147, 249, 0.1);

        i {
          font-size: 24px;
        }
      }

      .stat-content {
        flex: 1;

        .stat-number {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
        }

        .stat-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .stat-change {
          font-size: 12px;
          display: flex;
          align-items: center;

          &.up {
            color: #67c23a;
          }

          &.down {
            color: #f56c6c;
          }

          i {
            margin-right: 3px;
          }
        }
      }
    }
  }

  .chart-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
  }
}

// 图表卡片样式
.chart-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .chart {
    height: 300px;
  }
}

// 统计摘要样式
.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    }

    .summary-icon {
      width: 50px;
      height: 50px;
      border-radius: 10px;
      background: rgba(64, 147, 249, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 20px;
        color: #4093f9;
      }
    }

    .summary-content {
      .summary-number {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
      }

      .summary-label {
        font-size: 13px;
        color: #666;
      }
    }
  }
}

// 排行榜样式
.ranking-section {
  margin-top: 30px;

  .ranking-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .ranking-header {
      margin-bottom: 20px;

      h3 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }

    .ranking-list {
      .ranking-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .ranking-number {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
          font-size: 14px;
          margin-right: 15px;

          &.rank-1 {
            background: #ffd700;
            color: white;
          }

          &.rank-2 {
            background: #c0c0c0;
            color: white;
          }

          &.rank-3 {
            background: #cd7f32;
            color: white;
          }

          &:not(.rank-1):not(.rank-2):not(.rank-3) {
            background: #f0f0f0;
            color: #666;
          }
        }

        .ranking-content {
          flex: 1;

          .ranking-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
          }

          .ranking-stats {
            font-size: 12px;
            color: #999;

            span {
              margin-right: 15px;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .statistics-page {
    padding: 15px;

    .page-header .page-title {
      font-size: 28px;
    }

    .overview-section {
      .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }

      .chart-container {
        grid-template-columns: 1fr;
      }
    }
  }
}

@media (max-width: 768px) {
  .statistics-page {
    padding: 10px;

    .page-header .page-title {
      font-size: 24px;
    }

    .tab-content {
      padding: 20px;
    }

    .overview-section .stats-grid {
      grid-template-columns: 1fr;
    }

    .stats-summary {
      grid-template-columns: repeat(2, 1fr);
    }

    .chart-card .chart {
      height: 250px;
    }
  }
}

@media (max-width: 480px) {
  .statistics-page {
    .stats-summary {
      grid-template-columns: 1fr;
    }

    .summary-card {
      padding: 15px;

      .summary-icon {
        width: 40px;
        height: 40px;

        i {
          font-size: 16px;
        }
      }

      .summary-content .summary-number {
        font-size: 20px;
      }
    }
  }
}
</style>
