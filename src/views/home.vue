<template>
  <div class="page-home">
    <!-- 顶部介绍区域 -->
    <div class="header-section">
      <div class="banner-container">
        <div class="banner-content">
          <el-carousel height="500px" indicator-position="outside" arrow="always">
            <el-carousel-item v-for="(banner, index) in bannerList" :key="index">
              <img :src="banner.url" :alt="banner.alt || '虚拟仿真平台'" class="banner-image">
              <div class="overlay-gradient"></div>
            </el-carousel-item>
          </el-carousel>
        </div>
      </div>
    </div>

    <!-- 中心简介区域 -->
    <div class="intro-section">
      <div class="section-header">
        <div class="title-wrapper">
          <h2 class="section-title">
            <font-awesome-icon icon="university" class="title-icon" />
            中心简介
          </h2>
          <div class="subtitle">INTRODUCTION</div>
        </div>
        <div class="divider"></div>
      </div>
      <div class="intro-content">
        <template v-if="(systemConfig.platformFor==='school'&&collegeId==='0')||systemConfig.platformFor==='college'">
          <div class="description-content" v-html="webConfig.schoolDesText"></div>
        </template>
        <template v-if="(systemConfig.platformFor==='school'&&collegeId!=='0')">
          <div class="description-content"
               v-html="find_obj_from_arr_by_id('collegeId',collegeId,navCollegeList)[1]['desText']">
          </div>
        </template>
      </div>
    </div>

    <!-- 课程列表区域 -->
    <div class="course-section">
      <div class="section-header">
        <div class="title-wrapper">
          <h2 class="section-title">
            <font-awesome-icon icon="graduation-cap" class="title-icon" />
            <template v-if="(systemConfig.platformFor==='school'&&collegeId==='0')||systemConfig.platformFor==='college'">学校课程</template>
            <template v-if="systemConfig.platformFor==='school'&&collegeId!=='0'">学院课程</template>
          </h2>
          <div class="subtitle">
            <template v-if="(systemConfig.platformFor==='school'&&collegeId==='0')||systemConfig.platformFor==='college'">SCHOOL COURSE</template>
            <template v-if="systemConfig.platformFor==='school'&&collegeId!=='0'">COLLEGE COURSE</template>
          </div>
        </div>
        <div class="divider"></div>
      </div>

      <!-- 学科筛选 -->
      <div class="subject-filter" v-if="(systemConfig.platformFor==='school'&&collegeId==='0')||systemConfig.platformFor==='college'">
        <div class="filter-tags">
          <span
              v-for="item in courseSubject.list"
              :key="item.value"
              :class="['tag', { 'active': courseSubject.activeId === item.value }]"
              @click="clickOneCourseSubject(item.value)"
          >
            {{ item.label }}
          </span>
        </div>
      </div>

      <!-- 课程列表 -->
      <div class="course-grid" v-loading="loading">
        <div v-if="course.list.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无课程数据" />
        </div>
        <transition-group name="fade" tag="div" class="grid-container">
          <div
              v-for="item in course.list"
              :key="item.courseId"
              class="course-card"
              @click="clickOneCourse(item)"
          >
            <div class="card-image">
              <img :src="item.avatarUrl" alt="课程封面" class="course-cover">
              <div class="image-overlay"></div>
            </div>
            <div class="card-content">
              <div class="card-header">
                <el-tooltip effect="dark" :content="item.name" placement="top">
                  <h3 class="course-title">{{ item.name }}</h3>
                </el-tooltip>
              </div>
              <div class="stats-container">
                <div class="stat-item">
                  <font-awesome-icon icon="users" class="stat-icon" />
                  <span>{{ item.userNumber }}人学习</span>
                </div>
                <div class="stat-item">
                  <font-awesome-icon icon="comments" class="stat-icon" />
                  <span>{{ item.commentNumber }}条评论</span>
                </div>
              </div>
              <div class="card-footer">
                <div class="course-type">{{ item.courseType }}</div>
                <div class="course-college">{{ item.collegeName }}</div>
              </div>
            </div>
          </div>
        </transition-group>
      </div>
    </div>

    <!-- 友情链接区域 -->
    <a href="#" name="link"></a>
    <div class="links-section">
      <div class="section-header">
        <div class="title-wrapper">
          <h2 class="section-title">
            <font-awesome-icon icon="link" class="title-icon" />
            友情链接
          </h2>
          <div class="subtitle">FRIENDSHIP LINK</div>
        </div>
        <div class="divider"></div>
      </div>

      <div class="links-grid">
        <div
            v-for="item in linkConfig.list"
            v-if="item.img"
            :key="item.url"
            class="link-item"
            @click="clickLink(item)"
        >
          <div class="link-logo">
            <img :src="item.img" :alt="item.name">
          </div>
          <div class="link-name">{{ item.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { CommonModel } from "../model/CommonModel";
import { CourseSubjectModel } from "../model/CourseSubjectModel";
import { CourseModel } from "../model/CourseModel";
import { find_obj_from_arr_by_id } from "../utils/common";
import { ConfigModel } from "../model/ConfigModel";

export default {
  name: "Home",
  computed: {
    ...mapState({
      webConfig: state => state.webConfig,
      systemConfig: state => state.systemConfig,
      collegeId: state => state.collegeId,
      navCollegeList: state => state.navCollegeList,
    })
  },
  watch: {
    collegeId() {
      if (this.collegeId !== "0") {
        this.clickOneCourseSubject("", this.collegeId)
      } else {
        this.clickOneCourseSubject("")
      }
    }
  },
  data() {
    return {
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      bannerList: [
        {
          url:  "http://*************:7035/files/20220720/216212798869344256.jpeg",
          alt: '虚拟仿真平台'
        },

      ],
      courseSubject: {
        courseSubjectId: "",
        list: [],
        object: [],
        activeId: "",
      },
      course: {
        list: [],
        query: {
          showIndex: true,
          opened: true,
        }
      },
      linkConfig: {
        list:[]
      },
      loading: false
    }
  },
  async mounted() {
    await this.getCourseSubjectList()
    this.clickOneCourseSubject("")
    this.linkConfig = JSON.parse(await ConfigModel.getConfig("linkConfig"));
  },
  methods: {
    async getCourseSubjectList() {
      let list = await CourseSubjectModel.getList({})
      let listResult = CommonModel.generateListFilterOptions("name", "courseSubjectId", list, true)
      this.$set(this.courseSubject, "list", listResult[0])
      this.$set(this.courseSubject, "object", listResult[1])
    },
    async clickOneCourseSubject(courseSubjectId, collegeId) {
      try {
        this.loading = true
        this.$set(this.courseSubject, "activeId", courseSubjectId)
        courseSubjectId = courseSubjectId === "" ? undefined : courseSubjectId
        this.$set(this.course.query, "courseSubjectId", courseSubjectId)
        if (collegeId) {
          this.$set(this.course.query, "collegeId", collegeId)
        } else {
          this.$set(this.course.query, "collegeId", undefined)
        }
        let courseList = await CourseModel.getList(this.course.query)
        this.$set(this.course, "list", courseList)
      } finally {
        this.loading = false
      }
    },
    clickOneCourse(item) {
      this.$router.push({
        name: "CourseInfo",
        query: {
          "id": item.courseId
        }
      })
    },
    clickLink(link){
      window.open(link.url)
    }
  }
}
</script>

<style lang="less" scoped>
@import '../style/app.less';

.page-home {
  background-color: #f8fafd;
  color: #333;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

// 顶部区域样式
.header-section {
  position: relative;
  margin-bottom: 0;

  .banner-container {
    position: relative;
    overflow: hidden;
    border-radius: 0 0 20px 20px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);

    .banner-content {
      position: relative;
      height: 500px;
      overflow: hidden;

      .banner-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.8s cubic-bezier(0.22, 0.61, 0.36, 1);
      }

      .overlay-gradient {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 30%;
        background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
        z-index: 1;
      }

      .el-carousel__container {
        height: 500px;
      }

      .el-carousel__item {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
}

// 标题图标样式
.title-icon {
  margin-right: 10px;
  color: #4093f9;
  font-size: 28px;
  vertical-align: middle;
}

// 中心简介区域样式
.intro-section {
  max-width: 1280px;
  margin: 50px auto 50px auto;

  .section-header {
    text-align: center;
    margin-bottom: 40px;

    .title-wrapper {
      margin-bottom: 15px;

      .section-title {
        font-size: 32px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        letter-spacing: 1px;
      }

      .subtitle {
        font-size: 14px;
        color: #4093f9;
        font-weight: 500;
        letter-spacing: 3px;
        text-transform: uppercase;
      }
    }

    .divider {
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #4093f9, #6ab1ea);
      margin: 0 auto;
      border-radius: 2px;
      animation: scaleIn 0.8s ease-out;
    }
  }

  .intro-content {
    .description-content {
      max-width: 1280px;
      margin: 0 auto;
      color: #555;
      font-size: 16px;
      line-height: 1.8;
      text-align: justify;
      padding: 0px;
      border-radius: 16px;
    }
  }
}

// 课程区域样式
.course-section {
  max-width: 1280px;
  margin: 0 auto 20px;
  padding: 0 20px;

  .section-header {
    text-align: center;
    margin-bottom: 40px;

    .title-wrapper {
      margin-bottom: 15px;

      .section-title {
        font-size: 32px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        letter-spacing: 1px;
      }

      .subtitle {
        font-size: 14px;
        color: #4093f9;
        font-weight: 500;
        letter-spacing: 3px;
        text-transform: uppercase;
      }
    }

    .divider {
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #4093f9, #6ab1ea);
      margin: 0 auto;
      border-radius: 2px;
      animation: scaleIn 0.8s ease-out;
    }
  }

  .subject-filter {
    margin-bottom: 30px;

    .filter-tags {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 15px;

      .tag {
        padding: 8px 24px;
        border-radius: 30px;
        background: #f0f5ff;
        color: #5a7dbf;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        &:hover {
          background: #e1ebff;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        &.active {
          background: #4093f9;
          color: white;
          font-weight: 500;
          box-shadow: 0 4px 12px rgba(67, 126, 235, 0.3);
        }
      }
    }
  }

  .course-grid {
    position: relative;
    min-height: 400px;

    .empty-state {
      padding: 60px 0;
    }

    .grid-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
      gap: 30px;
      padding: 10px 0;
    }

    .course-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      cursor: pointer;
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);

        .course-cover {
          transform: scale(1.05);
        }
      }

      .card-image {
        position: relative;
        height: 180px;
        overflow: hidden;

        .course-cover {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        .image-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 60%;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        }
      }

      .card-content {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;

        .card-header {


          .course-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.4;
            margin: 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 45px;
          }
        }

        .stats-container {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid #f0f4f9;

          .stat-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #666;

            .stat-icon {
              color: #4093f9;
              margin-right: 6px;
              font-size: 13px;
            }

            i {
              color: #4093f9;
              margin-right: 5px;
              font-size: 14px;
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;

          .course-type {
            background: rgba(67, 126, 235, 0.1);
            color: #4093f9;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
          }

          .course-college {
            font-size: 13px;
            color: #777;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

// 友情链接区域样式
.links-section {
  max-width: 1280px;
  margin: 0 auto 120px;
  padding: 0 20px 30px;

  .section-header {
    text-align: center;
    margin-bottom: 50px;

    .title-wrapper {
      margin-bottom: 15px;

      .section-title {
        font-size: 32px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
      }

      .subtitle {
        font-size: 14px;
        color: #4093f9;
        font-weight: 500;
        letter-spacing: 3px;
        text-transform: uppercase;
      }
    }

    .divider {
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #4093f9, #6ab1ea);
      margin: 0 auto;
      border-radius: 2px;
    }
  }

  .links-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 30px;

    .link-item {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      cursor: pointer;
      text-align: center;
      padding: 30px 20px;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);

        .link-logo img {
          transform: scale(1.05);
        }
      }

      .link-logo {
        height: 70px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;

        img {
          max-height: 50px;
          max-width: 140px;
          transition: transform 0.3s ease;
        }
      }

      .link-name {
        font-size: 15px;
        color: #555;
        font-weight: 500;
        transition: color 0.3s ease;
      }

      &:hover .link-name {
        color: #4093f9;
      }
    }
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    transform: scaleX(0);
    opacity: 0;
  }
  to {
    transform: scaleX(1);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .header-section .banner-container .banner-content {
    height: 400px;
  }

  .header-section .banner-container .school-info {
    padding: 30px 20px;
  }

  .course-section .grid-container {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .links-section .links-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}
</style>