<template>
  <div class="app-container">
    <div class="edit-container">
      <el-form label-width="140px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
        <el-form-item label="appId:" v-if="entityInfo.type==='edit'">
          {{ entityInfo.edit.appId }}
        </el-form-item>
        <el-form-item label="appSecret:" v-if="entityInfo.type==='edit'">
          <div class="flex flex-start">
            <span style="margin-right: 10px">{{ entityInfo.edit.appSecret }}</span>
            <el-button type="default" size="mini" @click="EntityInfoMethods().clickGetAppSecretBtn()">获取</el-button>
          </div>
        </el-form-item>
        <el-form-item label="课程名称:" prop="name">
          <el-input v-model.trim="entityInfo.edit.name">
            <span slot="suffix" v-if="entityInfo.edit.name">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.name.length }} / 40
                </span>
              </span>
            </span>
          </el-input>
        </el-form-item>
        <el-form-item label="是否开放:" prop="opened">
          <el-select v-model="entityInfo.edit.opened"
                     style="width: 100%">
            <el-option :value="true" label="开放" key="开放"></el-option>
            <el-option :value="false" label="关闭" key="关闭"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="在首页显示:" prop="opened">
          <el-select v-model="entityInfo.edit.showIndex"
                     style="width: 100%">
            <el-option :value="true" label="是" key="是"></el-option>
            <el-option :value="false" label="否" key="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程头图:" prop="avatarUrl">
          <erp-uploader-one-pic style="float:left;" :img-in="entityInfo.edit.avatarUrl"
                                uploader-id="avatarUrl"
                                uploader-title="" :uploader-size="[230,140]" :pixel-limit="[230,140]"
                                :size-limit="1024"
                                @uploadSuccess="data=>fileUpload(data,entityInfo.edit,'')"
                                @afterDelete="data=>fileDelete(data,entityInfo.edit,'')"></erp-uploader-one-pic>
        </el-form-item>
        <el-form-item label="课程类型:" prop="courseType">
          <el-select v-model="entityInfo.edit.courseType"
                     style="width: 100%">
            <el-option v-for="item in entityInfo.filter.courseType" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="网页链接地址:" prop="webUrl" v-if="entityInfo.edit.courseType==='网页版'">
          <el-input v-model.trim="entityInfo.edit.webUrl"></el-input>
        </el-form-item>
        <el-form-item label="客户端获取方式:" prop="clientText" v-if="entityInfo.edit.courseType==='客户端版'">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 5}"
                    v-model="entityInfo.edit.clientText"></el-input>
        </el-form-item>
        <el-form-item label="所属学科:" prop="courseSubjectId">
          <el-select v-model="entityInfo.edit.courseSubjectId"
                     style="width: 100%">
            <el-option v-for="item in entityInfo.filter.courseSubject" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属学院:" prop="collegeId">
          <el-select v-model="entityInfo.edit.collegeId" @change="EntityInfoMethods().initFilter(3)"
                     style="width: 100%">
            <el-option v-for="item in entityInfo.filter.college" :value="item.value" :label="item.label"
                       :key="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="限制用户登录次数:" prop="limitLogin">
          <el-select v-model="entityInfo.edit.limitLogin"
                     style="width: 100%">
            <el-option :value="true" label="是" key="是"></el-option>
            <el-option :value="false" label="否" key="否"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="限制次数:" prop="limitNumber" v-if="entityInfo.edit.limitLogin===true">
          <el-input type="number" v-model="entityInfo.edit.limitNumber"></el-input>
        </el-form-item>
        <el-form-item label="实验简介" prop="shortDesText">
          <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 5}"
                    v-model="entityInfo.edit.shortDesText"></el-input>
        </el-form-item>
        <el-form-item label="实验介绍:" prop="desText">
          <tinymce
            ref="tinymce_desText"
            v-model="entityInfo.edit.desText"
            :height="300"
          />
        </el-form-item>
        <el-form-item label="实验帮助:" prop="helpText">
          <tinymce
            ref="tinymce_helpText"
            v-model="entityInfo.edit.helpText"
            :height="300"
          />
        </el-form-item>
        <el-form-item label="联系方式:" prop="contactText">
          <tinymce
            ref="tinymce_helpContactText"
            v-model="entityInfo.edit.contactText"
            :height="200"
          />
        </el-form-item>
        <el-form-item label="软硬件要求:" prop="softHardwareText">
          <tinymce
            ref="tinymce_helpContactText"
            v-model="entityInfo.edit.softHardwareText"
            :height="200"
          />
        </el-form-item>
        <el-form-item class="flex flex-around">
          <el-button type="default"
                     @click="EntityInfoMethods().clickCancelBtn()">取 消
          </el-button>
          <el-button type="success" v-if="entityInfo.type==='add'"
                     @click="EntityInfoMethods().clickAddBtn()">新 增
          </el-button>
          <el-button type="success" v-if="entityInfo.type==='edit'"
                     @click="EntityInfoMethods().clickEditBtn()">确认修改
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {isObjArrHasSameIdValue, isObjArrHasSameIdValueAndOtherId, objectToLVArr} from "@/utils/common";
import elDragDialog from "@/directive/el-drag-dialog";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {CourseModel} from "@/model/CourseModel";
import enums from "@/enums";
import {CourseSubjectModel} from "@/model/CourseSubjectModel";
import {CommonModel} from "@/model/CommonModel";
import {CollegeModel} from "@/model/CollegeModel";
import Tinymce from "@/components/Tinymce"
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {UserModel} from "@/model/UserModel";
import {mapState} from "vuex";

export default {
  name: "courseDetail",
  directives: {
    elDragDialog
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      userInfo: state => state.user,
      permissionArr: state => state.user.permission
    })
  },
  components: {Tinymce, erpUploaderOnePic},
  data() {
    // 校检课程类型
    const validateCourseType = (rule, value, callback) => {
      if (value === 'web') {
        if (!this.entityInfo.edit.webUrl) {
          callback(new Error('请输入网页链接地址'));
        }
      }
      if (value === 'client') {
        if (!this.entityInfo.edit.clientUrl) {
          callback(new Error('请输入客户端下载方式'));
        }
      }
      callback()
    }
    // 校检限制次数
    const validateLimitNumber = (rule, value, callback) => {
      // 检测参数
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('只能输入正整数'));
        return
      }
      if (value > 20) {
        callback(new Error('最多20次'));
      }
      if (value < 0) {
        callback(new Error('不能小于0'));
      }
      callback()
    }

    return {
      window: window,
      enums: enums,
      // 详情
      entityInfo: {
        filter: {
          courseType: objectToLVArr(enums.courseType),
          college: [],
          courseSubject: []
        },
        $index: 0,
        title: "新增课程",
        type: "add",
        dialog: false,
        edit: {
          opened: true,
          courseType: "网页版"
        },
        uploading: false,
        uploadPreviewShow: false,
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "课程名称"), trigger: 'blur'},
          'opened': {required: true, message: '请选择是否开放', trigger: 'change'},
          'avatarUrl': {required: true, message: '请上传课程头图', trigger: 'change'},
          'courseType': {required: true, trigger: 'change', validator: validateCourseType},
          'courseSubjectId': {required: true, message: '请选择所属学科', trigger: 'change'},
          'collegeId': {required: true, message: '请选择所属学院', trigger: 'change'},
          'shortDesText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 200, "课程简介"),
            trigger: 'blur'
          },
          'desText': {required: true, message: '请输入实验介绍信息', trigger: 'change'},
          'helpText': {required: true, message: '请输入实验帮助信息', trigger: 'change'},
          'helpContactText': {required: true, message: '请输入联系方式信息', trigger: 'change'},
          'softHardwareText': {required: true, message: '请输入软硬件要求信息', trigger: 'change'},
          'limitLogin': {required: true, message: '请选择是否限制', trigger: 'change'},
          'limitNumber': {required: true, validator: validateLimitNumber, trigger: 'change'},
        },
      },
    }
  },
  async mounted() {
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let id = this.$route.query["id"]
      let info = await CourseModel.getOne(id)
      if (this.adminRoles[0] === "teacherSecretary") { // 如果是教秘
        if (info.collegeId !== this.userInfo.infos.collegeId) {// 安全-如果不是自己学院的课程，就跳出
          window.location.href = "/"
        }
      }
      if (info) {
        this.entityInfo.edit = info
      } else {
        msg_err("未找到该课程信息！")
      }
    } else {
      //VEAModel.setBreadThirdTitle("新增课程")
    }
    this.EntityInfoMethods().initFilter()
  },
  methods: {
    // 实体信息Methods
    EntityInfoMethods() {
      let $this = this
      return {
        // 点击获取appSecret按钮
        async clickGetAppSecretBtn() {
          let appSecret = await CourseModel.getOneCourseAppSecret($this.entityInfo.edit.courseId).catch(res => {
            msg_err("获取失败！")
          })
          if (!appSecret) {
            msg_err("获取失败！")
            return
          }
          $this.$set($this.entityInfo.edit, "appSecret", appSecret)
        },
        // 初始化筛选列表
        async initFilter(type) {
          if (!type) {
            // 获取学科列表
            let list = await CourseSubjectModel.getList({})
            let filterList = CommonModel.generateListFilterOptions("name", "courseSubjectId", list, false)
            $this.$set($this.entityInfo.filter, "courseSubject", filterList[0])
            // 获取学院列表
            let collegeQuery = {}
            // 如果是教秘角色 就只能看到自己学院的安排
            if ($this.adminRoles[0] === "teacherSecretary") {
              collegeQuery = {
                collegeId: $this.userInfo.infos.collegeId
              }
            }
            list = await CollegeModel.getList(collegeQuery)
            filterList = CommonModel.generateListFilterOptions("name", "collegeId", list, false)
            $this.$set($this.entityInfo.filter, "college", filterList[0])
          }
          if (type === 3) {
            // 获取学院下属的教秘列表
            let list = await UserModel.getList({
              collegeId: $this.entityInfo.edit.collegeId,
              role: "teacher",
              asSecretary: true,
            })
            let filterList = CommonModel.generateListFilterOptions("name", "userId", list, false)
            $this.$set($this.entityInfo.filter, "teacher", filterList[0])
          }
        },
        // 点击新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm('确认要新增该课程吗？')) {
                if (await CourseModel.addOrEdit($this.entityInfo.edit)) {
                  msg_success('新增成功')
                  $this.$router.go(-1);
                  $this.entityInfo.dialog = false
                }
              }
            }
          })
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              if (await msg_confirm('确认要修改该课程信息吗？')) {
                // 删除无需修改字段
                if (await CourseModel.addOrEdit($this.entityInfo.edit)) {
                  msg_success('修改成功')
                  $this.entityInfo.dialog = false
                  $this.$router.go(-1);
                }
              }
            }
          })
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.$router.go(-1);
        }
      }
    },
    // ERP通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // ERP通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
  }
}
</script>

<style scoped lang="scss">
</style>
