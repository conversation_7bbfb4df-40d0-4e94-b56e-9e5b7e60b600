<template>
  <div class="app-container">
    <!--顶部按钮-->
    <div class="top-tools">
      <div style="text-align: right">
        <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()" style="background-color: #67C23A;border-color:#67C23A">新增学科</el-button>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="学科名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程数量" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.courseNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <!--          <el-button type="danger" size="mini" round>删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="学科名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {CourseSubjectModel} from "@/model/CourseSubjectModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {ClazzModel} from "@/model/ClazzModel";

export default {
  name: "subjectManage",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {

      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增标签",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateName(r, v, c, 20, "学科名称"), trigger: 'blur'}
        },
      },
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList({})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(query) {
          $this.lists.loading = true;
          [$this.lists.list] = await CourseSubjectModel.getPageList(0,1000,"",query)
          $this.lists.loading = false
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {

        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增学科"
          $this.entityInfo.edit = {
            type: "document"// 定义类型是模型
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          });
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑学科"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名学科
              let listResult = await CourseSubjectModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的学科，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该学科吗？')) {
                let result = await CourseSubjectModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名标签
              let listResult = await CourseSubjectModel.getList({
                name: $this.entityInfo.edit.name,
                GradeId: {"$ne": $this.entityInfo.edit.GradeId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的学科，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该学科吗？')) {
                let result = await CourseSubjectModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
