<template>
  <div class="app-container">
    <div class="edit-container">
      <el-tabs type="card">
        <el-tab-pane label="第一步：任务信息" v-if="['administrator','teacherSecretary'].indexOf(adminRoles[0])!==-1">
          <el-form label-width="180px" ref="entityInfoForm"
                   :model="entityInfo.edit" :rules="entityInfo.formRules">
            <el-form-item label="任务名称:" prop="name">
              <el-input v-model.trim="entityInfo.edit.name">
            <span slot="suffix" v-if="entityInfo.edit.name">
              <span class="el-input__count">
                <span class="el-input__count-inner">
                  {{ entityInfo.edit.name.length }} / 40
                </span>
              </span>
            </span>
              </el-input>
            </el-form-item>
            <el-form-item label="是否开放:" prop="opened">
              <el-select v-model="entityInfo.edit.opened"
                         style="width: 100%">
                <el-option :value="true" label="开放" key="开放"></el-option>
                <el-option :value="false" label="关闭" key="关闭"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="选择课程:" prop="courseId">
              <el-select v-model="entityInfo.edit.courseId" :disabled="entityInfo.type==='edit'"
                         style="width: 100%">
                <el-option v-for="item in entityInfo.filter.course" :value="item.value" :label="item.label"
                           :key="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="限制用户登录次数:" prop="limitLogin">
              <el-select v-model="entityInfo.edit.limitLogin"
                         style="width: 100%">
                <el-option :value="true" label="是" key="是"></el-option>
                <el-option :value="false" label="否" key="否"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="限制次数:" prop="limitNumber" v-if="entityInfo.edit.limitLogin===true">
              <el-input type="number" v-model="entityInfo.edit.limitNumber"></el-input>
            </el-form-item>
            <el-form-item label="任务时间范围：" prop="dateRange">
              <el-date-picker
                v-model="entityInfo.edit.dateRange"
                :disabled="entityInfo.type!=='add'&&entityInfo.edit.experimentstatus===2"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :default-time="['00:00:00','23:59:59']"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="entityInfo.datePickerOptions">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="所属学院:" prop="collegeId">
              <el-select v-model="entityInfo.edit.collegeId" :disabled="entityInfo.type==='edit'"
                         style="width: 100%">
                <el-option v-for="item in entityInfo.filter.college" :value="item.value" :label="item.label"
                           :key="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否需要填写实验报告:" prop="needFillReport">
              <el-select v-model="entityInfo.edit.needFillReport"
                         style="width: 100%">
                <el-option :value="true" label="是"
                           key="是">
                </el-option>
                <el-option :value="false" label="否"
                           key="否">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="总分权重-课程实验分" prop="courseScorePoint" v-if="entityInfo.edit.needFillReport">
              <el-input placeholder="请输入小数，二者相加等于1" v-model="entityInfo.edit.courseScorePoint" type="number">
              </el-input>
            </el-form-item>
            <el-form-item label="总分权重-实验报告分" prop="reportScorePoint" v-if="entityInfo.edit.needFillReport">
              <el-input placeholder="请输入小数，二者相加等于1" v-model="entityInfo.edit.reportScorePoint" type="number">
              </el-input>
            </el-form-item>
            <el-form-item label="任务备注" prop="remarkText">
              <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 5}"
                        v-model="entityInfo.edit.remarkText"></el-input>
            </el-form-item>
            <el-form-item label="任务介绍和要求:" prop="desText">
              <tinymce
                ref="tinymce_desText"
                v-model="entityInfo.edit.desText"
                :height="300"
              />
            </el-form-item>
            <el-form-item class="flex flex-around">
              <el-button type="default"
                         @click="EntityInfoMethods().clickCancelBtn()">取 消
              </el-button>
              <el-button type="success" v-if="entityInfo.type==='add'"
                         style="background-color: #67C23A;border-color:#67C23A"
                         @click="EntityInfoMethods().clickAddBtn()">确认新增
              </el-button>
              <el-button type="success" v-if="entityInfo.type==='edit'"
                         style="background-color: #67C23A;border-color:#67C23A"
                         @click="EntityInfoMethods().clickEditBtn()">确认修改
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="第二步：教学班信息" v-if="entityInfo.type==='edit'">
          <div class="flex flex-between info-box">
            <div class="left">
              总计：{{ entityInfo.edit.teachingClazzInfo.calInfo.teacherNumber }}名教师
              {{ entityInfo.edit.teachingClazzInfo.calInfo.studentNumber }}名学生
            </div>
            <div class="right">
              <el-button type="primary" @click="selectTeacher.dialog=true"
                         v-permission="['administrator','teacherSecretary']">新增教师
              </el-button>
              <el-button type="primary" @click="importTeachingClazz.dialog=true"
                         v-permission="['administrator','teacherSecretary']">批量导入教学班
              </el-button>
            </div>
          </div>
          <el-tabs tab-position="left" @tab-click="v=>EntityInfoMethods().clickTeacherTab(v)"
                   v-model="entityInfo.teacherTabId" closable
                   @tab-remove="v=>EntityInfoMethods().clickRemoveTeacherBtn(v)">
            <!--教师角色时只显示自己的-->
            <el-tab-pane :label="item.teacherName+'('+item.teacherAccount+')'" :name="item.teacherId"
                         v-for="(item,index) in entityInfo.edit.teachingClazzInfo.teacherList"
                         v-if="adminRoles[0]==='teacher'?item.teacherId===userInfo.id:true">
              <div class="flex flex-between clazz-box">
                <div class="left">

                </div>
                <div class="right">
                  <el-button type="primary" size="small" @click="EntityInfoMethods().clickAddClazzBtn(index)">新增教学班
                  </el-button>
                  <el-button type="primary" size="small"
                             @click="EntityInfoMethods().clickSelectTeachingClazzListBtn(index)">
                    选择该教师已有的教学班
                  </el-button>
                </div>
              </div>
              <el-table :data="entityInfo.edit.teachingClazzInfo.teacherList[index]['teachingClazzList']['list']"
                        element-loading-text="加载中" border fit
                        style="width: 100%;"
                        :loading="entityInfo.edit.teachingClazzInfo.teacherList[index]['teachingClazzList']['loading']">
                <el-table-column label="教学班名称" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.name }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="关联行政班" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.clazzNames.join('、') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="学生人数" align="center">
                  <template slot-scope="scope">
                    <span>{{ scope.row.studentNumber }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="操作" width="250"
                                 class-name="small-padding fixed-width">
                  <template slot-scope="scope">
                    <el-button type="primary" size="mini" round
                               @click="TeachingClazzListMethods().clickEditOneBtn(scope.row,scope.$index)">编辑
                    </el-button>
                    <el-button type="primary" size="mini" round
                               @click="StudentListMethods().clickGetUserListBtn(scope.row,scope.$index)">学生列表
                    </el-button>
                    <el-button type="danger" size="mini" round
                               @click="TeachingClazzListMethods().clickDeleteBtn(scope.$index,scope.row)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-tab-pane>
          </el-tabs>
          <div class="buttons page-bottom-container">
            <el-button type="primary" @click="EntityInfoMethods().clickSaveTeachingClazzInfoBtn()"
                       style="background-color: #67C23A;border-color:#67C23A" size="big"
                       v-loading="entityInfo.updateTeachingClazzInfoLoading">保存和更新教学班信息
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>
      <!--学生列表弹窗-->
      <el-dialog
        :title="studentList.title"
        :visible.sync="studentList.dialog"
        :close-on-click-modal="false"
        :append-to-body="true"
        width="1000px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <list-search-filter :search-filter="studentList.lists.searchFilter"
                              @clickSearchFilterBtn="query=>StudentListMethods().clickSearchFilterBtn(query)"
                              @clickCleanBtn="StudentListMethods().clickCleanBtn()">
          </list-search-filter>
          <div class="flex flex-between" style="margin-bottom: 10px">
            <span></span>
            <div>
              <el-button size="small" type="primary" @click="selectStudent.dialog=true">新增学生</el-button>
              <el-button size="small" type="primary" @click="importStudent.dialog=true">批量导入学生</el-button>
            </div>
          </div>
          <!--列表-->
          <el-table :data="studentList.lists.list" v-loading="studentList.lists.loading" element-loading-text="加载中"
                    border fit max-height="500px"
                    style="width: 100%;">
            <el-table-column label="学号" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.account }}</span>
              </template>
            </el-table-column>
            <el-table-column label="姓名" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="行政班级" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.gradeEntity[0]["name"] }}-{{
                    scope.row.majorEntity[0]["name"]
                  }}-{{ scope.row.clazzEntity[0]["name"] }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-button type="text" size="small"
                           @click="StudentListMethods().clickDeleteBtn(scope.row,scope.$index)">删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--列表分页-->
          <div class="pagination-container">
            <el-pagination background @current-change="(number)=>StudentListMethods().pageChange(number)"
                           :current-page.sync="studentList.lists.pages.number"
                           :page-size.sync="studentList.lists.pages.size"
                           layout="total,prev, pager, next,jumper,sizes" :total="studentList.lists.pages.totalElements"
                           @size-change="(size)=>StudentListMethods().pageLimitChange(size)"
                           :page-count="studentList.lists.pages.totalPages">
            </el-pagination>
          </div>
        </div>
      </el-dialog>
      <!--教学班详情弹窗-->
      <el-dialog
        :title="teachingClazz.title"
        :visible.sync="teachingClazz.dialog"
        :close-on-click-modal="false"
        :append-to-body="true"
        width="1000px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <el-form label-width="120px" ref="teachingClazzForm" :model="teachingClazz.edit"
                   :rules="teachingClazz.formRules">
            <el-form-item label="教学班名称:" prop="account">
              <el-input v-model="teachingClazz.edit.name"></el-input>
            </el-form-item>
            <el-form-item label="选择行政班:" prop="account">
              <el-select
                style="width: 100%"
                v-model="teachingClazz.edit.clazzIds"
                filterable
                remote
                multiple
                reserve-keyword
                placeholder="请输入行政班名称搜索"
                :remote-method="v=>TeachingClazzListMethods().getClazzList(v)"
                :loading="teachingClazz.filter.clazzListLoading">
                <el-option
                  v-for="item in teachingClazz.filter.clazz"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="default"
                     @click="teachingClazz.dialog=false">取 消</el-button>
        <el-button type="success" @click="TeachingClazzListMethods().clickAddBtn()" v-if="teachingClazz.type==='add'">提 交</el-button>
        <el-button type="success" @click="TeachingClazzListMethods().clickEditBtn()"
                   v-if="teachingClazz.type==='edit'">提 交</el-button>
      </span>
      </el-dialog>
      <!--选择教师弹窗-->
      <el-dialog
        :title="selectTeacher.title"
        :visible.sync="selectTeacher.dialog"
        :close-on-click-modal="false"
        :append-to-body="true"
        width="500px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <el-form label-width="120px" ref="selectTeacherForm" :model="selectTeacher.edit"
                   :rules="selectTeacher.formRules">
            <el-form-item label="选择教师:" prop="account">
              <el-select
                style="width: 100%"
                v-model="selectTeacher.edit.teacherId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入教师姓名或工号搜索"
                :remote-method="v=>EntityInfoMethods().getTeacherSearchList(v)"
                :loading="selectTeacher.filter.teacherLoading">
                <el-option
                  v-for="item in selectTeacher.filter.teacher"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="default"
                     @click="selectTeacher.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityInfoMethods().clickSelectTeacherBtn()">确 认</el-button>
      </span>
      </el-dialog>
      <!--教学班选择列表-->
      <el-dialog
        :title="teachingClazzList.title"
        :visible.sync="teachingClazzList.dialog"
        :close-on-click-modal="false"
        :append-to-body="true"
        width="800px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <teaching-clazz-list :teacher-id-prop="entityInfo.teacherTabId" :as-selected="true"
                               @onCancel="teachingClazzList.dialog=false"
                               @onSelected="v=>TeachingClazzListMethods().onSelectTeachingClazz(v)"></teaching-clazz-list>
        </div>
      </el-dialog>
      <!--选择学生弹窗-->
      <el-dialog
        :title="selectStudent.title"
        :visible.sync="selectStudent.dialog"
        :close-on-click-modal="false"
        :append-to-body="true"
        width="900px"
        center
        v-el-drag-dialog>
        <div class="dialog-container">
          <el-form label-width="120px" ref="selectStudentForm" :model="selectStudent.edit"
                   :rules="selectStudent.formRules">
            <el-form-item label="选择学生:" prop="account">
              <el-select
                style="width: 100%"
                v-model="selectStudent.edit.studentId"
                filterable
                remote
                reserve-keyword
                placeholder="请输入学生姓名或学号搜索"
                :remote-method="v=>StudentListMethods().getStudentSearchList(v)"
                :loading="selectStudent.filter.studentLoading">
                <el-option
                  v-for="item in selectStudent.filter.student"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button type="default"
                     @click="selectTeacher.dialog=false">取 消</el-button>
        <el-button type="success" @click="StudentListMethods().clickSureSelectStudentBtn()">确 认</el-button>
      </span>
      </el-dialog>
      <!--教学班级内学生导入-->
      <div>
        <!--学生导入input-->
        <input
          id="importStudentFile"
          type="file"
          style="display: none"
          @change="(files)=>{StudentListMethods().importStudentFileChange(files)}"
        >
        <!--学生导入弹窗-->
        <el-dialog
          title="批量导入学生"
          :visible.sync="importStudent.dialog"
          width="500px"
          center
          v-el-drag-dialog>
          <div class="dialog-container">
            <el-form>
              <el-form-item label="导入模板(Excel):">
                <span style="margin-right: 15px">教学班内学生批量导入列表.xlsx</span>
                <el-button type="default" size="mini" @click="StudentListMethods().clickDownloadBtn()">下载</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="default"
                       @click="importStudent.dialog=false">取 消
            </el-button>
            <el-button type="success" :loading="importStudent.doing"
                       @click="StudentListMethods().clickImportStudentBtn()">导入学生
            </el-button>
          </div>
        </el-dialog>
      </div>
      <!--批量导入教学班-->
      <div>
        <!--导入input-->
        <input
          id="importTeachingClazzFile"
          type="file"
          style="display: none"
          @change="(files)=>{EntityInfoMethods().importTeachingClazzFileChange(files)}"
        >
        <!--导入弹窗-->
        <el-dialog
          title="批量导入教学班"
          :visible.sync="importTeachingClazz.dialog"
          width="500px"
          center
          v-el-drag-dialog>
          <div class="dialog-container">
            <el-form>
              <el-form-item label="导入模板(Excel):">
                <span style="margin-right: 15px">教学班批量导入列表.xlsx</span>
                <el-button type="default" size="mini" @click="EntityInfoMethods().clickDownloadBtn()">下载</el-button>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer">
            <el-button type="default"
                       @click="importTeachingClazz.dialog=false">取 消
            </el-button>
            <el-button type="success" :loading="importTeachingClazz.doing"
                       @click="EntityInfoMethods().clickImportTeachingClazzBtn()">导入教学班
            </el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {
  arrUnion,
  downloadFile, find_obj_from_arr_by_id,
  findObjectArrSomeObjFirstOne,
  isObjArrHasSameIdValue,
  isObjArrHasSameIdValueAndOtherId,
  objectToLVArr
} from "@/utils/common";
import elDragDialog from "@/directive/el-drag-dialog";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {CourseTaskModel} from "@/model/CourseTaskModel";
import enums from "@/enums";
import {CourseSubjectModel} from "@/model/CourseSubjectModel";
import {CommonModel} from "@/model/CommonModel";
import {CollegeModel} from "@/model/CollegeModel";
import Tinymce from "@/components/Tinymce"
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {UserModel} from "@/model/UserModel";
import {CourseModel} from "@/model/CourseModel";
import {CourseCommentModel} from "@/model/CourseCommentModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import {TeachingClazzModel} from "@/model/TeachingClazzModel";
import {ClazzModel} from "@/model/ClazzModel";
import {getSearchTeacherList} from "@/api/UserApi";
import TeachingClazzList from "../userManage/teachingClazz"
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {mapState} from "vuex";
import permission from "@/directive/permission";

export default {
  name: "courseTaskDetail",
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      userInfo: state => state.user,
      permissionArr: state => state.user.permission
    })
  },
  components: {Tinymce, erpUploaderOnePic, ListSearchFilter, TeachingClazzList},
  data() {
    // 校检限制次数
    const validateLimitNumber = (rule, value, callback) => {
      // 检测参数
      let reg = /^[0-9]+$/
      if (!reg.test(value)) {
        callback(new Error('只能输入正整数'));
        return
      }
      if (value > 20) {
        callback(new Error('最多20次'));
      }
      if (value < 1) {
        callback(new Error('不能小于1'));
      }
      callback()
    }
    return {
      window: window,
      enums: enums,
      tabShow: "second",
      // 详情
      entityInfo: {
        filter: {
          courseType: objectToLVArr(enums.courseType),
          college: [],
          courseSubject: []
        },
        $index: 0,
        title: "新增任务安排",
        type: "add",
        dialog: false,
        edit: {
          opened: true,
          needFillReport: true,
          dateRange: [],
          teachingClazzInfo: {
            "calInfo": {
              "studentNumber": 0,
              "teacherNumber": 0,
            },
            "teacherList": []
          }
        },
        teacherTabId: "",
        teacherTabIndex: 0,
        uploading: false,
        uploadPreviewShow: false,
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "任务安排名称"), trigger: 'blur'},
          'opened': {required: true, message: '请选择是否开放', trigger: 'change'},
          'needFillReport': {required: true, message: '请选择是否需要填写实验报告', trigger: 'change'},
          'courseScorePoint': {required: true, message: '请输入课程实验分数占比', trigger: 'change'},
          'reportScorePoint': {required: true, message: '请输入实验报告分数占比', trigger: 'change'},
          'courseId': {required: true, message: '请选择课程', trigger: 'change'},
          'collegeId': {required: true, message: '请选择所属学院', trigger: 'change'},
          'dateRange': {required: true, message: '请选择任务时间范围', trigger: 'change'},
          'remarkText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 200, "任务备注"),
            trigger: 'blur'
          },
          'desText': {required: true, message: '请输入任务介绍和要求文字', trigger: 'blur'},
          'limitLogin': {required: true, message: '请选择是否限制', trigger: 'change'},
          'limitNumber': {required: true, validator: validateLimitNumber, trigger: 'change'},
        },
      },
      // 学生列表弹出对话窗
      studentList: {
        dialog: false,
        title: "",
        lists: {
          list: [],
          loading: false,
          query: {},
          queryBase: {},
          sort: "",
          pages: {
            size: 1000
          },
          searchFilter: {
            search: [
              {
                type: 'input',
                label: '学号',
                key: 'account',
                value: ''
              },
              {
                type: 'input',
                label: '姓名',
                key: 'name',
                value: '',
              },
            ],
            filter: [],
          }
        }
      },
      // 教学班弹窗对哈窗
      teachingClazz: {
        title: "",
        dialog: false,
        edit: {},
        type: "add",
        index: 0,
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "教学班名称"), trigger: 'blur'},
        },
        filter: {
          clazz: []
        }
      },
      // 教学班列表弹窗对哈窗
      teachingClazzList: {
        title: "",
        dialog: false,
        edit: {},
        type: "add",
        index: 0,
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "教学班名称"), trigger: 'blur'},
        },
      },
      // 选择教师弹窗
      selectTeacher: {
        title: "",
        dialog: false,
        edit: {},
        type: "add",
        index: 0,
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "教学班名称"), trigger: 'blur'},
        },
        filter: {
          teacher: []
        }
      },
      // 选择学生弹窗
      selectStudent: {
        title: "",
        dialog: false,
        edit: {},
        type: "add",
        index: 0,
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 40, "教学班名称"), trigger: 'blur'},
        },
        filter: {
          student: []
        }
      },
      // 教学班内学生导入列表
      importStudent: {
        dialog: false,
        doing: false,
      },
      // 导入教学班
      importTeachingClazz: {
        dialog: false,
        doing: false,
      }
    }
  },
  async mounted() {
    let type = this.$route.query["type"]
    this.entityInfo.type = type
    if (type === "edit") {// 编辑模式
      let id = this.$route.query["id"]
      let info = await CourseTaskModel.getOne(id)
      if (info) {
        // 如果是教师登录
        if (this.adminRoles[0] === 'teacher') {
          // 判断是否有自己的安排
          let teacherId = this.userInfo.id
          if (info.teacherIds.indexOf(teacherId) === -1) {// 安全- 不存在登录教师的安排信息，就跳转到首页
            window.location.href = "/"
          } else {// 存在自己的安排就只显示自己的安排
            // 通过v-if控制
            // let teacherList = info.teachingClazzInfo.teacherList
            // // 存储原来的信息
            // info.teachingClazzInfoOld = JSON.parse(JSON.stringify(info.teachingClazzInfo))
            // let [teacherIndex, teacherMe] = find_obj_from_arr_by_id("teacherId", teacherId, teacherList)
            // teacherList = [teacherMe]
            // info.teachingClazzInfo.teacherList = teacherList
          }
        }
        // 格式化时间信息
        info.dateRange = [new Date(info.startTime), new Date(info.endTime)]
        this.entityInfo.edit = info
        this.EntityInfoMethods().calNumber();
      } else {
        msg_err("未找到该任务安排信息！")
      }
    } else {
      //VEAModel.setBreadThirdTitle("新增任务安排")
    }
    this.EntityInfoMethods().initFilter(1)

  },
  methods: {
    // 实体信息Methods
    EntityInfoMethods() {
      let $this = this
      return {
        // 点击更新教学班信息按钮
        async clickSaveTeachingClazzInfoBtn() {
          this.calNumber();
          let teachingClazzInfo = $this.entityInfo.edit.teachingClazzInfo
          if (await msg_confirm("确认要更新教学班信息？")) {
            $this.entityInfo.updateTeachingClazzInfoLoading = true
            let data = await CourseTaskModel.updateTeachingClazzInfo($this.entityInfo.edit.courseTaskId, teachingClazzInfo).catch(err => {
              msg_err("更新失败")
              $this.entityInfo.updateTeachingClazzInfoLoading = false
            })
            $this.entityInfo.updateTeachingClazzInfoLoading = false
            if (data) {
              msg_success("更新成功")
            }
          }
        },
        // 点击删除某个教师按钮
        async clickRemoveTeacherBtn(tabName) {
          if (await msg_confirm("确认要删除该教师吗？")) {
            let teacherList = $this.entityInfo.edit.teachingClazzInfo.teacherList
            let [index, teacherObject] = find_obj_from_arr_by_id("teacherId", tabName, teacherList)
            teacherList.splice(index, 1)
            this.calNumber()
          }
        },
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("https://resouce.ttt.com/bigPlatform/%E6%95%99%E5%AD%A6%E7%8F%AD%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "教学班批量导入列表.xlsx")
        },
        // 点击了学生导入按钮
        clickImportTeachingClazzBtn() {
          const uploader = document.getElementById('importTeachingClazzFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importTeachingClazzFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importTeachingClazzFile').value = ''
          $this.importTeachingClazz.doing = true
          let resultObject = await TeachingClazzModel.importTeachingClazz(file).catch(err => {
            $this.importTeachingClazz.dialog = false
            msg_err("批量导入教学班失败")
          })
          // 导入成功后操作
          if (resultObject) {
            $this.importTeachingClazz.dialog = false
            msg_success('批量导入教学班成功')
            // 更新实体内的教学班和教师信息
            for (let teacherId in resultObject) {
              if (resultObject.hasOwnProperty(teacherId)) {
                let teacher = await UserModel.getOne(teacherId)
                let teacherList = $this.entityInfo.edit.teachingClazzInfo.teacherList
                // 查找该教师是否已存在
                let [index, teacherObject] = find_obj_from_arr_by_id("teacherId", teacherId, teacherList)
                if (!teacherObject) {// 不存在该教师
                  this.addOneTeacher(teacherId, resultObject[teacherId], teacher.name, teacher.account)
                } else {// 已存在
                  let oldTeachingClazzIdList = teacherList[index]["teachingClazzIdList"]
                  let newTeachingClazzIdList = arrUnion(oldTeachingClazzIdList, resultObject[teacherId])
                  $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index], "teachingClazzIdList", newTeachingClazzIdList)
                }
              }
            }
            this.calNumber()
          }
          $this.importTeachingClazz.doing = false
        },
        // 点击选择已存在的教学班列表
        clickSelectTeachingClazzListBtn() {
          $this.teachingClazzList.dialog = true
        },
        // 新增一个教师
        addOneTeacher(teacherId, teachingClazzIdList, teacherName, teacherAccount) {
          $this.entityInfo.edit.teacherIds.push(teacherId)
          $this.entityInfo.edit.teachingClazzInfo.teacherList.push({
              "teacherId": teacherId,
              "teacherName": teacherName ? teacherName : $this.selectTeacher.filter.teacherObject[teacherId]["name"],
              "teacherAccount": teacherAccount ? teacherAccount : $this.selectTeacher.filter.teacherObject[teacherId]["account"],
              "teachingClazzIdList": teachingClazzIdList,
              "teachingClazzList": {
                list: [],
                loading: false,
              }
            }
          )
          this.calNumber()
        },
        // 选择了某个教师
        async clickSelectTeacherBtn() {
          let teacherId = $this.selectTeacher.edit.teacherId
          // 判断是否已存在该教师
          let teacher = findObjectArrSomeObjFirstOne($this.entityInfo.edit.teachingClazzInfo.teacherList, "teacherId", teacherId)
          if (teacher) {
            msg_err("已选择过该老师!");
          } else {//
            this.addOneTeacher(teacherId, [])
            $this.selectTeacher.dialog = false
          }
        },
        // 获取教师列表
        async getTeacherSearchList(nameOrAccount) {
          let list = await UserModel.getSearchTeacherList({
            name: nameOrAccount,
            account: nameOrAccount
          })
          let resultList = []
          let resultObject = {}
          list.forEach(li => {
            resultList.push({
              label: `${li.collegeEntity[0]["name"]}-${li.name}-${li.account}`,
              value: li.userId
            })
            resultObject[li.userId] = li
          })
          $this.$set($this.selectTeacher.filter, "teacher", resultList)
          $this.$set($this.selectTeacher.filter, "teacherObject", resultObject)
        },
        // 初始化筛选列表
        async initFilter(type) {
          if (type === 1) {
            // 获取课程列表
            let list = await CourseModel.getList({})
            let filterList = CommonModel.generateListFilterOptions("name", "courseId", list, false)
            $this.$set($this.entityInfo.filter, "course", filterList[0])
            // 获取学院列表
            let collegeQuery = {}
            // 如果是教秘角色 就只能看到自己学院的安排
            if ($this.adminRoles[0] === "teacherSecretary") {
              collegeQuery = {
                collegeId: $this.userInfo.infos.collegeId
              }
            }
            list = await CollegeModel.getList(collegeQuery)
            filterList = CommonModel.generateListFilterOptions("name", "collegeId", list, false)
            $this.$set($this.entityInfo.filter, "college", filterList[0])
          }
        }
        ,
        // 点击新增按钮
        clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
              if (validate) {
                // 构建参数
                let entity = JSON.parse(JSON.stringify($this.entityInfo.edit))
                // 判断分数占比
                if (!entity.needFillReport) { // 不需要填写
                  entity.courseScorePoint = 0.4
                  entity.reportScorePoint = 0.6
                } else {// 需要填写
                  console.log(parseFloat(entity.courseScorePoint) + parseFloat(entity.reportScorePoint))
                  if (parseFloat(entity.courseScorePoint) + parseFloat(entity.reportScorePoint) === 1) {

                  } else {
                    msg_err("课程分数权重和实验报告权重之和不为1！")
                    return
                  }
                }
                // 任务时间
                entity.startTime = new Date(entity.dateRange[0]).getTime()
                entity.endTime = new Date(entity.dateRange[1]).getTime()
                entity.teachingClazzInfo = {
                  calInfo: {},
                  teacherList: []
                }
                if (entity.endTime <= entity.startTime) {
                  msg_err("开始时间和结束时间不能相同！")
                  return
                }
                entity.creatorId = $this.userInfo.id; // 记录创建人id
                delete entity.dateRange
                if (await msg_confirm('确认要新增该任务安排吗？')) {
                  if (await CourseTaskModel.addOrEdit(entity)) {
                    msg_success('新增成功')
                    $this.$router.go(-1);
                    $this.entityInfo.dialog = false
                  }
                }
              }
            }
          )
        },
        // 点击修改按钮
        clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 构建参数
              let entity = JSON.parse(JSON.stringify($this.entityInfo.edit))
              // 判断分数占比
              if (!entity.needFillReport) { // 不需要填写
                entity.courseScorePoint = 0.4
                entity.reportScorePoint = 0.6
              } else {// 需要填写
                if (parseFloat(entity.courseScorePoint) + parseFloat(entity.reportScorePoint) === 1) {

                } else {
                  msg_err("课程分数权重和实验报告权重之和不为1！")
                  msg_success(entity.courseScorePoint + entity.reportScorePoint)
                  return
                }
              }
              // 任务时间
              entity.startTime = new Date(entity.dateRange[0]).getTime()
              entity.endTime = new Date(entity.dateRange[1]).getTime()
              if (entity.endTime <= entity.startTime) {
                msg_err("开始时间和结束时间不能相同！")
                return
              }
              delete entity.dateRange
              if (await msg_confirm('确认要修改该任务安排信息吗？')) {
                // 删除无需修改字段
                if (await CourseTaskModel.addOrEdit(entity)) {
                  msg_success('修改成功')
                  $this.entityInfo.dialog = false
                  $this.$router.go(-1);
                }
              }
            }
          })
        }
        ,
        // 点击取消按钮
        clickCancelBtn() {
          $this.$router.go(-1);
        }
        ,
        // 点击教师tab
        async clickTeacherTab(tab) {
          // let index = parseInt(tab.index)
          let index = $this.entityInfo.edit.teacherIds.indexOf(tab.name) // 适配教师tab隐藏的情况 需要保证teacherIds有序
          $this.$set($this.entityInfo, "teacherTabIndex", index)
          $this.$set($this.entityInfo, "teacherTabId", tab.name)
          // 获取教学班列表
          this.getTeachingClazzList(index)
        }
        ,
        // 获取教学班列表
        async getTeachingClazzList(index) {
          let teachingClazzIdList = $this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzIdList"]
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "loading", false)
          let list = [];
          for (let i = 0; i < teachingClazzIdList.length; i++) {
            let clazz = await TeachingClazzModel.getOneDetail(teachingClazzIdList[i])
            list.push(clazz)
          }
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "list", list)
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "loading", true)
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "loaded", true)
        }
        ,
        // 点击新增教学班按钮
        clickAddClazzBtn(index) {
          $this.teachingClazz.dialog = true;
          $this.teachingClazz.type = "add"
          $this.teachingClazz.title = "新增教学班"
          $this.teachingClazz.edit = {};
          setTimeout(() => {
            $this.$refs['teachingClazzForm'].clearValidate()
          });
        }
        ,
        // 计算教师和学生人数
        calNumber() {
          let list = $this.entityInfo.edit.teachingClazzInfo.teacherList
          let teachingClazzIds = new Set();// 记录所有教学班id
          let teacherIds = new Set();// 记录所有教师id
          let teacherNumber = list.length
          let studentNumber = 0;
          list.forEach(li => {
            teacherIds.add(li.teacherId)
            let listClazz = li.teachingClazzList.list
            listClazz.forEach(clazz => {
              studentNumber += clazz.studentNumber
            })
            let listClazzIds = li.teachingClazzIdList
            listClazzIds.forEach(id => {
              teachingClazzIds.add(id)
            })
          })
          $this.$set($this.entityInfo.edit.teachingClazzInfo, "calInfo", {
            teacherNumber,
            studentNumber
          })
          // 设置教学班id集合
          $this.$set($this.entityInfo.edit.teachingClazzInfo, "teachingClazzIds", [...teachingClazzIds])
          // 设置教师id集合
          $this.$set($this.entityInfo.edit.teachingClazzInfo, "teacherIds", [...teacherIds])
        }
      }
    },
    // 学生列表弹窗Methods
    StudentListMethods() {
      let $this = this
      return {
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("https://resouce.ttt.com/bigPlatform/%E6%95%99%E5%AD%A6%E7%8F%AD%E5%86%85%E5%AD%A6%E7%94%9F%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "教学班内学生批量导入列表.xlsx")
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await TeachingClazzModel.importStudentsByTeachingClazzId(file, $this.studentList.teachingClazzId).catch(err => {
            $this.importStudent.dialog = false
            msg_err("教学班内批量导入学生失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('教学班内批量导入学生成功')
            await this.getList(0, $this.studentList.lists.pages.size, {})
            // 更新上层教学班学生人数
            $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList["list"][$this.studentList.teachingClazzIndex], "studentNumber", $this.studentList.lists.pages.totalElements)
            $this.EntityInfoMethods().calNumber();
          }
          $this.importStudent.doing = false
        },
        // 点击确认新增某个学生按钮
        async clickSureSelectStudentBtn() {
          let studentId = $this.selectStudent.edit.studentId
          let teachingClazzId = $this.studentList.teachingClazzId
          // 判断该学生是否已存在教学班中
          let student = $this.selectStudent.filter.studentObject[studentId]
          if (student.teachingClazzIds.indexOf(teachingClazzId) !== -1) {
            msg_err("选择的学生已存在于该教学班中!");
          } else {//
            // 执行新增接口
            if (await TeachingClazzModel.addOneStudent(studentId, teachingClazzId)) {
              await this.getList(0, $this.studentList.lists.pages.size, {})
              $this.selectStudent.dialog = false
              // 更新上层教学班学生人数
              $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList["list"][$this.studentList.teachingClazzIndex], "studentNumber", $this.studentList.lists.pages.totalElements)
              $this.EntityInfoMethods().calNumber();
            }
          }
        },
        // 获取学生搜索列表
        async getStudentSearchList(nameOrAccount) {
          let list = await UserModel.getSearchStudentList({
            name: nameOrAccount,
            account: nameOrAccount
          })
          let resultList = []
          let resultObject = {}
          list.forEach(li => {
            resultList.push({
              label: `${li.gradeEntity[0]["name"]}-${li.majorEntity[0]["name"]}-${li.clazzEntity[0]["name"]}-${li.name}(${li.account})`,
              value: li.userId
            })
            resultObject[li.userId] = li
          })
          $this.$set($this.selectStudent.filter, "student", resultList)
          $this.$set($this.selectStudent.filter, "studentObject", resultObject)
        },
        // 点击删除学生按钮
        async clickDeleteBtn(student, index) {
          if (await msg_confirm("确认要从教学班中删除该学生？")) {
            if (await TeachingClazzModel.deleteOneStudent(student.userId, $this.studentList.teachingClazzId)) {
              msg_success("删除成功")
              await this.getList(0, $this.studentList.lists.pages.size, {})
              // 更新上层教学班学生人数
              $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList["list"][$this.studentList.teachingClazzIndex],
                "studentNumber", $this.studentList.lists.pages.totalElements)
              $this.EntityInfoMethods().calNumber();
            }
          }
        },
        // 点击学生列表按钮
        clickGetUserListBtn(teachingClazz, index) {
          $this.$set($this.studentList, "teachingClazzId", teachingClazz.teachingClazzId)
          $this.$set($this.studentList, "teachingClazzIndex", index)
          $this.$set($this.studentList.lists, "queryBase", {
            teachingClazzId: teachingClazz.teachingClazzId
          })
          this.getList(0, $this.studentList.lists.pages.size, {})
          $this.studentList.dialog = true
        },
        // 获取列表
        async getList(page, size, query) {
          $this.studentList.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.studentList.lists.queryBase);
          [list, $this.studentList.lists.pages] = await TeachingClazzModel.getStudentList(page - 1, size, "", query)
          $this.$set($this.studentList.lists, "list", list)
          $this.studentList.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.studentList.lists.pages.size, $this.studentList.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.studentList.lists.pages.number - 1, size, $this.studentList.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.studentList.lists.query = query
          this.getList(0, $this.studentList.lists.pages.size, $this.studentList.lists.query)
        },
      }
    },
    // 教学班列表方法集
    TeachingClazzListMethods() {
      let $this = this;
      return {
        // 选择了教学班
        onSelectTeachingClazz(list) {
          list.forEach(li => {
            // 判断是否已存在该教学班
            if ($this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzIdList.indexOf(li.teachingClazzId) === -1) {// 不存在才加入
              $this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzIdList.push(li.teachingClazzId)
              $this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList.list.push(
                li
              )
            }
          })
          $this.EntityInfoMethods().calNumber()
          $this.teachingClazzList.dialog = false
        },
        // 搜索行政班列表
        async getClazzList(name, clazzIds) {
          // 获取课程列表
          let list = []
          if (name) {
            list = await ClazzModel.getSearchList({
              name: name
            })
          }
          // 搜索在班级id列表里的
          if (clazzIds) {
            list = await ClazzModel.getSearchList({
              clazzIds: clazzIds
            })
          }
          let resultList = []
          list.forEach(li => {
            resultList.push({
              label: `${li.gradeEntity[0]["name"]}-${li.majorEntity[0]["name"]}-${li.name}`,
              value: li.clazzId
            })
          })
          $this.$set($this.teachingClazz.filter, "clazz", resultList)
        },
        // 点击编辑某个教学班按钮
        async clickEditOneBtn(entity, index) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.teachingClazz.dialog = true;
          $this.$set($this.teachingClazz, "edit", entity);
          $this.teachingClazz.type = "edit"
          $this.teachingClazz.title = "编辑教学班"
          // 获取教学班的行政班级名称列表
          await this.getClazzList(null, entity.clazzIds)
          setTimeout(() => {
            $this.$refs['teachingClazzForm'].clearValidate()
          }, 300)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['teachingClazzForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名学科
              let listResult = await TeachingClazzModel.getList({
                name: $this.teachingClazz.edit.name,
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的教学班，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该教学班吗？')) {
                // 设置教师id
                $this.teachingClazz.edit.teacherId = $this.entityInfo.teacherTabId;
                let result = await TeachingClazzModel.addOne($this.teachingClazz.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  let teachingClazzId = result.data.teachingClazzId
                  let teachingClazz = await TeachingClazzModel.getOneDetail(teachingClazzId)
                  $this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzIdList.push(teachingClazz.teachingClazzId)
                  $this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList.list.push(
                    teachingClazz
                  )
                  $this.EntityInfoMethods().calNumber()
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['teachingClazzForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名标签
              let listResult = await TeachingClazzModel.getList({
                name: $this.teachingClazz.edit.name,
                teachingClazzId: {"$ne": $this.teachingClazz.edit.teachingClazzId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的教学班，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该教学班吗？')) {
                let result = await TeachingClazzModel.editOne($this.teachingClazz.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  let teachingClazzId = result.data.teachingClazzId
                  let teachingClazz = await TeachingClazzModel.getOneDetail(teachingClazzId)
                  // 更新列表中的教学班信息
                  $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList.list, $this.teachingClazz.index, teachingClazz);
                  $this.EntityInfoMethods().calNumber()
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.teachingClazz.dialog = false
        },
        // 点击从列表中删除某个教学班按钮
        clickDeleteBtn(index) {
          $this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzIdList.splice(index, 1)
          $this.entityInfo.edit.teachingClazzInfo.teacherList[$this.entityInfo.teacherTabIndex].teachingClazzList.list.splice(index, 1)
          $this.EntityInfoMethods().calNumber()
        }
      }
    },

  }
}
</script>

<style scoped lang="scss">
.info-box {
  padding: 0px 20px;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 10px;
  margin-bottom: 10px;

  .left {
    color: #555;
    font-size: 15px;
  }

  .right {

  }
}

.clazz-box {
  margin-bottom: 10px;
}
</style>
