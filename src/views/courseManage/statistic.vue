<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <div class="course-name"> {{ courseName }}</div>
      <div class="date">最后统计时间 {{ entityInfo.edit.updateTime | dateFormat }}</div>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <!--          <el-button class="el-button" type="success"-->
          <!--                     @click="ListMethods().clickExportBtn()" :loading="exportAll.doing">导出全部课程评价-->
          <!--          </el-button>-->
        </div>
      </div>
    </div>
    <!--详情-->
    <div class="entity-container flex flex-around">
      <div class="number-box" style="width: 700px;">
        <el-descriptions title="数量统计" style="margin-bottom: 20px;" border :column="3" direction="vertical">
          <el-descriptions-item label="实验浏览量">{{ entityInfo.edit.viewNumber }}</el-descriptions-item>
          <el-descriptions-item label="实验人次">{{ entityInfo.edit.scoreNumber }}</el-descriptions-item>
          <el-descriptions-item label="实验人数">{{ entityInfo.edit.userNumber }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="分数统计" style="margin-bottom: 20px;" border :column="3" direction="vertical">
          <el-descriptions-item label="平均分">{{ entityInfo.edit.averageScore.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="最高分">{{ entityInfo.edit.maxScore.toFixed(2) }}</el-descriptions-item>
          <el-descriptions-item label="最低分">{{ entityInfo.edit.minScore.toFixed(2) }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="用时统计" style="margin-bottom: 20px;" border :column="3" direction="vertical">
          <el-descriptions-item label="平均用时(秒)">{{ entityInfo.edit.averageUsedTime }}</el-descriptions-item>
          <el-descriptions-item label="最大用时(秒)">{{ entityInfo.edit.maxUsedTime }}</el-descriptions-item>
          <el-descriptions-item label="最少用时(秒)">{{ entityInfo.edit.minUsedTime }}</el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="完成统计" style="margin-bottom: 20px;" border :column="5" direction="vertical">
          <el-descriptions-item label="完成率">{{
              (entityInfo.edit.completePoint * 100).toFixed(2)
            }}%
          </el-descriptions-item>
          <el-descriptions-item label="通过率">{{ (entityInfo.edit.passPoint * 100).toFixed(2) }}%</el-descriptions-item>
          <el-descriptions-item label="优秀人次">{{ entityInfo.edit.excellentNumber }}</el-descriptions-item>
          <el-descriptions-item label="达标人次">{{ entityInfo.edit.standardNumber }}</el-descriptions-item>
          <el-descriptions-item label="不达标人次">{{ entityInfo.edit.failNumber }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="chart-box" style="width: 900px">
        <!--每日实验人次柱状图-->
        <div id="charts-scoreNumber" style="width: 900px;height: 500px;"></div>
      </div>
    </div>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {StatisticModel} from "@/model/StatisticModel";
import * as echarts from 'echarts';

export default {
  name: "courseStatistic",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    }),
    courseId() {
      return this.$route.query["courseId"]
    },
    courseName() {
      return this.$route.query["courseName"]
    },
  },

  filters: {dateFormat},
  data() {
    let $this = this;
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      entityInfo: {
        edit: {
          averageScore: 0.0,
          maxScore: 0.0,
          minScore: 0.0,
          completePoint: 0.0,
          passPoint: 0.0
        }
      },
      //
    }
  },
  async mounted() {
    let result = await StatisticModel.getCourseRealTimeValue(this.courseId)
    if (result) {
      this.$set(this.entityInfo, "edit", result)
      setTimeout(() => {
        //this.drawViewNumberChart()
        this.drawScoreNumberChart()
      }, 1000)
    } else {
      alert("获取最近统计结果失败！")
    }
  },
  methods: {
    // 渲染每日浏览量柱状图
    drawViewNumberChart() {
      let chartDom = document.getElementById('charts-viewNumber');
      let myChart = echarts.init(chartDom);
      let option = {
        title: {
          text: "近30日浏览量",
          x: "center",
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130],
            type: 'bar'
          }
        ],
        tooltip: {
          trigger: 'axis',
          axisPointer: {            // 坐标轴指示器，坐标轴触发有效
            type: 'line'        // 默认为直线，可选为：'line' | 'shadow'
          }
        }
      };
      option && myChart.setOption(option);

    },
    // 渲染每日实验人次柱状图
    async drawScoreNumberChart() {
      // 获取最近30天实验人次数据
      let resultList = await StatisticModel.getLast30DayScoreNumber(this.courseId)
      let dataX = [];
      let dataY = [];
      for (let i in resultList) {
        if (resultList.hasOwnProperty(i)) {
          dataX.push(i)
          dataY.push(resultList[i])
        }
      }
      let chartDom = document.getElementById('charts-scoreNumber');
      let myChart = echarts.init(chartDom);
      let option = {
        title: {
          text: "近30日实验人次",
          x: "center",
        },
        xAxis: {
          type: 'category',
          data: dataX
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: dataY,
            type: 'bar'
          }
        ]
      };
      myChart.setOption(option);
    }
  }

}
</script>

<style scoped lang="scss">
.course-name {
  text-align: center;
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
  color: #555;
}

.date {
  text-align: center;
  font-size: 14px;
  color: #999;
}
</style>
