<template>
  <div class="app-container">
    <div class="flex flex-between info-box">
      <div class="left">
        总计：{{ entityInfo.edit.teachingClazzInfo.calInfo.teacherNumber }}名教师
        {{ entityInfo.edit.teachingClazzInfo.calInfo.studentNumber }}名学生
      </div>
      <div class="right">
        <el-button type="primary" :loading="entityInfo.exportScoreLoading"
                   v-permission="['administrator','teacherSecretary']"
                   @click="EntityInfoMethods().clickExportAllTeacherAllTeachingClazzScoreList()">导出所有教师成绩
        </el-button>
      </div>
    </div>
    <el-tabs tab-position="left" @tab-click="v=>EntityInfoMethods().clickTeacherTab(v)"
             v-model="entityInfo.teacherTabId" closable
             @tab-remove="v=>EntityInfoMethods().clickRemoveTeacherBtn(v)">
      <el-tab-pane :label="item.teacherName+'('+item.teacherAccount+')'" :name="item.teacherId"
                   v-for="(item,index) in entityInfo.edit.teachingClazzInfo.teacherList"
                   v-if="adminRoles[0]==='teacher'?item.teacherId===userInfo.id:true">
        <div class="flex flex-between clazz-box">
          <div class="left">

          </div>
          <div class="right">
            <el-button type="primary" size="small" :loading="entityInfo.exportTeacherScoreLoading"
                       @click="EntityInfoMethods().clickExportOneTeacherAllTeachingClazzScoreList(index)">导出该教师成绩
            </el-button>
          </div>
        </div>
        <el-table :data="entityInfo.edit.teachingClazzInfo.teacherList[index]['teachingClazzList']['list']"
                  element-loading-text="加载中" border fit
                  style="width: 100%;"
                  :loading="entityInfo.edit.teachingClazzInfo.teacherList[index]['teachingClazzList']['loading']">
          <el-table-column label="教学班名称" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="关联行政班" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.clazzNames.join('、') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学生人数" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.studentNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="课程完成人数" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.courseCompletedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="报告完成人数" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.reportCompletedNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="250"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" round
                         @click="StudentListMethods().clickGetUserListBtn(scope.row,scope.$index)">学生列表
              </el-button>
              <el-button type="primary" size="mini" round :loading="studentList.exportScoreLoading"
                         @click="StudentListMethods().clickExportOneTeachingClazzScoreList(scope.row,scope.$index)">导出成绩
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!--学生列表弹窗-->
    <el-dialog
      :title="studentList.title"
      :visible.sync="studentList.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="1200px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <list-search-filter :search-filter="studentList.lists.searchFilter"
                            @clickSearchFilterBtn="query=>StudentListMethods().clickSearchFilterBtn(query)"
                            @clickCleanBtn="StudentListMethods().clickCleanBtn()">
        </list-search-filter>
        <!--列表-->
        <el-table :data="studentList.lists.list" v-loading="studentList.lists.loading" element-loading-text="加载中"
                  border fit max-height="500px"
                  style="width: 100%;">
          <el-table-column label="学号" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.userEntity[0].account }}</span>
            </template>
          </el-table-column>
          <el-table-column label="姓名" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.userEntity[0].name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="行政班级" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.gradeEntity[0]["name"] }}-{{
                  scope.row.majorEntity[0]["name"]
                }}-{{ scope.row.clazzEntity[0]["name"] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验分数" align="center" width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.courseCompleted">{{ scope.row.courseScore | scoreFormat(2) }}</span>
              <span v-if="!scope.row.courseCompleted">未完成</span>
            </template>
          </el-table-column>
          <el-table-column label="实验报告" align="center" width="80px">
            <template slot-scope="scope">
              <template v-if="scope.row.reportFilled">
                <span v-if="scope.row.reportCorrected">{{ scope.row.reportScore |scoreFormat(2) }}</span>
                <span v-if="!scope.row.reportCorrected">未批改</span>
              </template>
              <span v-if="!scope.row.reportFilled">未填写</span>
            </template>
          </el-table-column>
          <el-table-column label="综合分数" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.row.totalScore |scoreFormat(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="剩余登录次数" align="center" width="110px" v-if="entityInfo.edit.limitLogin">
            <template slot-scope="scope">
              <span>{{ scope.row.leftLimitNumber }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="220px">
            <template slot-scope="scope">
              <el-button type="primary" size="small" v-if="entityInfo.edit.limitLogin"
                         @click="StudentListMethods().clickResetLoginLimitNumberBtn(scope.row,scope.$index)">重置登录次数
              </el-button>
              <el-button type="primary" size="small" v-if="scope.row.reportFilled" v-permission="['teacher','teacherSecretary']"
                         @click="StudentListMethods().clickCorrectReportBtn(scope.row,scope.$index)">批改报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--列表分页-->
        <div class="pagination-container">
          <el-pagination background @current-change="(number)=>StudentListMethods().pageChange(number)"
                         :current-page.sync="studentList.lists.pages.number"
                         :page-size.sync="studentList.lists.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="studentList.lists.pages.totalElements"
                         @size-change="(size)=>StudentListMethods().pageLimitChange(size)"
                         :page-count="studentList.lists.pages.totalPages">
          </el-pagination>
        </div>
      </div>
    </el-dialog>
    <!--批改实验报告弹窗-->
    <el-dialog
      :close-on-click-modal="false"
      :title="reportInfo.title"
      :visible.sync="reportInfo.dialog"
      :append-to-body="true"
      width="1200px"
      center
      v-el-drag-dialog>
      <div class="dialog-container report-container">
        <div class="header flex flex-between">
          <div class="li">
            <span class="title">姓名：</span>
            <span class="content">{{ reportInfo.courseRecord.userEntity[0]["name"] }}</span>
          </div>
          <div class="li">
            <span class="title">学号：</span>
            <span class="content">{{ reportInfo.courseRecord.userEntity[0]["account"] }}</span>
          </div>
          <div class="li">
            <span class="title">班级：</span>
            <span class="content">{{ reportInfo.courseRecord.clazzEntity[0]["name"] }}</span>
          </div>
        </div>
        <div class="html-container html-view limit-height" v-html="reportInfo.edit.content"></div>
        <div class="footer flex flex-between">
          <div class="left flex flex-start">
            <span class="title">分数：</span>
            <el-input style="width: 200px" type="number" v-model.number="reportInfo.edit.score"></el-input>
          </div>
          <div class="right flex flex-end">
            <div style="color: #999">
              批阅老师:<span v-if="reportInfo.edit.correctTeacherId">
              {{ reportInfo.edit.correctTeacherName }}{{
                " "
              }}{{ reportInfo.edit.correctDate | dateFormat }}
              </span>
              <span v-else>
                {{ userInfo.name }}{{
                  " "
                }}{{ new Date() | dateFormat }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" size="small"
                   @click="reportInfo.dialog=false">取 消</el-button>
         <el-button type="primary" size="small"
                    @click="StudentListMethods().clickSureCorrectBtn()">确定批改</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {
  arrUnion,
  downloadFile, find_obj_from_arr_by_id,
  findObjectArrSomeObjFirstOne,
  isObjArrHasSameIdValue,
  isObjArrHasSameIdValueAndOtherId,
  objectToLVArr
} from "@/utils/common";
import elDragDialog from "@/directive/el-drag-dialog";
import {VEAModel} from "@/model/VEAModel";
import {validateMaxLength} from "@/utils/validate";
import {CourseTaskModel} from "@/model/CourseTaskModel";
import enums from "@/enums";
import {CourseSubjectModel} from "@/model/CourseSubjectModel";
import {CommonModel} from "@/model/CommonModel";
import {CollegeModel} from "@/model/CollegeModel";
import Tinymce from "@/components/Tinymce"
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {UserModel} from "@/model/UserModel";
import {CourseModel} from "@/model/CourseModel";
import {CourseCommentModel} from "@/model/CourseCommentModel";
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import {TeachingClazzModel} from "@/model/TeachingClazzModel";
import {ClazzModel} from "@/model/ClazzModel";
import {getSearchTeacherList} from "@/api/UserApi";
import TeachingClazzList from "../userManage/teachingClazz"
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {CourseRecordModel} from "@/model/CourseRecordModel";
import {dateFormat, scoreFormat} from "@/filters";
import {CourseReportModel} from "@/model/CourseReportModel";
import {mapGetters, mapState} from "vuex";
import {getTeachingClazzStudentList} from "@/api/CourseRecordApi";
import permission from "@/directive/permission";

export default {
  name: "courseTaskDetail",
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission,
      userInfo: state => state.user,
    })
  },
  filters: {
    scoreFormat: scoreFormat,
    dateFormat: dateFormat,
  },
  components: {Tinymce, erpUploaderOnePic, ListSearchFilter, TeachingClazzList},
  data() {
    return {
      window: window,
      enums: enums,
      // 详情
      entityInfo: {
        edit: {
          opened: true,
          needFillReport: true,
          dateRange: [],
          teachingClazzInfo: {
            "calInfo": {
              "studentNumber": 0,
              "teacherNumber": 0,
            },
            "teacherList": []
          }
        },
        teacherTabId: "",
        teacherTabIndex: 0,
      },
      // 学生列表弹出对话窗
      studentList: {
        dialog: false,
        title: "",
        lists: {
          list: [],
          loading: false,
          query: {},
          queryBase: {},
          sort: "",
          pages: {
            size: 20
          },
          searchFilter: {
            search: [
              {
                type: 'input',
                label: '学号',
                key: 'account',
                value: ''
              },
              {
                type: 'input',
                label: '姓名',
                key: 'name',
                value: '',
              },
            ],
            filter: [],
          }
        }
      },
      // 报告详情
      reportInfo: {
        dialog: false,
        scoreChange: false,// 分数是否改变
        edit: {
          reportScore: "",
        },
        courseRecord: {
          userEntity: [{}],
          clazzEntity: [{}]
        },
        // 输入检测
        formRules: {
          'reportScore': {required: true}
        },
      }
    }
  },
  async mounted() {
    let id = this.$route.query["id"]
    let info = await CourseTaskModel.getOne(id)
    if (info) {
      // 如果是教师登录
      if (this.adminRoles[0] === 'teacher') {
        // 判断是否有自己的安排
        let teacherId = this.userInfo.id
        if (info.teacherIds.indexOf(teacherId) === -1) {// 安全- 不存在登录教师的安排信息，就跳转到首页
          window.location.href = "/"
        } else {// 存在自己的安排就只显示自己的安排
          // 通过v-if控制
          // let teacherList = info.teachingClazzInfo.teacherList
          // let [, teacherMe] = find_obj_from_arr_by_id("teacherId", teacherId, teacherList)
          // teacherList = [teacherMe]
          // info.teachingClazzInfo.teacherList = teacherList
        }
      }
      this.entityInfo.edit = info
      this.EntityInfoMethods().calNumber();
    } else {
      msg_err("未找到该任务安排信息！")
    }
  },
  methods: {
    // 实体信息Methods
    EntityInfoMethods() {
      let $this = this
      return {
        // 点击导出所有老师所有教学班成绩按钮
        async clickExportAllTeacherAllTeachingClazzScoreList() {
          let courseTaskId = $this.entityInfo.edit.courseTaskId
          $this.entityInfo.exportScoreLoading = true
          await CourseTaskModel.exportAllTeacherAllTeachingClazzScoreList(courseTaskId).catch(res => {
            $this.entityInfo.exportScoreLoading = false
          })
          $this.entityInfo.exportScoreLoading = false
        },
        // 点击导出该老师所有教学班成绩按钮
        async clickExportOneTeacherAllTeachingClazzScoreList() {
          let courseTaskId = $this.entityInfo.edit.courseTaskId
          let teacherId = $this.entityInfo.teacherTabId
          $this.entityInfo.exportTeacherScoreLoading = true
          await CourseTaskModel.exportOneTeacherAllTeachingClazzScoreList(courseTaskId, teacherId).catch(res => {
            $this.entityInfo.exportTeacherScoreLoading = false
          })
          $this.entityInfo.exportTeacherScoreLoading = false
        },
        // 初始化筛选列表
        async initFilter(type) {

        },
        // 点击教师tab
        async clickTeacherTab(tab) {
          //let index = parseInt(tab.index)
          let index = $this.entityInfo.edit.teacherIds.indexOf(tab.name) // 适配教师tab隐藏的情况 需要保证teacherIds有序
          $this.$set($this.entityInfo, "teacherTabIndex", index)
          $this.$set($this.entityInfo, "teacherTabId", tab.name)
          // 获取教学班列表
          this.getTeachingClazzList(index)
        }
        ,
        // 获取教学班列表
        async getTeachingClazzList(index) {
          let teachingClazzIdList = $this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzIdList"]
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "loading", false)
          let list = [];
          let courseTaskId = $this.entityInfo.edit.courseTaskId
          let teacherId = $this.entityInfo.teacherTabId
          for (let i = 0; i < teachingClazzIdList.length; i++) {
            let clazz = await CourseRecordModel.getOneTeachingClazzDetail(courseTaskId, teacherId, teachingClazzIdList[i])
            list.push(clazz)
          }
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "list", list)
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "loading", true)
          $this.$set($this.entityInfo.edit.teachingClazzInfo.teacherList[index]["teachingClazzList"], "loaded", true)
        }
        ,
        // 计算教师和学生人数
        calNumber() {
          let list = $this.entityInfo.edit.teachingClazzInfo.teacherList
          let teachingClazzIds = new Set();// 记录所有教学班id
          let teacherIds = new Set();// 记录所有教师id
          let teacherNumber = list.length
          let studentNumber = 0;
          list.forEach(li => {
            teacherIds.add(li.teacherId)
            let listClazz = li.teachingClazzList.list
            listClazz.forEach(clazz => {
              studentNumber += clazz.studentNumber
            })
            let listClazzIds = li.teachingClazzIdList
            listClazzIds.forEach(id => {
              teachingClazzIds.add(id)
            })
          })
          $this.$set($this.entityInfo.edit.teachingClazzInfo, "calInfo", {
            teacherNumber,
            studentNumber
          })
          // 设置教学班id集合
          $this.$set($this.entityInfo.edit.teachingClazzInfo, "teachingClazzIds", [...teachingClazzIds])
          // 设置教师id集合
          $this.$set($this.entityInfo.edit.teachingClazzInfo, "teacherIds", [...teacherIds])
        }
      }
    },
    // 学生列表弹窗Methods
    StudentListMethods() {
      let $this = this
      return {
        // 点击重置登录次数按钮
        async clickResetLoginLimitNumberBtn(entity, index) {
          if (await CourseRecordModel.resetRecordLeftLimitNumber(entity.courseRecordId)) {
            msg_success("重置成功!")
            this.getList($this.studentList.lists.pages.number, $this.studentList.lists.pages.size, $this.studentList.lists.query)
          }
        },
        // 点击导出某个教学班的实验成绩按钮
        async clickExportOneTeachingClazzScoreList(teachingClazz, index) {
          let courseTaskId = $this.entityInfo.edit.courseTaskId
          let teacherId = $this.entityInfo.teacherTabId
          let teachingClazzId = teachingClazz.teachingClazzId
          $this.studentList.exportScoreLoading = true
          await CourseTaskModel.exportOneTeachingClazzScoreList(courseTaskId, teacherId, teachingClazzId).catch(res => {
            $this.studentList.exportScoreLoading = false
          })
          $this.studentList.exportScoreLoading = false
        },

        // 点击确认批改报告按钮
        async clickSureCorrectBtn() {
          let value = $this.reportInfo.edit.score
          let validate = true
          if (value < 0) {
            msg_err('请输入0-100内正整数作为分数')
            validate = false
          }
          if (value === "") {
            msg_err('请输入0-100内正整数作为分数')
            validate = false
          }
          if (value > 100) {
            msg_err('请输入0-100内正整数作为分数')
            validate = false
          }
          // 判断是否改变
          if (value === $this.reportInfo.edit.scoreOld && value !== 0) {
            $this.reportInfo.dialog = false;
            return
          }
          if (validate) {
            let result = await CourseReportModel.teacherCorrectReport(
              $this.reportInfo.edit.courseReportId,
              $this.reportInfo.edit.score,
              {}
            )
            if (result) {
              msg_success("批改报告成功")
              // 修改列表栏里的相关分数
              $this.$set($this.studentList.lists.list[$this.reportInfo.$index], "reportScore", result.reportScore)
              $this.$set($this.studentList.lists.list[$this.reportInfo.$index], "reportCorrected", true)
              $this.$set($this.studentList.lists.list[$this.reportInfo.$index], "totalScore", result.totalScore)
              $this.reportInfo.dialog = false;
            }
          }
        },
        // 点击批改实验报告按钮
        async clickCorrectReportBtn(entity, index) {
          if (!entity.reportFilled) {
            msg_err("该学生尚未填写课程报告！")
            return
          }

          // 获取报告详情
          let data = await CourseReportModel.getOneCourseRecordReport(entity.courseRecordId)
          if (data) {
            $this.reportInfo.title = "课程报告"
            $this.reportInfo.courseRecord = JSON.parse(JSON.stringify(entity))
            $this.reportInfo.edit = data
            $this.reportInfo.edit.scoreOld = $this.reportInfo.edit.score // 记录旧数值，判断是否改变
            $this.reportInfo.dialog = true;
            $this.reportInfo.$index = index;
          } else {
            msg_err("未找到报告")
          }

        },
        // 点击学生列表按钮
        clickGetUserListBtn(teachingClazz, index) {
          $this.$set($this.studentList, "teachingClazzId", teachingClazz.teachingClazzId)
          $this.$set($this.studentList, "teachingClazzIndex", index)
          $this.$set($this.studentList.lists, "queryBase", {
            courseTaskId: $this.entityInfo.edit.courseTaskId,
            teacherId: $this.entityInfo.teacherTabId,
            teachingClazzId: teachingClazz.teachingClazzId,
          })
          this.getList(0, $this.studentList.lists.pages.size, {})
          $this.studentList.dialog = true
        },
        // 获取列表
        async getList(page, size, query) {
          $this.studentList.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.studentList.lists.queryBase);
          [list, $this.studentList.lists.pages] = await CourseRecordModel.getTeachingClazzStudentList(page - 1, size, "", query)
          $this.$set($this.studentList.lists, "list", list)
          $this.studentList.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.studentList.lists.pages.size, $this.studentList.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.studentList.lists.pages.number - 1, size, $this.studentList.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.studentList.lists.query = query
          this.getList(0, $this.studentList.lists.pages.size, $this.studentList.lists.query)
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.info-box {
  padding: 0px 20px;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 10px;
  margin-bottom: 10px;

  .left {
    color: #555;
    font-size: 15px;
  }

  .right {

  }
}

.clazz-box {
  margin-bottom: 10px;
}

.html-container {
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 10px;
  background: #F5F5F5;
}

// html预览
.html-view img {
  max-width: 100%;
}

.html-view.limit-height {
  max-height: 500px;
  overflow-y: scroll;
}
</style>
