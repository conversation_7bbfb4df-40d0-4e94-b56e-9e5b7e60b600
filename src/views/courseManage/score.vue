<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <div class="clazz-name"> {{ courseName }}</div>
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickExportBtn()" :loading="exportScore.doing">导出成绩
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="学号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userEntity[0].account }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userEntity[0].name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行政班级" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gradeEntity[0]["name"] }}-{{ scope.row.majorEntity[0]["name"] }}-{{
              scope.row.clazzEntity[0]["name"]
            }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实验分数" align="center" width="80px">
        <template slot-scope="scope">
          <span v-if="scope.row.courseCompleted">{{ scope.row.courseScore | scoreFormat(2) }}</span>
          <span v-if="!scope.row.courseCompleted">未完成</span>
        </template>
      </el-table-column>
      <el-table-column label="剩余登录次数" align="center" width="120px" v-if="courseInfo.limitLogin">
        <template slot-scope="scope">
          <span>{{ scope.row.leftLimitNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220px">
        <template slot-scope="scope">
          <el-button type="primary" size="small" v-if="scope.row.courseCompleted"
                     @click="ListMethods().clickViewScoreRecordBtn(scope.row,scope.$index)">成绩记录
          </el-button>
          <el-button type="primary" size="small" v-if="courseInfo.limitLogin"
                     @click="ListMethods().clickResetLoginLimitNumberBtn(scope.row,scope.$index)">重置登录次数
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--实体弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="学号:" prop="account">

          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
    <!--成绩列表弹窗-->
    <el-dialog
      title="成绩记录列表"
      :visible.sync="scoreList.dialog"
      width="980px"
      center
      :close-on-click-modal="false"
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-table :data="scoreList.list" v-loading="scoreList.loading"
                  element-loading-text="加载中" fit border max-height="500px"
                  style="width: 100%;">
          <el-table-column label="课程名称" prop="courseName" align="center">
            <template slot-scope="scope">
              <span>{{ courseName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验结果" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ enums.courseScoreResult[scope.row.result] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验成绩" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.row.score | scoreFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验开始时间" prop="courseName" align="center" width="160px">
            <template slot-scope="scope">
              <span>{{ scope.row.startTime | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验结束时间" prop="courseName" align="center" width="160px">
            <template slot-scope="scope">
              <span>{{ scope.row.endTime | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验用时" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.row.usedTime | scoreUseTimeFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <el-button type="text" size="small"
                         @click="ListMethods().clickViewScoreDetailBtn(scope.row,scope.$index)">查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!--成绩详情弹窗-->
    <el-dialog
      :title="scoreInfo.title"
      :visible.sync="scoreInfo.dialog"
      :close-on-click-modal="true"
      :append-to-body="true"
      width="1200px"
      center
      v-el-drag-dialog>
      <div class="dialog-container dialog-scoreInfo">
        <div class="info flex flex-start flex flex-wrap">
          <div class="li name">
            <span class="title">实验名称：</span>
            <span class="text">{{ courseName }}</span>
          </div>
          <div class="li">
            <span class="title">实验开始时间：</span>
            <span class="text">{{ scoreInfo.info.startTime | dateFormat }}</span>
          </div>
          <div class="li">
            <span class="title">实验结束时间：</span>
            <span class="text">{{ scoreInfo.info.endTime | dateFormat }}</span>
          </div>
          <div class="li">
            <span class="title">实验用时：</span>
            <span class="text">{{ scoreInfo.info.usedTime | scoreUseTimeFilter }}</span>
          </div>
          <div class="li">
            <span class="title">实验满分：</span>
            <span class="text">{{ scoreInfo.info.fullScore | scoreFormat }}分</span>
          </div>
          <div class="li">
            <span class="title">实验得分：</span>
            <span class="text">{{ scoreInfo.info.score | scoreFormat }}</span>
          </div>
          <div class="li">
            <span class="title">实验结果：</span>
            <span class="text">{{ enums.courseScoreResult[scoreInfo.info.result] }}</span>
          </div>
        </div>
        <div class="list">
          <el-table :data="scoreInfo.info.stepInfo" v-loading="scoreInfo.loading"
                    element-loading-text="加载中" fit border max-height="400px"
                    style="width: 100%;">
            <el-table-column label="步骤名称" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否完成" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.done ? "完成" : "未完成" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤开始时间" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.startTime |dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤结束时间" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.endTime |dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤用时(秒)" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.timeUsed  | scoreUseTimeFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤合理用时" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.expectTime  | scoreUseTimeFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤满分" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.maxScore | scoreFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤得分" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.score | scoreFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤操作次数" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.repeatCount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤评价" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.evaluation }}</span>
              </template>
            </el-table-column>
            <el-table-column label="赋分模型" align="center">
              <template slot-scope="scope">
                <div>
                  <div class="text" v-for="item in scope.row.scoringModel.split('\n')">{{ item }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template slot-scope="scope">
                <div>
                  <div class="text" v-for="item in scope.row.remarks.split('\n')">{{ item }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
    <!--导出成绩弹窗-->
    <el-dialog
      title="导出成绩"
      :visible.sync="exportScore.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="exportScoreForm" :model="exportScore.edit" :rules="exportScore.formRules">
          <el-form-item label="总共数量:" prop="totalNum">
            <span>{{ lists.pages.totalElements }}</span>
          </el-form-item>
          <el-form-item label="从第几条开始:" prop="startNum">
            <el-input v-model="exportScore.edit.startNum" type="number"></el-input>
          </el-form-item>
          <el-form-item label="到第几条结束:" prop="endNum">
            <el-input v-model="exportScore.edit.endNum" type="number"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="exportScore.dialog=false">取 消</el-button>
        <el-button type="success" @click="ListMethods().clickExportStartBtn()">开始导出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat, scoreFormat, scoreUseTimeFilter} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {CourseRecordModel} from "@/model/CourseRecordModel";
import {CourseScoreModel} from "@/model/CourseScoreModel";
import {isRegExp} from "xe-utils";
import {msg_err, msg_success} from "@/utils/ele_component";
import {CourseModel} from "@/model/CourseModel";

export default {
  name: "courseScore",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    }),
    courseId() {
      return this.$route.query["courseId"]
    },
    courseName() {
      return this.$route.query["courseName"]
    },
  },

  filters: {dateFormat, scoreFormat: scoreFormat, scoreUseTimeFilter,},
  data() {
    let $this = this;
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学号',
              key: 'account',
              value: ''
            },
            {
              type: 'input',
              label: '姓名',
              key: 'name',
              value: '',
            },
          ],
          filter: [
            {
              type: 'select',
              label: '是否是任务成绩',
              key: 'recordType',
              value: '',
              data: [
                {"label": "是", value: "task"},
                {"label": "否", value: "open"},
              ],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
            }
          ],
        }

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增课程评价",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {},
      },
      // 分数列表弹窗
      scoreList: {
        dialog: false,
        list: [],
        listLoading: false,
      },
      // 分数详情
      scoreInfo: {
        title: "成绩详情",
        dialog: false,
        info: {
          stepInfo: [{}, {}],
        },
        listLoading: false,
      },
      // 导出课程评价
      exportScore: {
        doing: false,
        edit: {},
        dialog: false,
      },
      // 课程信息
      courseInfo: {}
    }
  },
  async mounted() {
    this.courseInfo = await CourseModel.getOne(this.courseId)
    this.lists.queryBase = {
      courseId: this.courseId
    }
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击批量导出按钮
        async clickExportBtn() {
          $this.exportScore.dialog = true
        },
        // 点击开始导出成绩按钮
        async clickExportStartBtn() {
          let totalNumber = $this.lists.pages.totalElements
          let startNum = parseInt($this.exportScore.edit.startNum)
          let endNum = parseInt($this.exportScore.edit.endNum)
          // 判断输入合法性
          if (startNum > 0 && startNum <= totalNumber && startNum <= endNum) {
            if (endNum > 0 && endNum <= totalNumber) {
              let query = $this.lists.queryLast;
              await CourseScoreModel.exportScoreList($this.courseId, startNum, endNum, query)
              $this.exportScore.dialog = false
            } else {
              msg_err("结束数字不合法！")
            }
          } else {
            msg_err("开始数字不合法！")
          }
        },
        // 点击查看成绩记录按钮
        async clickViewScoreRecordBtn(entity, index) {
          $this.scoreList.dialog = true;
          $this.$set($this.scoreList, "listLoading", true)
          let list = await CourseScoreModel.getList({
            courseRecordId: entity.courseRecordId
          }).catch(err => {
            $this.$set($this.scoreList, "listLoading", false)
          })
          $this.$set($this.scoreList, "listLoading", false)
          $this.$set($this.scoreList, "list", list)
        },
        // 点击重置登录次数按钮
        async clickResetLoginLimitNumberBtn(entity, index) {
          if (await CourseRecordModel.resetRecordLeftLimitNumber(entity.courseRecordId)) {
            msg_success("重置成功!")
            this.getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
          }
        },
        // 点击查看成绩详情按钮
        async clickViewScoreDetailBtn(entity, index) {
          let entityObject = JSON.parse(JSON.stringify(entity))
          $this.$set($this.scoreInfo, "info", entityObject)
          $this.scoreInfo.dialog = true
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          $this.lists.queryLast = query;
          [list, $this.lists.pages] = await CourseRecordModel.getOneCourseScoreList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.clazz-name {
  text-align: center;
  margin-bottom: 15px;
  color: #555;
}

.dialog-scoreInfo {
  .info {
    .li {
      margin-bottom: 10px;
      width: 33%;

      .title {
        font-size: 14px;
        color: #666;
      }

      .text {
        font-size: 14px;
        color: #999;
      }
    }

    .li.name {
      width: 100%;
    }
  }
}
</style>
