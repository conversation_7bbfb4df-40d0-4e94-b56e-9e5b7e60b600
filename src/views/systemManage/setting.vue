<template>
  <div class="app-container">
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>网站信息</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="webConfig.cardShow=!webConfig.cardShow">
          <i class="el-icon-arrow-up" v-show="webConfig.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!webConfig.cardShow"></i>
          {{ webConfig.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button style="float:right" size="small"
                   @click="clickSaveBtn('webConfig',webConfig.config,'webConfigManage')">保存
        </el-button>
      </div>
      <div class="container" v-show="webConfig.cardShow">
        <el-form ref="webConfigManage" :model="webConfig.config" :rules="webConfig.formRules" label-width="120px">
          <el-form-item label="网站Logo:" prop="logoUrl">
            <erp-uploader-one-pic style="float:left;" :img-in="webConfig.config.logoUrl"
                                  uploader-id="logoUrl"
                                  uploader-title="" :uploader-size="[200,100]" :pixel-limit="[550,50]"
                                  :size-limit="1024"
                                  @uploadSuccess="data=>fileUpload(data,webConfig.config,'')"
                                  @afterDelete="data=>fileDelete(data,webConfig.config,'')"></erp-uploader-one-pic>
          </el-form-item>
          <el-form-item label="首页轮播图:" prop="indexFocus">
            <div class="button-box" style="margin-bottom: 10px">
              <el-button type="success" @click="WebInfoMethods().clickAddFocusBtn()">增加轮播图</el-button>
            </div>
            <template v-for="(item,index) in webConfig.config.indexFocus">
              <erp-uploader-one-pic style="float:left;margin-right: 15px" :img-in="item.image"
                                    uploader-id="image"
                                    uploader-title="" :uploader-size="[200,100]" :pixel-limit="[1920,500]"
                                    :size-limit="2048"
                                    @uploadSuccess="data=>fileUpload(data,webConfig.config.indexFocus[index],'')"
                                    @afterDelete="data=>WebInfoMethods().clickDeleteFocusBtn(data,index)"></erp-uploader-one-pic>
            </template>

          </el-form-item>
          <el-form-item label="网站名称:" prop="webName">
            <el-input v-model="webConfig.config.webName"></el-input>
          </el-form-item>
          <el-form-item label="学校名称:" prop="schoolName">
            <el-input v-model="webConfig.config.schoolName"></el-input>
          </el-form-item>
          <el-form-item label="学校简介:" prop="schoolDesText">
            <tinymce
              id="tinymce_schoolDesText"
              ref="tinymce_schoolDesText"
              v-model="webConfig.config.schoolDesText"
              :height="300"
            />
          </el-form-item>
          <el-form-item label="网站备案号:" prop="beiAnText">
            <el-input v-model="webConfig.config.beiAnText"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px">
      <div slot="header" class="clearfix">
        <span>系统设置</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="systemConfig.cardShow=!systemConfig.cardShow">
          <i class="el-icon-arrow-up" v-show="systemConfig.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!systemConfig.cardShow"></i>
          {{ systemConfig.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button style="float:right" size="small"
                   @click="clickSaveBtn('systemConfig',systemConfig.config,'systemConfigManage')">保存
        </el-button>
      </div>
      <div class="container" v-show="systemConfig.cardShow">
        <el-form ref="systemConfigManage" :model="systemConfig.config" :rules="systemConfig.formRules"
                 label-width="140px">
          <el-form-item label="平台用途:" prop="platformFor">
            <el-alert title="如果用作校级大平台，1、前台导航显示各个学院导航入口。" style="margin-bottom: 10px"></el-alert>
            <el-select v-model="systemConfig.config.platformFor">
              <el-option value="school" label="校级大平台" key="school"></el-option>
              <el-option value="college" label="院级小平台" key="college"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="校外用户注册功能:" prop="regFunction">
            <el-select v-model="systemConfig.config.regFunction">
              <el-option value="open" label="开放" key="open"></el-option>
              <el-option value="close" label="关闭" key="close"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="注册用户默认启用:" prop="regUserEnable">
            <el-alert title="如果默认不启用，需管理员或教秘到行政班管理-校外班级中查找相关用户，启用该用户后才能登录系统。" style="margin-bottom: 10px"></el-alert>
            <el-select v-model="systemConfig.config.regUserEnable">
              <el-option value="true" label="是" key="true"></el-option>
              <el-option value="false" label="否" key="false"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px" class="introduce-box">
      <div slot="header" class="clearfix">
        <span>平台介绍设置</span>
        <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                   @click="introduceConfig.cardShow=!introduceConfig.cardShow">
          <i class="el-icon-arrow-up" v-show="introduceConfig.cardShow"></i>
          <i class="el-icon-arrow-down" v-show="!introduceConfig.cardShow"></i>
          {{ introduceConfig.cardShow ? '收起' : '展开' }}
        </el-button>
        <el-button style="float:right" size="small"
                   @click="clickSaveBtn('introduceConfig',introduceConfig.config,'introduceConfigManage')">保存
        </el-button>
      </div>
      <div class="container" v-show="introduceConfig.cardShow">
        <div style="margin-bottom: 15px" class="flex flex-start">
          <el-button type="primary" size="normal" @click="IntroduceMethods().clickAddBtn()">新增栏目</el-button>
          <span class="flex flex-start">
              <span style="font-size: 13px;color: #666;margin-left: 15px">介绍导航名称：</span>
             <el-input v-model="introduceConfig.config.name" placeholder="介绍导航名称"
                       style="width: 200px;margin-left: 5px"></el-input>
          </span>

        </div>
        <el-tabs tab-position="left"
                 @tab-click="v=>IntroduceMethods().clickTab(v)"
                 v-model="introduceConfig.tabId">
          <el-tab-pane :label="item.name" v-for="(item,index) in introduceConfig.config.tabs">
            <div class="tools">
              <el-button type="primary" size="small" @click="IntroduceMethods().clickChangeNameBtn(index)">改名
              </el-button>
              <el-button type="danger" size="small" @click="IntroduceMethods().clickDeleteBtn(index)">删除</el-button>
              <el-switch
                style="margin-left: 15px"
                v-model="item.showNav"
                active-text="导航显示"
                inactive-text="导航不显示"
                active-color="#13ce66"
                inactive-color="#ff4949">
              </el-switch>
            </div>
            <tinymce
              :id="'tinymce_'+item.id"
              :ref="'tinymce_'+item.id"
              v-model="item.text"
              :height="300"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-card>
    <el-card style="margin-bottom: 20px" class="link-box">
      <div slot="header" class="clearfix">
        <div>
          <span>友情链接</span>
          <el-button style="margin-left: 10px; padding: 3px 0" type="text"
                     @click="linkConfig.cardShow=!linkConfig.cardShow">
            <i class="el-icon-arrow-up" v-show="linkConfig.cardShow"></i>
            <i class="el-icon-arrow-down" v-show="!linkConfig.cardShow"></i>
            {{ linkConfig.cardShow ? '收起' : '展开' }}
          </el-button>
          <el-button style="float:right" size="small"
                     @click="clickSaveBtn('linkConfig',linkConfig.config,'linkConfigManage')">保存
          </el-button>
        </div>
        <div style="font-size: 13px;color: #999;margin-top: 10px">
          图片大小200KB以内，图片分辨率290px X 140px
        </div>
      </div>
      <div class="card-container success-container" v-show="linkConfig.cardShow">
        <el-table :data="linkConfig.config.list" border fit
                  highlight-current-row
                  style="width: 100%;">
          <el-table-column label="名称" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.name"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="地址" align="center">
            <template slot-scope="scope">
              <el-input v-model="scope.row.url"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="图片" align="center" width="300px">
            <template slot-scope="scope">
              <erp-uploader-one-pic :key="scope.row.id" v-if="scope.$index!==linkConfig.config.list.length-1"
                                    :img-in="scope.row.img"
                                    :uploader-id="'success_'+scope.row.id"
                                    @afterDelete="data=>LinkMethods().afterDelete(data)"
                                    :show-des="false"
                                    :uploader-size="[188,100]" :pixel-limit="[300,160]"
                                    :size-limit="200"
                                    @uploadSuccess="data=>LinkMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
              <erp-uploader-one-pic :key="linkConfig.config.list[linkConfig.config.list.length-1].id"
                                    v-if="scope.$index===linkConfig.config.list.length-1"
                                    :img-in="linkConfig.config.list[linkConfig.config.list.length-1].img"
                                    :uploader-id="'success_'+linkConfig.config.list[linkConfig.config.list.length-1].id"
                                    @afterDelete="data=>LinkMethods().afterDelete(data)"
                                    :show-des="false"
                                    :uploader-size="[188,100]" :pixel-limit="[300,160]"
                                    :size-limit="200"
                                    @uploadSuccess="data=>LinkMethods().fileUploadSuccess(data)"></erp-uploader-one-pic>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="250"
                           class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" round
                         v-show="scope.$index!==0&&scope.$index!==linkConfig.config.list.length-1"
                         @click="LinkMethods().clickUpBtn(scope.row,scope.$index)">上移
              </el-button>
              <el-button type="primary" size="mini" round
                         v-show="scope.$index!==linkConfig.config.list.length-1&&scope.$index!==linkConfig.config.list.length-2"
                         @click="LinkMethods().clickDownBtn(scope.row,scope.$index)">下移
              </el-button>
              <el-button type="danger" size="mini" round v-show="scope.$index!==linkConfig.config.list.length-1"
                         @click="LinkMethods().clickDelBtn(scope.row,scope.$index)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>

import Tinymce from "@/components/Tinymce";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import {msg_confirm, msg_confirm_choose, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {ConfigModel} from "@/model/ConfigModel";
import {find_obj_from_arr_by_id, randomNumber} from "@/utils/common";
import $ from "jquery"
import Sortable from "sortablejs";

export default {
  name: "systemSetting",
  components: {Tinymce, erpUploaderOnePic},
  data() {
    return {
      // 网站信息
      webConfig: {
        cardShow: false,
        config: {},
        formRules: {
          'logoUrl': {required: true, message: '请上传网站Logo图片', trigger: 'change'},
          'webName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 25, "网站名称"),
            trigger: 'blur'
          },
          'schoolName': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 30, "学校名称"),
            trigger: 'blur'
          },
          'schoolDesText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 1500, "学校简介"),
            trigger: 'blur'
          },
          'beiAnText': {
            required: true,
            validator: (r, v, c) => validateMaxLength(r, v, c, 20, "网站备案号"),
            trigger: 'blur'
          },
        }
      },
      // 系统设置
      systemConfig: {
        cardShow: false,
        config: {},
        formRules: {
          'platformFor': {required: true, message: '请选择', trigger: 'change'},
          'regFunction': {required: true, message: '请选择', trigger: 'change'},
          'regUserEnable': {required: true, message: '请选择', trigger: 'change'},
        }
      },
      // 平台介绍
      introduceConfig: {
        cardShow: false,
        config: {
          tabs: []
        },
      },
      // 友情链接
      linkConfig: {
        cardShow: false,
        config: {
          list: [{
            id: new Date().getTime(),
          }]
        },
      },
    }
  },
  async mounted() {
    // 获取配置信息
    ConfigModel.getConfig("webConfig").then(res => {
      let webConfig=JSON.parse(res)
      this.$set(this.webConfig, "config", webConfig)
    })
    ConfigModel.getConfig("systemConfig").then(res => {
      this.$set(this.systemConfig, "config", JSON.parse(res))
    })
    ConfigModel.getConfig("introduceConfig").then(res => {
      this.$set(this.introduceConfig, "config", JSON.parse(res))
    })
    ConfigModel.getConfig("linkConfig").then(res => {
      this.$set(this.linkConfig, "config", JSON.parse(res))
    })
  },
  methods: {
    // 点击保存按钮
    async clickSaveBtn(field, config, formRef) {
      if (this.$refs[formRef]) {
        this.$refs[formRef].validate(async validate => {
          if (validate) {
            // 保存接口
            if (await ConfigModel.editConfig(field, config)) {
              msg_success("保存成功")
            }
          }
        });
      } else {
        if (formRef === "linkConfigManage") {
          if (!this.LinkMethods().beforeSaveCheck()) {
            return
          }
        }
        if (formRef === "introduceConfigManage") {
          if (!this.introduceConfig.config.name) {
            msg_err("请输入介绍导航名称！")
            return
          }
          if (this.introduceConfig.config.name.length > 10) {
            msg_err("介绍导航名称最多10个字！")
            return
          }
        }
        // 保存接口
        if (await ConfigModel.editConfig(field, config)) {
          msg_success("保存成功")
        }
      }
    },
    // 文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 网站信息方法集
    WebInfoMethods(){
      let $this=this;
      return{
        // 点击新增轮播图按钮
        clickAddFocusBtn(){
          $this.webConfig.config.indexFocus.push({
            image:""
          })
        },
        // 点击删除轮播按钮
        clickDeleteFocusBtn(index){
          $this.webConfig.config.indexFocus.splice(index,1)
        }
      }
    },
    // 平台介绍方法集
    IntroduceMethods() {
      let $this = this;
      return {
        // 点击新增tab按钮
        async clickAddBtn() {
          let name = await msg_input("输入栏目名称", "请输入要新增的栏目名称", "")
          if (!name) {
            msg_err("请输入要新增的栏目名称！")
            return
          }
          if (name.length > 15) {
            msg_err("栏目名称最多15个字！")
            return
          }
          $this.introduceConfig.config.tabs.push({
            name: name,
            showNav: false,
            text: "",
            id: String(new Date().getTime())
          })
        },
        // 点击tab
        clickTab() {

        },
        // 点击改名按钮
        async clickChangeNameBtn(index) {
          let name = await msg_input("修改栏目名称", "请输入要修改的栏目名称", $this.introduceConfig.config.tabs[index]["name"])
          if (!name) {
            msg_err("请输入要修改的栏目名称！")
            return
          }
          if (name.length > 15) {
            msg_err("栏目名称最多15个字！")
            return
          }
          $this.$set($this.introduceConfig.config.tabs[index], "name", name);
        },
        // 点击删除tab
        async clickDeleteBtn(index) {
          if (await msg_confirm("确认要删除此栏目吗？")) {
            $this.introduceConfig.config.tabs.splice(index, 1);
          }
        }
      }
    },
    // 成功案例相关
    LinkMethods() {
      let $this = this;
      return {
        // 点击删除按钮
        clickDelBtn(edit, index) {
          $this.linkConfig.config.list.splice(index, 1)
        },
        // 点击上移按钮
        clickUpBtn(edit, index) {
          let arr = []
          arr = $this.linkConfig.config.list
          arr[index] = arr.splice(index - 1, 1, arr[index])[0]
          $this.$set($this.linkConfig.config, "list", arr)
        },
        // 点击下移按钮
        clickDownBtn(edit, index) {
          let arr = []
          arr = $this.linkConfig.config.list
          arr[index] = arr.splice(index + 1, 1, arr[index])[0]
          $this.$set($this.linkConfig.config, "list", arr)
        },
        fileUploadSuccess(data) {
          console.log(data)
          let id = Number(data[0].replace("success_", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.linkConfig.config.list)[0]
          console.log(index)
          if (!$this.linkConfig.config.list[index].hasOwnProperty('img')) {
            $this.linkConfig.config.list.push({
              id: new Date().getTime(),
            })
          }
          $this.$set($this.linkConfig.config.list[index], "img", data[1])
        },
        afterDelete(data) {
          let id = Number(data[0].replace("success_", ""))
          let index = find_obj_from_arr_by_id("id", id, $this.linkConfig.config.list)[0]
          $this.$set($this.linkConfig.config.list[index], "img", "")
        },
        // 保存时检测是否填写完整
        beforeSaveCheck() {
          let result = true
          if ($this.linkConfig.config.list.length < 2) {
            msg_err("至少上传一个成功案例")
            result = false
          }
          // 判断是否填写完整
          $this.linkConfig.config.list.forEach((li, index) => {
            if (index !== $this.linkConfig.config.list.length - 1) {
              if (!li.name || !li.url) {
                msg_err(`第${index + 1}项未填写完整！`)
                result = false
              }
            }
          })
          return result
        }
      }
    },
  }
}
</script>

<style scoped lang="scss">
.introduce-box {
  .tools {
    margin-bottom: 20px;
  }
}
</style>
