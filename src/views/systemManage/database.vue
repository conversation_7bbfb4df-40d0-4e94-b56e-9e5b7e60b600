<template>
  <div class="app-container">
    <!--顶部按钮-->
    <div class="top-tools">
      <div style="font-size: 15px;color: #888;text-align: center">
        <p>系统将在每日深夜自动备份数据库，您也可以点击"立即备份"按钮就行手动备份</p>
        <p>如需要恢复数据，为了保证数据安全，请联系系统运维人员处理</p>
      </div>
      <div style="text-align: right">
        <el-button class="el-button" type="success" style="background-color: #67C23A;border-color:#67C23A"
                   @click="ListMethods().clickBackupBtn()"
        >立即备份
        </el-button>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="文件名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery, objectToLVArr} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {HelpQuestionModel} from "@/model/HelpQuestionModel";
import Tinymce from "@/components/Tinymce";
import {DatabaseModel} from "@/model/DatabaseModel";

export default {
  name: "systemDatabase",
  components: {ListSearchFilter, Tinymce},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增标签",
        type: "add",
        filter: {
          helpQuestionType: objectToLVArr(enums.helpQuestionType),
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateMaxLength(r, v, c, 50, "问题标题"), trigger: 'blur'},
          'type': {required: true, message: '请选择问题类型', trigger: 'change'},
          'content': {required: true, message: '请输入问题内容', trigger: 'change'},
        },
      },
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList({})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(query) {
          $this.lists.loading = true;
          let list = await DatabaseModel.getList(query)
          list.sort(function (a, b) {// 按文件名创建时间倒序排列
            return b.createTime - a.createTime;
          });
          $this.lists.list = list;
          $this.lists.loading = false
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {

        },
        // 点击立即备份按钮
        async clickBackupBtn() {
          let result = await DatabaseModel.backupNow()
          if (result) {
            msg_success("备份成功")
          }
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
