<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickExportBtn()"
                     :loading="exportObject.doing">导出成绩
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="模块名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.moduleName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="功能名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.methodName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="账号id" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.userId }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.ip }}</span>
        </template>
      </el-table-column>
      <el-table-column label="时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column label="信息" align="center">-->
      <!--        <template slot-scope="scope">-->

      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--导出弹窗-->
    <el-dialog
      title="导出成绩"
      :visible.sync="exportObject.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="exportObjectForm" :model="exportObject.edit" :rules="exportObject.formRules">
          <el-form-item label="总共数量:" prop="totalNum">
            <span>{{ lists.pages.totalElements }}</span>
          </el-form-item>
          <el-form-item label="从第几条开始:" prop="startNum">
            <el-input v-model="exportObject.edit.startNum" type="number"></el-input>
          </el-form-item>
          <el-form-item label="到第几条结束:" prop="endNum">
            <el-input v-model="exportObject.edit.endNum" type="number"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="exportObject.dialog=false">取 消</el-button>
        <el-button type="success" @click="ListMethods().clickExportStartBtn()">开始导出</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {byteFormat, dateFormat, stringBrFilter} from "@/filters";
import {
  date_format,
  getQuery,
  objectToLVArr
} from "@/utils/common";
import {LogModel} from "@/model/LogModel";
import {CourseScoreModel} from "@/model/CourseScoreModel";
import {msg_err} from "@/utils/ele_component";

export default {
  name: "systemLog",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles
    })
  },
  filters: {dateFormat, byteFormat},
  data: function () {
    let $this = this;
    return {
      window: window,
      getQuery: getQuery,
      stringBrFilter: stringBrFilter,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: 'IP',
              key: 'ip',
              value: ''
            },
            {
              type: 'input',
              label: '模块名称',
              key: 'moduleName',
              value: ''
            },
            {
              type: 'input',
              label: '功能名称',
              key: 'methodName',
              value: ''
            },
          ],
          filter: [
            {
              type: 'timeHourRange',
              label: ['开始时间', '结束时间', '创建时间'],
              value: '',
              data: [],
              change: function (value) {
              },
              format: function (value) {
                return {
                  "$and": [
                    {
                      createTime: {
                        '$gte': value[0].getTime()
                      }
                    },
                    {
                      createTime: {
                        '$lte': value[1].getTime()
                      }
                    }]
                }
              }
            }
          ],
        }
      },
      // 导出日志
      exportObject: {
        doing: false,
        edit: {},
        dialog: false,
      },
    }
  },
  async mounted() {
    await this.ListMethods().initFilter();
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击批量导出按钮
        async clickExportBtn() {
          $this.exportObject.dialog = true
        },
        // 点击开始导出成绩按钮
        async clickExportStartBtn() {
          let totalNumber = $this.lists.pages.totalElements
          let startNum = parseInt($this.exportObject.edit.startNum)
          let endNum = parseInt($this.exportObject.edit.endNum)
          // 判断输入合法性
          if (startNum > 0 && startNum <= totalNumber && startNum <= endNum) {
            if (endNum > 0 && endNum <= totalNumber) {
              let query = $this.lists.queryLast;
              await LogModel.exportList(startNum, endNum, query)
              $this.exportObject.dialog = false
            } else {
              msg_err("结束数字不合法！")
            }
          } else {
            msg_err("开始数字不合法！")
          }
        },
        // 初始化筛选
        async initFilter() {

        },
        // 点击搜索重置按钮
        clickCleanBtn() {

        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          $this.lists.queryLast = JSON.parse(JSON.stringify(query));
          if(query["$and"]){
            $this.lists.queryLast["startTime"]=($this.lists.searchFilter.filter[0]["value"][0]).getTime()
            $this.lists.queryLast["endTime"]=($this.lists.searchFilter.filter[0]["value"][1]).getTime()
          }
          [list, $this.lists.pages] = await LogModel.getPageList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">

</style>
