<template>
  <div class="page-course">
    <!--课程列表-->
    <div class="curriculum-list content-container">
      <el-card class="filter-card" :body-style="{padding:'24px'}" style="width: 99.4%;">
        <div class="filter-box">
          <div class="filter-header">
            <span class="filter-title">
              <font-awesome-icon icon="graduation-cap" />
              学科分类
            </span>
            <p class="filter-subtitle">选择您感兴趣的学科领域</p>
           
          </div>
          <div class="subject-filter-box flex flex-start flex-wrap">
            <div 
              :class="['subject-item', { 'active': courseSubject.activeId === item.value }]" 
              v-for="item in courseSubject.list"
              :key="item.value"
              @click="clickOneCourseSubject(item.value)"
            >
              <font-awesome-icon icon="book-open" class="subject-icon" />
              <span class="subject-text">{{ item.label }}</span>
              <div class="subject-indicator"></div>
            </div>
          </div>
        </div>
      </el-card>
      <!-- 课程列表 -->
      <div class="course-grid" v-loading="loading">
        <transition-group name="fade" tag="div" class="grid-container">
          <div
              v-for="item in course.list"
              :key="item.courseId"
              class="course-card"
              @click="clickOneCourse(item)"
          >
            <div class="card-image">
              <img :src="item.avatarUrl" alt="课程封面" class="course-cover">
              <div class="image-overlay"></div>
            </div>
            <div class="card-content">
              <div class="card-header">
                <el-tooltip effect="dark" :content="item.name" placement="top">
                  <h3 class="course-title">{{ item.name }}</h3>
                </el-tooltip>
              </div>
              <div class="stats-container">
                <div class="stat-item">
                  <font-awesome-icon icon="users" class="stat-icon" />
                  <span>{{ item.userNumber }}人学习</span>
                </div>
                <div class="stat-item">
                  <font-awesome-icon icon="comments" class="stat-icon" />
                  <span>{{ item.commentNumber }}条评论</span>
                </div>
              </div>
              <div class="card-footer">
                <div class="course-type">
                  <font-awesome-icon icon="award" class="type-icon" />
                  {{ item.courseType }}
                </div>
                <div class="course-college">{{ item.collegeName }}</div>
              </div>
            </div>
          </div>
        </transition-group>
      </div>
      <el-empty description="暂无数据" v-if="course.list.length===0 && !loading"></el-empty>
    </div>
  </div>
</template>

<script>
import {mapState} from "vuex";
import {CourseSubjectModel} from "../model/CourseSubjectModel";
import {CommonModel} from "../model/CommonModel";
import {CourseModel} from "../model/CourseModel";

export default {
  name: "Course",
  computed: {
    ...mapState({
      webConfig: state => state.webConfig,
    })
  },
  data() {
    return {
      courseSubject: {
        list: [],
        object: [],
        activeId: ""
      },
      course: {
        list: []
      },
      loading: false
    }
  },
  async mounted() {
    // 获取学科列表
    await this.getCourseSubjectList()
    // 默认获取全部首页推荐课程
    this.clickOneCourseSubject("")
  },
  methods: {
    // 获取学科列表
    async getCourseSubjectList() {
      let list = await CourseSubjectModel.getList({})
      let listResult = CommonModel.generateListFilterOptions("name", "courseSubjectId", list, true)
      this.$set(this.courseSubject, "list", listResult[0])
      this.$set(this.courseSubject, "object", listResult[1])
    },
    // 点击某个学科的首页推荐课程列表
    async clickOneCourseSubject(courseSubjectId) {
      try {
        this.loading = true
        // 获取对应的课程列表
        let courseList = await CourseModel.getList({
          courseSubjectId: courseSubjectId === "" ? undefined : courseSubjectId,
          opened: true,
        })
        this.$set(this.course, "list", courseList)
        // 设置active
        this.$set(this.courseSubject, "activeId", courseSubjectId)
      } finally {
        this.loading = false
      }
    },
    // 点击某个课程
    clickOneCourse(item) {
      this.$router.push({
        name: "CourseInfo",
        query: {
          "id": item.courseId
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
@import '../style/app.less';

.page-course {
  margin-top: 20px;
}

.curriculum-list {
  margin-bottom: 30px;

  .filter-card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 16px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);

    &:hover {
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
      transform: translateY(-1px);
    }
  }

  .filter-box {
    .filter-header {
      margin-bottom: 20px;
      padding-bottom: 16px;
      border-bottom: 2px solid #f0f2f5;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 60px;
        height: 2px;
        background: linear-gradient(90deg, #4093f9, lighten(#4093f9, 20%));
        border-radius: 1px;
      }

      .filter-title {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 4px;

        svg {
          margin-right: 8px;
          color: #4093f9;
          font-size: 16px;
        }
      }

      .filter-subtitle {
        font-size: 13px;
        color: #666;
        margin-left: 24px;
      }
    }

    .subject-filter-box {
      gap: 12px;
      margin-top: 4px;
    }

    .subject-item {
      position: relative;
      cursor: pointer;
      padding: 12px 20px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 500;
      color: #555;
      background: #f8f9fa;
      border: 2px solid transparent;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      overflow: hidden;
      min-width: 80px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      .subject-icon {
        color: #4093f9;
        font-size: 12px;
        transition: all 0.3s ease;
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.6s ease;
      }

      .subject-text {
        position: relative;
        z-index: 2;
        transition: all 0.3s ease;
      }

      .subject-indicator {
        position: absolute;
        bottom: 0;
        left: 50%;
        width: 0;
        height: 3px;
        background: linear-gradient(90deg, #4093f9, lighten(#4093f9, 15%));
        border-radius: 2px 2px 0 0;
        transform: translateX(-50%);
        transition: width 0.3s ease;
      }

      &:hover {
        color: #4093f9;
        background: #fff;
        border-color: fade(#4093f9, 20%);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        .subject-icon {
          transform: scale(1.05);
        }

        &::before {
          left: 100%;
        }

        .subject-indicator {
          width: 60%;
        }
      }

      &.active {
        color: #fff !important;
        background: #409eff !important;
        border: 2px solid transparent !important;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3) !important;

        .subject-icon {
          color: #fff;
          transform: scale(1.1);
        }

        .subject-indicator {
          width: 100%;
          background: rgba(255, 255, 255, 0.8);
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4) !important;
        }
      }
    }
  }

  .course-grid {
    position: relative;
    min-height: 400px;
    margin-top: 20px;

    .grid-container {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
      gap: 30px;
      padding: 10px 0;
    }

    .course-card {
      background: white;
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
      transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
      cursor: pointer;
      position: relative;
      height: 100%;
      display: flex;
      flex-direction: column;

      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);

        .course-cover {
          transform: scale(1.05);
        }
      }

      .card-image {
        position: relative;
        height: 180px;
        overflow: hidden;

        .course-cover {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.5s ease;
        }

        .image-overlay {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 60%;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
        }
      }

      .card-content {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;

        .card-header {
          .course-title {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            line-height: 1.4;
            margin: 0;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 45px;
          }
        }

        .stats-container {
          display: flex;
          gap: 20px;
          margin-bottom: 20px;
          padding-bottom: 15px;
          border-bottom: 1px solid #f0f4f9;

          .stat-item {
            display: flex;
            align-items: center;
            font-size: 13px;
            color: #666;

            .stat-icon {
              color: #4093f9;
              margin-right: 5px;
              font-size: 12px;
            }

            i {
              color: #4093f9;
              margin-right: 5px;
              font-size: 14px;
            }
          }
        }

        .card-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;

          .course-type {
            background: rgba(67, 126, 235, 0.1);
            color: #4093f9;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 4px;

            .type-icon {
              font-size: 10px;
            }
          }

          .course-college {
            font-size: 13px;
            color: #777;
            max-width: 120px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
    }
  }
}

// 动画效果
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

@media (max-width: 768px) {
  .curriculum-list .course-grid .grid-container {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}
</style>