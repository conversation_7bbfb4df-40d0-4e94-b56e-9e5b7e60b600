<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()" style="background-color: #67C23A;border-color:#67C23A">新增班级</el-button>
          <el-button class="el-button" type="success"
                     @click="importStudent.dialog=true">批量导入学生
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属专业" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.majorName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所属年级" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.gradeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学生人数" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.studentNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     @click="ListMethods().clickStudentListBtn(scope.row)">学生列表</el-button>
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <!--          <el-button type="danger" size="mini" round>删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="班级名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="所属专业:" prop="majorId">
            <el-select
              style="width: 100%"
              v-model="entityInfo.edit.majorId"
              filterable
              remote
              reserve-keyword
              placeholder="请选择专业"
              :remote-method="v=>EntityMethods().queryMajor(v)"
              :loading="entityInfo.filter.majorList.loading"
              @change="v=>EntityMethods().onMajorChange(v)">
              <el-option
                v-for="item in entityInfo.filter.majorList.list"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属年级:" prop="gradeId">
            <el-select v-model="entityInfo.edit.gradeId" @change="v=>EntityMethods().onGradeChange(v)"
                       style="width: 100%">
              <el-option v-for="item in entityInfo.filter.grade" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
    <!--学生导入input-->
    <input
      id="importStudentFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importStudentFileChange(files)}"
    >
    <!--学生导入弹窗-->
    <el-dialog
      title="批量导入学生"
      :visible.sync="importStudent.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">学生批量导入列表.xlsx</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importStudent.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importStudent.doing"
                   @click="ListMethods().clickImportStudentBtn()">导入学生
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {validateMaxLength} from "@/utils/validate";
import {ClazzModel} from "@/model/ClazzModel";
import {MajorModel} from "@/model/MajorModel";
import {CommonModel} from "@/model/CommonModel";
import {GradeModel} from "@/model/GradeModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";

export default {
  name: "clazzManage",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {

      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '班级名称',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [
            {
              type: 'selectRemote',
              label: '所属专业',
              placeholder: "",
              key: 'majorId',
              width: "270px",
              value: '',
              data: [],
              dataObject: [],
              dataOrigin: [],// 存储数据库返回的默认列表
              searchFunction: async function (query) {
                query = `{"name":{'$regex': ".*${query}.*"}}`
                let list = await MajorModel.getList(query)
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    label: li.name,
                    value: li.majorId
                  })
                })
                $this.$set($this.lists.searchFilter.filter[0], "data", listResult)
              },
              async change(v) {
                let query = `{}`
                let list = await MajorModel.getList(query)
                let listResult = []
                list.forEach(li => {
                  listResult.push({
                    label: li.name,
                    value: li.majorId
                  })
                })
                $this.$set($this.lists.searchFilter.filter[0], "data", listResult)
              }
            },
            {
              type: 'select',
              label: '所属年级',
              key: 'gradeId',
              value: '',
              data: [],
              dataObject: {},
              dataOrigin: [],// 存储数据库返回的默认列表
              change: function (value) {

              }
            },
          ],
        }

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增班级",
        type: "add",
        filter: {
          majorList: {
            list: [],
            loading: false,
          }
        },
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateName(r, v, c, 20, "班级名称"), trigger: 'blur'},
          'majorId': {required: true, message: '请选择所属专业', trigger: 'change'},
          'gradeId': {required: true, message: '请选择所属年级', trigger: 'change'},
        },
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      }
    }
  },
  async mounted() {
    // 初始化专业列表
    await this.EntityMethods().initFilter()
    this.ListMethods().initFilter()
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          [list, $this.lists.pages] = await ClazzModel.getPageList(page - 1, size, "", query)
          // 遍历list获取必要信息
          list.forEach(li => {
            // 获取专业名称
            li.majorName = $this.entityInfo.filter["majorObject"][li.majorId]
            // 获取年级名称
            li.gradeName = $this.entityInfo.filter["gradeObject"][li.gradeId]
          })
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增班级"
          $this.entityInfo.edit = {};
          $this.EntityMethods().initFilter();
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑班级"
          $this.EntityMethods().initFilter();
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 初始化筛选列表
        async initFilter() {
          // 初始化专业搜索
          $this.lists.searchFilter.filter[0].change("")
          // 获取年级列表
          let gradeList = await GradeModel.getList({})
          let generateListFilter0 = CommonModel.generateListFilterOptions('name', 'gradeId', gradeList)
          $this.$set($this.lists.searchFilter.filter[1], "data", generateListFilter0[0])
          $this.$set($this.lists.searchFilter.filter[1], "dataObject", generateListFilter0[1])
          $this.$set($this.lists.searchFilter.filter[1], "dataOrigin", gradeList)
        },
        // 点击学生列表按钮
        clickStudentListBtn(entity) {
          $this.$router.push(`/user/student?clazzId=${entity.clazzId}&clazzName=${entity.name}&majorName=${entity.majorName}&gradeName=${entity.gradeName}`);
        },
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("https://resouce.ttt.com/bigPlatform/%E5%AD%A6%E7%94%9F%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "学生批量导入列表.xlsx")
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await ClazzModel.importStudents(file).catch(err => {
            $this.importStudent.dialog = false
            msg_err("批量导入学生失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('批量导入学生成功')
            $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
          }
          $this.importStudent.doing = false
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        onMajorChange() {
          this.queryMajor("")
          $this.$forceUpdate();
        },
        // 专业查询
        async queryMajor(v) {
          $this.$set($this.entityInfo.filter.majorList, "loading", true)
          let query = `{"name":{'$regex': ".*${v}.*"}}`
          let list = await MajorModel.getList(query)
          let listResult = []
          list.forEach(li => {
            listResult.push({
              value: li.majorId,
              label: li.name
            })
          })
          $this.$set($this.entityInfo.filter.majorList, "list", listResult)
          $this.$set($this.entityInfo.filter.majorList, "loading", false)
        },
        onGradeChange() {
          $this.$forceUpdate();
        },
        // 初始化筛选
        async initFilter() {
          // 获取专业列表
          let majorList = await MajorModel.getList({})
          let majorFilterList = CommonModel.generateListFilterOptions("name", "majorId", majorList, false)
          this.queryMajor("")
          $this.$set($this.entityInfo.filter, "majorObject", majorFilterList[1])
          // 获取年级列表
          let gradeList = await GradeModel.getList({})
          let gradeFilterList = CommonModel.generateListFilterOptions("name", "gradeId", gradeList, false)
          $this.$set($this.entityInfo.filter, "grade", gradeFilterList[0])
          $this.$set($this.entityInfo.filter, "gradeObject", gradeFilterList[1])
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名班级
              let listResult = await ClazzModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的班级，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该班级吗？')) {
                let result = await ClazzModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名标签
              let listResult = await ClazzModel.getList({
                name: $this.entityInfo.edit.name,
                clazzId: {"$ne": $this.entityInfo.edit.clazzId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的班级，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该班级吗？')) {
                // 删除不必要po
                delete $this.entityInfo.edit.studentNumber
                let result = await ClazzModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
