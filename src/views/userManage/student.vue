<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <div class="clazz-name" v-if="clazzName!=='校外人员'">{{ gradeName }} {{ majorName }} {{ clazzName }}</div>
      <div class="clazz-name" v-if="clazzName==='校外人员'">{{ clazzName }}</div>
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"
                     @click="ListMethods().clickAddBtn()">新增学生
          </el-button>
          <el-button class="el-button" type="success"
                     @click="importStudent.dialog=true" :loading="importStudent.doing">批量导入学生
          </el-button>
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickExportBtn()" :loading="exportStudent.doing">批量导出学生
          </el-button>
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="学号/账号" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.account }}</span>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="性别" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.sex }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.deleted === 0 ? "已启用" : "已禁用" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="身份" align="center" width="100" v-if="clazzName==='校外人员'">
        <template slot-scope="scope">
          <span>{{ scope.row.identity }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="text" size="mini" round
                     @click="TransferStudentMethods().clickTransferBtn(scope.row)">转移
          </el-button>
          <el-button type="text" size="mini" round
                     @click="TransferStudentMethods().clickResetPasswordBtn(scope.row)">重置密码
          </el-button>
          <el-button type="text" size="mini" round v-if="scope.row.deleted===1"
                     @click="TransferStudentMethods().clickEnableUserBtn(scope.row)">启用用户
          </el-button>
          <el-button type="text" size="mini" round v-if="scope.row.deleted===0"
                     @click="TransferStudentMethods().clickDisableUserBtn(scope.row)">禁用用户
          </el-button>
          <!--          <el-button type="danger" size="mini" round>删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--实体弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="学号:" prop="account">
            <el-input v-model.trim="entityInfo.edit.account" :disabled="entityInfo.type==='edit'">
              <div slot="suffix" v-if="entityInfo.edit.account">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.account.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="姓名:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="性别:" prop="sex">
            <el-select v-model="entityInfo.edit.sex"
                       style="width: 100%">
              <el-option value="男" label="男" key="男"></el-option>
              <el-option value="女" label="女" key="女"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
    <!--学生导入input-->
    <input
      id="importStudentFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importStudentFileChange(files)}"
    >
    <!--学生导入弹窗-->
    <el-dialog
      title="批量导入学生"
      :visible.sync="importStudent.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">班级学生导入列表.xls</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importStudent.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importStudent.doing"
                   @click="ListMethods().clickImportStudentBtn()">导入学生
        </el-button>
      </div>
    </el-dialog>
    <!--转移班级弹窗-->
    <el-dialog
      title="转移学生到其他班级"
      :visible.sync="transferStudent.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="transferStudentForm" :model="transferStudent.edit"
                 :rules="transferStudent.formRules">
          <el-form-item label="所属学院:" prop="collegeId">
            <el-select v-model="transferStudent.edit.collegeId" @change="v=>TransferStudentMethods().onCollegeChange(v)"
                       style="width: 100%">
              <el-option v-for="item in transferStudent.filter.collegeList" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属专业:" prop="majorId" v-if="transferStudent.edit.collegeId">
            <el-select v-model="transferStudent.edit.majorId" @change="v=>TransferStudentMethods().onMajorChange(v)"
                       style="width: 100%">
              <el-option v-for="item in transferStudent.filter.majorList" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属年级:" prop="gradeId">
            <el-select v-model="transferStudent.edit.gradeId" @change="v=>TransferStudentMethods().onGradeChange(v)"
                       style="width: 100%">
              <el-option v-for="item in transferStudent.filter.gradeList" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属班级:" prop="clazzId" v-if="transferStudent.edit.majorId&&transferStudent.edit.gradeId">
            <el-select v-model="transferStudent.edit.clazzId" @change="v=>TransferStudentMethods().onClazzChange(v)"
                       style="width: 100%">
              <el-option v-for="item in transferStudent.filter.clazzList" :value="item.value" :label="item.label"
                         :key="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="transferStudent.dialog=false">取 消</el-button>
        <el-button type="success" @click="TransferStudentMethods().clickSureBtn()">确认转移</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {UserModel} from "@/model/UserModel";
import {ClazzModel} from "@/model/ClazzModel";
import {MajorModel} from "@/model/MajorModel";
import {GradeModel} from "@/model/GradeModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import {CollegeModel} from "@/model/CollegeModel";
import {CommonModel} from "@/model/CommonModel";
import {ConfigModel} from "@/model/ConfigModel";

export default {
  name: "studentManage",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    }),
    clazzId() {
      return this.$route.query["clazzId"]
    },
    clazzName() {
      return this.$route.query["clazzName"]
    },
    majorName() {
      return this.$route.query["majorName"]
    },
    gradeName() {
      return this.$route.query["gradeName"]
    },
  },

  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {

      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学号',
              key: 'account',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '姓名',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [],
        }

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增学生",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateName(r, v, c, 30, "姓名"), trigger: 'blur'},
          'account': {required: true, validator: (r, v, c) => validateName(r, v, c, 30, "姓名"), trigger: 'blur'},
          'sex': {required: true, message: '请选择性别', trigger: 'change'},
        },
      },
      // 导入学生
      importStudent: {
        dialog: false,
        doing: false,
      },
      // 导出学生
      exportStudent: {
        doing: false,
      },
      // 转移学生
      transferStudent: {
        dialog: false,
        student: {},
        edit: {},
        filter: {
          gradeList: [],
          majorList: [],
          collegeList: [],
          clazzList: []
        },
        // 输入检测
        formRules: {
          'gradeId': {required: true, message: '请选择所属年级', trigger: 'change'},
          'collegeId': {required: true, message: '请选择所属学院', trigger: 'change'},
          'majorId': {required: true, message: '请选择所属专业', trigger: 'change'},
          'clazzId': {required: true, message: '请选择所属班级', trigger: 'change'},
        },
      },
      // 系统设置
      systemConfig: {}
    }
  },
  async mounted() {
    // 获取配置信息
    ConfigModel.getConfig("systemConfig").then(res => {
      this.$set(this, "systemConfig", JSON.parse(res))
    })
    this.lists.queryBase = {
      clazzId: this.clazzId
    }
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await UserModel.getPageList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增学生"
          $this.entityInfo.edit = {
            sex: "女"
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑学生"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("http://resouce.ttt.com/2021052502/%E5%AD%A6%E7%94%9F%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "班级学生导入列表.xls")
        },
        // 点击了学生导入按钮
        clickImportStudentBtn() {
          const uploader = document.getElementById('importStudentFile')
          uploader.click()
        },
        // 导入学生文件选择
        async importStudentFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importStudentFile').value = ''
          $this.importStudent.doing = true
          // todo
          if (await ClazzModel.importStudentsByClazzId(file, $this.clazzId).catch(err => {
            $this.importStudent.dialog = false
            msg_err("批量导入学生失败")
          })) {
            $this.importStudent.dialog = false
            msg_success('批量导入学生成功')
            $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
          }
          $this.importStudent.doing = false
        },
        // 点击批量导出按钮
        async clickExportBtn() {
          $this.exportStudent.doing = true
          await ClazzModel.exportStudentByClazzId($this.clazzId, `${$this.gradeName}-${$this.majorName}-${$this.clazzName}`)
          $this.exportStudent.doing = false
        }
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有相同学号学生或教师
              let listResult = await UserModel.getList({
                account: $this.entityInfo.edit.account,
              })
              if (listResult.length > 0) {
                let findUser = listResult[0]
                if (findUser.role === "student") {
                  let clazz = await ClazzModel.getOne(findUser.clazzId)
                  let major = await MajorModel.getOne(clazz.majorId)
                  let grade = await GradeModel.getOne(clazz.gradeId)
                  msg_err(`在 ${grade.name} ${major.name} ${clazz.name}中已存在相同学号的学生，请修改后再试！`)
                }
                if (findUser.role === "teacher") {
                  let college = await CollegeModel.getOne(findUser.collegeId)
                  msg_err(`在${college.name}中已存在相同工号的教师，请修改后再试！`)
                }
                return
              }
              if (await msg_confirm('确认要新增该学生吗？')) {
                $this.entityInfo.edit.clazzId = $this.clazzId
                $this.entityInfo.edit.role = "student"
                let result = await UserModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有相同学号学生
              let listResult = await UserModel.getList({
                account: $this.entityInfo.edit.account,
                userId: {"$ne": $this.entityInfo.edit.userId}
              })
              if (listResult.length > 0) {
                let findUser = listResult[0]
                if (findUser.role === "student") {
                  let clazz = await ClazzModel.getOne(findUser.clazzId)
                  let major = await MajorModel.getOne(clazz.majorId)
                  let grade = await GradeModel.getOne(clazz.gradeId)
                  msg_err(`在 ${grade.name} ${major.name} ${clazz.name}中已存在相同学号的学生，请修改后再试！`)
                }
                if (findUser.role === "teacher") {
                  let college = await CollegeModel.getOne(findUser.collegeId)
                  msg_err(`在${college.name}中已存在相同工号的教师，请修改后再试！`)
                }
                return
              }
              if (await msg_confirm('确认要编辑该学生吗？')) {
                let result = await UserModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
    // 转移学生班级Methods
    TransferStudentMethods() {
      let $this = this;
      return {
        // 点击启用用户按钮
        async clickEnableUserBtn(student) {
          student.deleted = 0
          if (await UserModel.addOrEdit(student)) {
            msg_success("已启用")
            // 如果是启用校外人员账号就 发送启用邮件
            if ($this.clazzName === '校外人员') {
              await UserModel.sendAccountEnableEmail(student.userId)
            }
          }
          $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
        },
        // 点击禁用用户按钮
        async clickDisableUserBtn(student) {
          student.deleted = 1
          if (await UserModel.addOrEdit(student)) {
            msg_success("已禁用")
          }
          $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
        },
        // 点击重置密码按钮
        async clickResetPasswordBtn(student) {
          student.password = "988f9440546c44bc67d5444281877a03b05ca49574576e08"
          student.hasLogin = false
          if (await UserModel.addOrEdit(student)) {
            msg_success("密码已重置为123456")
          }
        },
        // 点击列表转移按钮
        async clickTransferBtn(student) {
          $this.transferStudent.dialog = true
          this.getCollegeList();
          this.getGradeList();
          $this.transferStudent.student = JSON.parse(JSON.stringify(student))
        },
        // 获取年级列表
        async getGradeList() {
          let gradeList = await GradeModel.getList({})
          let gradeFilterList = CommonModel.generateListFilterOptions("name", "gradeId", gradeList, false)
          $this.$set($this.transferStudent.filter, "gradeList", gradeFilterList[0])
        },
        // 获取学院列表
        async getCollegeList() {
          let collegeList = await CollegeModel.getList({})
          let collegeFilterList = CommonModel.generateListFilterOptions("name", "collegeId", collegeList, false)
          $this.$set($this.transferStudent.filter, "collegeList", collegeFilterList[0])
        },
        // 获取专业列表
        async getMajorList(v) {
          let majorList = await MajorModel.getList({
            collegeId: v
          })
          let majorFilterList = CommonModel.generateListFilterOptions("name", "majorId", majorList, false)
          $this.$set($this.transferStudent.filter, "majorList", majorFilterList[0])
        },
        // 获取班级列表
        async getClazzList(majorId, gradeId) {
          let clazzList = await ClazzModel.getList({
            majorId: majorId,
            gradeId: gradeId
          })
          let clazzFilterList = CommonModel.generateListFilterOptions("name", "clazzId", clazzList, false)
          $this.$set($this.transferStudent.filter, "clazzList", clazzFilterList[0])
        },
        async onCollegeChange(v) {
          this.getMajorList(v)
          $this.$set($this.transferStudent.edit, "majorId", "")
        },
        async onMajorChange(v) {
          this.getClazzList(v, $this.transferStudent.edit.gradeId)
          $this.$set($this.transferStudent.edit, "clazzId", "")
        },
        async onClazzChange(v) {

        },
        async onGradeChange(v) {
          this.getClazzList($this.transferStudent.edit.majorId, v)
          $this.$set($this.transferStudent.edit, "clazzId", "")
        },
        // 点击确认转移按钮
        async clickSureBtn() {
          $this.$refs['transferStudentForm'].validate(async validate => {
            if (validate) {
              if ($this.transferStudent.edit.clazzId === $this.transferStudent.student.clazzId) {
                msg_err("所需班级和当前班级相同！")
                return
              }
              if (await msg_confirm('确认要转移该学生到所选班级吗？')) {
                let result = await UserModel.transferStudentToNewClazz($this.transferStudent.student.userId, $this.transferStudent.edit.clazzId)
                if (result) {
                  msg_success("转移成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.transferStudent.dialog = false
        },
      }
    }
  }
}
</script>

<style scoped lang="scss">
.clazz-name {
  text-align: center;
  margin-bottom: 15px;
  color: #555;
}
</style>
