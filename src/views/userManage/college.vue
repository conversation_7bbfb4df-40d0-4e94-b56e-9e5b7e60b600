<template>
  <div class="app-container">
    <!--顶部按钮-->
    <div class="top-tools">
      <div style="text-align: right">
        <el-button class="el-button" type="success" @click="ListMethods().clickAddBtn()"
                   style="background-color: #67C23A;border-color:#67C23A">新增学院
        </el-button>
        <el-button class="el-button" type="success"
                   @click="importTeacher.dialog=true">批量导入教师
        </el-button>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              style="width: 100%;">
      <el-table-column label="学院名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="教师人数" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.teacherNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否显示到首页导航" align="center" width="150px">
        <template slot-scope="scope">
          <span>{{ scope.row.showIndex?"是":"否" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="350"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="success" size="mini" round
                     @click="ListMethods().clickTeacherListBtn(scope.row)">教师列表
          </el-button>
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <!--          <el-button type="danger" size="mini" round>删除</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      width="1000px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="130px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="学院名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="显示到首页导航:" prop="showIndex">
            <el-select v-model="entityInfo.edit.showIndex" style="width: 100%">
              <el-option :value="true" label="是" key="是"></el-option>
              <el-option :value="false" label="否" key="否"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="介绍内容:" prop="desText" v-if="entityInfo.edit.showIndex">
            <tinymce
              id="tinymce_desText"
              ref="tinymce_desText"
              v-model="entityInfo.edit.desText"
              :height="300"
            />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
    <!--教师导入input-->
    <input
      id="importTeacherFile"
      type="file"
      style="display: none"
      @change="(files)=>{ListMethods().importTeacherFileChange(files)}"
    >
    <!--教师导入弹窗-->
    <el-dialog
      title="批量导入教师"
      :visible.sync="importTeacher.dialog"
      width="500px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form>
          <el-form-item label="导入模板(Excel):">
            <span style="margin-right: 15px">教师批量导入列表.xlsx</span>
            <el-button type="default" size="mini" @click="ListMethods().clickDownloadBtn()">下载</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="default"
                   @click="importTeacher.dialog=false">取 消
        </el-button>
        <el-button type="success" :loading="importTeacher.doing"
                   @click="ListMethods().clickImportTeacherBtn()">导入教师
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {CollegeModel} from "@/model/CollegeModel";
import {BaseUploadModel} from "@/model/BaseUploadModel";
import Tinymce from "@/components/Tinymce";

export default {
  name: "collegeManage",
  components: {ListSearchFilter,Tinymce},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {

      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增标签",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateName(r, v, c, 20, "学院名称"), trigger: 'blur'},
          'showIndex': {required: true, message: '请选择', trigger: 'change'},
          'desText': {required: true, message: '请输入学院简介', trigger: 'change'},
        },
      },
      // 导入教师
      importTeacher: {
        dialog: false,
        doing: false,
      }
    }
  },
  mounted() {
    // 获取列表
    this.ListMethods().getList({})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击教师列表按钮
        clickTeacherListBtn(entity) {
          $this.$router.push(`/user/teacher?collegeId=${entity.collegeId}&collegeName=${entity.name}`);
        },
        // 获取列表
        async getList(query) {
          $this.lists.loading = true;
          [$this.lists.list] = await CollegeModel.getPageList(0, 1000, "", query)
          $this.lists.loading = false
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {

        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增学院"
          $this.entityInfo.edit = {
            showIndex: false,
            desText: ""
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
            window.tinymce.get("tinymce_desText").setContent("")
          },500);
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑学院"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
            if(entity.showIndex){
              window.tinymce.get("tinymce_desText").setContent(entity.desText)
            }
          }, 500)
        },
        // 点击下载导入列表按钮
        clickDownloadBtn() {
          // todo 做到配置文件里
          downloadFile("https://resouce.ttt.com/bigPlatform/%E6%95%99%E5%B8%88%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E5%88%97%E8%A1%A8.xlsx", "教师批量导入列表.xlsx")
        },
        // 点击了教师导入按钮
        clickImportTeacherBtn() {
          const uploader = document.getElementById('importTeacherFile')
          uploader.click()
        },
        // 导入教师文件选择
        async importTeacherFileChange(files) {
          const file = files.target.files[0]
          // 判断文件类型
          if (!BaseUploadModel.isImportExcelFile(file)) {
            return false
          }
          document.getElementById('importTeacherFile').value = ''
          $this.importTeacher.doing = true
          // todo
          if (await CollegeModel.importTeachers(file).catch(err => {
            $this.importTeacher.dialog = false
            msg_err("批量导入教师失败")
          })) {
            $this.importTeacher.dialog = false
            msg_success('批量导入教师成功')
            $this.ListMethods().getList({})
          }
          $this.importTeacher.doing = false
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名学院
              let listResult = await CollegeModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的学院，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该学院吗？')) {
                let result = await CollegeModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有重名标签
              let listResult = await CollegeModel.getList({
                name: $this.entityInfo.edit.name,
                collegeId: {"$ne": $this.entityInfo.edit.collegeId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名的学院，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该学院吗？')) {
                let result = await CollegeModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
