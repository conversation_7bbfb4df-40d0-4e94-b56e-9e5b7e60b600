<template>
  <div class="app-container">
    <!--筛选-->
    <div class="header-container clearfix">
      <div class="clazz-name" v-if="!asSelected">{{ collegeName }} {{ teacherName }}({{ teacherAccount }})</div>
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)">
      </list-search-filter>
      <!--  操作  -->
      <div class="right">
        <div style="text-align: right">
          <!--          <el-button class="el-button" type="danger" style="background-color: #67C23A;border-color:#67C23A"-->
          <!--                     @click="ListMethods().clickAddBtn()">新增教学班-->
          <!--          </el-button>-->
        </div>
      </div>
    </div>
    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading" element-loading-text="加载中" border fit
              @selection-change="v=>ListMethods().onSelected(v)"
              style="width: 100%;">
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
      <el-table-column label="名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关联行政班" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.clazzNames.join('、') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学生人数" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.studentNumber }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <!--      <el-table-column align="center" label="操作" width="350"-->
      <!--                       class-name="small-padding fixed-width">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-button type="primary" size="mini" round-->
      <!--                     @click="ListMethods().clickEditBtn(scope.row)">编辑-->
      <!--          </el-button>-->
      <!--          &lt;!&ndash;          <el-button type="danger" size="mini" round>删除</el-button>&ndash;&gt;-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
    <!--列表分页-->
    <div class="pagination-container">
      <el-pagination background @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>
    <!--工具-->
    <div class="button flex flex-center" style="margin-top: 20px">
      <el-button type="default" @click="ListMethods().clickCancelSelectedBtn()">取消</el-button>
      <el-button type="primary" @click="ListMethods().clickSureSelectedBtn()">确认选择</el-button>
    </div>
    <!--实体弹窗-->
    <el-dialog
      :title="entityInfo.title"
      :visible.sync="entityInfo.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm" :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="工号:" prop="account">
            <el-input v-model.trim="entityInfo.edit.account" :disabled="entityInfo.type==='edit'">
              <div slot="suffix" v-if="entityInfo.edit.account">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.account.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="姓名:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 30
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="性别:" prop="sex">
            <el-select v-model="entityInfo.edit.sex"
                       style="width: 100%">
              <el-option value="男" label="男" key="男"></el-option>
              <el-option value="女" label="女" key="女"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button type="default"
                    @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, downloadFile, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_input, msg_success} from "@/utils/ele_component";
import {TeachingClazzModel} from "@/model/TeachingClazzModel";
import {ClazzModel} from "@/model/ClazzModel";
import {MajorModel} from "@/model/MajorModel";
import {GradeModel} from "@/model/GradeModel";
import {UserModel} from "@/model/UserModel";

export default {
  name: "teachingClazzManage",
  components: {ListSearchFilter},
  directives: {
    elDragDialog, permission
  },
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    }),
    collegeName() {
      return this.$route.query["collegeName"]
    },
    teacherId() {
      return this.$route.query["teacherId"]
    },
    teacherName() {
      return this.$route.query["teacherName"]
    },
    teacherAccount() {
      return this.$route.query["teacherAccount"]
    },
  },
  watch: {
    teacherIdProp(n, o) {
      if (this.asSelected) {// 列表选择模式
        this.$set(this.lists, "queryBase", {
          teacherId: n
        })
        this.ListMethods().getList(0, 20, {})
      }
    }
  },
  props: {
    asSelected: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    teacherIdProp: {
      type: String,
      default: () => {
        return ""
      }
    }
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 校检名称
    const validateName = (rule, value, callback, maxLength, propName) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入' + propName))
        return
      }
      if (value.length > maxLength) {
        callback(new Error('最多输入' + maxLength + '个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；“”‘、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      // 仅支持中英文
      regEn = /[a-zA-Z]+/
      regCn = /[\u4e00-\u9fa5]+/g
      let regNumber = /^[0-9]+$/
      if (regEn.test(value) || regCn.test(value) || regNumber.test(value)) {

      } else {
        callback(new Error('仅支持中英文和数字'));
      }
      callback()
    }
    return {
      enums: enums,
      getQuery: getQuery,
      // 外部方法
      date_format: date_format,
      // 列表
      lists: {
        list: [],
        selectedList: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '名称',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
          ],
          filter: [],
        }

      },
      userInfo: {},
      entityInfo: {
        dialog: false,
        title: "新增教学班",
        type: "add",
        filter: {},
        edit: {
          name: "",
        },
        // 输入检测
        formRules: {
          'name': {required: true, validator: (r, v, c) => validateName(r, v, c, 30, "姓名"), trigger: 'blur'},
          'account': {required: true, validator: (r, v, c) => validateName(r, v, c, 30, "姓名"), trigger: 'blur'},
          'sex': {required: true, message: '请选择性别', trigger: 'change'},
        },
      },

    }
  },
  async mounted() {
    if (this.asSelected) {// 列表选择模式
      this.lists.queryBase = {
        teacherId: this.teacherIdProp
      }
    } else {
      this.lists.queryBase = {
        teacherId: this.teacherId
      }
    }
    // 获取列表
    this.ListMethods().getList(0, 20, {})
  },
  methods: {
    // 列表Methods
    ListMethods() {
      let $this = this
      return {
        // 点击确认选择按钮
        clickSureSelectedBtn() {
          $this.$emit("onSelected", $this.lists.selectedList)
        },
        // 点击取消选择按钮
        clickCancelSelectedBtn() {
          $this.$emit("onCancel", {})
        },
        // 当选择某些教学班时
        onSelected(v) {
          $this.$set($this.lists, "selectedList", v)
        },
        // 获取列表
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await TeachingClazzModel.getPageList(page - 1, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },
        // 分页-改变页码
        async pageChange(page) {
          this.getList(page, $this.lists.pages.size, $this.lists.query)
        },
        // 分页-改变每页显示数量
        async pageLimitChange(size) {
          this.getList($this.lists.pages.number - 1, size, $this.lists.query)
        },
        // 点击搜索按钮
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },
        // 点击新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增教学班"
          $this.entityInfo.edit = {};
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
        // 点击编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑教学班"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },
      }
    },
    // 实体方法集
    EntityMethods() {
      let $this = this;
      return {
        // 点击新增按钮
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有相同名称教学班
              let listResult = await TeachingClazzModel.getList({
                name: $this.entityInfo.edit.name,
                teacherId: $this.teacherId,
              })
              if (listResult.length > 0) {
                msg_err(`已存在相同名称的教学班，请修改后再试！`)
                return
              }
              if (await msg_confirm('确认要新增该教学班吗？')) {
                $this.entityInfo.edit.teacherId = $this.teacherId
                let result = await TeachingClazzModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击编辑按钮
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 判断是否有相同工号教学班
              let listResult = await TeachingClazzModel.getList({
                name: $this.entityInfo.edit.name,
                teachingClazzId: {"$ne": $this.entityInfo.edit.teachingClazzId},
                teacherId: $this.teacherId,
              })
              if (listResult.length > 0) {
                msg_err(`已存在相同名称的教学班，请修改后再试！`)
                return
              }
              if (await msg_confirm('确认要编辑该教学班吗？')) {
                let result = await TeachingClazzModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList($this.lists.pages.number, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },
        // 点击取消按钮
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        },
      }
    },
  }
}
</script>

<style scoped lang="scss">
.clazz-name {
  text-align: center;
  margin-bottom: 15px;
  color: #555;
}
</style>
