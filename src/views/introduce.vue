<template>
  <div class="page-help">
    <!-- 全宽页头 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <font-awesome-icon icon="university" />
        </div>
        <div class="header-text">
          <h1 class="header-title">基地中心简介</h1>
          <p class="header-subtitle">校级和二级学院中心简介</p>
        </div>
      </div>

    </div>

    <div class="content-container">
      <div class="introduce-wrapper">
        <div class="nav-box">
          <div class="nav-header">
            <font-awesome-icon icon="book-open" />
            <span>中心导航</span>
          </div>
          <div class="nav-list">
            <div
                :class="['nav-item', { active: item.id === introduceId }]"
                v-for="(item,index) in introduceConfig.tabs"
                @click="clickTab(item.id)"
                :key="item.id"
            >
              <font-awesome-icon icon="lightbulb" class="nav-icon" />
              <span class="nav-text">{{ item.name }}</span>
              <font-awesome-icon icon="chevron-right" class="arrow-icon" v-if="item.id === introduceId" />
            </div>
          </div>
        </div>
        <div v-if="introduceId" class="content-box html-view"
             v-html="find_obj_from_arr_by_id('id',introduceId,introduceConfig.tabs)[1]['text']">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {HelpQuestionModel} from "../model/HelpQuestionModel";
import {mapState} from "vuex";
import {find_obj_from_arr_by_id} from "../utils/common";
import store from "../store";

export default {
  name: "Introduce",
  computed: {
    ...mapState({
      introduceConfig: state => state.introduceConfig,
      introduceId: state => state.introduceId,
    })
  },
  data() {
    return {
      find_obj_from_arr_by_id: find_obj_from_arr_by_id,
      tabId: "",
      showBox: 0,
      // 问题列表
      questionList: {
        student: [],
        teacher: []
      },
      // 问题详情
      questionInfo: {}
    }
  },
  mounted() {
  },
  methods: {
    // 点击左侧导航
    clickTab(tabId) {
      this.tabId = tabId
      store.dispatch("setIntroduceId", tabId)
    },
    // 获取学生问题列表
    async getStudentQuestionList() {
      let list = await HelpQuestionModel.getList({
        type: "学生"
      });
      this.$set(this.questionList, "student", list)
    },
    // 获取教师问题列表
    async getTeacherQuestionList() {
      let list = await HelpQuestionModel.getList({
        type: "教师"
      });
      this.$set(this.questionList, "teacher", list)
    },
    // 点击某个问题
    clickOneQuestion(item) {
      this.questionInfo = item;
      this.showBox = 1;
    },
    // 回任务列表
    clickNavBackBtn() {
      this.showBox = 0;
    }
  }
}
</script>

<style scoped lang="less">
@import '../style/app.less';

.page-help {

  /* 全宽页头样式 */

  .page-header {
    width: 100%;
    background: linear-gradient(135deg, #4093f9 0%, #5ca0f2 50%, #7ba7ff 100%);
    color: white;
    padding: 30px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);


    .header-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 30px;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;

      .header-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);

        i {
          font-size: 36px;
          color: white;
        }
      }

      .header-text {
        text-align: left;

        .header-title {
          font-size: 42px;
          font-weight: 700;
          margin: 0 0 8px 0;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          letter-spacing: 1px;
        }

        .header-subtitle {
          font-size: 18px;
          opacity: 0.9;
          margin: 0;
          font-weight: 300;
          letter-spacing: 0.5px;
        }
      }

      .header-icon {
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 2px solid rgba(255, 255, 255, 0.3);

        svg {
          font-size: 36px;
          color: white;
        }
      }
    }

    @keyframes pulse {
      0%, 100% {
        opacity: 0.8;
        transform: scale(1);
      }
      50% {
        opacity: 1;
        transform: scale(1.2);
      }
    }
  }

  .content-container {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
  }

  margin-top: 0;
  margin-bottom: 20px;

  .introduce-wrapper {
    display: flex;
    gap: 24px;
    align-items: flex-start;
  }

  .nav-box {
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    min-width: 260px;
    flex-shrink: 0;

    .nav-header {
      background: linear-gradient(135deg, #4093f9, #5ca0f2);
      color: white;
      padding: 20px 24px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;

      i {
        font-size: 18px;
      }
    }

    .nav-list {
      padding: 8px 0;

      .nav-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 14px 24px;
        font-size: 15px;
        cursor: pointer;
        border-left: 3px solid transparent;
        transition: all 0.3s ease;
        color: #555;
        line-height: 1.5;

        .nav-icon {
          color: #4093f9;
          font-size: 14px;
          width: 16px;
          flex-shrink: 0;
        }

        &:hover {
          background-color: #f8f9fa;
          color: #4093f9;
          border-left-color: #4093f9;
          transform: translateX(2px);
        }

        &.active {
          background-color: #f0f7ff;
          color: #4093f9;
          border-left-color: #4093f9;
          font-weight: 600;
          transform: translateX(2px);

          .nav-text {
            color: #4093f9;
          }

          .arrow-icon {
            opacity: 1;
            transform: translateX(0);
          }
        }

        .nav-text {
          flex: 1;
          transition: color 0.3s ease;
        }

        .arrow-icon {
          opacity: 0;
          transition: all 0.3s ease;
          transform: translateX(-4px);
          color: #4093f9;
          font-size: 12px;
        }

        &.active {
          .nav-icon {
            color: #4093f9;
            transform: scale(1.1);
          }
        }
      }
    }

    .content-box {
      flex: 1;
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      padding: 32px;
      min-height: 500px;
      line-height: 1.8;

      h1, h2, h3, h4, h5, h6 {
        margin-top: 24px;
        margin-bottom: 16px;
        color: #2c3e50;
      }

      p {
        margin-bottom: 16px;
        color: #555;
      }

      img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin: 16px 0;
      }

      ul, ol {
        margin: 16px 0;
        padding-left: 24px;

        li {
          margin-bottom: 8px;
          color: #555;
        }
      }

      blockquote {
        border-left: 4px solid #4093f9;
        padding-left: 20px;
        margin: 20px 0;
        color: #666;
        font-style: italic;
        background-color: #f8f9fa;
        padding: 16px 20px 16px 24px;
        border-radius: 4px;
      }

      .nav-bar {
        margin-bottom: 30px;
      }

      .show-box {
        font-size: 15px;
        line-height: 1.8;

        &.main {
          .title {
            font-weight: bold;
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid #f0f0f0;
          }

          .des {
            color: #666;
            font-size: 16px;
            line-height: 1.8;
          }

          .question-list {
            .title {
              margin-bottom: 15px;
            }

            .li {
              cursor: pointer;
              padding-left: 20px;
              color: #4093f9;
              margin-bottom: 10px;
            }
          }
        }
      }
    }

    // 响应式布局
    @media (max-width: 1200px) {
      .introduce-wrapper {
        flex-direction: column;
      }

      .nav-box {
        min-width: auto;
        margin-bottom: 20px;
      }

      .content-box {
        width: 100%;
      }
    }

    @media (max-width: 768px) {
      .nav-box {
        .nav-item {
          padding: 12px 16px;
          font-size: 14px;
        }
      }

      .content-box {
        padding: 20px;
      }
    }
  }
}
</style>
