<template>
  <div class="page-userTask">
    <el-tabs v-model="tabShow" @tab-click="clickTaskTab">
      <el-tab-pane name="unStart">
        <span slot="label">
          <font-awesome-icon icon="clock" class="tab-icon" />
          未开始
        </span>
        <!--加载状态-->
        <div v-if="unStartList.loading" class="loading-container">
          <el-skeleton :rows="3" animated class="loading-skeleton">
            <template #template>
              <div class="loading-item" v-for="i in 3" :key="i">
                <div class="loading-left">
                  <el-skeleton-item variant="image" style="width: 230px; height: 140px" />
                </div>
                <div class="loading-right">
                  <el-skeleton-item variant="h3" style="width: 50%" />
                  <el-skeleton-item variant="text" style="width: 30%; margin-top: 10px" />
                  <el-skeleton-item variant="text" style="width: 40%; margin-top: 5px" />
                  <el-skeleton-item variant="text" style="width: 35%; margin-top: 5px" />
                  <el-skeleton-item variant="button" style="width: 80px; margin-top: 15px; margin-right: 10px" />
                  <el-skeleton-item variant="button" style="width: 80px; margin-top: 15px" />
                </div>
              </div>
            </template>
          </el-skeleton>
        </div>
        
        <!--数据列表-->
        <div v-if="!unStartList.loading && unStartList.list.length>0">
          <div class="course-list">
            <div class="li flex flex-start" v-for="(item,index) in unStartList.list">
              <div class="left-box">
                <img :src="item.courseEntity[0].avatarUrl" alt="" class="headImg">
              </div>
              <div class="right-box">
                <div class="name">
                  <span class="name">{{ item.courseEntity[0].name }}</span>
                </div>
                <div class="task-info">
                  <div class="li">
                    <font-awesome-icon icon="chalkboard-teacher" class="info-icon" />
                    <span class="title">负责教师：</span>
                    <span class="text">{{ item.teacherEntity[0].name }}</span>
                  </div>
                  <div class="li">
                    <font-awesome-icon icon="clock" class="info-icon" />
                    <span class="title">任务时间：</span>
                    <span class="text">{{
                      item.courseTaskEntity[0]["startTime"] | dateFormat
                    }} 至 {{ item.courseTaskEntity[0]["endTime"] | dateFormat }}</span>
                  </div>
                </div>
                <div class="statistics-info flex flex-start">
                  <div class="li">
                    <font-awesome-icon icon="chart-line" class="stat-icon" />
                    <span class="title">学习次数：</span>
                    <span class="number">{{ item.courseScoreEntity.length }}次</span>
                  </div>
                  <div class="li">
                    <font-awesome-icon icon="book-open" class="stat-icon" />
                    <span class="title">课程分：</span>
                    <span class="number" v-if="item.courseCompleted">{{ item.courseScore | scoreFormat }}分</span>
                    <span class="number" v-else>未完成</span>
                  </div>
                  <div class="li" v-if="item.courseTaskEntity[0].needFillReport">
                    <font-awesome-icon icon="edit" class="stat-icon" />
                    <span class="title">报告分：</span>
                    <span class="number" v-if="item.reportFilled===false">未填写</span>
                    <span class="number" v-if="item.reportFilled&&!item.reportCorrected">未批改</span>
                    <span class="number" v-if="item.reportCorrected">{{ item.reportScore | scoreFormat }}分</span>
                  </div>
                  <div class="li">
                    <font-awesome-icon icon="trophy" class="stat-icon" />
                    <span class="title">课程总分：</span>
                    <template>
                      <span class="number" v-if="item.courseTaskEntity[0].needFillReport&&item.reportCorrected">{{
                        item.totalScore | scoreFormat
                      }}分</span>
                      <span class="number" v-else>--</span>
                    </template>
                    <template>
                      <span class="number"
                            v-if="!item.courseTaskEntity[0].needFillReport">{{ item.totalScore | scoreFormat }}分</span>
                    </template>
                  </div>
                </div>
                <div class="buttons flex flex-start">
                  <el-button size="small" type="default" @click="clickEnterCourseBtn(item)">
                    <font-awesome-icon icon="rocket" class="btn-icon" />
                    进入课程
                  </el-button>
                  <el-button size="small" type="default" @click="clickShowTaskDesBtn(item)">
                    <font-awesome-icon icon="lightbulb" class="btn-icon" />
                    任务介绍
                  </el-button>
                  <el-button size="small" type="default"
                             :disabled="item.userEntity[0]['commentCourseId'].indexOf(item.courseId)>-1?'disabled':false"
                             @click="clickOpenCommentBtn(item,index)">
                    <font-awesome-icon icon="comments" class="btn-icon" />
                    课程评价
                  </el-button>
                  <el-button size="small" type="default" @click="clickShowScoreBtn(item,index)">
                    <font-awesome-icon icon="chart-line" class="btn-icon" />
                    我的成绩
                  </el-button>
                  <el-button size="small" type="default" @click="clickWriteReportBtn(item,index)"
                             v-if="item.courseTaskEntity[0]['needFillReport']&&!item.reportCorrected">
                    <font-awesome-icon icon="edit" class="btn-icon" />
                    填写报告
                  </el-button>
                  <el-button size="small" type="default" @click="clickViewReportBtn(item,index)"
                             v-if="item.courseTaskEntity[0]['needFillReport']&&item.reportCorrected">
                    <font-awesome-icon icon="eye" class="btn-icon" />
                    查看报告
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          
          <!--列表分页-->
          <div class="pagination-container">
            <el-pagination background @current-change="(number)=>pageChange(number)"
                           :current-page.sync="unStartList.pages.number" :page-size.sync="unStartList.pages.size"
                           layout="total,prev, pager, next,jumper,sizes" :total="unStartList.pages.totalElements"
                           @size-change="(size)=>pageLimitChange(size)"
                           :page-count="unStartList.pages.totalPages">
            </el-pagination>
          </div>
        </div>
        
        <!--空数据状态-->
        <div v-if="!unStartList.loading && unStartList.list.length===0" class="empty-container">
          <el-empty description="暂无未开始的任务">
            <template #description>
              <div class="empty-content">
                <font-awesome-icon icon="clock" class="empty-icon" />
                <p class="empty-text">暂无未开始的任务</p>
                <p class="empty-subtext">当前没有待开始的课程任务，<br>请耐心等待教师发布新任务</p>
              </div>
            </template>
          </el-empty>
        </div>
      </el-tab-pane>
      <el-tab-pane name="doing">
        <span slot="label">
          <font-awesome-icon icon="play-circle" class="tab-icon" />
          进行中
        </span>
        <!--加载状态-->
        <div v-if="doingList.loading" class="loading-container">
          <el-skeleton :rows="3" animated class="loading-skeleton">            <template #template>              <div class="loading-item" v-for="i in 3" :key="i">                <div class="loading-left">                  <el-skeleton-item variant="image" style="width: 230px; height: 140px" />
                </div>                <div class="loading-right">                  <el-skeleton-item variant="h3" style="width: 50%" />                  <el-skeleton-item variant="text" style="width: 30%; margin-top: 10px" />                  <el-skeleton-item variant="text" style="width: 40%; margin-top: 5px" />                  <el-skeleton-item variant="text" style="width: 35%; margin-top: 5px" />                  <el-skeleton-item variant="button" style="width: 80px; margin-top: 15px; margin-right: 10px" />                  <el-skeleton-item variant="button" style="width: 80px; margin-top: 15px" />                </div>              </div>            </template>          </el-skeleton>        </div>        
        <!--数据列表-->        <div v-if="!doingList.loading && doingList.list.length>0">          <div class="course-list">            <div class="li flex flex-start" v-for="(item,index) in doingList.list">              <div class="left-box">                <img :src="item.courseEntity[0].avatarUrl" alt="" class="headImg">              </div>              <div class="right-box">                <div class="name">                  <span class="name">{{ item.courseEntity[0].name }}</span>                </div>                <div class="task-info">                  <div class="li">                    <font-awesome-icon icon="chalkboard-teacher" class="info-icon" />                    <span class="title">负责教师：</span>                    <span class="text">{{ item.teacherEntity[0].name }}</span>                  </div>                  <div class="li">                    <font-awesome-icon icon="clock" class="info-icon" />                    <span class="title">任务时间：</span>                    <span class="text">{{
                      item.courseTaskEntity[0]["startTime"] | dateFormat
                    }} 至 {{ item.courseTaskEntity[0]["endTime"] | dateFormat }}</span>                  </div>                </div>                <div class="statistics-info flex flex-start">                  <div class="li">                    <font-awesome-icon icon="chart-line" class="stat-icon" />                    <span class="title">学习次数：</span>                    <span class="number">{{ item.courseScoreEntity.length }}次</span>                  </div>                  <div class="li">                    <font-awesome-icon icon="book-open" class="stat-icon" />                    <span class="title">课程分：</span>                    <span class="number" v-if="item.courseCompleted">{{ item.courseScore | scoreFormat }}分</span>                    <span class="number" v-else>未完成</span>                  </div>                  <div class="li" v-if="item.courseTaskEntity[0].needFillReport">                    <font-awesome-icon icon="edit" class="stat-icon" />                    <span class="title">报告分：</span>                    <span class="number" v-if="item.reportFilled===false">未填写</span>                    <span class="number" v-if="item.reportFilled&&!item.reportCorrected">未批改</span>                    <span class="number" v-if="item.reportCorrected">{{ item.reportScore | scoreFormat }}分</span>                  </div>                  <div class="li">                    <font-awesome-icon icon="trophy" class="stat-icon" />                    <span class="title">课程总分：</span>                    <template>                      <span class="number" v-if="item.courseTaskEntity[0].needFillReport&&item.reportCorrected">{{
                        item.totalScore | scoreFormat
                      }}分</span>                      <span class="number" v-else>--</span>                    </template>                    <template>                      <span class="number"
                            v-if="!item.courseTaskEntity[0].needFillReport">{{ item.totalScore | scoreFormat }}分</span>                    </template>                  </div>                </div>                <div class="buttons flex flex-start">                  <el-button size="small" type="default" @click="clickEnterCourseBtn(item)">                    <font-awesome-icon icon="rocket" class="btn-icon" />                    进入课程
                  </el-button>                  <el-button size="small" type="default" @click="clickShowTaskDesBtn(item)">                    <font-awesome-icon icon="lightbulb" class="btn-icon" />                    任务介绍
                  </el-button>                  <el-button size="small" type="default"
                             :disabled="item.userEntity[0]['commentCourseId'].indexOf(item.courseId)>-1?'disabled':false"
                             @click="clickOpenCommentBtn(item,index)">                    <font-awesome-icon icon="comments" class="btn-icon" />                    课程评价
                  </el-button>                  <el-button size="small" type="default" @click="clickShowScoreBtn(item,index)">                    <font-awesome-icon icon="chart-line" class="btn-icon" />                    我的成绩
                  </el-button>                  <el-button size="small" type="default" @click="clickWriteReportBtn(item,index)"
                             v-if="item.courseTaskEntity[0]['needFillReport']&&!item.reportCorrected">                    <font-awesome-icon icon="edit" class="btn-icon" />                    填写报告
                  </el-button>                  <el-button size="small" type="default" @click="clickViewReportBtn(item,index)"
                             v-if="item.courseTaskEntity[0]['needFillReport']&&item.reportCorrected">                    <font-awesome-icon icon="eye" class="btn-icon" />                    查看报告
                  </el-button>                </div>              </div>            </div>          </div>          
          <!--列表分页-->          <div class="pagination-container">            <el-pagination background @current-change="(number)=>pageChange(number)"
                           :current-page.sync="doingList.pages.number" :page-size.sync="doingList.pages.size"
                           layout="total,prev, pager, next,jumper,sizes" :total="doingList.pages.totalElements"
                           @size-change="(size)=>pageLimitChange(size)"
                           :page-count="doingList.pages.totalPages">            </el-pagination>          </div>        </div>        
        <!--空数据状态-->        <div v-if="!doingList.loading && doingList.list.length===0" class="empty-container">          <el-empty description="暂无进行中的任务">            <template #description>              <div class="empty-content">                <font-awesome-icon icon="play-circle" class="empty-icon" />                <p class="empty-text">暂无进行中的任务</p>                <p class="empty-subtext">当前没有进行中的课程任务，<br>请从"未开始"任务中开始新的学习</p>              </div>            </template>          </el-empty>        </div>
      </el-tab-pane>
      <el-tab-pane name="done">
        <span slot="label">
          <font-awesome-icon icon="check-circle" class="tab-icon" />
          已结束
        </span>
        <div class="course-list" v-if="doneList.list.length>0">
          <div class="li flex flex-start" v-for="(item,index) in doneList.list">
            <div class="left-box">
              <img :src="item.courseEntity[0].avatarUrl" alt="" class="headImg">
            </div>
            <div class="right-box">
              <div class="name">
                <span class="name">{{ item.courseEntity[0].name }}</span>
              </div>
              <div class="task-info">
                <div class="li">
                  <font-awesome-icon icon="chalkboard-teacher" class="info-icon" />
                  <span class="title">负责教师：</span>
                  <span class="text">{{ item.teacherEntity[0].name }}</span>
                </div>
                <div class="li">
                  <font-awesome-icon icon="clock" class="info-icon" />
                  <span class="title">任务时间：</span>
                  <span
                      class="text">{{
                      item.courseTaskEntity[0]["startTime"] | dateFormat
                    }} 至 {{ item.courseTaskEntity[0]["endTime"] | dateFormat }}</span>
                </div>
              </div>
              <div class="statistics-info flex flex-start">
                <div class="li">
                  <font-awesome-icon icon="chart-line" class="stat-icon" />
                  <span class="title">学习次数：</span>
                  <span class="number">{{ item.courseScoreEntity.length }}次</span>
                </div>
                <div class="li">
                  <font-awesome-icon icon="book-open" class="stat-icon" />
                  <span class="title">课程分：</span>
                  <span class="number" v-if="item.courseCompleted">{{ item.courseScore | scoreFormat }}分</span>
                  <span class="number" v-else>未完成</span>
                </div>
                <div class="li" v-if="item.courseTaskEntity[0].needFillReport">
                  <font-awesome-icon icon="edit" class="stat-icon" />
                  <span class="title">报告分：</span>
                  <span class="number" v-if="item.reportFilled===false">未填写</span>
                  <span class="number" v-if="item.reportFilled&&!item.reportCorrected">未批改</span>
                  <span class="number" v-if="item.reportCorrected">{{ item.reportScore | scoreFormat }}分</span>
                </div>
                <div class="li">
                  <font-awesome-icon icon="trophy" class="stat-icon" />
                  <span class="title">课程总分：</span>
                  <template>
                    <span class="number" v-if="item.courseTaskEntity[0].needFillReport&&item.reportCorrected">{{
                        item.totalScore | scoreFormat
                      }}分</span>
                    <span class="number" v-else>--</span>
                  </template>
                  <template>
                    <span class="number"
                          v-if="!item.courseTaskEntity[0].needFillReport">{{ item.totalScore | scoreFormat }}分</span>
                  </template>
                </div>
              </div>
              <div class="buttons flex flex-start">
                <el-button size="small" type="default" @click="clickEnterCourseBtn(item)">
                  <font-awesome-icon icon="rocket" class="btn-icon" />
                  进入课程
                </el-button>
                <el-button size="small" type="default" @click="clickShowTaskDesBtn(item)">
                  <font-awesome-icon icon="lightbulb" class="btn-icon" />
                  任务介绍
                </el-button>
                <el-button size="small" type="default"
                           :disabled="item.userEntity[0]['commentCourseId'].indexOf(item.courseId)>-1?'disabled':false"
                           @click="clickOpenCommentBtn(item,index)">
                  <font-awesome-icon icon="comments" class="btn-icon" />
                  课程评价
                </el-button>
                <el-button size="small" type="default" @click="clickShowScoreBtn(item,index)">
                  <font-awesome-icon icon="chart-line" class="btn-icon" />
                  我的成绩
                </el-button>
                <el-button size="small" type="default" @click="clickWriteReportBtn(item,index)"
                           v-if="item.courseTaskEntity[0]['needFillReport']&&!item.reportCorrected">
                  <font-awesome-icon icon="edit" class="btn-icon" />
                  填写报告
                </el-button>
                <el-button size="small" type="default" @click="clickViewReportBtn(item,index)"
                           v-if="item.courseTaskEntity[0]['needFillReport']&&item.reportCorrected">
                  <font-awesome-icon icon="eye" class="btn-icon" />
                  查看报告
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="pagination-container" v-if="doneList.list.length>0">
          <el-pagination background @current-change="(number)=>pageChange(number)"
                         :current-page.sync="doneList.pages.number" :page-size.sync="doneList.pages.size"
                         layout="total,prev, pager, next,jumper,sizes" :total="doneList.pages.totalElements"
                         @size-change="(size)=>pageLimitChange(size)"
                         :page-count="doneList.pages.totalPages">
          </el-pagination>
        </div>
        <el-empty description="暂无数据" v-if="doneList.list.length===0"></el-empty>
      </el-tab-pane>
    </el-tabs>
    <!--任务介绍弹窗-->
    <el-dialog
        :visible.sync="taskDes.dialog"
        width="980px"
        center
        :close-on-click-modal="false"
        v-el-drag-dialog>
      <span slot="title">
        <font-awesome-icon icon="lightbulb" class="dialog-icon" />
        任务介绍
      </span>
      <div class="dialog-container html-view" v-html="taskDes.content">

      </div>
    </el-dialog>
    <!--成绩列表弹窗-->
    <el-dialog
        :visible.sync="scoreList.dialog"
        width="980px"
        center
        :close-on-click-modal="false"
        >
      <span slot="title">
        <font-awesome-icon icon="chart-line" class="dialog-icon" />
        成绩列表
      </span>
      <div class="dialog-container">
        <el-descriptions size="medium"  style="margin-bottom: 0px" v-if="userInfo.role==='student'">
          <el-descriptions-item label="学号">{{ userInfo.account}}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ userInfo.name}}</el-descriptions-item>
          <el-descriptions-item label="院校">{{ userInfo.collegeEntity[0]["name"]}}</el-descriptions-item>
          <el-descriptions-item label="专业" v-if="!userInfo.asSocial&&userInfo.role!=='teacher'">{{ userInfo.majorEntity[0]["name"]}}</el-descriptions-item>
          <el-descriptions-item label="年级" v-if="!userInfo.asSocial&&userInfo.role!=='teacher'">{{ userInfo.gradeEntity[0]["name"]}}</el-descriptions-item>
          <el-descriptions-item label="班级" v-if="!userInfo.asSocial&&userInfo.role!=='teacher'">{{ userInfo.clazzEntity[0]["name"]}}</el-descriptions-item>
        </el-descriptions>
        <el-table :data="scoreList.list" v-loading="scoreList.loading"
                  element-loading-text="加载中" fit border max-height="500px"
                  style="width: 100%;">
          <el-table-column label="课程名称" prop="courseName" align="center">
            <template slot-scope="scope">
              <span>{{ scoreList.course.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验结果" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ enums.courseScoreResult[scope.row.result] }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验成绩" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.row.score | scoreFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验开始时间" prop="courseName" align="center" width="170px">
            <template slot-scope="scope">
              <span>{{ scope.row.startTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验结束时间" prop="courseName" align="center" width="170px">
            <template slot-scope="scope">
              <span>{{ scope.row.endTime | dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实验用时" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.row.usedTime | scoreUseTimeFilter }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="clickShowScoreDetailBtn(scope.row,scope.$index)">
                <font-awesome-icon icon="eye" class="btn-icon" />
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!--成绩详情弹窗-->
    <el-dialog
        :title="scoreInfo.title"
        :visible.sync="scoreInfo.dialog"
        :close-on-click-modal="true"
        :append-to-body="true"
        width="1200px"
        center
        >
      <div class="dialog-container dialog-scoreInfo">
        <div class="info flex flex-start flex flex-wrap">
          <div class="li name">
            <span class="title">实验名称：</span>
            <span class="text">{{ scoreList.course.name }}</span>
          </div>
          <div class="li">
            <span class="title">实验开始时间：</span>
            <span class="text">{{ scoreInfo.info.startTime |dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
          </div>
          <div class="li">
            <span class="title">实验结束时间：</span>
            <span class="text">{{ scoreInfo.info.endTime |dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
          </div>
          <div class="li">
            <span class="title">实验用时：</span>
            <span class="text">{{ scoreInfo.info.usedTime | scoreUseTimeFilter }}</span>
          </div>
          <div class="li">
            <span class="title">实验满分：</span>
            <span class="text">{{ scoreInfo.info.fullScore | scoreFormat }}</span>
          </div>
          <div class="li">
            <span class="title">实验得分：</span>
            <span class="text">{{ scoreInfo.info.score |scoreFormat }}</span>
          </div>
          <div class="li">
            <span class="title">实验结果：</span>
            <span class="text">{{ enums.courseScoreResult[scoreInfo.info.result] }}</span>
          </div>
        </div>
        <div class="list">
          <el-table :data="scoreInfo.info.stepInfo" v-loading="scoreInfo.loading"
                    element-loading-text="加载中" fit border max-height="400px"
                    style="width: 100%;">
            <el-table-column label="步骤名称" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.title }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否完成" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.done ? "完成" : "未完成" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤开始时间" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.startTime |dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤结束时间" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.endTime |dateFormat("yyyy-MM-dd HH:mm:ss") }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤用时(秒)" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.timeUsed  | scoreUseTimeFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤合理用时" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.expectTime  | scoreUseTimeFilter }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤满分" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.maxScore | scoreFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤得分" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.score | scoreFormat }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤操作次数" align="center" width="80px">
              <template slot-scope="scope">
                <span>{{ scope.row.repeatCount }}</span>
              </template>
            </el-table-column>
            <el-table-column label="步骤评价" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.evaluation }}</span>
              </template>
            </el-table-column>
            <el-table-column label="赋分模型" align="center">
              <template slot-scope="scope">
                <div>
                  <div class="text" v-for="item in scope.row.scoringModel.split('\n')">{{ item }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="备注" align="center">
              <template slot-scope="scope">
                <div>
                  <div class="text" v-for="item in scope.row.remarks.split('\n')">{{ item }}</div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-dialog>
    <!--查看实验报告弹窗-->
    <el-dialog
        :visible.sync="reportView.dialog"
        :close-on-click-modal="false"
        width="900px"
        center
        v-el-drag-dialog>
      <span slot="title">
        <font-awesome-icon icon="eye" class="dialog-icon" />
        实验报告
      </span>
      <div class="dialog-container report-view-container">
        <div v-html="reportView.content" class="html-view limit-height"></div>
      </div>
    </el-dialog>
    <!--写实验报告弹窗-->
    <el-dialog
        :close-on-click-modal="false"
        :visible.sync="writeReport.dialog"
        @close="closeWriteDialog"
        width="900px"
        center
        v-el-drag-dialog>
      <span slot="title">
        <font-awesome-icon icon="edit" class="dialog-icon" />
        填写实验报告
      </span>
      <div class="dialog-container">
        <tinymce
            id="tinymce_report_content"
            ref="tinymce_report_content"
            v-model="writeReport.content"
            @initEd="onWriteTinymceInit"
            :height="300"
        />
      </div>
      <div slot="footer">
        <el-button type="primary" size="small" :loading="writeReport.doing" @click="clickPostReportBtn">
          <font-awesome-icon icon="paper-plane" class="btn-icon" />
          提交实验报告
        </el-button>
      </div>
    </el-dialog>
    <!--填写课程评价弹窗-->
    <write-comment :show="writeComment.dialog" @clickClose="writeComment.dialog=false"
                   :record-id="writeComment.recordId"
                   @onSubmit="onCommentSubmit"></write-comment>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import Tinymce from "@/components/Tinymce";
import writeComment from "../components/writeComment";
import {CourseRecordModel} from "../../model/CourseRecordModel";
import {calAverageNumber} from "../../utils/common";
import {dateFormat, scoreFormat, scoreUseTimeFilter} from "../../filters";
import {msg_err, msg_success} from "../../utils/ele_component";
import enums from "../../enums";
import {CourseReportModel} from "../../model/CourseReportModel";
import {mapState} from "vuex";

export default {
  name: "UserTask",
  components: {Tinymce, writeComment},
  filters: {
    scoreFormat,
    dateFormat,
    scoreUseTimeFilter
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo,
    })
  },
  directives: {
    elDragDialog
  },
  data() {
    return {
      enums: enums,
      window: window,
      tabShow: "unStart",
      listName: "unStartList",
      // 未开始列表
      unStartList: {
        list: [],
        got: false,
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 10
        },
        searchFilter: {
          search: [],
          filter: [],
        }
      },
      // 进行中列表
      doingList: {
        list: [],
        got: false,
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 10
        },
        searchFilter: {
          search: [],
          filter: [],
        }
      },
      // 已结束列表
      doneList: {
        list: [],
        got: false,
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 10
        },
        searchFilter: {
          search: [],
          filter: [],
        }
      },
      taskDes: {
        dialog: false,
        content: ""
      },
      scoreList: {
        dialog: false,
        list: [],
        listLoading: false,
        course: {}
      },
      scoreInfo: {
        title: "成绩详情",
        dialog: false,
        info: {
          stepInfo: []
        },
        listLoading: false,
      },
      reportView: {
        dialog: false,
        content: "",
        report: {}
      },
      writeReport: {
        dialog: false,
        content: "",
      },
      writeComment: {
        dialog: false,
        content: ""
      },
    }
  },
  mounted() {
    this.getList(0, 10, {})
  },
  methods: {
    // 点击查看实验报告按钮
    async clickViewReportBtn(record, index) {
      let report = await CourseReportModel.getOneCourseRecordReport(record.courseRecordId)
      this.$set(this.reportView, "content", report.content)
      this.reportView.report = report
      this.reportView.content = report.content
      this.reportView.dialog = true
    },
    // 点击提交实验报告按钮
    async clickPostReportBtn() {
      let courseRecordId = this.writeReport.record.courseRecordId
      let content = this.writeReport.content
      if (content.length === 0) {
        msg_err("请先填写报告内容！")
        return
      }
      let result = await CourseReportModel.fillReport(courseRecordId, content).catch(res => {
        msg_success("提交实验失败！")
      })
      if (result) {
        msg_success("提交实验报告成功")
        this.$set(this[this.listName]["list"][this.writeReport.recordIndex], "reportFilled", true)
        this.writeReport.dialog = false;
      }
    },
    // 点击填写报告按钮
    async clickWriteReportBtn(record, index) {
      if (!record.courseTaskEntity[0]["needFillReport"]) {
        msg_err("此任务无需填写实验报告！")
        return
      }
      if (!record.courseCompleted) {
        msg_err("请先完成课程后，再填写实验报告！")
        return
      }
      if (record.reportFilled) {// 如果已经填写过报告且教师未批改
        if (record.reportCorrected) {
          msg_err("教师已批改您的报告，不能再次修改!")
          return;
        }
        // 获取报告详情
        let report = await CourseReportModel.getOneCourseRecordReport(record.courseRecordId)
        this.$set(this.writeReport, "content", report.content)
        this.writeReport.report = report
      }
      this.writeReport.dialog = true
      this.writeReport.record = record
      this.writeReport.recordIndex = index
    },
    // 点击我的成绩按钮
    clickShowScoreBtn(record, index) {
      this.$set(this.scoreList, "list", record.courseScoreEntity)
      this.$set(this.scoreList, "course", record.courseEntity[0])
      this.scoreList.dialog = true
    },
    // 点击成绩详情按钮
    clickShowScoreDetailBtn(score, index) {
      this.scoreInfo.dialog = true
      this.$set(this.scoreInfo, "info", score)
      this.$set(this.scoreInfo, "index", index)
    },
    // 点击课程评价按钮
    clickOpenCommentBtn(record, index) {
      if (record.userEntity[0]["commentCourseId"].indexOf(record.couresId) > -1) {
        msg_err("您已经评价过该课程啦！")
        return
      }
      if (!record.courseCompleted) {
        msg_err("请先至少完成一次课程后再评价！")
        return
      }
      this.writeComment.dialog = true
      this.writeComment.recordId = record.courseRecordId
      this.writeComment.index = index
    },
    // 当课程评价提交
    onCommentSubmit(result) {
      if (result === "success") {
        msg_success("提交评价成功！")
        this.writeComment.dialog = false
        window.location.reload()
      }
      if (result === "fail") {
        msg_success("提交评价失败！")
        this.writeComment.dialog = false
      }
    },
    // 点击任务介绍按钮
    clickShowTaskDesBtn(record) {
      let task = record.courseTaskEntity[0]
      this.$set(this.taskDes, "content", task.desText)
      this.taskDes.dialog = true
    },
    // 点击进入课程按钮
    clickEnterCourseBtn(record) {
      let course = record.courseEntity[0]
      this.$router.push({
        name: "CourseInfo",
        query: {
          "id": course.courseId
        }
      })
    },
    // 点击任务状态tab
    clickTaskTab(tab) {
      let status = tab.name
      let listName = status + "List"
      let got = this[listName]["got"]
      if (!got) {
        this.getList(0, 10, {})
      }
      this.listName = listName
    },
    // 获取列表
    async getList(page, size, query) {
      this.$set(this[this.listName], "loading", true)
      query = Object.assign(query, {
        status: this.tabShow
      });
      let [list, pages] = await CourseRecordModel.getOneUserTaskList(page - 1, size, "", query)
      list.forEach(li=>{
        li.courseScoreEntity.sort((a, b) => {// 成绩列表按从新到老排序
          return b.createTime - a.createTime
        })
      })
      this.$set(this[this.listName], "list", list)
      this.$set(this[this.listName], "pages", pages)
      this.$set(this[this.listName], "loading", false)
      this.$set(this[this.listName], "got", true)
    },
    // 分页-改变页码
    async pageChange(page) {
      this.getList(page, this[this.listName].pages.size, this[this.listName].query)
    },
    // 分页-改变每页显示数量
    async pageLimitChange(size) {
      this.getList(this[this.listName].pages.number - 1, size, this[this.listName].query)
    },
    // 点击搜索按钮
    clickSearchFilterBtn(query) {
      this[this.listName].query = query
      this.getList(0, this[this.listName].pages.size, this[this.listName].query)
    },
    // 当tinymce初始化完成
    onWriteTinymceInit() {
      // 手动赋值，当内容为空时，多个编辑框内容也会变化
      window.tinymce.get("tinymce_report_content").setContent(this.writeReport.content)
    },
    // 关闭实验报告填写弹窗
    closeWriteDialog() {
      // 适应弹窗打开多个tinymce的情况，每次关闭dialog时销毁，再次打开时初始化
      //this.$refs['tinymce_report_content'].destroyTinymce()
    },
  }
}
</script>

<style scoped lang="less">
@import '../../style/app.less';

.page-userTask {

}

// FontAwesome图标样式
.tab-icon {
  margin-right: 4px;
  color: #4093f9;
  font-size: 13px;
}

.info-icon {
  margin-right: 6px;
  color: #4093f9;
  font-size: 12px;
  width: 12px;
}

.stat-icon {
  margin-right: 6px;
  color: #4093f9;
  font-size: 12px;
  width: 12px;
}

.btn-icon {
  margin-right: 4px;
  font-size: 12px;
}

.dialog-icon {
  margin-right: 6px;
  color: #4093f9;
  font-size: 16px;
}

.course-list {
  > .li {
    border-bottom: 1px solid #f2f2f2;
    padding-bottom: 10px;
    margin-bottom: 10px;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0px;
    }

    .left-box {
      width: 250px;

      img.headImg {
        width: 230px;
        height: 140px;
      }
    }

    .right-box {
      width: 680px;

      div.name {
        span.name {
          font-size: 17px;
          font-weight: bold;
        }
      }

      div.task-info {
        margin-top: 10px;

        div.li {
          margin-bottom: 5px;

          span.title {
            font-size: 13px;
            color: #555;
          }

          span.text {
            font-size: 13px;
            color: #999;
          }
        }
      }

      div.statistics-info {
        margin-top: 11px;
        margin-bottom: 11px;

        > .li {
          margin-right: 20px;
          font-size: 14px;
          color: #666;

          .title {

          }

          .number {
            color: #25a170;
          }
        }
      }

      div.buttons {
        .el-button {
          margin-right: 5px;
        }
      }
    }
  }
}

.dialog-scoreInfo {
  .info {
    .li {
      margin-bottom: 10px;
      width: 33%;

      .title {
        font-size: 14px;
        color: #666;
      }

      .text {
        font-size: 14px;
        color: #999;
      }
    }

    .li.name {
      width: 100%;
    }
  }
}

</style>
