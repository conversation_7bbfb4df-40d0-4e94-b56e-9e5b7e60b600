<template>
  <div class="page-user flex flex-between content-container">
    <!--左侧导航-->
    <div class="user-left-container">
      <!--个人信息-->
      <el-card class="user-info-container flex flex-center flex-dr">
        <div>
          <img src="@/assets/imgs/user.png" alt="" class="avatar">
        </div>
        <div class="name">{{ userInfo.name }}</div>
        <div class="info">
          <span class="role" v-if="!userInfo.asSecretary">
            <font-awesome-icon :icon="getRoleIcon()" class="role-icon" />
            <template v-if="!userInfo.asSocial">
              {{ enums.roles[userInfo.role] }}
            </template>
            <template v-if="userInfo.asSocial">
              校外人员
            </template>
          </span>
          <span class="role" v-if="userInfo.asSecretary">
            <font-awesome-icon icon="user-tie" class="role-icon" />
            教秘
          </span>
          <span class="username">
            <font-awesome-icon icon="id-card" class="username-icon" />
            {{ userInfo.account }}
          </span>
        </div>
      </el-card>
      <!--导航菜单-->
      <el-card class="nav-container" :body-style="{padding:'10px 0px'}">
        <div :class="url==='/user/info'?'li active':'li'" @click="gotoUrl('/user/info')">
          <font-awesome-icon icon="user" class="nav-icon" />
          我的信息
        </div>
        <div :class="url==='/user/course'?'li active':'li'" @click="gotoUrl('/user/course')">
          <font-awesome-icon icon="graduation-cap" class="nav-icon" />
          我的课程
        </div>
        <div :class="url==='/user/task'?'li active':'li'" @click="gotoUrl('/user/task')">
          <font-awesome-icon icon="tasks" class="nav-icon" />
          我的任务
        </div>
        <div :class="url==='/user/favorite'?'li active':'li'" @click="gotoUrl('/user/favorite')">
          <font-awesome-icon icon="heart" class="nav-icon" />
          我的收藏
        </div>
        <!--        <div :class="route.path==='/user/sta'?'li active':'li'" @click="window.location.href='/user/sta'">学习统计</div>-->
        <!--        <div :class="route.path==='/user/message'?'li active':'li'" @click="window.location.href='/user/message'">我的消息</div>-->
      </el-card>
    </div>
    <!--右侧内容-->
    <el-card class="user-right-container">
      <router-view/>
    </el-card>
  </div>
</template>

<script>
import {mapState} from "vuex";
import enums from "@/enums/index"

export default {
  name: "UserIndex",
  computed: {
    ...mapState({
      userInfo: state => state.userInfo
    })
  },
  data() {
    return {
      enums: enums,
      window: window,
      route: this.$route,
      url: "/user/info",
      navShowIndex: 0,
    }
  },
  mounted() {
    this.url = "/user/info"
  },
  methods: {
    gotoUrl(url) {
      this.url = url
      this.$router.push(url)
    },
    getRoleIcon() {
      if (this.userInfo.asSocial) {
        return 'user-friends'
      }
      if (this.userInfo.role === 'student') {
        return 'user-graduate'
      } else if (this.userInfo.role === 'teacher') {
        return 'chalkboard-teacher'
      }
      return 'user'
    }
  }
}
</script>

<style scoped lang="less">
@import '../../style/app.less';

.page-user {
  margin-top: 20px;
  margin-bottom: 20px;
  align-items: start;
}

// FontAwesome图标样式
.role-icon {
  margin-right: 4px;
  color: #4093f9;
  font-size: 12px;
}

.username-icon {
  margin-right: 4px;
  color: #666;
  font-size: 12px;
}

.nav-icon {
  margin-right: 6px;
  color: #4093f9;
  font-size: 14px;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.user-left-container {
  width: 200px;

  .user-info-container {
    margin-bottom: 20px;

    img.avatar {
      width: 150px;
      height: 150px;
    }

    div.name {
      font-size: 16px;
      font-weight: 400;
      margin-top: 20px;
      margin-bottom: 20px;
      color: #333;
      text-align: center;
    }

    div.info {
      font-size: 14px;
      color: #666;
      text-align: center;

      span.role {
        margin-right: 10px;
      }
    }
  }

  .nav-container {
    background-color: #fff;

    .li {
      padding: 10px 20px;
      margin-bottom: 5px;
      font-size: 16px;
      cursor: pointer;
      text-align: center;
      border-right: 3px solid #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      overflow: hidden;

      &:hover, &.active {
        color: #4093f9;
        font-weight: bold;
        border-color: #4093f9;
        
        .nav-icon {
          color: #4093f9;
        }
      }
    }
  }
}

.user-right-container {
  width: 980px;
}
</style>
