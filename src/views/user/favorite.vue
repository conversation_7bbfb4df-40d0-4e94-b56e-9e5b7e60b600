<template>
  <div class="user-favorite-page">
    <div class="page-header">
      <h2><font-awesome-icon icon="heart" class="header-icon" />我的收藏</h2>
      <p>管理您收藏</p>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane name="resource">
          <span slot="label">
            <font-awesome-icon icon="folder" class="tab-icon" />
            教学资源
          </span>
        </el-tab-pane>
      </el-tabs>

      <div class="search-area">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索收藏内容"
          class="search-input"
          @keyup.enter.native="handleSearch"
        >
          <el-button slot="append" @click="handleSearch">
            <font-awesome-icon icon="search" />
          </el-button>
        </el-input>
      </div>
    </div>

    <!-- 收藏列表 -->
    <div class="favorite-list" v-loading="loading">
      <div class="favorite-grid" v-if="favoriteList.length > 0">
        <div class="favorite-item" v-for="item in favoriteList" :key="item.id">
          <div class="item-content" @click="goToDetail(item)">
            <div class="item-image" v-if="item.coverImage">
              <img :src="item.coverImage" :alt="item.title" />
            </div>
            <div class="item-info">
              <div class="item-type">
                <el-tag type="success" size="mini">教学资源</el-tag>
              </div>
              <h4 class="item-title">{{ item.title }}</h4>
              <p class="item-summary">{{ item.summary }}</p>
              <div class="item-meta">
                <span class="meta-date">
                  <font-awesome-icon icon="clock" class="meta-icon" />
                  {{ formatDate(item.favoriteTime) }}
                </span>
                <span class="meta-views">
                  <font-awesome-icon icon="eye" class="meta-icon" />
                  {{ item.views }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="item-actions">
            <el-button 
              type="text" 
              size="small" 
              @click="removeFavorite(item)"
              class="remove-btn"
            >
              <font-awesome-icon icon="trash" class="btn-icon" />
              取消收藏
            </el-button>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-else-if="!loading">
        <p>{{ getEmptyText() }}</p>
        <el-button type="primary" @click="goToExplore">
          <font-awesome-icon icon="search" class="btn-icon" />
          去发现内容
        </el-button>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container" v-if="total > 0">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {NewsModel} from '@/model/NewsModel'
import {ResourceModel} from '@/model/ResourceModel'

export default {
  name: 'UserFavorite',
  data() {
    return {
      activeTab: 'resource',
      searchKeyword: '',
      favoriteList: [],
      loading: false,
      currentPage: 1,
      pageSize: 12,
      total: 0
    }
  },
  
  created() {
    this.fetchFavoriteList()
  },
  
  methods: {
    // 获取收藏列表
    async fetchFavoriteList() {
      this.loading = true
      try {
        const params = {}
        if (this.searchKeyword) {
          params.keyword = this.searchKeyword
        }
        
        const result = await ResourceModel.getUserFavoriteResources(this.currentPage, this.pageSize, params)
        
        if (result) {
          // 处理收藏列表，将resourceEntity字段展开（现在是JSONArray）
          this.favoriteList = result.list.map(item => {
            // resourceEntity现在是JSONArray，取第一个元素
            const resource = (item.resourceEntity && item.resourceEntity.length > 0) 
              ? item.resourceEntity[0] 
              : {}
            return {
              favoriteId: item.favoriteId,
              id: resource.resourceId || resource.id,
              resourceId: resource.resourceId || resource.id,
              title: resource.title || '未知标题',
              summary: resource.description || resource.summary || '暂无描述',
              coverImage: resource.thumbnail || resource.coverImage,
              views: resource.views || 0,
              downloads: resource.downloads || 0,
              type: resource.type || 'resource',
              categoryName: resource.categoryName || '教学资源',
              author: resource.author || '未知作者',
              favoriteTime: item.createTime
            }
          })
          
          this.total = result.total
        }
        
      } catch (error) {
        this.$message.error('获取收藏列表失败')
        console.error('获取收藏列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchFavoriteList()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchFavoriteList()
    },
    
    // 跳转到详情
    goToDetail(item) {
      this.$router.push(`/resource/detail/${item.resourceId || item.id}`)
    },
    
    // 取消收藏
    async removeFavorite(item) {
      try {
        await this.$confirm('确定要取消收藏此内容吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const isFavorited = await ResourceModel.toggleResourceFavorite(item.resourceId || item.id)
        
        if (!isFavorited) {
          this.$message.success('已取消收藏')
          this.fetchFavoriteList()
        } else {
          this.$message.error('取消收藏失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消收藏失败:', error)
          this.$message.error('取消收藏失败')
        }
      }
    },
    
    // 切换标签
    handleTabClick() {
      this.currentPage = 1
      this.fetchFavoriteList()
    },
    
    // 去发现内容
    goToExplore() {
      this.$router.push('/resource')
    },
    
    
    // 获取空状态文本
    getEmptyText() {
      return '还没有收藏任何教学资源'
    },
    
    // 格式化日期
    formatDate(date) {
      return NewsModel.formatDate(date, 'YYYY-MM-DD')
    },
    
    // 模拟收藏数据
    getMockFavoriteList() {
      return [
        {
          id: 1,
          originalId: 1,
          type: 'news',
          title: '基地成功举办2024年虚拟仿真实验教学研讨会',
          summary: '为进一步推进虚拟仿真实验教学改革，提升实验教学质量，我基地于近日成功举办了2024年虚拟仿真实验教学研讨会。',
          coverImage: 'https://picsum.photos/120/80?random=1',
          views: 256,
          favoriteTime: '2024-01-16 10:30:00'
        },
        {
          id: 2,
          originalId: 2,
          type: 'news',
          title: '基地新增5个虚拟仿真实验项目通过教育部认定',
          summary: '近日，教育部发布了2024年度虚拟仿真实验教学项目认定结果，我基地申报的5个项目全部通过认定。',
          coverImage: 'https://picsum.photos/120/80?random=2',
          views: 189,
          favoriteTime: '2024-01-14 15:20:00'
        },
        {
          id: 3,
          originalId: 1,
          type: 'notice',
          title: '【重要】关于2024年春季学期实验教学安排的通知',
          summary: '为保障新学期实验教学工作顺利进行，现就相关事宜通知如下：1. 实验课程开课时间为2024年3月1日。',
          coverImage: 'https://picsum.photos/120/80?random=3',
          views: 458,
          favoriteTime: '2024-01-21 09:15:00'
        },
        {
          id: 4,
          originalId: 3,
          type: 'news',
          title: '基地与多家企业签署产学研合作协议',
          summary: '为深化产教融合，推进校企合作，我基地与华为、腾讯、阿里巴巴等知名企业签署了产学研合作协议。',
          coverImage: 'https://picsum.photos/120/80?random=4',
          views: 342,
          favoriteTime: '2024-01-12 14:45:00'
        },
        {
          id: 5,
          originalId: 2,
          type: 'notice',
          title: '基地实验室维护通知',
          summary: '定期维护设备，确保实验教学质量。维护时间：2024年1月25日-27日，期间相关实验室暂停使用。',
          coverImage: 'https://picsum.photos/120/80?random=5',
          views: 123,
          favoriteTime: '2024-01-19 11:30:00'
        },
        {
          id: 6,
          originalId: 1,
          type: 'resource',
          title: '计算机组成原理实验指导书',
          summary: '详细介绍计算机组成原理实验的理论基础、实验步骤和操作要点，帮助学生更好地理解计算机硬件工作原理。',
          coverImage: 'https://picsum.photos/120/80?random=6',
          views: 428,
          favoriteTime: '2024-01-18 16:20:00'
        },
        {
          id: 7,
          originalId: 3,
          type: 'resource',
          title: '数据结构与算法可视化演示',
          summary: '通过动画演示各种数据结构和算法的工作过程，包括排序算法、查找算法、图算法等核心内容。',
          coverImage: 'https://picsum.photos/120/80?random=7',
          views: 672,
          favoriteTime: '2024-01-15 11:45:00'
        },
        {
          id: 8,
          originalId: 5,
          type: 'resource',
          title: '虚拟仿真实验平台操作手册',
          summary: '提供虚拟仿真实验平台的详细操作指南，包括实验环境搭建、实验操作流程、常见问题解决方案等。',
          coverImage: 'https://picsum.photos/120/80?random=8',
          views: 356,
          favoriteTime: '2024-01-13 14:30:00'
        }
      ]
    }
  }
}
</script>

<style lang="less" scoped>

.user-favorite-page {
  .page-header {
    margin-bottom: 30px;
    
    h2 {
      font-size: 24px;
      color: #333;
      margin-bottom: 8px;
      display: flex;
      align-items: center;
      
      .header-icon {
        margin-right: 8px;
        color: #4093f9;
      }
    }
    
    p {
      color: #666;
      font-size: 14px;
      margin: 0;
    }
  }
  
  .filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    
    .search-area {
      margin-top: 15px;
      
      .search-input {
        max-width: 400px;
        
        .search-icon {
          color: #4093f9;
        }
        
        ::v-deep .el-input__inner {
          border-radius: 20px 0 0 20px;
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 20px 20px 0;
          
          .el-button {
            border-radius: 0 20px 20px 0;
            background-color: #4093f9;
            border-color: #4093f9;
            color: white;
          }
        }
      }
    }
    
    .tab-icon {
      margin-right: 4px;
      color: #4093f9;
      font-size: 13px;
    }
  }
  
  .favorite-list {
    min-height: 400px;
    
    .favorite-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
      
      .favorite-item {
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-4px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .item-content {
          padding: 15px;
          cursor: pointer;
          
          .item-image {
            width: 100%;
            height: 120px;
            margin-bottom: 12px;
            border-radius: 6px;
            overflow: hidden;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
          
          .item-info {
            .item-type {
              margin-bottom: 8px;
            }
            
            .item-title {
              font-size: 16px;
              color: #333;
              margin-bottom: 8px;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              
              &:hover {
                color: #4093f9;
              }
            }
            
            .item-summary {
              font-size: 13px;
              color: #666;
              line-height: 1.5;
              margin-bottom: 10px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            .item-meta {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #999;
              
              .meta-date, .meta-views {
                display: flex;
                align-items: center;
                gap: 4px;
                
                .meta-icon {
                  color: #4093f9;
                  font-size: 11px;
                }
              }
            }
          }
        }
        
        .item-actions {
          padding: 12px 15px;
          border-top: 1px solid #f0f0f0;
          text-align: right;
          
          .remove-btn {
            color: #f56c6c;
            
            .btn-icon {
              margin-right: 4px;
              font-size: 12px;
            }
            
            &:hover {
              color: darken(#f56c6c, 10%);
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      
      .empty-icon {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 16px;
        display: block;
      }
      
      .btn-icon {
        margin-right: 4px;
        font-size: 12px;
      }
      
      p {
        font-size: 16px;
        color: #999;
        margin: 0 0 20px 0;
      }
    }
  }
  
  .pagination-container {
    text-align: center;
    margin-top: 30px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .user-favorite-page {
    .favorite-list {
      .favorite-grid {
        grid-template-columns: 1fr;
        gap: 15px;
      }
    }
    
    .filter-section {
      padding: 15px;
      
      .search-area {
        .search-input {
          max-width: 100%;
        }
      }
    }
  }
}
</style>