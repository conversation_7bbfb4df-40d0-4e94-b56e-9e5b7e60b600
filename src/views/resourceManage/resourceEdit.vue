<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-page-header @back="goBack" :content="pageTitle">
      </el-page-header>
    </div>

    <!-- 表单区域 -->
    <el-card style="margin-top: 20px;">
      <el-form ref="resourceForm" :model="formData" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="资源标题:" prop="title">
              <el-input v-model.trim="formData.title" placeholder="请输入资源标题">
                <div slot="suffix" v-if="formData.title">
                  <span class="el-input__count">
                    <span class="el-input__count-inner">
                      {{ formData.title.length }} / 100
                    </span>
                  </span>
                </div>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="资源分类:" prop="categoryId">
              <el-select v-model="formData.categoryId" placeholder="请选择资源分类" style="width: 100%;" @change="handleCategoryChange">
                <el-option v-for="category in categoryOptions" :key="category.categoryId"
                           :label="category.name" :value="category.categoryId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="作者:" prop="author">
              <el-input v-model.trim="formData.author" placeholder="请输入作者姓名">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="文件类型:" prop="type">
              <el-select v-model="formData.type" placeholder="请选择文件类型"
                         @change="onFileTypeChange" style="width: 100%;">
                <el-option 
                  v-for="item in enums.resourceFileTypes" 
                  :key="item.value" 
                  :label="item.label" 
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="资源描述:" prop="description">
          <el-input type="textarea" v-model.trim="formData.description"
                    placeholder="请输入资源描述" :rows="4">
          </el-input>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="文件URL:" prop="fileUrl">
              <el-input v-model.trim="formData.fileUrl" placeholder="请输入文件下载地址">
                <el-button slot="append" @click="uploadFile">上传文件</el-button>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="缩略图URL:">
              <el-input v-model.trim="formData.thumbnail" placeholder="请输入缩略图地址">
                <el-button slot="append" @click="uploadThumbnail">上传图片</el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="文件大小(字节):" prop="fileSize">
              <el-input-number v-model="formData.fileSize" :min="0" style="width: 100%;"
                               placeholder="文件大小">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否可预览:">
              <el-switch v-model="formData.canPreview"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否可下载:">
              <el-switch v-model="formData.canDownload"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="资源标签:">
          <el-tag v-for="tag in formData.tags" :key="tag" closable @close="removeTag(tag)"
                  style="margin-right: 10px;">
            {{ tag }}
          </el-tag>
          <el-input v-if="tagInputVisible" ref="tagInput" v-model="tagInputValue"
                    size="small" style="width: 120px;" @keyup.enter.native="confirmTag"
                    @blur="confirmTag">
          </el-input>
          <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="关联课程:">
              <el-switch v-model="formData.relatedToCourse" @change="onCourseRelationChange">
              </el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="formData.relatedToCourse">
            <el-form-item label="课程名称:" prop="courseName">
              <el-select
                v-model="formData.courseId"
                placeholder="请选择课程"
                filterable
                remote
                :remote-method="searchCourses"
                :loading="courseLoading"
                style="width: 100%;"
                @change="onCourseSelect"
                clearable>
                <el-option
                  v-for="course in courseOptions"
                  :key="course.courseId"
                  :label="course.name"
                  :value="course.courseId">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="语言:">
              <el-select v-model="formData.language" placeholder="请选择语言" style="width: 100%;">
                <el-option label="中文" value="中文"></el-option>
                <el-option label="English" value="English"></el-option>
                <el-option label="日本語" value="日本語"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="难度:">
              <el-select v-model="formData.difficulty" placeholder="请选择难度" style="width: 100%;">
                <el-option label="初级" value="初级"></el-option>
                <el-option label="中级" value="中级"></el-option>
                <el-option label="高级" value="高级"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态:" prop="status">
              <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%;">
                <el-option label="正常" value="active"></el-option>
                <el-option label="隐藏" value="hidden"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 视频/音频特有字段 -->
        <el-row :gutter="20" v-if="formData.type === 'video' || formData.type === 'audio'">
          <el-col :span="12">
            <el-form-item label="时长:">
              <el-input v-model.trim="formData.duration" placeholder="如: 45分钟">
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="formData.type === 'video'">
            <el-form-item label="分辨率:">
              <el-input v-model.trim="formData.resolution" placeholder="如: 1920x1080">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- PDF特有字段 -->
        <el-row :gutter="20" v-if="formData.type === 'pdf'">
          <el-col :span="12">
            <el-form-item label="预览页数:">
              <el-input-number v-model="formData.previewPages" :min="0" style="width: 100%;"
                               placeholder="PDF可预览的页数">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预计阅读时间:">
              <el-input v-model.trim="formData.estimatedReadTime" placeholder="如: 6小时">
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 缩略图预览 -->
        <el-form-item label="缩略图预览:" v-if="formData.thumbnail">
          <img :src="formData.thumbnail" style="max-width: 200px; max-height: 150px; border-radius: 4px;">
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item style="margin-top: 30px;">
          <el-button type="primary" @click="submitForm" :loading="submitLoading">
            {{ isEdit ? '更新资源' : '创建资源' }}
          </el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 文件上传组件（隐藏） -->
    <input ref="fileInput" type="file" style="display: none;" @change="handleFileUpload">
    <input ref="imageInput" type="file" accept="image/*" style="display: none;" @change="handleImageUpload">
  </div>
</template>

<script>
import {ResourceModel, ResourceCategoryModel} from "@/model/ResourceModel";
import {msg_success, msg_err, msg_confirm} from "@/utils/ele_component";
import {FileModel} from "@/model/FileModel";
import {CourseModel} from "@/model/CourseModel";
import {FILE_URL} from "@/config/main";
import enums from "@/enums/index";

export default {
  name: "resourceEdit",
  data() {
    const validateTitle = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入资源标题'))
        return
      }
      if (value.length > 100) {
        callback(new Error('标题最多100个字符'))
        return
      }
      callback()
    }

    const validateDescription = (rule, value, callback) => {
      if (value && value.length > 500) {
        callback(new Error('描述最多500个字符'))
        return
      }
      callback()
    }

    return {
      enums,
      isEdit: false,
      resourceId: '',
      submitLoading: false,
      categoryOptions: [],
      courseOptions: [],
      courseLoading: false,

      // 标签输入相关
      tagInputVisible: false,
      tagInputValue: '',

      formData: {
        title: '',
        description: '',
        type: '',
        typeName: '',
        categoryId: '',
        author: '',
        fileUrl: '',
        thumbnail: '',
        fileSize: 0,
        tags: [],
        relatedToCourse: false,
        courseId: '',
        courseName: '',
        canDownload: true,
        canPreview: true,
        language: '中文',
        difficulty: '中级',
        estimatedReadTime: '',
        previewPages: 0,
        duration: '',
        resolution: '',
        format: '',
        status: 'active'
      },

      formRules: {
        title: [
          { validator: validateTitle, trigger: 'blur' }
        ],
        description: [
          { validator: validateDescription, trigger: 'blur' }
        ],
        categoryId: [
          { required: true, message: '请选择资源分类', trigger: 'change' }
        ],
        author: [
          { required: true, message: '请输入作者姓名', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择文件类型', trigger: 'change' }
        ],
        fileUrl: [
          { required: true, message: '请输入文件URL', trigger: 'blur' }
        ],
        fileSize: [
          { required: true, message: '请输入文件大小', trigger: 'blur' }
        ],
        courseName: [
          { required: true, message: '请输入课程名称', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },

  computed: {
    pageTitle() {
      return this.isEdit ? '编辑资源' : '新增资源';
    }
  },

  async mounted() {
    // 获取URL参数
    const type = this.$route.query.type;
    const id = this.$route.query.id;

    this.isEdit = type === 'edit';
    if (this.isEdit) {
      this.resourceId = id;
    }

    // 初始化数据
    await this.initData();

    // 如果是编辑模式，加载资源数据
    if (this.isEdit && this.resourceId) {
      await this.loadResourceData();
    }
  },

  methods: {
    // 初始化数据
    async initData() {
      try {
        // 获取资源分类列表
        this.categoryOptions = await ResourceCategoryModel.getList({status: 'active'});

        // 获取所有课程列表
        this.courseOptions = await CourseModel.getList({});
      } catch (error) {
        console.error('初始化数据失败:', error);
        msg_err('初始化数据失败');
      }
    },
    // 资源分类改变
    handleCategoryChange(value) {
      // 清空课程选项
      this.categoryName=value
    },
    // 加载资源数据（编辑模式）
    async loadResourceData() {
      try {
        const resourceData = await ResourceModel.getOne(this.resourceId);
        if (resourceData) {
          // 处理标签数据
          if (resourceData.tags && Array.isArray(resourceData.tags)) {
            resourceData.tags = resourceData.tags;
          } else if (resourceData.tags) {
            resourceData.tags = [];
          } else {
            resourceData.tags = [];
          }

          Object.assign(this.formData, resourceData);

          // 如果资源关联了课程，需要将课程信息添加到选项中以便回显
          if (resourceData.relatedToCourse && resourceData.courseId && resourceData.courseName) {
            this.courseOptions = [{
              courseId: resourceData.courseId,
              name: resourceData.courseName
            }];
          }
        } else {
          msg_err('资源不存在');
          this.goBack();
        }
      } catch (error) {
        console.error('加载资源数据失败:', error);
        msg_err('加载资源数据失败');
      }
    },

    // 文件类型变化
    onFileTypeChange(type) {
      const typeNameMap = {
        'pdf': 'PDF文档',
        'docx': 'Word文档',
        'pptx': 'PowerPoint',
        'xlsx': 'Excel表格',
        'video': '视频文件',
        'audio': '音频文件',
        'image': '图片文件',
        'zip': 'ZIP压缩包',
        'rar': 'RAR压缩包',
        'txt': '文本文件'
      };
      this.formData.typeName = typeNameMap[type] || type;
    },

    // 课程关联变化
    onCourseRelationChange(value) {
      if (!value) {
        this.formData.courseId = '';
        this.formData.courseName = '';
        this.courseOptions = [];
      }
    },

    // 搜索课程
    async searchCourses(query) {
      try {
        this.courseLoading = true;
        let searchParams = {};

        // 如果有搜索词，添加正则过滤条件
        if (query && query.trim() !== '') {
          searchParams = {
            name: {
              '$regex': `.*${query}.*`
            }
          };
        }

        const courses = await CourseModel.getList(searchParams);
        this.courseOptions = courses || [];
      } catch (error) {
        console.error('搜索课程失败:', error);
        this.courseOptions = [];
      } finally {
        this.courseLoading = false;
      }
    },

    // 选择课程
    onCourseSelect(courseId) {
      if (courseId) {
        const selectedCourse = this.courseOptions.find(course => course.courseId === courseId);
        if (selectedCourse) {
          this.formData.courseId = selectedCourse.courseId;
          this.formData.courseName = selectedCourse.name;
        }
      } else {
        this.formData.courseId = '';
        this.formData.courseName = '';
      }
    },

    // 显示标签输入框
    showTagInput() {
      this.tagInputVisible = true;
      this.$nextTick(() => {
        this.$refs.tagInput.$refs.input.focus();
      });
    },

    // 确认添加标签
    confirmTag() {
      const value = this.tagInputValue.trim();
      if (value && !this.formData.tags.includes(value)) {
        this.formData.tags.push(value);
      }
      this.tagInputVisible = false;
      this.tagInputValue = '';
    },

    // 删除标签
    removeTag(tag) {
      const index = this.formData.tags.indexOf(tag);
      if (index > -1) {
        this.formData.tags.splice(index, 1);
      }
    },

    // 上传文件
    uploadFile() {
      this.$refs.fileInput.click();
    },

    // 上传缩略图
    uploadThumbnail() {
      this.$refs.imageInput.click();
    },

    // 处理文件上传
    async handleFileUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const uploadResult = await FileModel.uploadOne(file);
        if (uploadResult) {
          this.formData.fileUrl = FILE_URL + uploadResult.location;
          this.formData.fileSize = uploadResult.byteSize;
          msg_success('文件上传成功');
        } else {
          msg_err('文件上传失败');
        }
      } catch (error) {
        console.error('文件上传失败:', error);
        msg_err('文件上传失败');
      }

      // 清空input值
      event.target.value = '';
    },

    // 处理图片上传
    async handleImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      try {
        const uploadResult = await FileModel.uploadOne(file);
        if (uploadResult) {
          this.formData.thumbnail = FILE_URL + uploadResult.location;
          msg_success('图片上传成功');
        } else {
          msg_err('图片上传失败');
        }
      } catch (error) {
        console.error('图片上传失败:', error);
        msg_err('图片上传失败');
      }

      // 清空input值
      event.target.value = '';
    },

    // 提交表单
    async submitForm() {
      this.$refs.resourceForm.validate(async (valid) => {
        if (!valid) return;

        if (this.submitLoading) return;

        try {
          this.submitLoading = true;

          // 准备提交数据
          const submitData = {
            ...this.formData
          };

          // 设置文件类型名称
          this.onFileTypeChange(submitData.type);
          submitData.typeName = this.formData.typeName;

          // 处理课程关联
          if (!submitData.relatedToCourse) {
            submitData.courseId = '';
            submitData.courseName = '';
          }

          // 如果是编辑模式，保留原有的统计数据
          if (this.isEdit) {
            submitData.resourceId = this.resourceId;
          }

          const result = await ResourceModel.addOrEdit(submitData);

          if (result.code === "000000") {
            msg_success(this.isEdit ? '更新成功' : '创建成功');
            this.goBack();
          } else {
            msg_err(result.message || '操作失败');
          }
        } catch (error) {
          console.error('提交失败:', error);
          msg_err('操作失败');
        } finally {
          this.submitLoading = false;
        }
      });
    },

    // 返回
    goBack() {
      this.$router.push('/resource/resource');
    }
  }
}
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.el-tag {
  margin-right: 10px;
  margin-bottom: 10px;
}
</style>
