<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()"
                     style="background-color: #67C23A;border-color:#67C23A">
            新增资源
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="资源标题" align="left" min-width="200">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center;">
            <img v-if="scope.row.thumbnail" :src="scope.row.thumbnail"
                 style="width: 40px; height: 30px; object-fit: cover; margin-right: 10px; border-radius: 4px;">
            <div>
              <div style="font-weight: bold; margin-bottom: 4px;">{{ scope.row.title }}</div>
              <div style="font-size: 12px; color: #909399;">{{ scope.row.description | truncate(50) }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" width="120">
        <template slot-scope="scope">
          <el-tag type="primary">{{ scope.row.categoryEntity[0]["name"] }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件类型" align="center" width="100">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <i :class="getResourceTypeIcon(scope.row.type)" style="margin-right: 5px;"></i>
            <span>{{ scope.row.typeName }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="作者" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ scope.row.author }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关联课程" align="center" width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.relatedToCourse" type="success">{{ scope.row.courseName }}</el-tag>
          <el-tag v-else type="info">公共资源</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="文件大小" align="center" width="100">
        <template slot-scope="scope">
          <span>{{ formatFileSize(scope.row.fileSize) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="统计数据" align="center" width="150">
        <template slot-scope="scope">
          <div style="font-size: 12px;">
            <div>浏览: {{ scope.row.views || 0 }}</div>
            <div>下载: {{ scope.row.downloads || 0 }}</div>
            <div>收藏: {{ scope.row.favorites || 0 }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="权限" align="center" width="100">
        <template slot-scope="scope">
          <div style="font-size: 12px;">
            <el-tag v-if="scope.row.canPreview" type="success" size="mini">可预览</el-tag>
            <el-tag v-if="scope.row.canDownload" type="primary" size="mini">可下载</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '正常' : '隐藏' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="上传时间" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.uploadTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="text" size="mini" round
                     @click="ListMethods().clickViewBtn(scope.row)">查看
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number"
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes"
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--查看详情弹窗-->
    <el-dialog title="资源详情"
               :visible.sync="viewDialog.visible"
               width="800px"
               center>
      <div v-if="viewDialog.data" style="padding: 20px;">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="资源标题">{{ viewDialog.data.title }}</el-descriptions-item>
          <el-descriptions-item label="资源分类">{{ viewDialog.data.categoryName }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ viewDialog.data.typeName }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(viewDialog.data.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ viewDialog.data.author }}</el-descriptions-item>
          <el-descriptions-item label="上传时间">{{ viewDialog.data.uploadTime | dateFormat }}</el-descriptions-item>
          <el-descriptions-item label="关联课程">
            <el-tag v-if="viewDialog.data.relatedToCourse" type="success">{{ viewDialog.data.courseName }}</el-tag>
            <el-tag v-else type="info">公共资源</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="viewDialog.data.status === 'active' ? 'success' : 'danger'">
              {{ viewDialog.data.status === 'active' ? '正常' : '隐藏' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="浏览量">{{ viewDialog.data.views || 0 }}</el-descriptions-item>
          <el-descriptions-item label="下载量">{{ viewDialog.data.downloads || 0 }}</el-descriptions-item>
          <el-descriptions-item label="收藏量">{{ viewDialog.data.favorites || 0 }}</el-descriptions-item>
          <el-descriptions-item label="权限设置">
            <el-tag v-if="viewDialog.data.canPreview" type="success" size="small">可预览</el-tag>
            <el-tag v-if="viewDialog.data.canDownload" type="primary" size="small">可下载</el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>资源描述</h4>
          <div style="padding: 10px; background: #f5f5f5; border-radius: 4px; margin-top: 10px;">
            {{ viewDialog.data.description || '暂无描述' }}
          </div>
        </div>

        <div v-if="viewDialog.data.tags && viewDialog.data.tags.length > 0" style="margin-top: 20px;">
          <h4>资源标签</h4>
          <div style="margin-top: 10px;">
            <el-tag v-for="tag in viewDialog.data.tags" :key="tag" size="small" style="margin-right: 8px;">
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <div v-if="viewDialog.data.thumbnail" style="margin-top: 20px;">
          <h4>缩略图</h4>
          <img :src="viewDialog.data.thumbnail" style="max-width: 200px; max-height: 150px; border-radius: 4px; margin-top: 10px;">
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="viewDialog.visible = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {ResourceModel, ResourceCategoryModel} from "@/model/ResourceModel";

export default {
  name: "resourceManage",
  components: {ListSearchFilter},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {
    dateFormat,
    truncate: function (value, length) {
      if (!value) return ''
      if (value.length <= length) return value
      return value.substring(0, length) + '...'
    }
  },
  data() {
    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,

      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 20  // 默认分页大小
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '资源标题',
              key: 'title',
              value: '',
              format: function (v) {
                return v
              }
            },
            {
              type: 'input',
              label: '作者',
              key: 'author',
              value: '',
              format: function (v) {
                return v
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '资源分类',
              key: 'categoryId',
              value: '',
              data: [],
              change: function (value) {
                // 分类变化回调
              }
            },
            {
              type: 'select',
              label: '文件类型',
              key: 'type',
              value: '',
              data: [{label: '全部', value: ''}, ...enums.resourceFileTypes]
            },
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '正常', value: 'active'},
                {label: '隐藏', value: 'hidden'}
              ]
            }
          ]
        }
      },

      // 查看详情弹窗
      viewDialog: {
        visible: false,
        data: null
      }
    }
  },

  async mounted() {
    // 初始化筛选条件
    await this.ListMethods().initFilter()
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await ResourceModel.getPageList(page, size, "", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.$router.push('/resource/resourceEdit?type=add');
        },

        // 编辑按钮点击
        async clickEditBtn(entity) {
          $this.$router.push(`/resource/resourceEdit?type=edit&id=${entity.resourceId}`);
        },

        // 查看按钮点击
        clickViewBtn(entity) {
          $this.viewDialog.data = entity;
          $this.viewDialog.visible = true;
        },

        // 删除按钮点击
        async clickDeleteBtn(entity) {
          if (await msg_confirm('确认要删除该资源吗？此操作不可撤销！')) {
            let result = await ResourceModel.deleteOne(entity.resourceId)
            if (result !== false) {
              msg_success("删除成功")
              this.getList(0, $this.lists.pages.size, $this.lists.query)
            } else {
              msg_err("删除失败")
            }
          }
        },

        // 初始化筛选条件
        async initFilter() {
          // 获取资源分类列表
          const categories = await ResourceCategoryModel.getList({status: 'active'});
          const categoryOptions = [
            {label: '全部', value: ''}
          ];
          categories.forEach(category => {
            categoryOptions.push({
              label: category.name,
              value: category.categoryId
            });
          });

          // 更新分类筛选选项
          const categoryFilter = $this.lists.searchFilter.filter.find(item => item.key === 'categoryId');
          if (categoryFilter) {
            categoryFilter.data = categoryOptions;
          }
        }
      }
    },

    // 工具方法
    getResourceTypeIcon(type) {
      const iconMap = {
        'pdf': 'el-icon-document',
        'docx': 'el-icon-document',
        'doc': 'el-icon-document',
        'video': 'el-icon-video-camera',
        'audio': 'el-icon-headset',
        'image': 'el-icon-picture',
        'zip': 'el-icon-folder-opened',
        'rar': 'el-icon-folder-opened'
      };
      return iconMap[type] || 'el-icon-document';
    },

    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 B';
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      const size = (bytes / Math.pow(1024, i)).toFixed(1);
      return `${size} ${sizes[i]}`;
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}
</style>
