<template>
  <div class="app-container">
    <!--顶部操作按钮-->
    <div class="top-tools">
      <div style="text-align: right">
        <el-button class="el-button" type="success"
                   style="background-color: #67C23A;border-color:#67C23A"
                   @click="ListMethods().clickAddBtn()">
          新增资源分类
        </el-button>
      </div>
    </div>

    <!--列表-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="分类名称" align="center" min-width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center" min-width="250">
        <template slot-scope="scope">
          <span>{{ scope.row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column label="图标" align="center" width="80">
        <template slot-scope="scope">
          <i :class="scope.row.icon" style="font-size: 18px;"></i>
        </template>
      </el-table-column>
      <el-table-column label="主题色" align="center" width="100">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center; justify-content: center;">
            <div :style="{width: '20px', height: '20px', backgroundColor: scope.row.color, borderRadius: '4px'}"></div>
            <span style="margin-left: 8px;">{{ scope.row.color }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.sort }}</span>
        </template>
      </el-table-column>
      <el-table-column label="资源数量" align="center" width="100">
        <template slot-scope="scope">
          <el-tag type="info">{{ scope.row.resourceCount || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="浏览次数" align="center" width="100">
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.viewCount || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--弹窗-->
    <el-dialog :title="entityInfo.title"
               :visible.sync="entityInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="1000px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="entityInfoForm"
                 :model="entityInfo.edit" :rules="entityInfo.formRules">
          <el-form-item label="分类名称:" prop="name">
            <el-input v-model.trim="entityInfo.edit.name" placeholder="请输入分类名称">
              <div slot="suffix" v-if="entityInfo.edit.name">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ entityInfo.edit.name.length }} / 20
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="分类描述:" prop="description">
            <el-input type="textarea" v-model.trim="entityInfo.edit.description"
                      placeholder="请输入分类描述" :rows="3">
            </el-input>
          </el-form-item>
          <el-form-item label="图标:" prop="icon">
            <IconSelector v-model="entityInfo.edit.icon" />
          </el-form-item>
          <el-form-item label="主题色:" prop="color">
            <el-color-picker v-model="entityInfo.edit.color" show-alpha></el-color-picker>
            <el-input v-model.trim="entityInfo.edit.color" placeholder="请选择或输入颜色值"
                      style="margin-left: 10px; width: 200px;">
            </el-input>
          </el-form-item>
          <el-form-item label="排序:" prop="sort">
            <el-input-number v-model="entityInfo.edit.sort" :min="0" :max="999" placeholder="排序值，数字越小越靠前">
            </el-input-number>
          </el-form-item>
          <el-form-item label="状态:" prop="status">
            <el-radio-group v-model="entityInfo.edit.status">
              <el-radio label="active">启用</el-radio>
              <el-radio label="hidden">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="entityInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()"
                   v-if="entityInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="entityInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {ResourceCategoryModel} from "@/model/ResourceModel";
import IconSelector from "@/components/IconSelector";

export default {
  name: "resourceCategoryManage",
  directives: {elDragDialog, permission},
  components: {IconSelector},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 表单验证函数
    const validateName = (rule, value, callback) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入分类名称'))
        return
      }
      if (value.length > 20) {
        callback(new Error('最多输入20个字，当前已输入' + value.length + "个字"))
      }
      // 特殊字符检测
      let regEn = /[`!#$%^&*()@_+<>?:"{},.\/;'[\]]/im,
        regCn = /[·！#￥（——）：；""'、，|《。》？、【】[\]]/im;
      if (regEn.test(value) || regCn.test(value)) {
        callback(new Error('不支持特殊符号'));
      }
      callback()
    }

    const validateDescription = (rule, value, callback) => {
      if (value && value.length > 200) {
        callback(new Error('最多输入200个字，当前已输入' + value.length + "个字"))
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,

      // 列表数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "",
        pages: {
          size: 1000  // 不分页时设置较大值
        }
      },

      // 实体信息
      entityInfo: {
        dialog: false,
        title: "新增资源分类",
        type: "add",
        filter: {},
        edit: {
          name: "",
          description: "",
          icon: "el-icon-document",
          color: "#409EFF",
          sort: 0,
          status: "active"
        },
        formRules: {
          'name': {
            required: true,
            validator: validateName,
            trigger: 'blur'
          },
          'description': {
            validator: validateDescription,
            trigger: 'blur'
          },
          'icon': {
            required: true,
            message: '请输入图标类名',
            trigger: 'blur'
          },
          'color': {
            required: true,
            message: '请选择主题色',
            trigger: 'change'
          },
          'sort': {
            required: true,
            type: 'number',
            message: '请输入排序值',
            trigger: 'blur'
          },
          'status': {
            required: true,
            message: '请选择状态',
            trigger: 'change'
          }
        }
      }
    }
  },

  mounted() {
    // 获取列表
    this.ListMethods().getList({})
  },

  methods: {
    // 列表方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表（不分页）
        async getList(query) {
          $this.lists.loading = true;
          $this.lists.list = await ResourceCategoryModel.getList(query)
          $this.lists.loading = false
        },

        // 新增按钮
        async clickAddBtn() {
          $this.entityInfo.dialog = true;
          $this.entityInfo.type = "add"
          $this.entityInfo.title = "新增资源分类"
          $this.entityInfo.edit = {
            name: "",
            description: "",
            icon: "el-icon-document",
            color: "#409EFF",
            sort: 0,
            status: "active"
          };
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          });
        },

        // 编辑按钮
        async clickEditBtn(entity) {
          entity = JSON.parse(JSON.stringify(entity))
          $this.entityInfo.dialog = true;
          $this.$set($this.entityInfo, "edit", entity);
          $this.entityInfo.type = "edit"
          $this.entityInfo.title = "编辑资源分类"
          setTimeout(() => {
            $this.$refs['entityInfoForm'].clearValidate()
          }, 300)
        },

        // 删除按钮
        async clickDeleteBtn(entity) {
          if (await msg_confirm('确认要删除该资源分类吗？此操作不可撤销！')) {
            let result = await ResourceCategoryModel.deleteOne(entity.categoryId)
            if (result !== false) {
              msg_success("删除成功")
              this.getList({})
            } else {
              msg_err("删除失败")
            }
          }
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 新增提交
        async clickAddBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查
              let listResult = await ResourceCategoryModel.getList({
                name: $this.entityInfo.edit.name
              })
              if (listResult.length > 0) {
                msg_err("已存在同名资源分类，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要新增该资源分类吗？')) {
                let result = await ResourceCategoryModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['entityInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查（排除自身）
              let listResult = await ResourceCategoryModel.getList({
                name: $this.entityInfo.edit.name,
                categoryId: {"$ne": $this.entityInfo.edit.categoryId}
              })
              if (listResult.length > 0) {
                msg_err("已存在同名资源分类，请修改后再试！")
                return
              }
              if (await msg_confirm('确认要编辑该资源分类吗？')) {
                let result = await ResourceCategoryModel.addOrEdit($this.entityInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList({})
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.entityInfo.dialog = false
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.top-tools {
  margin-bottom: 15px;
}
</style>
