<template>
  <div class="news-detail-page">
    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-button">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回列表</el-button>
      </div>

      <!-- 新闻详情 -->
      <div class="news-detail" v-if="newsDetail" v-loading="loading">
        <div class="news-header">
          <h1 class="news-title">{{ newsDetail.title }}</h1>
          <div class="news-meta">
            <span class="meta-item">
              <i class="el-icon-time"></i>
              发布时间：{{ formatDate(newsDetail.publishTime, 'YYYY-MM-DD HH:mm') }}
            </span>
            <span class="meta-item">
              <i class="el-icon-user"></i>
              作者：{{ newsDetail.author }}
            </span>
            <span class="meta-item">
              <i class="el-icon-view"></i>
              浏览量：{{ newsDetail.views }}
            </span>
            <span class="meta-item">
              <i class="el-icon-folder"></i>
              分类：{{ getCategoryName(newsDetail.category) }}
            </span>
          </div>
          
          <!-- 标签 -->
          <div class="news-tags" v-if="newsDetail.tags && newsDetail.tags.length > 0">
            <el-tag v-for="tag in newsDetail.tags" :key="tag" size="small" type="info">
              {{ tag }}
            </el-tag>
          </div>
        </div>

        <div class="news-content">
          <div class="content-wrapper html-view" v-html="newsDetail.content"></div>
        </div>

        <!-- 分享和操作 -->
        <div class="news-actions" v-if="false">
          <div class="action-buttons">
            <el-button type="primary" @click="shareNews">
              <i class="el-icon-share"></i>
              分享
            </el-button>
            <el-button @click="collectNews" :type="isCollected ? 'warning' : 'default'">
              <i :class="isCollected ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
              {{ isCollected ? '已收藏' : '收藏' }}
            </el-button>
          </div>
        </div>

        <!-- 相关推荐 -->
        <div class="related-news" v-if="relatedNews.length > 0">
          <h3>相关新闻</h3>
          <div class="related-list">
            <div class="related-item" v-for="item in relatedNews" :key="item.id" @click="goToDetail(item.id)">
              <img :src="item.coverImage" :alt="item.title" />
              <div class="related-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.summary }}</p>
                <span class="related-date">{{ formatDate(item.publishTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div class="empty-state" v-else-if="!loading">
        <i class="el-icon-warning"></i>
        <p>未找到相关新闻</p>
        <el-button type="primary" @click="goBack">返回列表</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {NewsModel} from '@/model/NewsModel'
import {ConfigModel} from '@/model/ConfigModel'

export default {
  name: 'NewsDetail',
  data() {
    return {
      newsDetail: null,
      relatedNews: [],
      loading: false,
      isCollected: false,
      newsCategories: []
    }
  },
  
  async created() {
    await this.loadNewsCategories()
    this.fetchNewsDetail()
  },
  
  watch: {
    '$route'(to, from) {
      if (to.path !== from.path) {
        this.fetchNewsDetail()
      }
    }
  },
  
  methods: {
    // 加载新闻分类配置
    async loadNewsCategories() {
      try {
        const config = JSON.parse(await ConfigModel.getConfig("newsConfig"))
        if (config && config.categories) {
          this.newsCategories = JSON.parse(config.categories)
        } else {
          // 默认分类
          this.newsCategories = [
            {label: '基地动态', value: 'base_news'},
            {label: '活动通知', value: 'activity'},
            {label: '学术交流', value: 'academic'},
            {label: '成果展示', value: 'achievement'}
          ]
        }
      } catch (error) {
        console.error('加载新闻分类配置失败:', error)
        this.newsCategories = [
          {label: '基地动态', value: 'base_news'},
          {label: '活动通知', value: 'activity'},
          {label: '学术交流', value: 'academic'},
          {label: '成果展示', value: 'achievement'}
        ]
      }
    },
    
    // 根据分类值获取分类名称
    getCategoryName(categoryValue) {
      if (!categoryValue || !this.newsCategories.length) return categoryValue
      const category = this.newsCategories.find(cat => cat.value === categoryValue)
      return category ? category.label : categoryValue
    },
    
    // 获取新闻详情
    async fetchNewsDetail() {
      const id = this.$route.params.id
      if (!id) {
        this.$message.error('新闻ID无效')
        this.goBack()
        return
      }
      
      this.loading = true
      try {
        const detail = await NewsModel.getNewsDetail(id)
        if (detail) {
          this.newsDetail = detail
          // 增加浏览量
          await NewsModel.increaseNewsViews(id)
          // 获取相关新闻
          await this.fetchRelatedNews(id, detail.category)
        } else {
          this.$message.error('新闻不存在')
          this.goBack()
        }
      } catch (error) {
        this.$message.error('获取新闻详情失败')
        console.error('获取新闻详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 获取相关新闻
    async fetchRelatedNews(id, categoryId) {
      try {
        const related = await NewsModel.getRelatedNews(id, categoryId)
        this.relatedNews = related || []
      } catch (error) {
        console.error('获取相关新闻失败:', error)
      }
    },
    
    // 返回列表
    goBack() {
      this.$router.push('/news')
    },
    
    // 跳转到另一个详情
    goToDetail(id) {
      if (id === parseInt(this.$route.params.id)) return
      this.$router.push(`/news/${id}`)
    },
    
    // 分享新闻
    shareNews() {
      const url = window.location.href
      const title = this.newsDetail.title
      
      if (navigator.share) {
        navigator.share({
          title: title,
          url: url
        }).catch(err => {
          console.log('分享失败:', err)
          this.fallbackShare(url, title)
        })
      } else {
        this.fallbackShare(url, title)
      }
    },
    
    // 备用分享方式
    fallbackShare(url, title) {
      // 复制链接到剪贴板
      if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.$message.error('复制链接失败')
        })
      } else {
        this.$message.info('请手动复制页面链接进行分享')
      }
    },
    
    // 收藏新闻
    collectNews() {
      this.isCollected = !this.isCollected
      if (this.isCollected) {
        this.$message.success('收藏成功')
        // 这里可以调用收藏API
      } else {
        this.$message.success('取消收藏')
        // 这里可以调用取消收藏API
      }
    },
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return ''
      return NewsModel.formatDate(date, format)
    }
  }
}
</script>

<style lang="less" scoped>


.news-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-button {
    margin-bottom: 20px;
  }
  
  .news-detail {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .news-header {
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 20px;
      margin-bottom: 30px;
      
      .news-title {
        font-size: 28px;
        font-weight: bold;
        color: #333;
        line-height: 1.4;
        margin-bottom: 15px;
      }
      
      .news-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        color: #666;
        font-size: 14px;
        margin-bottom: 15px;
        
        .meta-item {
          display: flex;
          align-items: center;
          gap: 5px;
          
          i {
            color: #4093f9;
          }
        }
      }
      
      .news-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        
        .el-tag {
          margin: 0;
        }
      }
    }
    
    .news-content {
      .content-wrapper {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        
        // 富文本内容样式
        ::v-deep {
          img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
          
          p {
            margin-bottom: 15px;
            text-align: justify;
            text-indent: 2em;
          }
          
          h1, h2, h3, h4, h5, h6 {
            margin: 25px 0 15px 0;
            color: #333;
            font-weight: bold;
          }
          
          h3 {
            font-size: 20px;
            color: #4093f9;
            border-left: 4px solid #4093f9;
            padding-left: 12px;
          }
          
          ul, ol {
            margin: 15px 0;
            padding-left: 30px;
            
            li {
              margin-bottom: 8px;
              line-height: 1.6;
            }
          }
          
          blockquote {
            border-left: 4px solid #4093f9;
            padding-left: 20px;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 4px;
            color: #666;
            font-style: italic;
          }
          
          strong {
            color: #4093f9;
            font-weight: bold;
          }
        }
      }
    }
    
    .news-actions {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
      
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
      }
    }
    
    .related-news {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;
      
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        
        &:before {
          content: '';
          width: 4px;
          height: 20px;
          background: #4093f9;
          margin-right: 10px;
        }
      }
      
      .related-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        
        .related-item {
          display: flex;
          background: #f8f9fa;
          border-radius: 8px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            background: #fff;
          }
          
          img {
            width: 100px;
            height: 80px;
            object-fit: cover;
            flex-shrink: 0;
          }
          
          .related-content {
            padding: 15px;
            flex: 1;
            
            h4 {
              font-size: 14px;
              color: #333;
              margin-bottom: 8px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.4;
              font-weight: 500;
            }
            
            p {
              font-size: 12px;
              color: #666;
              margin-bottom: 5px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.4;
            }
            
            .related-date {
              font-size: 12px;
              color: #999;
            }
          }
        }
      }
    }
  }
  
  .empty-state {
    background: white;
    border-radius: 12px;
    padding: 60px 20px;
    text-align: center;
    color: #999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    i {
      font-size: 64px;
      margin-bottom: 16px;
      color: #ddd;
    }
    
    p {
      font-size: 16px;
      margin: 0 0 20px 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-detail-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
    
    .news-detail {
      padding: 20px;
      
      .news-header {
        .news-title {
          font-size: 24px;
        }
        
        .news-meta {
          flex-direction: column;
          gap: 10px;
        }
      }
      
      .news-content {
        .content-wrapper {
          font-size: 15px;
          
          ::v-deep {
            p {
              text-indent: 1.5em;
            }
            
            h3 {
              font-size: 18px;
            }
          }
        }
      }
      
      .related-news {
        .related-list {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .news-detail-page {
    .news-detail {
      padding: 15px;
      
      .news-header {
        .news-title {
          font-size: 20px;
        }
      }
      
      .news-content {
        .content-wrapper {
          font-size: 14px;
          
          ::v-deep {
            p {
              text-indent: 1em;
            }
            
            h3 {
              font-size: 16px;
            }
          }
        }
      }
      
      .news-actions {
        .action-buttons {
          flex-direction: column;
          align-items: center;
          
          .el-button {
            width: 120px;
          }
        }
      }
    }
  }
}
</style>