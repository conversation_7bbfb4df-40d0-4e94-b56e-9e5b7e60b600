<template>
  <div class="news-page">
    <!-- 页面头部 - 全宽 -->
    <div class="page-header">
      <div class="header-background">
        <div class="bg-pattern"></div>
        <div class="bg-gradient"></div>
      </div>
      <div class="header-content">
        <div class="title-wrapper">
          <h1 class="page-title">
            <i class="el-icon-news title-icon"></i>
            <span class="title-text">基地新闻</span>
          </h1>
          <div class="page-subtitle">
            <span class="subtitle-text">了解基地最新动态</span>
            <div class="subtitle-decoration"></div>
          </div>
        </div>
        <div class="header-stats">
          <div class="stat-item">
            <div class="stat-number">{{ total || 0 }}</div>
            <div class="stat-label">新闻总数</div>
          </div>
        </div>
      </div>
      
      <!-- 轮播区域 - 全宽 -->
      <div class="carousel-section">
        <el-carousel 
          :interval="5000" 
          :height="carouselHeight"
          indicator-position="outside"
          arrow="hover"
          v-loading="carouselLoading"
        >
          <el-carousel-item v-for="item in carouselList" :key="item.newsId">
            <div class="carousel-item" @click="goToDetail(item.newsId)">
              <div class="carousel-image">
                <img :src="item.coverImage" :alt="item.title" />
                <div class="carousel-overlay"></div>
              </div>
              <div class="carousel-content">
                <div class="carousel-category">{{ item.categoryName }}</div>
                <h3 class="carousel-title">{{ item.title }}</h3>
                <p class="carousel-summary">{{ item.summary }}</p>
                <div class="carousel-meta">
                  <span class="carousel-date">
                    <i class="el-icon-time"></i>
                    {{ formatDate(item.publishTime, 'YYYY-MM-DD') }}
                  </span>
                  <span class="carousel-views">
                    <i class="el-icon-view"></i>
                    {{ item.views }}
                  </span>
                  <span v-if="item.isHot" class="hot-badge">热门</span>
                </div>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>
    </div>

    <!-- 内容区域 - 居中 -->
    <div class="content-container">
      <!-- 搜索和筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="请输入搜索关键词"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-area">
          <el-select v-model="selectedCategory" placeholder="选择分类" @change="handleCategoryChange">
            <el-option label="全部" value=""></el-option>
            <el-option 
              v-for="category in newsCategories" 
              :key="category.value" 
              :label="category.label" 
              :value="category.value">
            </el-option>
          </el-select>
          
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="handleDateChange"
          >
          </el-date-picker>
        </div>
      </div>

      <!-- 新闻列表区域 -->
      <div class="news-list-section" v-loading="loading">
        <div class="news-grid" v-if="newsList.length > 0">
          <div class="news-item" v-for="item in newsList" :key="item.newsId" @click="goToDetail(item.newsId)">
            <div class="news-image">
              <img :src="item.coverImage" :alt="item.title" />
              <div class="news-category">{{ item.categoryName }}</div>
            </div>
            <div class="news-content">
              <h3 class="news-title">{{ item.title }}</h3>
              <p class="news-summary">{{ item.summary }}</p>
              <div class="news-meta">
                <span class="news-date">
                  <i class="el-icon-time"></i>
                  {{ formatDate(item.publishTime) }}
                </span>
                <span class="news-views">
                  <i class="el-icon-view"></i>
                  {{ item.views }}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空数据状态 -->
        <div class="empty-state" v-else-if="!loading">
          <i class="el-icon-document"></i>
          <p>暂无新闻数据</p>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {NewsModel} from '@/model/NewsModel'
import {ConfigModel} from '@/model/ConfigModel'

export default {
  name: 'NewsPage',
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedCategory: '',
      dateRange: null,
      
      // 分页
      currentPage: 1,
      pageSize: 12,
      total: 0,
      
      // 数据
      newsList: [],
      loading: false,
      
      // 轮播
      carouselList: [],
      carouselLoading: false,
      carouselHeight: '300px',
      
      // 分类配置
      newsCategories: []
    }
  },
  
  async created() {
    await this.loadNewsCategories()
    this.fetchCarouselList()
    this.fetchNewsList()
  },
  
  methods: {
    // 获取新闻分类配置
    async loadNewsCategories() {
      try {
        const config = JSON.parse(await ConfigModel.getConfig("newsConfig"));
        if (config && config.categories) {
          this.newsCategories = JSON.parse(config.categories)
        } else {
          // 如果配置不存在，使用默认分类
          this.newsCategories = [
            { label: "基地动态", value: "base_news" },
            { label: "活动通知", value: "activity" },
            { label: "学术交流", value: "academic" },
            { label: "成果展示", value: "achievement" }
          ]
        }
      } catch (error) {
        console.error('获取新闻分类配置失败:', error)
        // 错误情况下使用默认分类
        this.newsCategories = [
          { label: "基地动态", value: "base_news" },
          { label: "活动通知", value: "activity" },
          { label: "学术交流", value: "academic" },
          { label: "成果展示", value: "achievement" }
        ]
      }
    },
    
    // 获取轮播数据
    async fetchCarouselList() {
      this.carouselLoading = true
      try {
        const carouselData = await NewsModel.getNewsCarouselList()
        this.carouselList = carouselData
      } catch (error) {
        this.$message.error('获取轮播数据失败')
        console.error('获取轮播数据失败:', error)
      } finally {
        this.carouselLoading = false
      }
    },
    
    // 获取新闻列表
    async fetchNewsList() {
      this.loading = true
      try {
        const params = {
          keyword: this.searchKeyword?{'$regex': `.*${this.searchKeyword}.*`}:undefined,
          category: this.selectedCategory?this.selectedCategory:undefined,
          publishTime: this.dateRange ? {
            $gte: new Date(this.dateRange[0]).getTime(),
            $lte: (() => {
              const endDate = new Date(this.dateRange[1]);
              endDate.setHours(23, 59, 59, 999);
              return endDate.getTime();
            })()
          } : undefined

        }
        
        const response = await NewsModel.getNewsPageList(this.currentPage-1, this.pageSize, params)
        
        this.newsList = response.list
        this.total = response.total
      } catch (error) {
        this.$message.error('获取新闻列表失败')
        console.error('获取新闻列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.fetchNewsList()
    },
    
    // 分类筛选
    handleCategoryChange() {
      this.currentPage = 1
      this.fetchNewsList()
    },
    
    // 日期筛选
    handleDateChange() {
      this.currentPage = 1
      this.fetchNewsList()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchNewsList()
      // 滚动到顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    
    // 跳转详情
    goToDetail(id) {
      this.$router.push(`/news/${id}`)
    },
    
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD') {
      if (!date) return ''
      return NewsModel.formatDate(date, format)
    }
  }
}
</script>

<style lang="less" scoped>

.news-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  
  .page-header {
    position: relative;
    border-radius: 0;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(64, 147, 249, 0.15);
    margin-bottom: 40px;
    
    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(64, 147, 249, 0.85) 0%, rgba(44, 90, 160, 0.9) 100%);
      
      .bg-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: 
          radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
          radial-gradient(circle at 40% 80%, rgba(255, 255, 255, 0.04) 0%, transparent 50%);
        animation: patternMove 20s ease-in-out infinite;
      }
      
      .bg-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.08) 50%, transparent 70%);
        animation: gradientShine 3s ease-in-out infinite;
      }
    }
    
    .header-content {
      position: relative;
      z-index: 2;
      padding: 40px 0;
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .title-wrapper {
        flex: 1;
        
        .page-title {
          display: flex;
          align-items: center;
          font-size: 36px;
          font-weight: bold;
          color: white;
          margin-bottom: 15px;
          animation: titleSlideIn 1s ease-out;
          
          .title-icon {
            font-size: 40px;
            margin-right: 15px;
            animation: iconRotate 2s ease-in-out infinite;
          }
          
          .title-text {
            position: relative;
            
            &::after {
              content: '';
              position: absolute;
              bottom: -5px;
              left: 0;
              width: 0;
              height: 3px;
              background: rgba(255, 255, 255, 0.8);
              animation: underlineExpand 1.5s ease-out 0.5s forwards;
            }
          }
        }
        
        .page-subtitle {
          position: relative;
          
          .subtitle-text {
            font-size: 18px;
            color: rgba(255, 255, 255, 0.9);
            animation: subtitleFadeIn 1s ease-out 0.3s both;
          }
          
          .subtitle-decoration {
            width: 60px;
            height: 2px;
            background: rgba(255, 255, 255, 0.6);
            margin-top: 10px;
            border-radius: 1px;
            animation: decorationSlide 1s ease-out 0.8s both;
          }
        }
      }
      
      .header-stats {
        .stat-item {
          text-align: center;
          background: rgba(255, 255, 255, 0.15);
          padding: 20px 30px;
          border-radius: 12px;
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          animation: statsSlideIn 1s ease-out 0.6s both;
          
          .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: white;
            line-height: 1;
            margin-bottom: 5px;
            animation: numberCount 2s ease-out 1s both;
          }
          
          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }
    }
  }
  
  // 动画定义
  @keyframes patternMove {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    50% { transform: translate(10px, -10px) rotate(2deg); }
  }
  
  @keyframes gradientShine {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
    100% { transform: translateX(100%); }
  }
  
  @keyframes titleSlideIn {
    0% { 
      opacity: 0; 
      transform: translateY(30px); 
    }
    100% { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes iconRotate {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(5deg); }
  }
  
  @keyframes underlineExpand {
    0% { width: 0; }
    100% { width: 80px; }
  }
  
  @keyframes subtitleFadeIn {
    0% { 
      opacity: 0; 
      transform: translateY(20px); 
    }
    100% { 
      opacity: 1; 
      transform: translateY(0); 
    }
  }
  
  @keyframes decorationSlide {
    0% { 
      width: 0; 
      opacity: 0; 
    }
    100% { 
      width: 60px; 
      opacity: 1; 
    }
  }
  
  @keyframes statsSlideIn {
    0% { 
      opacity: 0; 
      transform: translateX(30px); 
    }
    100% { 
      opacity: 1; 
      transform: translateX(0); 
    }
  }
  
  @keyframes numberCount {
    0% { 
      transform: scale(0.5); 
      opacity: 0; 
    }
    50% { 
      transform: scale(1.1); 
    }
    100% { 
      transform: scale(1); 
      opacity: 1; 
    }
  }
  
  .carousel-section {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px 30px;
    
    ::v-deep .el-carousel {
      border-radius: 12px;
      
      .el-carousel__container {
        border-radius: 12px;
      }
      
      .el-carousel__indicators--outside {
        margin-top: 20px;
        
        .el-carousel__indicator {
          .el-carousel__button {
            background-color: rgba(255, 255, 255, 0.5);
            
            &.is-active {
              background-color: rgba(255, 255, 255, 0.9);
            }
          }
        }
      }
      
      .el-carousel__arrow {
        background-color: rgba(255, 255, 255, 0.8);
        color: #333;
        
        &:hover {
          background-color: rgba(255, 255, 255, 0.9);
        }
      }
    }
    
    .carousel-item {
      position: relative;
      height: 100%;
      cursor: pointer;
      border-radius: 12px;
      overflow: hidden;
      transition: transform 0.3s ease;
      
      &:hover {
        transform: scale(1.02);
        
        .carousel-overlay {
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.3) 0%,
            rgba(0, 0, 0, 0.7) 100%
          );
        }
        
        .carousel-title {
          color: #fff;
        }
      }
      
      .carousel-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .carousel-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            to bottom,
            rgba(0, 0, 0, 0.2) 0%,
            rgba(0, 0, 0, 0.6) 100%
          );
          transition: background 0.3s ease;
        }
      }
      
      .carousel-content {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 30px;
        color: white;
        z-index: 2;
        
        .carousel-category {
          display: inline-block;
          background: rgba(64, 147, 249, 0.9);
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          margin-bottom: 10px;
        }
        
        .carousel-title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
          line-height: 1.3;
          color: rgba(255, 255, 255, 0.95);
          transition: color 0.3s ease;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .carousel-summary {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.5;
          margin-bottom: 15px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .carousel-meta {
          display: flex;
          align-items: center;
          gap: 20px;
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          
          .carousel-date, .carousel-views {
            display: flex;
            align-items: center;
            gap: 4px;
          }
          
          .hot-badge {
            background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
            color: white;
            padding: 2px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 500;
            animation: hotPulse 2s ease-in-out infinite;
          }
        }
      }
    }
  }
  
  @keyframes hotPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
  }
  
  .content-container {
    width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    box-sizing: border-box;
  }
  
  .search-filter-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    padding: 30px;
    border-radius: 16px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(64, 147, 249, 0.1);
    
    .search-area {
      margin-bottom: 25px;
      text-align: center;
      
      .search-input {
        max-width: 600px;
        
        ::v-deep .el-input__inner {
          border-radius: 30px 0 0 30px;
          height: 50px;
          line-height: 46px; // 调整行高以适应边框
          border: 2px solid rgba(64, 147, 249, 0.2);
          font-size: 15px;
          padding-left: 20px;
          transition: all 0.3s ease;
          box-sizing: border-box;
          
          &:focus {
            border-color: #4093f9;
            box-shadow: 0 0 0 3px rgba(64, 147, 249, 0.1);
          }
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 30px 30px 0;
          border: 2px solid rgba(64, 147, 249, 0.2);
          border-left: none;
          height: 50px;
          
          .el-button {
            border-radius: 0 30px 30px 0;
            height: 46px; // 调整按钮高度以适应容器
            padding: 0 25px;
            background: linear-gradient(135deg, #4093f9 0%, darken(#4093f9, 10%) 100%);
            border: none;
            color: white;
            font-weight: 500;
            transition: all 0.3s ease;
            
            &:hover {
              background: linear-gradient(135deg, darken(#4093f9, 5%) 0%, darken(#4093f9, 15%) 100%);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(64, 147, 249, 0.3);
            }
          }
        }
      }
    }
    
    .filter-area {
      display: flex;
      justify-content: center;
      gap: 20px;
      flex-wrap: wrap;
      
      .el-select, .el-date-picker {
        min-width: 180px;
        
        ::v-deep .el-input__inner {
          border-radius: 12px;
          height: 42px;
          line-height: 38px; // 调整行高以适应边框
          border: 2px solid rgba(64, 147, 249, 0.15);
          transition: all 0.3s ease;
          box-sizing: border-box;
          
          &:focus {
            border-color: #4093f9;
            box-shadow: 0 0 0 3px rgba(64, 147, 249, 0.1);
          }
        }
      }
      
      ::v-deep .el-select:hover .el-input__inner,
      ::v-deep .el-date-editor:hover .el-input__inner {
        border-color: #4093f9;
      }
    }
  }
  
  .news-list-section {
    min-height: 400px;
    
    .news-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 25px;
      
      .news-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .news-image {
          position: relative;
          height: 200px;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }
          
          &:hover img {
            transform: scale(1.05);
          }
          
          .news-category {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(64, 147, 249, 0.9);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
          }
        }
        
        .news-content {
          padding: 20px;
          
          .news-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            
            &:hover {
              color: #4093f9;
            }
          }
          
          .news-summary {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
          }
          
          .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #999;
            
            .news-date, .news-views {
              display: flex;
              align-items: center;
              gap: 5px;
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      
      i {
        font-size: 64px;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
  
  .pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 40px 0 60px;
    padding: 20px 20px 15px 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    ::v-deep .el-pagination {
      .el-pagination__total {
        font-size: 14px;
        color: #666;
        margin-right: 20px;
      }
      
      .el-pager {
        li {
          min-width: 36px;
          height: 36px;
          line-height: 36px;
          font-size: 14px;
          margin: -5px 2px;
          border-radius: 6px;
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          color: #495057;
          transition: all 0.2s ease;
          
          &:hover {
            background: #e9ecef;
            border-color: #dee2e6;
          }
          
          &.active {
            background: #4093f9;
            color: white;
            border-color: #4093f9;
          }
        }
      }

      
      .btn-prev, .btn-next {
        min-width: 36px;
        height: 36px;
        line-height: 36px;
        font-size: 14px;
        margin: -5px 8px;
        border-radius: 6px;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        color: #495057;
        transition: all 0.2s ease;
        
        &:hover {
          background: #4093f9;
          color: white;
          border-color: #4093f9;
        }
        
        &:disabled {
          background: #f8f9fa;
          color: #adb5bd;
          border-color: #e9ecef;
          cursor: not-allowed;
          
          &:hover {
            background: #f8f9fa;
            color: #adb5bd;
            border-color: #e9ecef;
          }
        }
      }
      
      .el-pagination__jump {
        margin-left: 20px;
        
        .el-pagination__editor {
          width: 50px;
          height: 36px;
          border-radius: 6px;

          text-align: center;
          font-size: 14px;
          
          &:focus {
            border-color: #4093f9;
            outline: none;
          }
        }
        
        .el-pagination__goto {
          font-size: 14px;
          color: #666;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .news-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
    
    .page-header {
      .header-content {
        padding: 40px 20px;
        flex-direction: column;
        text-align: center;
        gap: 30px;
        
        .title-wrapper {
          .page-title {
            font-size: 28px;
            justify-content: center;
            
            .title-icon {
              font-size: 32px;
            }
          }
          
          .page-subtitle {
            .subtitle-text {
              font-size: 16px;
            }
          }
        }
        
        .header-stats {
          .stat-item {
            padding: 15px 20px;
            
            .stat-number {
              font-size: 24px;
            }
          }
        }
      }
    }
    
    .search-filter-section {
      padding: 15px;
      
      .search-area {
        .search-input {
          max-width: 100%;
        }
      }
      
      .filter-area {
        flex-direction: column;
        align-items: center;
        
        .el-select, .el-date-picker {
          width: 100%;
          max-width: 300px;
        }
      }
    }
    
    .news-list-section {
      .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }
    }
    
    .pagination-container {
      margin: 30px 0 40px;
      padding: 15px;
      
      ::v-deep .el-pagination {
        .el-pager {
          li {
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            font-size: 13px;
            margin: 0 1px;
          }
        }
        
        .btn-prev, .btn-next {
          min-width: 32px;
          height: 32px;
          line-height: 32px;
          font-size: 13px;
          margin: 0 4px;
        }
        
        .el-pagination__jump {
          margin-left: 10px;
          
          .el-pagination__editor {
            width: 45px;
            height: 32px;
            font-size: 13px;
          }
          
          .el-pagination__goto {
            font-size: 13px;
          }
        }
        
        .el-pagination__total {
          font-size: 13px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .news-page {
    .page-header {
      .header-content {
        padding: 30px 15px;
        
        .title-wrapper {
          .page-title {
            font-size: 24px;
            
            .title-icon {
              font-size: 28px;
              margin-right: 10px;
            }
          }
          
          .page-subtitle {
            .subtitle-text {
              font-size: 14px;
            }
          }
        }
      }
    }
    
    .news-list-section {
      .news-grid {
        .news-item {
          .news-image {
            height: 150px;
          }
          
          .news-content {
            padding: 15px;
            
            .news-title {
              font-size: 16px;
            }
            
            .news-summary {
              font-size: 13px;
            }
          }
        }
      }
    }
    
    .pagination-container {
      padding: 10px;
      
      ::v-deep .el-pagination {
        .el-pager {
          li {
            min-width: 30px;
            height: 30px;
            line-height: 30px;
            font-size: 12px;
            margin: 0 1px;
          }
        }
        
        .btn-prev, .btn-next {
          min-width: 30px;
          height: 30px;
          line-height: 30px;
          font-size: 12px;
          margin: 0 3px;
        }
        
        .el-pagination__jump {
          display: none; // 在小屏幕隐藏跳转功能
        }
        
        .el-pagination__total {
          font-size: 12px;
        }
      }
    }
  }
}
</style>