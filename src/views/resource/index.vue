<template>
  <div class="resource-center-page">
    <div class="content-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <h1 class="page-title">资源中心</h1>
        <div class="page-subtitle">丰富的教学资源，助力学习成长</div>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索资源名称、关键词或描述"
            class="search-input"
            size="large"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
          <!--暂时隐藏热门搜素-->
<!--          <div class="search-suggestions" v-if="searchSuggestions.length > 0">-->
<!--            <div class="suggestion-title">热门搜索</div>-->
<!--            <div class="suggestion-tags">-->
<!--              <el-tag -->
<!--                v-for="tag in searchSuggestions" -->
<!--                :key="tag"-->
<!--                @click="searchByTag(tag)"-->
<!--                class="suggestion-tag"-->
<!--              >-->
<!--                {{ tag }}-->
<!--              </el-tag>-->
<!--            </div>-->
<!--          </div>-->
        </div>
      </div>

      <!-- 资源统计 -->
      <div class="stats-section">
        <div class="stats-container">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalResources }}</div>
              <div class="stat-label">总资源数</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-view"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalViews }}</div>
              <div class="stat-label">总浏览量</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-download"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalDownloads }}</div>
              <div class="stat-label">总下载量</div>
            </div>
          </div>
          
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalFavorites }}</div>
              <div class="stat-label">总收藏量</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 资源分类 -->
      <div class="category-section">
        <div class="section-header">
          <h2 class="section-title">资源分类</h2>
          <div class="section-subtitle">按类型快速查找所需资源</div>
        </div>
        
        <div class="category-grid">
          <div class="category-card" v-for="category in categoryList" :key="category.id" @click="goToCategory(category.categoryId)">
            <div class="category-icon">
              <i :class="category.icon"></i>
            </div>
            <div class="category-content">
              <h3 class="category-name">{{ category.name }}</h3>
              <p class="category-description">{{ category.description }}</p>
              <div class="category-stats">
                <span class="resource-count">{{ category.resourceCount }}个资源</span>
                <span class="view-count">{{ category.viewCount }}次浏览</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 推荐资源 -->
      <div class="featured-section">
        <div class="section-header">
          <h2 class="section-title">推荐资源</h2>
          <div class="section-subtitle">精选优质资源推荐</div>
        </div>
        
        <div class="featured-grid">
          <div class="resource-card" v-for="resource in featuredResources" :key="resource.resourceId" @click="goToDetail(resource.resourceId)">
            <div class="resource-thumbnail">
              <img :src="resource.thumbnail" :alt="resource.title" />
              <div class="resource-type">
                <i :class="getResourceTypeIcon(resource.type)"></i>
                {{ resource.typeName }}
              </div>
            </div>
            
            <div class="resource-content">
              <h3 class="resource-title">{{ resource.title }}</h3>
              <p class="resource-description">{{ resource.description }}</p>
              
              <div class="resource-meta">
                <span class="resource-author">
                  <i class="el-icon-user"></i>
                  {{ resource.author }}
                </span>
                <span class="resource-date">
                  <i class="el-icon-time"></i>
                  {{ formatDate(resource.uploadTime) }}
                </span>
                <span class="resource-course">
                  <el-tag 
                    :color="getResourceTypeLabel(resource).color" 
                    size="mini"
                    effect="plain"
                  >
                    <i class="el-icon-collection"></i>
                    {{ getResourceTypeLabel(resource).label }}
                  </el-tag>
                </span>
              </div>
              
              <div class="resource-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ resource.views }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-download"></i>
                  {{ resource.downloads }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-star-off"></i>
                  {{ resource.favorites }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最新资源 -->
      <div class="latest-section">
        <div class="section-header">
          <h2 class="section-title">最新资源</h2>
          <el-button type="text" @click="goToAllResources">查看更多 >></el-button>
        </div>
        
        <div class="latest-list">
          <div class="latest-item" v-for="resource in latestResources" :key="resource.resourceId" @click="goToDetail(resource.resourceId)">
            <div class="item-icon">
              <i :class="getResourceTypeIcon(resource.type)"></i>
            </div>
            <div class="item-content">
              <h4 class="item-title">{{ resource.title }}</h4>
              <p class="item-info">{{ resource.author }} · {{ formatDate(resource.uploadTime) }} · {{ getFileExtension(resource.fileUrl) }}</p>
              <div class="item-course">
                <el-tag 
                  :color="getResourceTypeLabel(resource).color" 
                  size="mini"
                  effect="plain"
                >
                  <i class="el-icon-collection"></i>
                  {{ getResourceTypeLabel(resource).label }}
                </el-tag>
              </div>
              <div class="item-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ resource.views }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-download"></i>
                  {{ resource.downloads }}
                </span>
              </div>
            </div>
            <div class="item-actions">
              <el-button type="text" size="small" @click.stop="previewResource(resource)">
                <i class="el-icon-view"></i>
                预览
              </el-button>
              <el-button type="text" size="small" @click.stop="downloadResource(resource)" v-if="resource.canDownload">
                <i class="el-icon-download"></i>
                下载
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ResourceModel } from '@/model/ResourceModel'

export default {
  name: 'ResourceCenter',
  data() {
    return {
      // 搜索相关
      searchKeyword: '',
      searchSuggestions: [],
      
      // 统计数据
      stats: {
        totalResources: 0,
        totalViews: 0,
        totalDownloads: 0,
        totalFavorites: 0
      },
      
      // 分类列表
      categoryList: [],
      
      // 推荐资源
      featuredResources: [],
      
      // 最新资源
      latestResources: [],
      
      // 加载状态
      loading: false
    }
  },
  
  async created() {
    await this.loadInitialData()
  },
  
  methods: {
    // 加载初始数据
    async loadInitialData() {
      this.loading = true
      try {
        // 并行加载所有数据
        const [stats, categories, featured, latest, hotTags] = await Promise.all([
          ResourceModel.getResourceStats(),
          ResourceModel.getResourceCategories(),
          ResourceModel.getFeaturedResources(),
          ResourceModel.getLatestResources(),
          ResourceModel.getHotSearchTags()
        ])
        
        this.stats = stats
        this.categoryList = categories
        this.featuredResources = featured
        this.latestResources = latest
        this.searchSuggestions = hotTags
        
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('数据加载失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    // 搜索功能
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning('请输入搜索关键词')
        return
      }
      
      this.$router.push({
        name: 'ResourceSearch',
        query: { keyword: this.searchKeyword }
      })
    },
    
    // 点击标签搜索
    searchByTag(tag) {
      this.searchKeyword = tag
      this.handleSearch()
    },
    
    // 跳转到分类页面
    goToCategory(categoryId) {
      this.$router.push({
        name: 'ResourceCategory',
        params: { categoryId }
      })
    },
    
    // 跳转到资源详情页
    goToDetail(resourceId) {
      this.$router.push({
        name: 'ResourceDetail',
        params: { id: resourceId }
      })
    },
    
    // 跳转到所有资源页面
    goToAllResources() {
      this.$router.push({
        name: 'ResourceSearch'
      })
    },
    
    // 预览资源
    previewResource(resource) {
      if (ResourceModel.canPreview(resource.type)) {
        this.goToDetail(resource.resourceId)
      } else {
        this.$message.info('该资源类型暂不支持预览，请下载后查看')
      }
    },
    
    // 下载资源
    async downloadResource(resource) {
      try {
        this.$message.info('正在准备下载...')
        
        // 增加下载量
        await ResourceModel.increaseResourceDownloads(resource.resourceId)
        
        // 获取下载链接
        const downloadResult = await ResourceModel.downloadResource(resource.resourceId)
        
        if (downloadResult.success) {
          // 创建下载链接
          const link = document.createElement('a')
          link.href = downloadResult.downloadUrl
          link.download = resource.title
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          this.$message.success('下载开始')
        } else {
          this.$message.error(downloadResult.message || '下载失败')
        }
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },
    
    // 获取资源类型图标
    getResourceTypeIcon(type) {
      return ResourceModel.getResourceTypeIcon(type)
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return ResourceModel.formatDate(dateStr, 'MM-DD')
    },
    
    // 获取文件扩展名
    getFileExtension(fileUrl) {
      if (!fileUrl) return '未知'
      const extension = fileUrl.split('.').pop()
      return extension ? extension.toUpperCase() : '未知'
    },
    
    // 获取资源类型标签
    getResourceTypeLabel(resource) {
      return ResourceModel.getResourceTypeLabel(resource)
    }
  }
}
</script>

<style lang="less" scoped>
.resource-center-page {
  background-color: #f2f2f2;
  padding: 30px 0;
  
  .page-header {
    text-align: center;
    margin-bottom: 40px;
    
    .page-title {
      font-size: 32px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .page-subtitle {
      font-size: 16px;
      color: #666;
    }
  }
  
  .search-section {
    margin-bottom: 40px;
    
    .search-container {
      max-width: 600px;
      margin: 0 auto;
      
      .search-input {
        width: 100%;
        
        ::v-deep .el-input__inner {
          height: 50px;
          border-radius: 25px 0 0 25px;
          font-size: 16px;
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 25px 25px 0;
          border-left: none;
          
          .el-button {
            height: 50px;
            padding: 0 25px;
            border-radius: 0 25px 25px 0;
            border-left: none;
          }
        }
      }
      
      .search-suggestions {
        margin-top: 20px;
        text-align: center;
        
        .suggestion-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }
        
        .suggestion-tags {
          .suggestion-tag {
            margin: 0 5px;
            cursor: pointer;
            
            &:hover {
              color: #4093f9;
            }
          }
        }
      }
    }
  }
  
  .stats-section {
    margin-bottom: 40px;
    
    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 20px;
      
      .stat-card {
        background: white;
        border-radius: 12px;
        padding: 25px;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .stat-icon {
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: linear-gradient(135deg, #4093f9, #2c5aa0);
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          
          i {
            font-size: 24px;
            color: white;
          }
        }
        
        .stat-content {
          .stat-number {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
          }
          
          .stat-label {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
  
  .category-section, .featured-section, .latest-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .section-header {
      margin-bottom: 25px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .section-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
      }
      
      .section-subtitle {
        font-size: 14px;
        color: #666;
      }
    }
  }
  
  .category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    
    .category-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 25px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #e9ecef;
        transform: translateY(-3px);
      }
      
      .category-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4093f9, #2c5aa0);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        
        i {
          font-size: 24px;
          color: white;
        }
      }
      
      .category-content {
        .category-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }
        
        .category-description {
          font-size: 14px;
          color: #666;
          margin-bottom: 15px;
          line-height: 1.5;
        }
        
        .category-stats {
          display: flex;
          justify-content: space-between;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
  
  .featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 25px;
    
    .resource-card {
      background: #f8f9fa;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }
      
      .resource-thumbnail {
        position: relative;
        height: 180px;
        overflow: hidden;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        
        .resource-type {
          position: absolute;
          top: 10px;
          right: 10px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 5px 10px;
          border-radius: 15px;
          font-size: 12px;
          display: flex;
          align-items: center;
          gap: 5px;
        }
      }
      
      .resource-content {
        padding: 20px;
        
        .resource-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .resource-description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 15px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .resource-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 15px;
          font-size: 12px;
          color: #999;
          flex-wrap: wrap;
          gap: 10px;
          
          span {
            display: flex;
            align-items: center;
            gap: 5px;
          }
          
          .resource-course {
            .el-tag {
              border: none;
              font-size: 11px;
            }
          }
        }
        
        .resource-stats {
          display: flex;
          justify-content: space-between;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
  }
  
  .latest-list {
    .latest-item {
      display: flex;
      align-items: center;
      padding: 15px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        background: #f8f9fa;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .item-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: linear-gradient(135deg, #4093f9, #2c5aa0);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        
        i {
          font-size: 18px;
          color: white;
        }
      }
      
      .item-content {
        flex: 1;
        
        .item-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
        }
        
        .item-info {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }
        
        .item-course {
          margin-bottom: 8px;
          
          .el-tag {
            border: none;
            font-size: 11px;
          }
        }
        
        .item-stats {
          display: flex;
          gap: 20px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #999;
          }
        }
      }
      
      .item-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-center-page {
    padding: 20px 0;
    
    .stats-section {
      .stats-container {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    .category-grid, .featured-grid {
      grid-template-columns: 1fr;
    }
    
    .latest-list {
      .latest-item {
        flex-direction: column;
        text-align: center;
        
        .item-icon {
          margin-right: 0;
          margin-bottom: 10px;
        }
        
        .item-content {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>