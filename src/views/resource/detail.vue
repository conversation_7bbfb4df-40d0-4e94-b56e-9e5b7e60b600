<template>
  <div class="resource-detail-page">
    <div class="content-container">
      <!-- 返回按钮 -->
      <div class="back-navigation">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/resource' }">资源中心</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: `/resource/category/${resourceDetail.categoryId}` }" v-if="resourceDetail.categoryId">
            {{ resourceDetail.categoryEntity[0]["name"] }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ resourceDetail.title }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 资源详情 -->
      <div class="resource-detail" v-if="resourceDetail" v-loading="loading">
        <div class="resource-header">
          <div class="resource-info">
            <h1 class="resource-title">{{ resourceDetail.title }}</h1>
            <div class="resource-meta">
              <span class="meta-item">
                <i class="el-icon-user"></i>
                作者：{{ resourceDetail.author }}
              </span>
              <span class="meta-item">
                <i class="el-icon-time"></i>
                上传时间：{{ formatDate(resourceDetail.uploadTime, 'YYYY-MM-DD HH:mm') }}
              </span>
              <span class="meta-item">
                <i class="el-icon-folder"></i>
                分类：{{resourceDetail.categoryEntity[0]["name"] }}
              </span>
              <span class="meta-item">
                <i class="el-icon-document"></i>
                大小：{{ formatFileSize(resourceDetail.fileSize) }}
              </span>
              <span class="meta-item">
                <i :class="getResourceTypeIcon(resourceDetail.type)"></i>
                格式：{{ getFileExtension(resourceDetail.fileUrl) }}
              </span>
              <span class="meta-item">
                <i class="el-icon-collection"></i>
                {{ getResourceTypeLabel(resourceDetail).label }}
              </span>
            </div>
            <div class="resource-stats">
              <div class="stat-item">
                <i class="el-icon-view"></i>
                <span>{{ resourceDetail.views }}</span>
                <span>浏览</span>
              </div>
              <div class="stat-item">
                <i class="el-icon-download"></i>
                <span>{{ resourceDetail.downloads }}</span>
                <span>下载</span>
              </div>
              <div class="stat-item">
                <i class="el-icon-star-off"></i>
                <span>{{ resourceDetail.favorites }}</span>
                <span>收藏</span>
              </div>
            </div>
          </div>
          
          <div class="resource-actions">
            <el-button 
              type="primary" 
              size="large" 
              class="action-button"
              @click="downloadResource" 
              :loading="downloading"
              v-if="resourceDetail.canDownload"
            >
              <i class="el-icon-download"></i>
              下载资源
            </el-button>
            <el-button 
              :type="isFavorited ? 'warning' : 'default'" 
              size="large" 
              class="action-button"
              @click="toggleFavorite"
              :loading="favoriting"
            >
              <i :class="isFavorited ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
              {{ isFavorited ? '已收藏' : '收藏' }}
            </el-button>
          </div>
        </div>

        <!-- 资源描述 -->
        <div class="resource-description">
          <h3>资源描述</h3>
          <div class="description-content" v-html="resourceDetail.description"></div>
          
          <!-- 课程关联信息 -->
          <div class="course-info" v-if="resourceDetail.relatedToCourse && resourceDetail.courseName">
            <h4>关联课程</h4>
            <div class="course-card" @click="clickCourse(resourceDetail.courseId)">
              <div class="course-icon">
                <i class="el-icon-reading"></i>
              </div>
              <div class="course-content">
                <h5>{{ resourceDetail.courseName }}</h5>
                <p>此资源属于《{{ resourceDetail.courseName }}》课程的教学材料</p>
              </div>
            </div>
          </div>
          
          <div class="resource-tags" v-if="resourceDetail.tags && resourceDetail.tags.length > 0">
            <span class="tags-label">标签：</span>
            <el-tag v-for="tag in resourceDetail.tags" :key="tag" size="small">{{ tag }}</el-tag>
          </div>
        </div>

        <!-- 资源预览 -->
        <div class="resource-preview" v-if="resourceDetail.canPreview">
          <div class="preview-header">
            <h3>资源预览</h3>
            <div class="preview-actions">
              <el-button @click="toggleFullscreen" size="small">
                <i class="el-icon-full-screen"></i>
                全屏预览
              </el-button>
              <el-button @click="openInNewWindow" size="small">
                <i class="el-icon-link"></i>
                新窗口打开
              </el-button>
            </div>
          </div>

          <div class="preview-content" :class="{ 'fullscreen': isFullscreen }">
            <!-- 图片预览 -->
            <div v-if="resourceDetail.type === 'image'" class="image-preview">
              <img :src="resourceDetail.fileUrl" :alt="resourceDetail.title" />
            </div>

            <!-- 视频预览 -->
            <div v-else-if="resourceDetail.type === 'video'" class="video-preview">
              <video :src="resourceDetail.fileUrl" controls></video>
            </div>

            <!-- 音频预览 -->
            <div v-else-if="resourceDetail.type === 'audio'" class="audio-preview">
              <audio :src="resourceDetail.fileUrl" controls></audio>
              <div class="audio-info">
                <img :src="resourceDetail.thumbnail" :alt="resourceDetail.title" />
                <div class="audio-meta">
                  <h4>{{ resourceDetail.title }}</h4>
                  <p>{{ resourceDetail.author }}</p>
                </div>
              </div>
            </div>

            <!-- 文本文档预览 -->
            <div v-else-if="resourceDetail.type === 'txt'" class="text-preview">
              <div class="text-content">
                <pre>{{ textContent }}</pre>
              </div>
            </div>

            <!-- PDF预览 -->
            <div v-else-if="resourceDetail.type === 'pdf'" class="pdf-preview">
              <iframe :src="resourceDetail.fileUrl" frameborder="0"></iframe>
            </div>

            <!-- 其他类型 -->
            <div v-else class="default-preview">
              <div class="preview-placeholder">
                <i :class="getResourceTypeIcon(resourceDetail.type)"></i>
                <p>{{ resourceDetail.title }}</p>
                <p>{{ resourceDetail.typeName }}</p>
                <el-button type="primary" @click="downloadResource" v-if="resourceDetail.canDownload">下载查看</el-button>
                <p v-else>此类型文件暂不支持在线预览</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 相关资源 -->
        <div class="related-resources" v-if="relatedResources.length > 0">
          <h3>相关资源</h3>
          <div class="related-grid">
            <div class="related-item" v-for="item in relatedResources" :key="item.id" @click="goToDetail(item.id)">
              <img :src="item.thumbnail" :alt="item.title" />
              <div class="related-content">
                <h4>{{ item.title }}</h4>
                <p>{{ item.author }}</p>
                <div class="related-stats">
                  <span><i class="el-icon-view"></i>{{ item.views }}</span>
                  <span><i class="el-icon-download"></i>{{ item.downloads }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-container">
        <el-loading text="加载中..."></el-loading>
      </div>
      
      <!-- 资源不存在 -->
      <div v-else class="not-found">
        <el-empty description="资源不存在">
          <el-button type="primary" @click="goBack">返回</el-button>
        </el-empty>
      </div>
    </div>
  </div>
</template>

<script>
import { ResourceModel } from '@/model/ResourceModel'

export default {
  name: 'ResourceDetail',
  data() {
    return {
      // 资源详情
      resourceDetail: {
        categoryEntity:[{}]
      },

      // 相关资源
      relatedResources: [],
      
      // 状态
      loading: false,
      downloading: false,
      favoriting: false,
      isFavorited: false,
      isFullscreen: false,
      
      // 文本内容
      textContent: ''
    }
  },
  
  async created() {
    await this.loadResourceDetail()
  },
  
  methods: {
    // 加载资源详情
    async loadResourceDetail() {
      this.loading = true
      try {
        const resourceId = this.$route.params.id
        if (!resourceId) {
          this.$message.error('资源ID无效')
          this.goBack()
          return
        }
        
        // 加载资源详情
        this.resourceDetail = await ResourceModel.getResourceDetail(resourceId)
        
        if (!this.resourceDetail) {
          this.$message.error('资源不存在')
          return
        }
        
        // 增加浏览量
        await ResourceModel.increaseResourceViews(resourceId)
        this.resourceDetail.views += 1
        
        // 加载相关资源
        this.relatedResources = await ResourceModel.getRelatedResources(
          resourceId, 
          this.resourceDetail.categoryId
        )
        
        // TODO: 检查用户是否已收藏此资源
        this.isFavorited = await ResourceModel.isResourceFavorited(resourceId);
        
        // 如果是文本文档，加载文本内容
        if (this.resourceDetail.type === 'txt' && this.resourceDetail.fileUrl) {
          await this.loadTextContent()
        }
        
      } catch (error) {
        console.error('加载资源详情失败:', error)
        this.$message.error('加载失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 跳转到课程详情
    clickCourse(courseId){
      window.open("/courseInfo/?id=" + courseId)
    },
    
    // 跳转到其他资源详情
    goToDetail(resourceId) {
      this.$router.push({
        name: 'ResourceDetail',
        params: { id: resourceId }
      })
      // 重新加载数据
      this.loadResourceDetail()
    },
    
    // 下载资源
    async downloadResource() {
      if (!this.resourceDetail.canDownload) {
        this.$message.warning('该资源不支持下载')
        return
      }
      
      this.downloading = true
      try {
        this.$message.info('正在准备下载...')
        
        // 获取下载链接
        const downloadResult = await ResourceModel.downloadResource(this.resourceDetail.resourceId)
        
        if (downloadResult.success) {
          try {
            // 方案1：尝试使用 fetch 下载文件（推荐）
            const response = await fetch(downloadResult.downloadUrl)
            if (!response.ok) {
              throw new Error('下载请求失败')
            }
            
            const blob = await response.blob()
            const url = window.URL.createObjectURL(blob)
            
            // 创建下载链接
            const link = document.createElement('a')
            link.href = url
            link.download = this.resourceDetail.title || '未知文件'
            link.style.display = 'none'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            
            // 清理临时URL
            window.URL.revokeObjectURL(url)
            
            this.$message.success('下载开始')
            this.resourceDetail.downloads += 1
          } catch (error) {
            console.error('下载失败:', error)
            // 降级方案：使用传统方式，但设置更多属性
            const link = document.createElement('a')
            link.href = downloadResult.downloadUrl
            link.download = this.resourceDetail.title || '未知文件'
            link.target = '_blank'
            link.rel = 'noopener noreferrer'
            link.style.display = 'none'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            
            this.$message.success('下载链接已打开')
            this.resourceDetail.downloads += 1
          }
        } else {
          this.$message.error(downloadResult.message || '下载失败')
        }
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      } finally {
        this.downloading = false
      }
    },
    
    // 切换收藏状态
    async toggleFavorite() {
      this.favoriting = true
      try {
        const newFavoriteStatus = await ResourceModel.toggleResourceFavorite(
          this.resourceDetail.resourceId,
          this.isFavorited
        )
        
        this.isFavorited = newFavoriteStatus
        
        if (this.isFavorited) {
          this.$message.success('收藏成功')
          this.resourceDetail.favorites += 1
        } else {
          this.$message.success('取消收藏')
          this.resourceDetail.favorites -= 1
        }
        
      } catch (error) {
        console.error('收藏操作失败:', error)
        this.$message.error('操作失败，请稍后重试')
      } finally {
        this.favoriting = false
      }
    },
    
    // 切换全屏预览
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      
      if (this.isFullscreen) {
        // 监听ESC键退出全屏
        document.addEventListener('keydown', this.handleEscKey)
      } else {
        document.removeEventListener('keydown', this.handleEscKey)
      }
    },
    
    // 处理ESC键
    handleEscKey(event) {
      if (event.key === 'Escape') {
        this.isFullscreen = false
        document.removeEventListener('keydown', this.handleEscKey)
      }
    },

    // 在新窗口打开资源
    openInNewWindow() {
      if (this.resourceDetail.fileUrl) {
        window.open(this.resourceDetail.fileUrl, '_blank')
      }
    },

    // 加载文本内容
    async loadTextContent() {
      try {
        const response = await fetch(this.resourceDetail.fileUrl)
        if (response.ok) {
          this.textContent = await response.text()
        } else {
          this.textContent = '无法加载文本内容'
        }
      } catch (error) {
        console.error('加载文本内容失败:', error)
        this.textContent = '加载文本内容失败'
      }
    },
    
    // 获取资源类型图标
    getResourceTypeIcon(type) {
      return ResourceModel.getResourceTypeIcon(type)
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      return ResourceModel.formatFileSize(bytes)
    },
    
    // 格式化日期
    formatDate(dateStr, format) {
      return ResourceModel.formatDate(dateStr, format)
    },
    
    // 获取文件扩展名
    getFileExtension(fileUrl) {
      if (!fileUrl) return '未知'
      const extension = fileUrl.split('.').pop()
      return extension ? extension.toUpperCase() : '未知'
    },
    
    // 获取资源类型标签
    getResourceTypeLabel(resource) {
      return ResourceModel.getResourceTypeLabel(resource)
    }
  },
  
  beforeDestroy() {
    // 清理事件监听
    document.removeEventListener('keydown', this.handleEscKey)
  }
}
</script>

<style lang="less" scoped>
.resource-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .resource-detail {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .resource-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 2px solid #f0f0f0;
      
      .resource-info {
        flex: 1;
        
        .resource-title {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          margin-bottom: 15px;
          line-height: 1.4;
        }
        
        .resource-meta {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          margin-bottom: 20px;
          
          .meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            color: #666;
            
            i {
              color: #4093f9;
            }
          }
        }
        
        .resource-stats {
          display: flex;
          gap: 30px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 16px;
            color: #333;
            
            i {
              color: #4093f9;
              font-size: 20px;
            }
          }
        }
      }
      
      .resource-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
        min-width: 200px;
        
        .action-button {
          width: 100%;
          min-width: 120px;
          text-align: center;
          justify-content: center;
          margin-left: 0 !important;
        }
      }
    }
    
    .resource-preview {
      margin-bottom: 30px;
      
      .preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        
        h3 {
          font-size: 20px;
          color: #333;
        }
      }
      
      .preview-content {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.fullscreen {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 9999;
          background: rgba(0, 0, 0, 0.9);
          border-radius: 0;
        }
        
        .image-preview {
          img {
            max-width: 100%;
            max-height: 600px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
        
        .video-preview {
          video {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 8px;
          }
        }
        
        .audio-preview {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20px;
          
          audio {
            width: 100%;
            max-width: 500px;
          }
          
          .audio-info {
            display: flex;
            align-items: center;
            gap: 15px;
            
            img {
              width: 80px;
              height: 80px;
              border-radius: 8px;
              object-fit: cover;
            }
            
            .audio-meta {
              h4 {
                font-size: 18px;
                color: #333;
                margin-bottom: 5px;
              }
              
              p {
                font-size: 14px;
                color: #666;
              }
            }
          }
        }
        
        .pdf-preview {
          iframe {
            width: 100%;
            height: 600px;
            border-radius: 8px;
          }
        }
        
        .text-preview {
          width: 100%;
          align-self: stretch;
          
          .text-content {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            height: 600px;
            width: 100%;
            overflow-y: auto;
            
            pre {
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 14px;
              line-height: 1.5;
              color: #333;
              height: 100%;
              text-align: left;
            }
          }
        }
        
        .default-preview {
          .preview-placeholder {
            text-align: center;
            color: #666;
            
            i {
              font-size: 64px;
              margin-bottom: 20px;
              color: #4093f9;
            }
            
            p {
              font-size: 16px;
              margin-bottom: 10px;
            }
          }
        }
      }
    }
    
    .resource-description {
      margin-bottom: 30px;
      
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 15px;
      }
      
      .description-content {
        font-size: 16px;
        line-height: 1.8;
        color: #333;
        margin-bottom: 20px;
        
        ::v-deep {
          p {
            margin-bottom: 15px;
          }
          
          img {
            max-width: 100%;
            border-radius: 8px;
            margin: 20px 0;
          }
          
          h3 {
            font-size: 18px;
            margin: 20px 0 10px 0;
            color: #333;
          }
          
          ul, ol {
            margin: 15px 0;
            padding-left: 30px;
            
            li {
              margin-bottom: 8px;
            }
          }
          
          blockquote {
            background: #f8f9fa;
            border-left: 4px solid #4093f9;
            padding: 15px 20px;
            margin: 20px 0;
            font-style: italic;
            color: #666;
          }
        }
      }
      
      .course-info {
        margin: 20px 0;
        
        h4 {
          font-size: 16px;
          color: #333;
          margin-bottom: 15px;
        }
        
        .course-card {
          cursor: pointer;
          background: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          display: flex;
          align-items: center;
          gap: 15px;
          border-left: 4px solid #409EFF;
          
          .course-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, #409EFF, #2c5aa0);
            display: flex;
            align-items: center;
            justify-content: center;
            
            i {
              font-size: 24px;
              color: white;
            }
          }
          
          .course-content {
            h5 {
              font-size: 18px;
              color: #333;
              margin-bottom: 5px;
            }
            
            p {
              font-size: 14px;
              color: #666;
              margin: 0;
            }
          }
        }
      }
      
      .resource-tags {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .tags-label {
          font-size: 14px;
          color: #666;
        }
        
        .el-tag {
          margin-right: 5px;
        }
      }
    }
    
    .related-resources {
      h3 {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
      }
      
      .related-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        
        .related-item {
          background: #f8f9fa;
          border-radius: 8px;
          padding: 15px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
          }
          
          img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
          }
          
          .related-content {
            h4 {
              font-size: 14px;
              color: #333;
              margin-bottom: 5px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            p {
              font-size: 12px;
              color: #666;
              margin-bottom: 8px;
            }
            
            .related-stats {
              display: flex;
              justify-content: space-between;
              font-size: 12px;
              color: #999;
              
              span {
                display: flex;
                align-items: center;
                gap: 3px;
              }
            }
          }
        }
      }
    }
  }
  
  .loading-container {
    background: white;
    border-radius: 12px;
    padding: 100px;
    text-align: center;
  }
  
  .not-found {
    background: white;
    border-radius: 12px;
    padding: 100px;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-detail-page {
    padding: 20px 0;
    
    .resource-detail {
      padding: 20px;
      
      .resource-header {
        flex-direction: column;
        gap: 20px;
        
        .resource-actions {
          width: 100%;
          flex-direction: row;
          justify-content: space-between;
        }
      }
      
      .resource-meta {
        flex-direction: column;
        gap: 10px;
      }
      
      .related-resources {
        .related-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>