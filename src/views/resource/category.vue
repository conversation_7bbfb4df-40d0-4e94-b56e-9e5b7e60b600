<template>
  <div class="resource-category-page">
    <div class="content-container">
      <!-- 返回导航 -->
      <div class="back-navigation">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/resource' }">资源中心</el-breadcrumb-item>
          <el-breadcrumb-item>{{ categoryInfo.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 分类信息 -->
      <div class="category-header">
        <div class="category-info">
          <div class="category-icon">
            <i :class="categoryInfo.icon"></i>
          </div>
          <div class="category-content">
            <h1 class="category-name">{{ categoryInfo.name }}</h1>
            <p class="category-description">{{ categoryInfo.description }}</p>
            <div class="category-stats">
              <span class="stat-item">
                <i class="el-icon-document"></i>
                {{ categoryInfo.resourceCount }}个资源
              </span>
              <span class="stat-item">
                <i class="el-icon-view"></i>
                {{ categoryInfo.viewCount }}次浏览
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选和排序 -->
      <div class="filter-section">
        <div class="filter-controls">
          <div class="filter-left">
            <el-select v-model="filters.type" placeholder="资源类型" @change="handleFilterChange" clearable>
              <el-option 
                v-for="type in resourceFileTypes" 
                :key="type.value" 
                :label="type.label" 
                :value="type.value">
              </el-option>
            </el-select>
            
            <el-input
              v-model="filters.keyword"
              placeholder="搜索关键词、课程名称"
              @keyup.enter.native="handleFilterChange"
              @clear="handleFilterChange"
              clearable
            >
              <el-button slot="append" icon="el-icon-search" @click="handleFilterChange"></el-button>
            </el-input>
            
<!--            <el-select v-model="filters.resourceType" placeholder="资源类型" @change="handleFilterChange" clearable>-->
<!--              <el-option label="课程资源" value="course"></el-option>-->
<!--              <el-option label="公共资源" value="public"></el-option>-->
<!--            </el-select>-->
          </div>
          
          <div class="filter-right">
            <span class="sort-label">排序：</span>
            <el-select v-model="sortBy" @change="handleSortChange">
              <el-option label="最新上传" value="time"></el-option>
              <el-option label="最多浏览" value="views"></el-option>
              <el-option label="最多下载" value="downloads"></el-option>
              <el-option label="最多收藏" value="favorites"></el-option>
            </el-select>
          </div>
        </div>
        
        <div class="result-info">
          <span>共找到 {{ total }} 个资源</span>
        </div>
      </div>

      <!-- 资源列表 -->
      <div class="resource-list" v-loading="loading">
        <div class="resource-grid">
          <div class="resource-item" v-for="resource in resourceList" :key="resource.resourceId" @click="goToDetail(resource.resourceId)">
            <div class="resource-thumbnail">
              <img :src="resource.thumbnail" :alt="resource.title" />
              <div class="resource-type">
                <i :class="getResourceTypeIcon(resource.type)"></i>
                {{ resource.typeName }}
              </div>
              <div class="resource-actions">
                <el-button type="text" @click.stop="previewResource(resource)" v-if="resource.canPreview">
                  <i class="el-icon-view"></i>
                </el-button>
                <el-button type="text" @click.stop="downloadResource(resource)" v-if="resource.canDownload">
                  <i class="el-icon-download"></i>
                </el-button>
                <el-button type="text" @click.stop="toggleFavorite(resource)">
                  <i class="el-icon-star-off"></i>
                </el-button>
              </div>
            </div>
            
            <div class="resource-content">
              <h3 class="resource-title">{{ resource.title }}</h3>
              <p class="resource-description">{{ resource.description }}</p>
              
              <div class="resource-meta">
                <span class="meta-item">
                  <i class="el-icon-user"></i>
                  {{ resource.author }}
                </span>
                <span class="meta-item">
                  <i class="el-icon-time"></i>
                  {{ formatDate(resource.uploadTime) }}
                </span>
                <span class="meta-item">
                  <i class="el-icon-document"></i>
                  {{ formatFileSize(resource.fileSize) }}
                </span>
                <span class="meta-item">
                  <i :class="getResourceTypeIcon(resource.type)"></i>
                  {{ getFileExtension(resource.fileUrl) }}
                </span>
              </div>
              
              <div class="resource-stats">
                <span class="stat-item">
                  <i class="el-icon-view"></i>
                  {{ resource.views }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-download"></i>
                  {{ resource.downloads }}
                </span>
                <span class="stat-item">
                  <i class="el-icon-star-off"></i>
                  {{ resource.favorites }}
                </span>
              </div>
              
              <div class="resource-course-info">
                <el-tag 
                  :color="getResourceTypeLabel(resource).color" 
                  size="mini"
                  effect="plain"
                >
                  <i class="el-icon-collection"></i>
                  {{ getResourceTypeLabel(resource).label }}
                </el-tag>
              </div>
              
              <div class="resource-tags" v-if="resource.tags && resource.tags.length > 0">
                <el-tag v-for="tag in resource.tags.slice(0, 3)" :key="tag" size="mini">{{ tag }}</el-tag>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!loading && resourceList.length === 0" class="empty-state">
          <el-empty description="暂无资源">
            <el-button type="primary" @click="clearFilters">清除筛选条件</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section" v-if="total > 0">
        <el-pagination
          @current-change="handlePageChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { ResourceModel } from '@/model/ResourceModel'
import Enums from '@/enums/index'

export default {
  name: 'ResourceCategory',
  data() {
    return {
      // 分类信息
      categoryInfo: {
        id: null,
        name: '',
        description: '',
        icon: 'el-icon-folder',
        resourceCount: 0,
        viewCount: 0
      },
      
      // 资源列表
      resourceList: [],
      
      // 分页信息
      currentPage: 1,
      pageSize: 12,
      total: 0,
      
      // 筛选条件
      filters: {
        type: '',
        keyword: '',
        resourceType: ''
      },
      
      // 排序方式
      sortBy: 'time',
      
      // 加载状态
      loading: false,
      
      // 资源文件类型
      resourceFileTypes: Enums.resourceFileTypes
    }
  },
  
  async created() {
    await this.loadCategoryInfo()
    await this.loadResourceList()
  },
  
  watch: {
    '$route'() {
      // 路由变化时重新加载数据
      this.loadCategoryInfo()
      this.loadResourceList()
    }
  },
  
  methods: {
    // 加载分类信息
    async loadCategoryInfo() {
      try {
        const categoryId = this.$route.params.categoryId
        if (!categoryId) {
          this.$message.error('分类ID无效')
          this.goBack()
          return
        }
        
        // 获取所有分类信息
        const categories = await ResourceModel.getResourceCategories()
        const category = categories.find(cat => cat.categoryId == categoryId)
        
        if (category) {
          this.categoryInfo = category
        } else {
          this.$message.error('分类不存在')
          this.goBack()
        }
        
      } catch (error) {
        console.error('加载分类信息失败:', error)
        this.$message.error('加载失败，请稍后重试')
      }
    },
    
    // 加载资源列表
    async loadResourceList() {
      this.loading = true
      try {
        const categoryId = this.$route.params.categoryId
        const params = {
          type: this.filters.type ? this.filters.type : undefined,
          keyword: this.filters.keyword ? this.filters.keyword : undefined,
          resourceType: this.filters.resourceType ? this.filters.resourceType : undefined,
          sortBy: this.sortBy ? this.sortBy : undefined
        }
        
        // 处理资源类型筛选
        if (this.filters.resourceType === 'course') {
          params.isPublic = false
        } else if (this.filters.resourceType === 'public') {
          params.isPublic = true
        }
        
        const response = await ResourceModel.getResourcesByCategory(
          categoryId,
          this.currentPage,
          this.pageSize,
          params
        )
        
        this.resourceList = response.list
        this.total = response.total
        
      } catch (error) {
        console.error('加载资源列表失败:', error)
        this.$message.error('加载失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },
    
    // 处理筛选条件变化
    handleFilterChange() {
      this.currentPage = 1
      this.loadResourceList()
    },
    
    // 处理排序变化
    handleSortChange() {
      this.currentPage = 1
      this.loadResourceList()
    },
    
    // 处理分页变化
    handlePageChange(page) {
      this.currentPage = page
      this.loadResourceList()
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },
    
    // 清除筛选条件
    clearFilters() {
      this.filters = {
        type: '',
        keyword: '',
        resourceType: ''
      }
      this.sortBy = 'time'
      this.currentPage = 1
      this.loadResourceList()
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 跳转到资源详情
    goToDetail(resourceId) {
      this.$router.push({
        name: 'ResourceDetail',
        params: { id: resourceId }
      })
    },
    
    // 预览资源
    previewResource(resource) {
      this.goToDetail(resource.resourceId)
    },
    
    // 下载资源
    async downloadResource(resource) {
      if (!resource.canDownload) {
        this.$message.warning('该资源不支持下载')
        return
      }
      
      try {
        this.$message.info('正在准备下载...')
        
        // 增加下载量
        await ResourceModel.increaseResourceDownloads(resource.resourceId)
        
        // 获取下载链接
        const downloadResult = await ResourceModel.downloadResource(resource.resourceId)
        
        if (downloadResult.success) {
          // 创建下载链接
          const link = document.createElement('a')
          link.href = downloadResult.downloadUrl
          link.download = resource.title
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          
          this.$message.success('下载开始')
          
          // 更新列表中的下载量
          const index = this.resourceList.findIndex(item => item.id === resource.resourceId)
          if (index !== -1) {
            this.resourceList[index].downloads += 1
          }
        } else {
          this.$message.error(downloadResult.message || '下载失败')
        }
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },
    
    // 切换收藏状态
    async toggleFavorite(resource) {
      try {
        const newFavoriteStatus = await ResourceModel.toggleResourceFavorite(resource.resourceId, false)
        
        if (newFavoriteStatus) {
          this.$message.success('收藏成功')
          // 更新列表中的收藏量
          const index = this.resourceList.findIndex(item => item.id === resource.resourceId)
          if (index !== -1) {
            this.resourceList[index].favorites += 1
          }
        }
        
      } catch (error) {
        console.error('收藏失败:', error)
        this.$message.error('收藏失败，请稍后重试')
      }
    },
    
    // 获取资源类型图标
    getResourceTypeIcon(type) {
      return ResourceModel.getResourceTypeIcon(type)
    },
    
    // 格式化文件大小
    formatFileSize(bytes) {
      return ResourceModel.formatFileSize(bytes)
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return ResourceModel.formatDate(dateStr, 'MM-DD')
    },
    
    // 获取文件扩展名
    getFileExtension(fileUrl) {
      if (!fileUrl) return '未知'
      const extension = fileUrl.split('.').pop()
      return extension ? extension.toUpperCase() : '未知'
    },
    
    // 获取资源类型标签
    getResourceTypeLabel(resource) {
      return ResourceModel.getResourceTypeLabel(resource)
    }
  }
}
</script>

<style lang="less" scoped>
.resource-category-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .category-header {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .category-info {
      display: flex;
      align-items: center;
      gap: 20px;
      
      .category-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4093f9, #2c5aa0);
        display: flex;
        align-items: center;
        justify-content: center;
        
        i {
          font-size: 32px;
          color: white;
        }
      }
      
      .category-content {
        flex: 1;
        
        .category-name {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          margin-bottom: 10px;
        }
        
        .category-description {
          font-size: 16px;
          color: #666;
          margin-bottom: 15px;
          line-height: 1.5;
        }
        
        .category-stats {
          display: flex;
          gap: 30px;
          
          .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #999;
            
            i {
              color: #4093f9;
            }
          }
        }
      }
    }
  }
  
  .filter-section {
    background: white;
    border-radius: 12px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .filter-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      .filter-left {
        display: flex;
        gap: 15px;
        
        .el-select {
          width: 120px;
        }
        
        .el-input {
          width: 300px;
        }
      }
      
      .filter-right {
        display: flex;
        align-items: center;
        gap: 10px;
        
        .sort-label {
          font-size: 14px;
          color: #666;
        }
        
        .el-select {
          width: 120px;
        }
      }
    }
    
    .result-info {
      font-size: 14px;
      color: #666;
    }
  }
  
  .resource-list {
    .resource-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 25px;
      
      .resource-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          
          .resource-actions {
            opacity: 1;
          }
        }
        
        .resource-thumbnail {
          position: relative;
          height: 200px;
          overflow: hidden;
          
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          
          .resource-type {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 5px;
          }
          
          .resource-actions {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: flex;
            gap: 5px;
            opacity: 0;
            transition: opacity 0.3s ease;
            
            .el-button {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: rgba(255, 255, 255, 0.9);
              border: none;
              color: #4093f9;
              
              &:hover {
                background: white;
                color: #2c5aa0;
              }
            }
          }
        }
        
        .resource-content {
          padding: 20px;
          
          .resource-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
          }
          
          .resource-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.5;
          }
          
          .resource-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-bottom: 15px;
            
            .meta-item {
              display: flex;
              align-items: center;
              gap: 5px;
              font-size: 12px;
              color: #999;
              
              i {
                color: #4093f9;
              }
            }
          }
          
          .resource-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            
            .stat-item {
              display: flex;
              align-items: center;
              gap: 5px;
              font-size: 12px;
              color: #666;
            }
          }
          
          .resource-course-info {
            margin-bottom: 10px;
            
            .el-tag {
              border: none;
              font-size: 11px;
            }
          }
          
          .resource-tags {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 80px 20px;
    }
  }
  
  .pagination-section {
    margin-top: 40px;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-category-page {
    padding: 20px 0;
    
    .category-header {
      padding: 20px;
      
      .category-info {
        flex-direction: column;
        text-align: center;
        
        .category-icon {
          width: 60px;
          height: 60px;
          
          i {
            font-size: 24px;
          }
        }
        
        .category-content {
          .category-name {
            font-size: 24px;
          }
        }
      }
    }
    
    .filter-section {
      padding: 15px 20px;
      
      .filter-controls {
        flex-direction: column;
        gap: 15px;
        
        .filter-left {
          flex-direction: column;
          width: 100%;
          
          .el-select, .el-input {
            width: 100%;
          }
        }
        
        .filter-right {
          width: 100%;
          justify-content: space-between;
        }
      }
    }
    
    .resource-list {
      .resource-grid {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>