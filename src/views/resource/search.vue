<template>
  <div class="resource-search-page">
    <div class="content-container">
      <!-- 返回导航 -->
      <div class="back-navigation">
        <el-button @click="goBack" icon="el-icon-arrow-left">返回</el-button>
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/resource' }">资源中心</el-breadcrumb-item>
          <el-breadcrumb-item>搜索结果</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="search-container">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索资源名称、关键词或描述"
            class="search-input"
            size="large"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
          
<!--          <div class="search-suggestions" v-if="hotTags.length > 0">-->
<!--            <div class="suggestion-title">热门搜索</div>-->
<!--            <div class="suggestion-tags">-->
<!--              <el-tag -->
<!--                v-for="tag in hotTags" -->
<!--                :key="tag"-->
<!--                @click="searchByTag(tag)"-->
<!--                class="suggestion-tag"-->
<!--              >-->
<!--                {{ tag }}-->
<!--              </el-tag>-->
<!--            </div>-->
<!--          </div>-->
        </div>
      </div>

      <!-- 高级筛选 -->
      <div class="filter-section" v-if="false">
        <div class="filter-header">
          <h3>高级筛选</h3>
          <el-button type="text" @click="toggleAdvancedFilter">
            {{ showAdvancedFilter ? '收起' : '展开' }}
            <i :class="showAdvancedFilter ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
          </el-button>
        </div>
        
        <div class="filter-content" v-show="showAdvancedFilter">
          <div class="filter-row">
            <div class="filter-item">
              <label>资源类型：</label>
              <el-select v-model="filters.type" placeholder="请选择" @change="handleFilterChange" clearable>
                <el-option 
                  v-for="type in resourceFileTypes" 
                  :key="type.value" 
                  :label="type.label" 
                  :value="type.value">
                </el-option>
              </el-select>
            </div>
            
            <div class="filter-item">
              <label>资源分类：</label>
              <el-select v-model="filters.categoryId" placeholder="请选择" @change="handleFilterChange" clearable>
                <el-option 
                  v-for="category in categoryList" 
                  :key="category.id"
                  :label="category.name" 
                  :value="category.id"
                ></el-option>
              </el-select>
            </div>
            
            <div class="filter-item">
              <label>作者：</label>
              <el-input 
                v-model="filters.author" 
                placeholder="输入作者名称"
                @keyup.enter.native="handleFilterChange"
                @clear="handleFilterChange"
                clearable
              ></el-input>
            </div>
            
            <div class="filter-item">
              <label>课程名称：</label>
              <el-input 
                v-model="filters.courseName" 
                placeholder="输入课程名称"
                @keyup.enter.native="handleFilterChange"
                @clear="handleFilterChange"
                clearable
              ></el-input>
            </div>
          </div>
          
          <div class="filter-row">
            <div class="filter-item">
              <label>上传时间：</label>
              <el-date-picker
                v-model="filters.uploadTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="handleFilterChange"
                clearable
              >
              </el-date-picker>
            </div>
            
            <div class="filter-item">
              <label>资源类型：</label>
              <el-select v-model="filters.resourceType" @change="handleFilterChange" clearable>
                <el-option label="课程资源" value="course"></el-option>
                <el-option label="公共资源" value="public"></el-option>
              </el-select>
            </div>
            
            <div class="filter-item">
              <label>排序方式：</label>
              <el-select v-model="sortBy" @change="handleSortChange">
                <el-option label="相关度" value="relevance"></el-option>
                <el-option label="最新上传" value="time"></el-option>
                <el-option label="最多浏览" value="views"></el-option>
                <el-option label="最多下载" value="downloads"></el-option>
                <el-option label="最多收藏" value="favorites"></el-option>
              </el-select>
            </div>
            
            <div class="filter-item">
              <el-button type="primary" @click="handleFilterChange">搜索</el-button>
              <el-button @click="resetFilters">重置</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <!-- 结果统计 -->
        <div class="result-header">
          <div class="result-info">
            <span v-if="searchKeyword">搜索"{{ searchKeyword }}"，</span>
            <span>共找到 {{ total }} 个结果</span>
          </div>
          <div class="view-mode">
            <el-radio-group v-model="viewMode" @change="handleViewModeChange">
              <el-radio-button label="grid">
                <i class="el-icon-s-grid"></i>
              </el-radio-button>
              <el-radio-button label="list">
                <i class="el-icon-s-order"></i>
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>

        <!-- 网格视图 -->
        <div v-if="viewMode === 'grid'" class="grid-view" v-loading="loading">
          <div class="resource-grid">
            <div class="resource-item" v-for="resource in resourceList" :key="resource.id" @click="goToDetail(resource.id)">
              <div class="resource-thumbnail">
                <img :src="resource.thumbnail" :alt="resource.title" />
                <div class="resource-type">
                  <i :class="getResourceTypeIcon(resource.type)"></i>
                  {{ resource.typeName }}
                </div>
                <div class="resource-actions">
                  <el-button type="text" @click.stop="previewResource(resource)" v-if="resource.canPreview">
                    <i class="el-icon-view"></i>
                  </el-button>
                  <el-button type="text" @click.stop="downloadResource(resource)" v-if="resource.canDownload">
                    <i class="el-icon-download"></i>
                  </el-button>
                  <el-button type="text" @click.stop="toggleFavorite(resource)">
                    <i class="el-icon-star-off"></i>
                  </el-button>
                </div>
              </div>
              
              <div class="resource-content">
                <h3 class="resource-title">{{ resource.title }}</h3>
                <p class="resource-description">{{ resource.description }}</p>
                
                <div class="resource-meta">
                  <span class="meta-item">
                    <i class="el-icon-user"></i>
                    {{ resource.author }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-time"></i>
                    {{ formatDate(resource.uploadTime) }}
                  </span>
                  <span class="meta-item">
                    <el-tag 
                      :color="getResourceTypeLabel(resource).color" 
                      size="mini"
                      effect="plain"
                    >
                      <i class="el-icon-collection"></i>
                      {{ getResourceTypeLabel(resource).label }}
                    </el-tag>
                  </span>
                </div>
                
                <div class="resource-stats">
                  <span class="stat-item">
                    <i class="el-icon-view"></i>
                    {{ resource.views }}
                  </span>
                  <span class="stat-item">
                    <i class="el-icon-download"></i>
                    {{ resource.downloads }}
                  </span>
                  <span class="stat-item">
                    <i class="el-icon-star-off"></i>
                    {{ resource.favorites }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表视图 -->
        <div v-if="viewMode === 'list'" class="list-view" v-loading="loading">
          <div class="resource-list">
            <div class="list-item" v-for="resource in resourceList" :key="resource.id" @click="goToDetail(resource.id)">
              <div class="item-thumbnail">
                <img :src="resource.thumbnail" :alt="resource.title" />
                <div class="resource-type">
                  <i :class="getResourceTypeIcon(resource.type)"></i>
                </div>
              </div>
              
              <div class="item-content">
                <h3 class="item-title">{{ resource.title }}</h3>
                <p class="item-description">{{ resource.description }}</p>
                
                <div class="item-meta">
                  <span class="meta-item">
                    <i class="el-icon-user"></i>
                    {{ resource.author }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-folder"></i>
                    {{ resource.categoryName }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-time"></i>
                    {{ formatDate(resource.uploadTime, 'YYYY-MM-DD') }}
                  </span>
                  <span class="meta-item">
                    <i class="el-icon-document"></i>
                    {{ formatFileSize(resource.fileSize) }}
                  </span>
                  <span class="meta-item">
                    <i :class="getResourceTypeIcon(resource.type)"></i>
                    {{ getFileExtension(resource.fileUrl) }}
                  </span>
                </div>
                
                <div class="item-course-info">
                  <el-tag 
                    :color="getResourceTypeLabel(resource).color" 
                    size="mini"
                    effect="plain"
                  >
                    <i class="el-icon-collection"></i>
                    {{ getResourceTypeLabel(resource).label }}
                  </el-tag>
                </div>
                
                <div class="item-tags" v-if="resource.tags && resource.tags.length > 0">
                  <el-tag v-for="tag in resource.tags.slice(0, 4)" :key="tag" size="mini">{{ tag }}</el-tag>
                </div>
              </div>
              
              <div class="item-actions">
                <div class="action-stats">
                  <div class="stat-row">
                    <span class="stat-item">
                      <i class="el-icon-view"></i>
                      {{ resource.views }}
                    </span>
                    <span class="stat-item">
                      <i class="el-icon-download"></i>
                      {{ resource.downloads }}
                    </span>
                    <span class="stat-item">
                      <i class="el-icon-star-off"></i>
                      {{ resource.favorites }}
                    </span>
                  </div>
                </div>
                
                <div class="action-buttons">
                  <el-button type="text" @click.stop="previewResource(resource)" v-if="resource.canPreview">
                    <i class="el-icon-view"></i>
                    预览
                  </el-button>
                  <el-button type="text" @click.stop="downloadResource(resource)" v-if="resource.canDownload">
                    <i class="el-icon-download"></i>
                    下载
                  </el-button>
                  <el-button type="text" @click.stop="toggleFavorite(resource)">
                    <i class="el-icon-star-off"></i>
                    收藏
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空状态 -->
        <div v-if="!loading && resourceList.length === 0" class="empty-state">
          <el-empty description="没有找到相关资源">
            <el-button type="primary" @click="resetFilters">重新搜索</el-button>
          </el-empty>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-section" v-if="total > 0">
        <el-pagination
          @current-change="handlePageChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { ResourceModel } from '@/model/ResourceModel'
import Enums from '@/enums/index'

export default {
  name: 'ResourceSearch',
  data() {
    return {
      // 搜索关键词
      searchKeyword: '',
      
      // 热门标签
      hotTags: [],
      
      // 分类列表
      categoryList: [],
      
      // 高级筛选
      showAdvancedFilter: false,
      filters: {
        type: '',
        categoryId: '',
        author: '',
        courseName: '',
        resourceType: '',
        uploadTime: null
      },
      
      // 排序方式
      sortBy: 'relevance',
      
      // 视图模式
      viewMode: 'grid',
      
      // 资源列表
      resourceList: [],
      
      // 分页信息
      currentPage: 1,
      pageSize: 12,
      total: 0,
      
      // 加载状态
      loading: false,
      
      // 资源文件类型
      resourceFileTypes: Enums.resourceFileTypes
    }
  },
  
  async created() {
    // 从查询参数获取搜索关键词
    this.searchKeyword = this.$route.query.keyword || ''
    
    await this.loadInitialData()
    
    if (this.searchKeyword) {
      await this.performSearch()
    }
  },
  
  watch: {
    '$route.query.keyword'(newKeyword) {
      // 监听路由参数变化
      this.searchKeyword = newKeyword || ''
      if (this.searchKeyword) {
        this.resetSearchState()
        this.performSearch()
      }
    }
  },

  methods: {
    // 加载初始数据
    async loadInitialData() {
      try {
        const [hotTags, categories] = await Promise.all([
          ResourceModel.getHotSearchTags(),
          ResourceModel.getResourceCategories()
        ])

        this.hotTags = hotTags
        this.categoryList = categories

      } catch (error) {
        console.error('加载初始数据失败:', error)
      }
    },

    // 执行搜索
    async performSearch() {
      this.loading = true
      try {
        const params = {
          keyword: this.searchKeyword ? this.searchKeyword : undefined,
          sortBy: this.sortBy ? this.sortBy : undefined
        }

        // 处理filters中的参数，添加空值检测
        if (this.filters) {
          if (this.filters.resourceType) {
            params.resourceType = this.filters.resourceType ? this.filters.resourceType : undefined
          }
          if (this.filters.category) {
            params.category = this.filters.category ? this.filters.category : undefined
          }
          if (this.filters.author) {
            params.author = this.filters.author ? this.filters.author : undefined
          }
          if (this.filters.status) {
            params.status = this.filters.status ? this.filters.status : undefined
          }
        }

        // 处理时间范围
        if (this.filters && this.filters.uploadTime && this.filters.uploadTime.length === 2) {
          params.startTime = this.filters.uploadTime[0] ? this.filters.uploadTime[0] : undefined
          params.endTime = this.filters.uploadTime[1] ? this.filters.uploadTime[1] : undefined
        }

        // 处理资源类型筛选
        if (this.filters && this.filters.resourceType) {
          if (this.filters.resourceType === 'course') {
            params.isPublic = false
          } else if (this.filters.resourceType === 'public') {
            params.isPublic = true
          }
        }

        const response = await ResourceModel.searchResources(
            this.currentPage,
            this.pageSize,
            params
        )

        this.resourceList = response.list
        this.total = response.total

      } catch (error) {
        console.error('搜索失败:', error)
        this.$message.error('搜索失败，请稍后重试')
      } finally {
        this.loading = false
      }
    },

    // 处理搜索
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning('请输入搜索关键词')
        return
      }

      this.resetSearchState()
      this.performSearch()

      // 更新URL
      this.$router.replace({
        name: 'ResourceSearch',
        query: { keyword: this.searchKeyword }
      })
    },

    // 点击标签搜索
    searchByTag(tag) {
      this.searchKeyword = tag
      this.handleSearch()
    },

    // 重置搜索状态
    resetSearchState() {
      this.currentPage = 1
      this.total = 0
      this.resourceList = []
    },

    // 切换高级筛选
    toggleAdvancedFilter() {
      this.showAdvancedFilter = !this.showAdvancedFilter
    },

    // 处理筛选条件变化
    handleFilterChange() {
      this.resetSearchState()
      this.performSearch()
    },

    // 处理排序变化
    handleSortChange() {
      this.resetSearchState()
      this.performSearch()
    },

    // 重置筛选条件
    resetFilters() {
      this.searchKeyword = undefined
      this.filters = {
        type: undefined,
        categoryId: undefined,
        author: undefined,
        courseName: undefined,
        resourceType: undefined,
        uploadTime: undefined
      }
      this.sortBy = 'relevance'
      this.resetSearchState()

      // 清除URL参数
      this.$router.replace({ name: 'ResourceSearch' })
    },

    // 处理视图模式变化
    handleViewModeChange() {
      // 保存用户偏好到本地存储
      localStorage.setItem('resource-view-mode', this.viewMode)
    },

    // 处理分页变化
    handlePageChange(page) {
      this.currentPage = page
      this.performSearch()
      // 滚动到顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 跳转到资源详情
    goToDetail(resourceId) {
      this.$router.push({
        name: 'ResourceDetail',
        params: { id: resourceId }
      })
    },

    // 预览资源
    previewResource(resource) {
      this.goToDetail(resource.id)
    },

    // 下载资源
    async downloadResource(resource) {
      if (!resource.canDownload) {
        this.$message.warning('该资源不支持下载')
        return
      }

      try {
        this.$message.info('正在准备下载...')

        // 增加下载量
        await ResourceModel.increaseResourceDownloads(resource.id)

        // 获取下载链接
        const downloadResult = await ResourceModel.downloadResource(resource.id)

        if (downloadResult.success) {
          // 创建下载链接
          const link = document.createElement('a')
          link.href = downloadResult.downloadUrl
          link.download = resource.title
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          this.$message.success('下载开始')

          // 更新列表中的下载量
          const index = this.resourceList.findIndex(item => item.id === resource.id)
          if (index !== -1) {
            this.resourceList[index].downloads += 1
          }
        } else {
          this.$message.error(downloadResult.message || '下载失败')
        }
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败，请稍后重试')
      }
    },

    // 切换收藏状态
    async toggleFavorite(resource) {
      try {
        const newFavoriteStatus = await ResourceModel.toggleResourceFavorite(resource.id, false)

        if (newFavoriteStatus) {
          this.$message.success('收藏成功')
          // 更新列表中的收藏量
          const index = this.resourceList.findIndex(item => item.id === resource.id)
          if (index !== -1) {
            this.resourceList[index].favorites += 1
          }
        }

      } catch (error) {
        console.error('收藏失败:', error)
        this.$message.error('收藏失败，请稍后重试')
      }
    },

    // 获取资源类型图标
    getResourceTypeIcon(type) {
      return ResourceModel.getResourceTypeIcon(type)
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      return ResourceModel.formatFileSize(bytes)
    },

    // 格式化日期
    formatDate(dateStr, format = 'MM-DD') {
      return ResourceModel.formatDate(dateStr, format)
    },

    // 获取文件扩展名
    getFileExtension(fileUrl) {
      if (!fileUrl) return '未知'
      const extension = fileUrl.split('.').pop()
      return extension ? extension.toUpperCase() : '未知'
    },

    // 获取资源类型标签
    getResourceTypeLabel(resource) {
      return ResourceModel.getResourceTypeLabel(resource)
    }
  },
  
  mounted() {
    // 从本地存储获取用户偏好的视图模式
    const savedViewMode = localStorage.getItem('resource-view-mode')
    if (savedViewMode) {
      this.viewMode = savedViewMode
    }
  }
}
</script>

<style lang="less" scoped>
.resource-search-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .search-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .search-container {
      max-width: 600px;
      margin: 0 auto;
      
      .search-input {
        ::v-deep .el-input__inner {
          height: 50px;
          border-radius: 25px 0 0 25px;
          font-size: 16px;
          border-right: none;
        }
        
        ::v-deep .el-input-group {
          display: flex;
          align-items: stretch;
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 25px 25px 0;
          border-left: none;
          background-color: #409EFF;
          
          .el-button {
            height: 50px;
            padding: 0 25px;
            border-radius: 0 25px 25px 0;
            background-color: #409EFF;
            border-color: #409EFF;
            color: white;
            border: none;
            
            &:hover {
              background-color: #337ecc;
            }
          }
        }
      }
      
      .search-suggestions {
        margin-top: 20px;
        text-align: center;
        
        .suggestion-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }
        
        .suggestion-tags {
          .suggestion-tag {
            margin: 0 5px;
            cursor: pointer;
            
            &:hover {
              color: #4093f9;
            }
          }
        }
      }
    }
  }
  
  .filter-section {
    background: white;
    border-radius: 12px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .filter-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      
      h3 {
        font-size: 18px;
        color: #333;
        margin: 0;
      }
    }
    
    .filter-content {
      .filter-row {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        margin-bottom: 15px;
        
        .filter-item {
          display: flex;
          align-items: center;
          gap: 10px;
          
          label {
            font-size: 14px;
            color: #666;
            white-space: nowrap;
          }
          
          .el-select, .el-input {
            width: 160px;
          }
          
          .el-date-editor {
            width: 250px;
          }
        }
      }
    }
  }
  
  .search-results {
    .result-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 0 5px;
      
      .result-info {
        font-size: 14px;
        color: #666;
      }
      
      .view-mode {
        .el-radio-button {
          ::v-deep .el-radio-button__inner {
            padding: 8px 12px;
          }
        }
      }
    }
    
    .grid-view {
      .resource-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 25px;
        
        .resource-item {
          background: white;
          border-radius: 12px;
          overflow: hidden;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          
          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            
            .resource-actions {
              opacity: 1;
            }
          }
          
          .resource-thumbnail {
            position: relative;
            height: 180px;
            overflow: hidden;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
            
            .resource-type {
              position: absolute;
              top: 10px;
              right: 10px;
              background: rgba(0, 0, 0, 0.7);
              color: white;
              padding: 5px 10px;
              border-radius: 15px;
              font-size: 12px;
              display: flex;
              align-items: center;
              gap: 5px;
            }
            
            .resource-actions {
              position: absolute;
              bottom: 10px;
              right: 10px;
              display: flex;
              gap: 5px;
              opacity: 0;
              transition: opacity 0.3s ease;
              
              .el-button {
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.9);
                border: none;
                color: #4093f9;
                
                &:hover {
                  background: white;
                  color: #2c5aa0;
                }
              }
            }
          }
          
          .resource-content {
            padding: 20px;
            
            .resource-title {
              font-size: 16px;
              font-weight: bold;
              color: #333;
              margin-bottom: 8px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            .resource-description {
              font-size: 14px;
              color: #666;
              margin-bottom: 15px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.5;
            }
            
            .resource-meta {
              display: flex;
              align-items: center;
              gap: 15px;
              margin-bottom: 15px;
              flex-wrap: wrap;
              
              .meta-item {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 12px;
                color: #999;
                
                i {
                  color: #4093f9;
                }
                
                .el-tag {
                  border: none;
                  font-size: 11px;
                }
              }
            }
            
            .resource-stats {
              display: flex;
              justify-content: space-between;
              
              .stat-item {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 12px;
                color: #666;
              }
            }
          }
        }
      }
    }
    
    .list-view {
      .resource-list {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        
        .list-item {
          display: flex;
          padding: 20px;
          border-bottom: 1px solid #f0f0f0;
          cursor: pointer;
          transition: background 0.3s ease;
          
          &:hover {
            background: #f8f9fa;
          }
          
          &:last-child {
            border-bottom: none;
          }
          
          .item-thumbnail {
            position: relative;
            width: 120px;
            height: 90px;
            border-radius: 8px;
            overflow: hidden;
            margin-right: 20px;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
            
            .resource-type {
              position: absolute;
              bottom: 5px;
              right: 5px;
              background: rgba(0, 0, 0, 0.7);
              color: white;
              padding: 2px 6px;
              border-radius: 10px;
              font-size: 10px;
              
              i {
                font-size: 10px;
              }
            }
          }
          
          .item-content {
            flex: 1;
            
            .item-title {
              font-size: 18px;
              font-weight: bold;
              color: #333;
              margin-bottom: 8px;
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
            
            .item-description {
              font-size: 14px;
              color: #666;
              margin-bottom: 15px;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              line-height: 1.5;
            }
            
            .item-meta {
              display: flex;
              flex-wrap: wrap;
              gap: 20px;
              margin-bottom: 10px;
              
              .meta-item {
                display: flex;
                align-items: center;
                gap: 5px;
                font-size: 12px;
                color: #999;
                
                i {
                  color: #4093f9;
                }
              }
            }
            
            .item-course-info {
              margin-bottom: 10px;
              
              .el-tag {
                border: none;
                font-size: 11px;
              }
            }
            
            .item-tags {
              display: flex;
              gap: 5px;
              flex-wrap: wrap;
            }
          }
          
          .item-actions {
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: flex-end;
            min-width: 150px;
            
            .action-stats {
              .stat-row {
                display: flex;
                gap: 15px;
                margin-bottom: 10px;
                
                .stat-item {
                  display: flex;
                  align-items: center;
                  gap: 5px;
                  font-size: 12px;
                  color: #666;
                }
              }
            }
            
            .action-buttons {
              display: flex;
              gap: 5px;
              
              .el-button {
                padding: 5px 8px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 80px 20px;
    }
  }
  
  .pagination-section {
    margin-top: 40px;
    text-align: center;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .resource-search-page {
    padding: 20px 0;
    
    .search-section, .filter-section {
      padding: 20px;
    }
    
    .filter-content {
      .filter-row {
        flex-direction: column;
        gap: 15px;
        
        .filter-item {
          width: 100%;
          
          .el-select, .el-input, .el-date-editor {
            width: 100%;
          }
        }
      }
    }
    
    .search-results {
      .result-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }
      
      .grid-view {
        .resource-grid {
          grid-template-columns: 1fr;
        }
      }
      
      .list-view {
        .resource-list {
          .list-item {
            flex-direction: column;
            text-align: center;
            
            .item-thumbnail {
              width: 100%;
              height: 150px;
              margin-right: 0;
              margin-bottom: 15px;
            }
            
            .item-actions {
              width: 100%;
              flex-direction: row;
              justify-content: space-between;
              align-items: center;
              margin-top: 15px;
            }
          }
        }
      }
    }
  }
}
</style>