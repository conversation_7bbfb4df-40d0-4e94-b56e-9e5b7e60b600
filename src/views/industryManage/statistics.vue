<template>
  <div class="app-container">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <div class="stat-card total-enterprises">
          <div class="stat-icon">
            <i class="el-icon-office-building"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalEnterprises || 0 }}</div>
            <div class="stat-label">合作企业总数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card total-students">
          <div class="stat-icon">
            <i class="el-icon-user"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalStudents || 0 }}</div>
            <div class="stat-label">培养学生总数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card total-jobs">
          <div class="stat-icon">
            <i class="el-icon-suitcase"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalJobs || 0 }}</div>
            <div class="stat-label">招聘信息总数</div>
          </div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="stat-card cooperation-years">
          <div class="stat-icon">
            <i class="el-icon-time"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stats.avgCooperationYears || 0 }}</div>
            <div class="stat-label">平均合作年限</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 企业类型分布图表 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header">
            <span>企业类型分布</span>
          </div>
          <div id="enterpriseTypeChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <!-- 企业规模分布图表 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header">
            <span>企业规模分布</span>
          </div>
          <div id="enterpriseScaleChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 学生分类分布图表 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header">
            <span>学生分类分布</span>
          </div>
          <div id="studentCategoryChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <!-- 招聘职位分类图表 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="hover">
          <div slot="header">
            <span>招聘职位分类</span>
          </div>
          <div id="jobCategoryChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 年度统计趋势图表 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="chart-card" shadow="hover">
          <div slot="header">
            <span>年度统计趋势</span>
            <el-button-group style="float: right; margin-top: -3px;">
              <el-button size="mini" @click="refreshData" :loading="loading">
                <i class="el-icon-refresh"></i> 刷新数据
              </el-button>
            </el-button-group>
          </div>
          <div id="yearTrendChart" style="height: 400px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-row style="margin-top: 20px;">
      <el-col :span="24">
        <el-card class="table-card" shadow="hover">
          <div slot="header">
            <span>详细统计数据</span>
          </div>
          <el-table :data="detailStats" border style="width: 100%">
            <el-table-column prop="category" label="统计分类" align="center" width="150"></el-table-column>
            <el-table-column prop="total" label="总数" align="center" width="100"></el-table-column>
            <el-table-column prop="active" label="启用数量" align="center" width="100"></el-table-column>
            <el-table-column prop="inactive" label="禁用数量" align="center" width="100"></el-table-column>
            <el-table-column prop="percentage" label="占比" align="center" width="120">
              <template slot-scope="scope">
                <el-tag :type="getPercentageTagType(scope.row.percentage)">
                  {{ scope.row.percentage }}%
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" align="left"></el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import {IndustryStatisticsModel} from "@/model/IndustryModel"
import {msg_err} from "@/utils/ele_component"

export default {
  name: "industryStatisticsManage",
  data() {
    return {
      loading: false,
      // 基础统计数据
      stats: {
        totalEnterprises: 0,
        totalStudents: 0,
        totalJobs: 0,
        avgCooperationYears: 0
      },
      // 分类统计数据
      categoryStats: {},
      // 详细统计数据表格
      detailStats: [],
      // 图表实例
      charts: {
        enterpriseType: null,
        enterpriseScale: null,
        studentCategory: null,
        jobCategory: null,
        yearTrend: null
      }
    }
  },

  async mounted() {
    await this.loadStatisticsData()
    this.initCharts()
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    // 销毁所有图表实例
    Object.values(this.charts).forEach(chart => {
      if (chart) {
        chart.dispose()
      }
    })
  },

  methods: {
    // 加载统计数据
    async loadStatisticsData() {
      try {
        this.loading = true
        
        // 获取基础统计数据
        const industryStats = await IndustryStatisticsModel.getIndustryStats()
        this.stats = {
          totalEnterprises: industryStats.totalEnterprises || 0,
          totalStudents: industryStats.totalStudents || 0,
          totalJobs: industryStats.totalJobs || 0,
          avgCooperationYears: industryStats.avgCooperationYears || 0
        }

        // 获取分类统计数据
        this.categoryStats = await IndustryStatisticsModel.getCategoryStats()
        
        // 生成详细统计表格数据
        this.generateDetailStats()
        
      } catch (error) {
        msg_err("加载统计数据失败")
        console.error('加载统计数据失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 生成详细统计表格数据
    generateDetailStats() {
      this.detailStats = [
        {
          category: '合作企业',
          total: this.stats.totalEnterprises,
          active: this.categoryStats.enterprises?.active || 0,
          inactive: this.categoryStats.enterprises?.inactive || 0,
          percentage: this.stats.totalEnterprises ? ((this.categoryStats.enterprises?.active || 0) / this.stats.totalEnterprises * 100).toFixed(1) : 0,
          description: '已建立合作关系的企业数量'
        },
        {
          category: '优秀学生',
          total: this.stats.totalStudents,
          active: this.categoryStats.students?.active || 0,
          inactive: this.categoryStats.students?.inactive || 0,
          percentage: this.stats.totalStudents ? ((this.categoryStats.students?.active || 0) / this.stats.totalStudents * 100).toFixed(1) : 0,
          description: '通过产教融合培养的优秀学生'
        },
        {
          category: '招聘信息',
          total: this.stats.totalJobs,
          active: this.categoryStats.jobs?.active || 0,
          inactive: this.categoryStats.jobs?.inactive || 0,
          percentage: this.stats.totalJobs ? ((this.categoryStats.jobs?.active || 0) / this.stats.totalJobs * 100).toFixed(1) : 0,
          description: '企业发布的招聘职位信息'
        },
        {
          category: '轮播图',
          total: this.categoryStats.banners?.total || 0,
          active: this.categoryStats.banners?.active || 0,
          inactive: this.categoryStats.banners?.inactive || 0,
          percentage: this.categoryStats.banners?.total ? ((this.categoryStats.banners?.active || 0) / this.categoryStats.banners?.total * 100).toFixed(1) : 0,
          description: '产教融合模块的轮播图展示'
        }
      ]
    },

    // 获取百分比标签类型
    getPercentageTagType(percentage) {
      if (percentage >= 80) return 'success'
      if (percentage >= 60) return 'warning'
      return 'danger'
    },

    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initEnterpriseTypeChart()
        this.initEnterpriseScaleChart()
        this.initStudentCategoryChart()
        this.initJobCategoryChart()
        this.initYearTrendChart()
      })
    },

    // 初始化企业类型分布图表
    initEnterpriseTypeChart() {
      const chartDom = document.getElementById('enterpriseTypeChart')
      if (!chartDom) return
      
      this.charts.enterpriseType = echarts.init(chartDom)
      
      const typeData = this.categoryStats.enterpriseTypes || {}
      const data = Object.keys(typeData).map(key => ({
        name: this.getTypeName(key),
        value: typeData[key]
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: '企业类型',
          type: 'pie',
          radius: '70%',
          data: data,
          itemStyle: {
            borderRadius: 5,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c}'
          }
        }]
      }
      
      this.charts.enterpriseType.setOption(option)
    },

    // 初始化企业规模分布图表
    initEnterpriseScaleChart() {
      const chartDom = document.getElementById('enterpriseScaleChart')
      if (!chartDom) return
      
      this.charts.enterpriseScale = echarts.init(chartDom)
      
      const scaleData = this.categoryStats.enterpriseScales || {}
      const data = Object.keys(scaleData).map(key => ({
        name: this.getScaleName(key),
        value: scaleData[key]
      }))

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
          name: '企业规模',
          type: 'pie',
          radius: '70%',
          data: data,
          itemStyle: {
            borderRadius: 5,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: true,
            formatter: '{b}: {c}'
          }
        }]
      }
      
      this.charts.enterpriseScale.setOption(option)
    },

    // 初始化学生分类分布图表
    initStudentCategoryChart() {
      const chartDom = document.getElementById('studentCategoryChart')
      if (!chartDom) return
      
      this.charts.studentCategory = echarts.init(chartDom)
      
      const categoryData = this.categoryStats.studentCategories || {}
      const categories = Object.keys(categoryData)
      const values = categories.map(key => categoryData[key])

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: categories.map(key => this.getStudentCategoryName(key)),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '学生数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: '#67C23A'
          }
        }]
      }
      
      this.charts.studentCategory.setOption(option)
    },

    // 初始化招聘职位分类图表
    initJobCategoryChart() {
      const chartDom = document.getElementById('jobCategoryChart')
      if (!chartDom) return
      
      this.charts.jobCategory = echarts.init(chartDom)
      
      const jobData = this.categoryStats.jobCategories || {}
      const categories = Object.keys(jobData)
      const values = categories.map(key => jobData[key])

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: categories.map(key => this.getJobCategoryName(key)),
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '职位数量',
          type: 'bar',
          data: values,
          itemStyle: {
            color: '#409EFF'
          }
        }]
      }
      
      this.charts.jobCategory.setOption(option)
    },

    // 初始化年度统计趋势图表
    initYearTrendChart() {
      const chartDom = document.getElementById('yearTrendChart')
      if (!chartDom) return
      
      this.charts.yearTrend = echarts.init(chartDom)
      
      // 模拟年度趋势数据
      const years = ['2020', '2021', '2022', '2023', '2024']
      const enterpriseData = [12, 18, 25, 32, this.stats.totalEnterprises]
      const studentData = [150, 220, 350, 480, this.stats.totalStudents]
      const jobData = [45, 68, 95, 128, this.stats.totalJobs]

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['合作企业', '培养学生', '招聘信息']
        },
        xAxis: {
          type: 'category',
          data: years
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '合作企业',
            type: 'line',
            data: enterpriseData,
            smooth: true,
            itemStyle: { color: '#E6A23C' }
          },
          {
            name: '培养学生',
            type: 'line',
            data: studentData,
            smooth: true,
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '招聘信息',
            type: 'line',
            data: jobData,
            smooth: true,
            itemStyle: { color: '#409EFF' }
          }
        ]
      }
      
      this.charts.yearTrend.setOption(option)
    },

    // 获取类型中文名称
    getTypeName(type) {
      const typeMap = {
        'internet': '互联网',
        'finance': '金融',
        'manufacturing': '制造业',
        'education': '教育',
        'healthcare': '医疗',
        'other': '其他'
      }
      return typeMap[type] || type
    },

    // 获取规模中文名称
    getScaleName(scale) {
      const scaleMap = {
        'large': '大型企业',
        'medium': '中型企业',
        'small': '小型企业',
        'micro': '微型企业'
      }
      return scaleMap[scale] || scale
    },

    // 获取学生分类中文名称
    getStudentCategoryName(category) {
      const categoryMap = {
        'outstanding': '优秀毕业生',
        'scholarship': '奖学金获得者',
        'competition': '竞赛获奖者',
        'innovation': '创新创业',
        'academic': '学术研究',
        'other': '其他'
      }
      return categoryMap[category] || category
    },

    // 获取职位分类中文名称
    getJobCategoryName(category) {
      const categoryMap = {
        'development': '技术开发',
        'product': '产品管理',
        'design': '产品设计',
        'analysis': '数据分析',
        'operation': '运营管理',
        'hr': '人力资源',
        'marketing': '市场营销',
        'finance': '财务会计',
        'other': '其他'
      }
      return categoryMap[category] || category
    },

    // 刷新数据
    async refreshData() {
      await this.loadStatisticsData()
      this.initCharts()
    },

    // 处理窗口大小变化
    handleResize() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.stats-cards {
  .stat-card {
    display: flex;
    align-items: center;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    transition: all 0.3s;

    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 24px;
        color: #fff;
      }
    }

    .stat-content {
      flex: 1;
      
      .stat-number {
        font-size: 28px;
        font-weight: bold;
        line-height: 1;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }

    &.total-enterprises {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: #fff;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    &.total-students {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      color: #fff;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    &.total-jobs {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      color: #fff;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }

    &.cooperation-years {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      color: #fff;
      
      .stat-icon {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}

.chart-card {
  margin-bottom: 20px;
  
  ::v-deep .el-card__header {
    border-bottom: 1px solid #f0f2f5;
    font-weight: 600;
  }
}

.table-card {
  ::v-deep .el-card__header {
    border-bottom: 1px solid #f0f2f5;
    font-weight: 600;
  }
}
</style>