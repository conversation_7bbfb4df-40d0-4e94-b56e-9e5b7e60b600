<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()"
                     style="background-color: #67C23A;border-color:#67C23A">
            新增招聘信息
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="职位名称" align="center" width="200px">
        <template slot-scope="scope">
          <span>{{ scope.row.title }}</span>
          <div style="margin-top: 5px;">
            <el-tag v-if="scope.row.urgent" type="danger" size="mini">紧急</el-tag>
            <el-tag v-if="scope.row.hot" type="warning" size="mini" style="margin-left: 5px;">热门</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.companyName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="职位分类" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.categoryName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作地点" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.location }}</span>
        </template>
      </el-table-column>
      <el-table-column label="薪资" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.salary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作经验" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.experience }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学历要求" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.education }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发布时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.publishTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number"
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes"
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--新增/编辑弹窗-->
    <el-dialog :title="jobInfo.title"
               :visible.sync="jobInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="900px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="jobInfoForm"
                 :model="jobInfo.edit" :rules="jobInfo.formRules">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="职位名称:" prop="title">
                <el-input v-model.trim="jobInfo.edit.title" placeholder="请输入职位名称">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="公司名称:" prop="companyName">
                <el-input v-model.trim="jobInfo.edit.companyName" placeholder="请输入公司名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>


          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="职位分类:" prop="category">
                <el-select v-model="jobInfo.edit.category" @change="v=>EntityMethods().onCategoryChange(v)"
                           placeholder="请选择职位分类" style="width: 100%">
                  <el-option label="技术开发" value="development"></el-option>
                  <el-option label="产品管理" value="product"></el-option>
                  <el-option label="产品设计" value="design"></el-option>
                  <el-option label="数据分析" value="analysis"></el-option>
                  <el-option label="运营管理" value="operation"></el-option>
                  <el-option label="人力资源" value="hr"></el-option>
                  <el-option label="市场营销" value="marketing"></el-option>
                  <el-option label="财务会计" value="finance"></el-option>
                  <el-option label="其他" value="other"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工作地点:" prop="location">
                <el-input v-model.trim="jobInfo.edit.location" placeholder="请输入工作地点">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="薪资范围:" prop="salary">
                <el-input v-model.trim="jobInfo.edit.salary" placeholder="如：10-15K">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="工作经验:" prop="experience">
                <el-input v-model.trim="jobInfo.edit.experience" placeholder="如：1-3年">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="学历要求:" prop="education">
                <el-select v-model="jobInfo.edit.education" placeholder="请选择学历要求" style="width: 100%">
                  <el-option label="不限" value="不限"></el-option>
                  <el-option label="高中" value="高中"></el-option>
                  <el-option label="大专" value="大专"></el-option>
                  <el-option label="本科" value="本科"></el-option>
                  <el-option label="硕士" value="硕士"></el-option>
                  <el-option label="博士" value="博士"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工作性质:" prop="jobType">
                <el-select v-model="jobInfo.edit.jobType" placeholder="请选择工作性质" style="width: 100%">
                  <el-option label="全职" value="全职"></el-option>
                  <el-option label="兼职" value="兼职"></el-option>
                  <el-option label="实习" value="实习"></el-option>
                  <el-option label="临时" value="临时"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="职位描述:" prop="description">
            <el-input type="textarea" :rows="3" v-model.trim="jobInfo.edit.description"
                      placeholder="请输入职位简要描述">
            </el-input>
          </el-form-item>

          <el-form-item label="详细职位描述:" prop="fullDescription">
            <tinymce
              v-model="jobInfo.edit.fullDescription"
              :height="300"
              id="tinymce_fullDescription"
              ref="tinymce_fullDescription"
              @initEd="handleEditorInit()"
            />
          </el-form-item>

          <el-form-item label="技能标签:" prop="skills">
            <div class="skill-tags-container">
              <div class="tags-display">
                <el-tag
                  v-for="(tag, index) in jobInfo.edit.skillsArray"
                  :key="index"
                  closable
                  @close="EntityMethods().removeSkillTag(index)"
                  type="primary"
                  size="small"
                  style="margin: 2px;">
                  {{ tag }}
                </el-tag>
              </div>
              <div class="add-tag-input" v-if="jobInfo.edit.showAddSkillInput">
                <el-input
                  v-model.trim="jobInfo.edit.newSkillTag"
                  size="small"
                  style="width: 100px;"
                  @keyup.enter.native="EntityMethods().addSkillTag()"
                  @blur="EntityMethods().addSkillTag()"
                  placeholder="输入技能">
                </el-input>
              </div>
              <el-button v-else size="small" type="primary" plain @click="jobInfo.edit.showAddSkillInput = true">+ 添加技能标签</el-button>
            </div>
          </el-form-item>

          <el-form-item label="任职要求:" prop="requirements">
            <div class="requirements-container">
              <div class="requirements-list">
                <div v-for="(req, index) in jobInfo.edit.requirementsArray" :key="index" class="requirement-item">
                  <el-input
                    v-model="jobInfo.edit.requirementsArray[index]"
                    size="small"
                    placeholder="请输入任职要求">
                    <template slot="append">
                      <el-button @click="EntityMethods().removeRequirement(index)" icon="el-icon-delete" size="mini"></el-button>
                    </template>
                  </el-input>
                </div>
              </div>
              <el-button size="small" type="primary" plain @click="EntityMethods().addRequirement()" icon="el-icon-plus">添加要求</el-button>
            </div>
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="标记设置:">
                <el-checkbox v-model="jobInfo.edit.urgent">紧急招聘</el-checkbox>
                <el-checkbox v-model="jobInfo.edit.hot" style="margin-left: 15px;">热门职位</el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="发布时间:" prop="publishTime">
                <el-date-picker
                  v-model="jobInfo.edit.publishTime"
                  type="datetime"
                  placeholder="选择发布时间"
                  style="width: 100%">
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 联系信息 -->
          <el-divider content-position="left">联系信息</el-divider>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="联系人:" prop="contact">
                <el-input v-model.trim="jobInfo.edit.contact" placeholder="请输入联系人姓名">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话:" prop="phone">
                <el-input v-model.trim="jobInfo.edit.phone" placeholder="请输入联系电话">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系邮箱:" prop="email">
                <el-input v-model.trim="jobInfo.edit.email" placeholder="请输入联系邮箱">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="jobInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()"
                   v-if="jobInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="jobInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import Tinymce from "@/components/Tinymce";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {IndustryJobModel} from "@/model/IndustryModel";

export default {
  name: "industryJobManage",
  components: {ListSearchFilter, Tinymce},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  watch: {
    // 监听弹窗显示状态
    'jobInfo.dialog'(newVal) {
      if (newVal) {
        // 弹窗打开时，延迟初始化tinymce
        this.$nextTick(() => {
          setTimeout(() => {
            this.reinitializeTinymce()
          }, 100)
        })
      }
    }
  },
  data() {
    let $this = this;
    // 统一表单验证函数
    const validateTitle = (rule, value, callback) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入职位名称'))
        return
      }
      if (value.length > 50) {
        callback(new Error('最多输入50个字，当前已输入' + value.length + "个字"))
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,

      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {deleted: 0},
        sort: "publishTime",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '职位名称',
              key: 'title',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '公司名称',
              key: 'companyName',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '职位分类',
              key: 'category',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '技术开发', value: 'development'},
                {label: '产品管理', value: 'product'},
                {label: '产品设计', value: 'design'},
                {label: '数据分析', value: 'analysis'},
                {label: '运营管理', value: 'operation'},
                {label: '人力资源', value: 'hr'},
                {label: '市场营销', value: 'marketing'},
                {label: '财务会计', value: 'finance'},
                {label: '其他', value: 'other'}
              ]
            },

          ]
        }
      },

      // 招聘信息相关
      jobInfo: {
        dialog: false,
        title: "新增招聘信息",
        type: "add",
        filter: {},
        edit: {
          title: "",
          companyId: null,
          companyName: "",
          companyLogo: "",
          category: "",
          categoryName: "",
          location: "",
          salary: "",
          experience: "",
          education: "",
          jobType: "全职",
          description: "",
          fullDescription: "",
          skillsArray: [],
          requirementsArray: [],
          newSkillTag: "",
          showAddSkillInput: false,
          urgent: false,
          hot: false,
          publishTime: new Date(),
          contact: "",
          phone: "",
          email: ""
        },
        formRules: {
          'title': {
            required: true,
            validator: validateTitle,
            trigger: 'blur'
          },
          'companyName': {
            required: true,
            message: '请输入公司名称',
            trigger: 'blur'
          },
          'category': {
            required: true,
            message: '请选择职位分类',
            trigger: 'change'
          },
          'location': {
            required: true,
            message: '请输入工作地点',
            trigger: 'blur'
          },
          'salary': {
            required: true,
            message: '请输入薪资范围',
            trigger: 'blur'
          },
          'experience': {
            required: true,
            message: '请输入工作经验要求',
            trigger: 'blur'
          },
          'education': {
            required: true,
            message: '请选择学历要求',
            trigger: 'change'
          },
          'publishTime': {
            required: true,
            message: '请选择发布时间',
            trigger: 'change'
          }
        }
      }
    }
  },

  async mounted() {
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await IndustryJobModel.getPageList(page, size, $this.lists.sort, query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.jobInfo.dialog = true;
          $this.jobInfo.type = "add"
          $this.jobInfo.title = "新增招聘信息"
          $this.jobInfo.edit = {
            title: "",
            companyId: null,
            companyName: "",
            companyLogo: "",
            category: "",
            categoryName: "",
            location: "",
            salary: "",
            experience: "",
            education: "",
            jobType: "全职",
            description: "",
            fullDescription: "",
            skillsArray: [],
            requirementsArray: [],
            newSkillTag: "",
            showAddSkillInput: false,
            urgent: false,
            hot: false,
            publishTime: new Date(),
            contact: "",
            phone: "",
            email: ""
          };
          setTimeout(() => {
            $this.$refs['jobInfoForm'].clearValidate()
          }, 300)
        },

        // 编辑按钮点击
        async clickEditBtn(job) {
          job = JSON.parse(JSON.stringify(job))

          // 处理技能标签和任职要求的显示
          if (job.skills && Array.isArray(job.skills)) {
            job.skillsArray = [...job.skills]
          } else {
            job.skillsArray = []
          }
          if (job.requirements && Array.isArray(job.requirements)) {
            job.requirementsArray = [...job.requirements]
          } else {
            job.requirementsArray = []
          }

          // 处理联系信息
          if (job.contactInfo) {
            job.contact = job.contactInfo.contact || ''
            job.phone = job.contactInfo.phone || ''
            job.email = job.contactInfo.email || ''
          }

          job.newSkillTag = ''
          job.showAddSkillInput = false
          // 处理时间格式
          if (job.publishTime) {
            job.publishTime = new Date(job.publishTime)
          }

          $this.jobInfo.dialog = true;
          $this.$set($this.jobInfo, "edit", job);
          $this.jobInfo.type = "edit"
          $this.jobInfo.title = "编辑招聘信息"
          setTimeout(() => {
            $this.$refs['jobInfoForm'].clearValidate()
          }, 300)
        },

        // 删除按钮点击
        async clickDeleteBtn(job) {
          if (await msg_confirm('确认要删除该招聘信息吗？删除后无法恢复！')) {
            let result = await IndustryJobModel.deleteOne(job.jobId);
            if (result !== false) {
              msg_success("删除成功");
              this.getList(0, $this.lists.pages.size, $this.lists.query);
            } else {
              msg_err("删除失败");
            }
          }
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 职位分类变化
        onCategoryChange(value) {
          const categoryMap = {
            'development': '技术开发',
            'product': '产品管理',
            'design': '产品设计',
            'analysis': '数据分析',
            'operation': '运营管理',
            'hr': '人力资源',
            'marketing': '市场营销',
            'finance': '财务会计',
            'other': '其他'
          }
          $this.jobInfo.edit.categoryName = categoryMap[value] || ''
        },

        // 添加技能标签
        addSkillTag() {
          if ($this.jobInfo.edit.newSkillTag && $this.jobInfo.edit.newSkillTag.trim()) {
            const tag = $this.jobInfo.edit.newSkillTag.trim()
            if (!$this.jobInfo.edit.skillsArray.includes(tag)) {
              $this.jobInfo.edit.skillsArray.push(tag)
            }
            $this.jobInfo.edit.newSkillTag = ''
            $this.jobInfo.edit.showAddSkillInput = false
          } else {
            $this.jobInfo.edit.showAddSkillInput = false
          }
        },

        // 移除技能标签
        removeSkillTag(index) {
          $this.jobInfo.edit.skillsArray.splice(index, 1)
        },

        // 添加任职要求
        addRequirement() {
          $this.jobInfo.edit.requirementsArray.push('')
        },

        // 移除任职要求
        removeRequirement(index) {
          $this.jobInfo.edit.requirementsArray.splice(index, 1)
        },

        // 新增提交
        async clickAddBtn() {
          $this.$refs['jobInfoForm'].validate(async validate => {
            if (validate) {
              // 处理数据格式
              let submitData = JSON.parse(JSON.stringify($this.jobInfo.edit))

              // 处理技能标签
              submitData.skills = submitData.skillsArray.filter(s => s && s.trim())
              delete submitData.skillsArray
              delete submitData.newSkillTag
              delete submitData.showAddSkillInput

              // 处理任职要求
              submitData.requirements = submitData.requirementsArray.filter(s => s && s.trim())
              delete submitData.requirementsArray

              // 处理联系信息
              submitData.contactInfo = {
                contact: submitData.contact || '',
                phone: submitData.phone || '',
                email: submitData.email || ''
              }
              delete submitData.contact
              delete submitData.phone
              delete submitData.email

              // 处理发布时间
              if (submitData.publishTime) {
                submitData.publishTime = new Date(submitData.publishTime).getTime()
              }

              // 确认提交
              if (await msg_confirm('确认要新增该招聘信息吗？')) {
                let result = await IndustryJobModel.addOrEdit(submitData)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['jobInfoForm'].validate(async validate => {
            if (validate) {
              // 处理数据格式
              let submitData = JSON.parse(JSON.stringify($this.jobInfo.edit))

              // 处理技能标签
              submitData.skills = submitData.skillsArray.filter(s => s && s.trim())
              delete submitData.skillsArray
              delete submitData.newSkillTag
              delete submitData.showAddSkillInput

              // 处理任职要求
              submitData.requirements = submitData.requirementsArray.filter(s => s && s.trim())
              delete submitData.requirementsArray

              // 处理联系信息
              submitData.contactInfo = {
                contact: submitData.contact || '',
                phone: submitData.phone || '',
                email: submitData.email || ''
              }
              delete submitData.contact
              delete submitData.phone
              delete submitData.email

              // 处理发布时间
              if (submitData.publishTime) {
                submitData.publishTime = new Date(submitData.publishTime).getTime()
              }

              // 确认提交
              if (await msg_confirm('确认要编辑该招聘信息吗？')) {
                let result = await IndustryJobModel.addOrEdit(submitData)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.jobInfo.dialog = false
        }
      }
    },

    // 富文本编辑器初始化完成回调
    handleEditorInit() {
      if (this.$refs['tinymce_fullDescription']) {
        this.$refs['tinymce_fullDescription'].setContent(this.jobInfo.edit.fullDescription || '')
      }
    },

    // 重新初始化所有tinymce编辑器
    reinitializeTinymce() {
      const tinymceRefs = [
        'tinymce_fullDescription'
      ]

      tinymceRefs.forEach(refName => {
        const tinymceRef = this.$refs[refName]
        if (tinymceRef) {
          // 销毁现有编辑器
          try {
            tinymceRef.destroyTinymce()
          } catch (e) {
            console.log('销毁tinymce失败:', e)
          }

          // 重新初始化编辑器
          setTimeout(() => {
            try {
              tinymceRef.initTinymce()
            } catch (e) {
              console.log('重新初始化tinymce失败:', e)
            }
          }, 50)
        }
      })
    }
  },

  destroyed() {
    // 组件销毁时确保编辑器被正确销毁
    if (this.$refs.tinymce_fullDescription) {
      this.$refs.tinymce_fullDescription.destroyTinymce()
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}

.dialog-container {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.skill-tags-container {
  .tags-display {
    margin-bottom: 10px;
    min-height: 32px;

    .el-tag {
      margin: 2px 4px 2px 0;
    }
  }

  .add-tag-input {
    display: inline-block;
  }
}

.requirements-container {
  .requirements-list {
    .requirement-item {
      margin-bottom: 8px;

      .el-input-group__append {
        padding: 0 10px;
      }
    }
  }
}
</style>
