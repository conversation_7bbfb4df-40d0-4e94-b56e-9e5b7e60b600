<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()"
                     style="background-color: #67C23A;border-color:#67C23A">
            新增优秀学生
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="学生照片" align="center" width="100px">
        <template slot-scope="scope">
          <el-image
            style="width: 60px; height: 60px; border-radius: 50%;"
            :src="scope.row.photo"
            fit="cover"
            :preview-src-list="[scope.row.photo]">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="姓名" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="专业" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.major }}</span>
        </template>
      </el-table-column>
      <el-table-column label="毕业年份" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.graduationYear }}</span>
        </template>
      </el-table-column>
      <el-table-column label="就业公司" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.company }}</span>
        </template>
      </el-table-column>
      <el-table-column label="职位" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.position }}</span>
        </template>
      </el-table-column>
      <el-table-column label="分类" align="center" width="120px">
        <template slot-scope="scope">
          <el-tag :type="getCategoryTagType(scope.row.category)">
            {{ scope.row.categoryName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="项目数量" align="center" width="80px">
        <template slot-scope="scope">
          <span>{{ scope.row.projectCount }}个</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80px">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number"
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes"
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--新增/编辑弹窗-->
    <el-dialog :title="studentInfo.title"
               :visible.sync="studentInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="900px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="studentInfoForm"
                 :model="studentInfo.edit" :rules="studentInfo.formRules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="学生姓名:" prop="name">
                <el-input v-model.trim="studentInfo.edit.name" placeholder="请输入学生姓名">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="学生照片:" prop="photo">
                <erp-uploader-one-pic style="float:left;" :img-in="studentInfo.edit.photo"
                                      uploader-id="photo"
                                      uploader-title="" :uploader-size="[200,100]" :pixel-limit="[200,200]"
                                      :size-limit="1024"
                                      @uploadSuccess="data=>fileUpload(data,studentInfo.edit,'')"
                                      @afterDelete="data=>fileDelete(data,studentInfo.edit,'')"></erp-uploader-one-pic>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="专业:" prop="major">
                <el-input v-model.trim="studentInfo.edit.major" placeholder="请输入专业名称">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="毕业年份:" prop="graduationYear">
                <el-input v-model.trim="studentInfo.edit.graduationYear" placeholder="如：2023">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="就业公司:" prop="company">
                <el-input v-model.trim="studentInfo.edit.company" placeholder="请输入就业公司">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职位:" prop="position">
                <el-input v-model.trim="studentInfo.edit.position" placeholder="请输入职位名称">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="学生分类:" prop="category">
                <el-select v-model="studentInfo.edit.category" @change="v=>EntityMethods().onCategoryChange(v)"
                           placeholder="请选择学生分类" style="width: 100%">
                  <el-option label="优秀毕业生" value="outstanding"></el-option>
                  <el-option label="奖学金获得者" value="scholarship"></el-option>
                  <el-option label="竞赛获奖者" value="competition"></el-option>
                  <el-option label="创新创业" value="innovation"></el-option>
                  <el-option label="学术研究" value="academic"></el-option>
                  <el-option label="其他" value="other"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="项目数量:" prop="projectCount">
                <el-input-number v-model="studentInfo.edit.projectCount" :min="0" :max="99"
                                label="项目数量" style="width: 100%">
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="排序序号:" prop="sort">
                <el-input-number v-model="studentInfo.edit.sort" :min="1" :max="999"
                                label="排序序号" style="width: 100%">
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="个人格言:" prop="quote">
            <el-input v-model.trim="studentInfo.edit.quote" placeholder="请输入个人格言">
            </el-input>
          </el-form-item>

          <el-form-item label="个人故事:" prop="story">
            <tinymce
              v-model="studentInfo.edit.story"
              :height="300"
              id="tinymce_story"
              ref="tinymce_story"
              @initEd="handleStoryEditorInit()"
            />
          </el-form-item>

          <el-form-item label="技能标签:">
            <div style="margin-bottom: 10px;">
              <el-input v-model="newSkill" placeholder="请输入技能标签" style="width: 200px; margin-right: 10px;"
                        @keyup.enter.native="addSkill()" @blur="addSkill()">
              </el-input>
              <el-button type="primary" size="small" @click="addSkill()">添加</el-button>
            </div>
            <div>
              <el-tag v-for="(skill, index) in studentInfo.edit.skills" :key="index"
                      closable
                      @close="removeSkill(index)"
                      style="margin-right: 8px; margin-bottom: 8px;">
                {{ skill }}
              </el-tag>
              <div v-if="!studentInfo.edit.skills || studentInfo.edit.skills.length === 0"
                   style="color: #999; font-size: 12px; margin-top: 5px;">
                暂无技能标签，请在上方输入框中添加
              </div>
            </div>
          </el-form-item>

          <el-form-item label="成就列表:">
            <div style="margin-bottom: 10px;">
              <el-button type="primary" size="small" @click="addAchievement()">添加成就</el-button>
            </div>
            <div v-for="(achievement, index) in studentInfo.edit.achievements" :key="achievement.id"
                 style="border: 1px solid #dcdfe6; padding: 15px; margin-bottom: 10px; border-radius: 4px;">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="成就名称:" :prop="`achievements.${index}.title`">
                    <el-input v-model="achievement.title" placeholder="请输入成就名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="成就类型:" :prop="`achievements.${index}.type`">
                    <el-select v-model="achievement.type" placeholder="请选择类型" style="width: 100%;">
                      <el-option label="奖学金" value="scholarship"></el-option>
                      <el-option label="竞赛" value="competition"></el-option>
                      <el-option label="荣誉" value="honor"></el-option>
                      <el-option label="创新" value="innovation"></el-option>
                      <el-option label="学术" value="academic"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4" style="text-align: right; padding-top: 32px;">
                  <el-button type="danger" size="mini" @click="removeAchievement(index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
            <div v-if="!studentInfo.edit.achievements || studentInfo.edit.achievements.length === 0"
                 style="text-align: center; color: #999; padding: 20px; border: 1px dashed #dcdfe6; border-radius: 4px;">
              暂无成就记录，点击上方按钮添加
            </div>
          </el-form-item>

          <el-form-item label="项目经历:">
            <div style="margin-bottom: 10px;">
              <el-button type="primary" size="small" @click="addProject()">添加项目</el-button>
            </div>
            <div v-for="(project, index) in studentInfo.edit.projects" :key="project.id"
                 style="border: 1px solid #dcdfe6; padding: 15px; margin-bottom: 10px; border-radius: 4px;">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="项目名称:" :prop="`projects.${index}.name`">
                    <el-input v-model="project.name" placeholder="请输入项目名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="担任角色:" :prop="`projects.${index}.role`">
                    <el-input v-model="project.role" placeholder="请输入担任角色"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-form-item label="项目描述:" :prop="`projects.${index}.description`">
                <el-input type="textarea" :rows="2" v-model="project.description" placeholder="请输入项目描述"></el-input>
              </el-form-item>
              <el-row :gutter="20">
                <el-col :span="20">
                  <el-form-item label="技术栈:" :prop="`projects.${index}.techStr`">
                    <el-input v-model="project.techStr" placeholder="请输入技术栈，用逗号分隔，如：Java,Spring Boot,Vue.js"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="4" style="text-align: right; padding-top: 32px;">
                  <el-button type="danger" size="mini" @click="removeProject(index)">删除</el-button>
                </el-col>
              </el-row>
            </div>
            <div v-if="!studentInfo.edit.projects || studentInfo.edit.projects.length === 0"
                 style="text-align: center; color: #999; padding: 20px; border: 1px dashed #dcdfe6; border-radius: 4px;">
              暂无项目经历，点击上方按钮添加
            </div>
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="联系邮箱:" prop="email">
                <el-input v-model.trim="studentInfo.edit.email" placeholder="请输入联系邮箱">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系电话:" prop="phone">
                <el-input v-model.trim="studentInfo.edit.phone" placeholder="请输入联系电话">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="状态:" prop="status">
            <el-radio-group v-model="studentInfo.edit.status">
              <el-radio label="active">启用</el-radio>
              <el-radio label="inactive">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 照片预览 -->
          <el-form-item v-if="studentInfo.edit.photo" label="照片预览:">
            <el-image
              style="width: 100px; height: 100px; border-radius: 50%;"
              :src="studentInfo.edit.photo"
              fit="cover">
            </el-image>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="studentInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()"
                   v-if="studentInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="studentInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import Tinymce from "@/components/Tinymce";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {IndustryStudentModel} from "@/model/IndustryModel";

export default {
  name: "industryStudentManage",
  components: {ListSearchFilter, erpUploaderOnePic, Tinymce},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 统一表单验证函数
    const validateName = (rule, value, callback) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入学生姓名'))
        return
      }
      if (value.length > 20) {
        callback(new Error('最多输入20个字，当前已输入' + value.length + "个字"))
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,

      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "sort,createTime",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '学生姓名',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '专业',
              key: 'major',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '学生分类',
              key: 'category',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '优秀毕业生', value: 'outstanding'},
                {label: '奖学金获得者', value: 'scholarship'},
                {label: '竞赛获奖者', value: 'competition'},
                {label: '创新创业', value: 'innovation'},
                {label: '学术研究', value: 'academic'},
                {label: '其他', value: 'other'}
              ]
            },
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '启用', value: 'active'},
                {label: '禁用', value: 'inactive'}
              ]
            }
          ]
        }
      },

      // 学生信息相关
      studentInfo: {
        dialog: false,
        title: "新增优秀学生",
        type: "add",
        filter: {},
        edit: {
          name: "",
          photo: "",
          major: "",
          graduationYear: "",
          company: "",
          position: "",
          category: "",
          categoryName: "",
          projectCount: 0,
          sort: 1,
          quote: "",
          story: "",
          skillsStr: "",
          achievements: [],
          projects: [],
          email: "",
          phone: "",
          status: "active"
        },
        formRules: {
          'name': {
            required: true,
            validator: validateName,
            trigger: 'blur'
          },
          'major': {
            required: true,
            message: '请输入专业名称',
            trigger: 'blur'
          },
          'graduationYear': {
            required: true,
            message: '请输入毕业年份',
            trigger: 'blur'
          },
          'company': {
            required: true,
            message: '请输入就业公司',
            trigger: 'blur'
          },
          'position': {
            required: true,
            message: '请输入职位名称',
            trigger: 'blur'
          },
          'category': {
            required: true,
            message: '请选择学生分类',
            trigger: 'change'
          },
          'sort': {
            required: true,
            message: '请输入排序序号',
            trigger: 'blur'
          },
          'status': {
            required: true,
            message: '请选择状态',
            trigger: 'change'
          }
        }
      },
      // 新增技能标签输入框
      newSkill: ""
    }
  },

  watch: {
    // 监听弹窗显示状态
    'studentInfo.dialog'(newVal) {
      if (newVal) {
        // 弹窗打开时，延迟初始化tinymce
        this.$nextTick(() => {
          setTimeout(() => {
            this.reinitializeTinymce()
          }, 100)
        })
      }
    }
  },

  async mounted() {
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 获取分类标签类型
    getCategoryTagType(category) {
      const typeMap = {
        'outstanding': 'success',
        'scholarship': 'warning',
        'competition': 'danger',
        'innovation': 'primary',
        'academic': 'info',
        'other': ''
      }
      return typeMap[category] || ''
    },

    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await IndustryStudentModel.getPageList(page, size, $this.lists.sort, query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.studentInfo.dialog = true;
          $this.studentInfo.type = "add"
          $this.studentInfo.title = "新增优秀学生"
          $this.studentInfo.edit = {
            name: "",
            photo: "",
            major: "",
            graduationYear: "",
            company: "",
            position: "",
            category: "",
            categoryName: "",
            projectCount: 0,
            sort: 1,
            quote: "",
            story: "",
            skillsStr: "",
            achievements: [],
            projects: [],
            email: "",
            phone: "",
            status: "active"
          };
          // 清空技能输入框
          $this.newSkill = ''
          setTimeout(() => {
            $this.$refs['studentInfoForm'].clearValidate()
          }, 300)
        },

        // 编辑按钮点击
        async clickEditBtn(student) {
          student = JSON.parse(JSON.stringify(student))

          // 处理技能标签
          if (student.skills && Array.isArray(student.skills)) {
            student.skills = [...student.skills]
          } else {
            student.skills = []
          }
          // 处理成就列表
          if (student.achievements && Array.isArray(student.achievements)) {
            student.achievements = student.achievements.map(a => ({
              ...a,
              id: a.id || Date.now() + Math.random()
            }))
          } else {
            student.achievements = []
          }
          // 处理项目经历
          if (student.projects && Array.isArray(student.projects)) {
            student.projects = student.projects.map(p => ({
              ...p,
              id: p.id || Date.now() + Math.random(),
              techStr: p.tech ? p.tech.join(', ') : ''
            }))
          } else {
            student.projects = []
          }
          // 处理联系信息
          if (student.contact) {
            student.email = student.contact.email || ''
            student.phone = student.contact.phone || ''
          }

          $this.studentInfo.dialog = true;
          $this.$set($this.studentInfo, "edit", student);
          $this.studentInfo.type = "edit"
          $this.studentInfo.title = "编辑优秀学生"
          setTimeout(() => {
            $this.$refs['studentInfoForm'].clearValidate()
          }, 300)
        },

        // 删除按钮点击
        async clickDeleteBtn(student) {
          if (await msg_confirm('确认要删除该学生信息吗？删除后无法恢复！')) {
            let result = await IndustryStudentModel.deleteOne(student.studentId);
            if (result !== false) {
              msg_success("删除成功");
              this.getList(0, $this.lists.pages.size, $this.lists.query);
            } else {
              msg_err("删除失败");
            }
          }
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 学生分类变化
        onCategoryChange(value) {
          const categoryMap = {
            'outstanding': '优秀毕业生',
            'scholarship': '奖学金获得者',
            'competition': '竞赛获奖者',
            'innovation': '创新创业',
            'academic': '学术研究',
            'other': '其他'
          }
          $this.studentInfo.edit.categoryName = categoryMap[value] || ''
        },

        // 新增提交
        async clickAddBtn() {
          $this.$refs['studentInfoForm'].validate(async validate => {
            if (validate) {
              // 处理数据格式
              let submitData = JSON.parse(JSON.stringify($this.studentInfo.edit))

              // 技能标签不需要额外处理，直接使用数组

              // 处理成就列表
              if (submitData.achievements && Array.isArray(submitData.achievements)) {
                submitData.achievements = submitData.achievements.filter(a => a.title && a.title.trim())
              }

              // 处理项目经历
              if (submitData.projects && Array.isArray(submitData.projects)) {
                submitData.projects = submitData.projects
                  .filter(p => p.name && p.name.trim())
                  .map(p => ({
                    ...p,
                    tech: p.techStr ? p.techStr.split(',').map(t => t.trim()).filter(t => t) : []
                  }))
                // 删除techStr字段
                submitData.projects.forEach(p => delete p.techStr)
              }

              // 处理联系信息
              submitData.contact = {
                email: submitData.email || '',
                phone: submitData.phone || ''
              }
              delete submitData.email
              delete submitData.phone

              // 重名检查
              let listResult = await IndustryStudentModel.getList({
                name: submitData.name,

              })
              if (listResult.length > 0) {
                msg_err("已存在同名学生，请修改后再试！")
                return
              }

              // 确认提交
              if (await msg_confirm('确认要新增该学生吗？')) {
                let result = await IndustryStudentModel.addOrEdit(submitData)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['studentInfoForm'].validate(async validate => {
            if (validate) {
              // 处理数据格式
              let submitData = JSON.parse(JSON.stringify($this.studentInfo.edit))

              // 技能标签不需要额外处理，直接使用数组

              // 处理成就列表
              if (submitData.achievements && Array.isArray(submitData.achievements)) {
                submitData.achievements = submitData.achievements.filter(a => a.title && a.title.trim())
              }

              // 处理项目经历
              if (submitData.projects && Array.isArray(submitData.projects)) {
                submitData.projects = submitData.projects
                  .filter(p => p.name && p.name.trim())
                  .map(p => ({
                    ...p,
                    tech: p.techStr ? p.techStr.split(',').map(t => t.trim()).filter(t => t) : []
                  }))
                // 删除techStr字段
                submitData.projects.forEach(p => delete p.techStr)
              }

              // 处理联系信息
              submitData.contact = {
                email: submitData.email || '',
                phone: submitData.phone || ''
              }
              delete submitData.email
              delete submitData.phone

              // 重名检查（排除自身）
              let listResult = await IndustryStudentModel.getList({
                name: submitData.name,
                studentId: {"$ne": submitData.studentId},

              })
              if (listResult.length > 0) {
                msg_err("已存在同名学生，请修改后再试！")
                return
              }

              // 确认提交
              if (await msg_confirm('确认要编辑该学生吗？')) {
                let result = await IndustryStudentModel.addOrEdit(submitData)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.studentInfo.dialog = false
        }
      }
    },
    // 添加成就
    addAchievement() {
      this.studentInfo.edit.achievements.push({
        id: Date.now() + Math.random(),
        title: '',
        type: 'honor'
      })
    },
    // 删除成就
    removeAchievement(index) {
      this.studentInfo.edit.achievements.splice(index, 1)
    },
    // 添加项目
    addProject() {
      this.studentInfo.edit.projects.push({
        id: Date.now() + Math.random(),
        name: '',
        description: '',
        role: '',
        techStr: '',
        tech: []
      })
    },
    // 删除项目
    removeProject(index) {
      this.studentInfo.edit.projects.splice(index, 1)
    },
    // 添加技能标签
    addSkill() {
      const skill = this.newSkill.trim()
      if (skill && !this.studentInfo.edit.skills.includes(skill)) {
        this.studentInfo.edit.skills.push(skill)
        this.newSkill = ''
      }
    },
    // 删除技能标签
    removeSkill(index) {
      this.studentInfo.edit.skills.splice(index, 1)
    },
    // 处理个人故事编辑器初始化
    handleStoryEditorInit() {
      this.$refs['tinymce_story'].setContent(this.studentInfo.edit.story || '')
    },
    // 重新初始化所有tinymce编辑器
    reinitializeTinymce() {
      const tinymceRefs = [
        'tinymce_story'
      ]

      tinymceRefs.forEach(refName => {
        const tinymceRef = this.$refs[refName]
        if (tinymceRef) {
          // 销毁现有编辑器
          try {
            tinymceRef.destroyTinymce()
          } catch (e) {
            console.log('销毁tinymce失败:', e)
          }

          // 重新初始化编辑器
          setTimeout(() => {
            try {
              tinymceRef.initTinymce()
            } catch (e) {
              console.log('重新初始化tinymce失败:', e)
            }
          }, 50)
        }
      })
    },
    // 文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}

.dialog-container {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style>
