<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()"
                     style="background-color: #67C23A;border-color:#67C23A">
            新增企业
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="企业Logo" align="center" width="100px">
        <template slot-scope="scope">
          <el-image
            style="width: 60px; height: 60px"
            :src="scope.row.logo"
            fit="cover"
            :preview-src-list="[scope.row.logo]">
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="企业名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column label="企业类型" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.typeName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="企业规模" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.scaleName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="所在地区" align="center" width="100px">
        <template slot-scope="scope">
          <span>{{ scope.row.location }}</span>
        </template>
      </el-table-column>
      <el-table-column label="合作年限" align="center" width="80px">
        <template slot-scope="scope">
          <span>{{ scope.row.cooperationYears }}年</span>
        </template>
      </el-table-column>
      <el-table-column label="培养学生" align="center" width="80px">
        <template slot-scope="scope">
          <span>{{ scope.row.studentCount }}人</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80px">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number"
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes"
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--新增/编辑弹窗-->
    <el-dialog :title="enterpriseInfo.title"
               :visible.sync="enterpriseInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="1200px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="enterpriseInfoForm"
                 :model="enterpriseInfo.edit" :rules="enterpriseInfo.formRules">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="企业名称:" prop="name">
                <el-input v-model.trim="enterpriseInfo.edit.name" placeholder="请输入企业名称">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业Logo:" prop="logo">
                <erp-uploader-one-pic style="float:left;" :img-in="enterpriseInfo.edit.logo"
                                  uploader-id="logo"
                                  uploader-title="" :uploader-size="[120,120]" :pixel-limit="[550,550]"
                                  :size-limit="1024"
                                  @uploadSuccess="data=>fileUpload(data,enterpriseInfo.edit,'')"
                                  @afterDelete="data=>fileDelete(data,enterpriseInfo.edit,'')"></erp-uploader-one-pic>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="企业类型:" prop="type">
                <el-select v-model="enterpriseInfo.edit.type" @change="v=>EntityMethods().onTypeChange(v)"
                           placeholder="请选择企业类型" style="width: 100%">
                  <el-option label="互联网" value="internet"></el-option>
                  <el-option label="金融" value="finance"></el-option>
                  <el-option label="制造业" value="manufacturing"></el-option>
                  <el-option label="教育" value="education"></el-option>
                  <el-option label="医疗" value="healthcare"></el-option>
                  <el-option label="其他" value="other"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="企业规模:" prop="scale">
                <el-select v-model="enterpriseInfo.edit.scale" @change="v=>EntityMethods().onScaleChange(v)"
                           placeholder="请选择企业规模" style="width: 100%">
                  <el-option label="大型企业" value="large"></el-option>
                  <el-option label="中型企业" value="medium"></el-option>
                  <el-option label="小型企业" value="small"></el-option>
                  <el-option label="微型企业" value="micro"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="所在地区:" prop="location">
                <el-input v-model.trim="enterpriseInfo.edit.location" placeholder="请输入所在地区">
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="成立年份:" prop="foundYear">
                <el-input v-model.trim="enterpriseInfo.edit.foundYear" placeholder="请输入成立年份">
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="合作项目数量:" prop="projectCount">
                <el-input-number v-model="enterpriseInfo.edit.projectCount" :min="0"
                                label="合作项目数量" style="width: 100%">
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="合作年限:" prop="cooperationYears">
                <el-input-number v-model="enterpriseInfo.edit.cooperationYears" :min="0" :max="50"
                                 label="合作年限" style="width: 100%">
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="培养学生数:" prop="studentCount">
                <el-input-number v-model="enterpriseInfo.edit.studentCount" :min="0" :max="9999"
                                label="培养学生数" style="width: 100%">
                </el-input-number>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="企业简介:" prop="description">
            <el-input type="textarea" :rows="3" v-model.trim="enterpriseInfo.edit.description"
                      placeholder="请输入企业简介">
            </el-input>
          </el-form-item>

          <el-form-item label="合作详情:" prop="cooperationDetail">
            <tinymce
              v-model="enterpriseInfo.edit.cooperationDetail"
              :height="300"
              id="tinymce_cooperationDetail"
              ref="tinymce_cooperationDetail"
              @initEd="EntityMethods().initEditor()"
            />
          </el-form-item>

          <el-form-item label="详细地址:" prop="address">
            <el-input v-model.trim="enterpriseInfo.edit.address" placeholder="请输入详细地址">
            </el-input>
          </el-form-item>

          <el-form-item label="企业官网:" prop="website">
            <el-input v-model.trim="enterpriseInfo.edit.website" placeholder="请输入企业官网地址">
            </el-input>
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="排序序号:" prop="sort">
                <el-input-number v-model="enterpriseInfo.edit.sort" :min="1" :max="999"
                                label="排序序号" style="width: 100%">
                </el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="状态:" prop="status">
                <el-radio-group v-model="enterpriseInfo.edit.status">
                  <el-radio label="active">启用</el-radio>
                  <el-radio label="inactive">禁用</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="enterpriseInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()"
                   v-if="enterpriseInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="enterpriseInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {IndustryEnterpriseModel} from "@/model/IndustryModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";
import Tinymce from "@/components/Tinymce";
import {setText} from "echarts/lib/util/graphic";

export default {
  name: "industryEnterpriseManage",
  components: {ListSearchFilter, erpUploaderOnePic, Tinymce},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 统一表单验证函数
    const validateName = (rule, value, callback) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入企业名称'))
        return
      }
      if (value.length > 100) {
        callback(new Error('最多输入100个字，当前已输入' + value.length + "个字"))
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,

      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {},
        sort: "sort,createTime",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '企业名称',
              key: 'name',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '企业类型',
              key: 'type',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '互联网', value: 'internet'},
                {label: '金融', value: 'finance'},
                {label: '制造业', value: 'manufacturing'},
                {label: '教育', value: 'education'},
                {label: '医疗', value: 'healthcare'},
                {label: '其他', value: 'other'}
              ]
            },
            {
              type: 'select',
              label: '企业规模',
              key: 'scale',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '大型企业', value: 'large'},
                {label: '中型企业', value: 'medium'},
                {label: '小型企业', value: 'small'},
                {label: '微型企业', value: 'micro'}
              ]
            },
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '启用', value: 'active'},
                {label: '禁用', value: 'inactive'}
              ]
            }
          ]
        }
      },

      // 企业信息相关
      enterpriseInfo: {
        dialog: false,
        title: "新增企业",
        type: "add",
        filter: {},
        edit: {
          name: "",
          logo: "",
          type: "",
          typeName: "",
          scale: "",
          scaleName: "",
          location: "",
          foundYear: "",
          description: "",
          address: "",
          website: "",
          cooperationYears: 0,
          projectCount:0,
          studentCount: 0,
          cooperationDetail: "",
          sort: 1,
          status: "active"
        },
        formRules: {
          'name': {
            required: true,
            validator: validateName,
            trigger: 'blur'
          },
          'type': {
            required: true,
            message: '请选择企业类型',
            trigger: 'change'
          },
          'scale': {
            required: true,
            message: '请选择企业规模',
            trigger: 'change'
          },
          'location': {
            required: true,
            message: '请输入所在地区',
            trigger: 'blur'
          },
          'sort': {
            required: true,
            message: '请输入排序序号',
            trigger: 'blur'
          },
          'status': {
            required: true,
            message: '请选择状态',
            trigger: 'change'
          }
        }
      }
    }
  },
  watch: {
    // 监听弹窗显示状态，确保tinymce重新初始化
    'enterpriseInfo.dialog'(newVal) {
      if (newVal) {
        // 弹窗打开时，延迟一定时间确保DOM渲染完成后重新初始化tinymce
        this.$nextTick(() => {
          setTimeout(() => {
            // 强制重新初始化所有tinymce编辑器
            this.reinitializeTinymce()
          }, 100)
        })
      }
    }
  },
  async mounted() {
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 重新初始化所有tinymce编辑器
    reinitializeTinymce() {
      const tinymceRefs = [
        'tinymce_cooperationDetail',
      ]

      tinymceRefs.forEach(refName => {
        const tinymceRef = this.$refs[refName]
        if (tinymceRef) {
          // 销毁现有编辑器
          try {
            tinymceRef.destroyTinymce()
          } catch (e) {
            console.log('销毁tinymce失败:', e)
          }

          // 重新初始化编辑器
          setTimeout(() => {
            try {
              tinymceRef.initTinymce()
            } catch (e) {
              console.log('重新初始化tinymce失败:', e)
            }
          }, 50)
        }
      })
    },
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await IndustryEnterpriseModel.getPageList(page, size, $this.lists.sort, query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.enterpriseInfo.dialog = true;
          $this.enterpriseInfo.type = "add"
          $this.enterpriseInfo.title = "新增企业"
          $this.enterpriseInfo.edit = {
            name: "",
            logo: "",
            type: "",
            typeName: "",
            scale: "",
            scaleName: "",
            location: "",
            foundYear: "",
            description: "",
            address: "",
            website: "",
            cooperationYears: 0,
            studentCount: 0,
            cooperationDetail: "",
            sort: 1,
            status: "active"
          };
          setTimeout(() => {
            $this.$refs['enterpriseInfoForm'].clearValidate()

          }, 500);
        },

        // 编辑按钮点击
        async clickEditBtn(enterprise) {
          enterprise = JSON.parse(JSON.stringify(enterprise))
          $this.enterpriseInfo.dialog = true;
          $this.$set($this.enterpriseInfo, "edit", enterprise);
          $this.enterpriseInfo.type = "edit"
          $this.enterpriseInfo.title = "编辑企业"
          setTimeout(() => {
            $this.$refs['enterpriseInfoForm'].clearValidate()
          }, 500)
        },

        // 删除按钮点击
        async clickDeleteBtn(enterprise) {
          if (await msg_confirm('确认要删除该企业吗？删除后无法恢复！')) {
            let result = await IndustryEnterpriseModel.deleteOne(enterprise.enterpriseId);
            if (result !== false) {
              msg_success("删除成功");
              this.getList(0, $this.lists.pages.size, $this.lists.query);
            } else {
              msg_err("删除失败");
            }
          }
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 初始化成功了tinymce编辑器
        initEditor(){
          $this.$refs['tinymce_cooperationDetail'].setContent($this.enterpriseInfo.edit.cooperationDetail || '')
        },
        // 企业类型变化
        onTypeChange(value) {
          const typeMap = {
            'internet': '互联网',
            'finance': '金融',
            'manufacturing': '制造业',
            'education': '教育',
            'healthcare': '医疗',
            'other': '其他'
          }
          $this.enterpriseInfo.edit.typeName = typeMap[value] || ''
        },

        // 企业规模变化
        onScaleChange(value) {
          const scaleMap = {
            'large': '大型企业',
            'medium': '中型企业',
            'small': '小型企业',
            'micro': '微型企业'
          }
          $this.enterpriseInfo.edit.scaleName = scaleMap[value] || ''
        },

        // 新增提交
        async clickAddBtn() {
          $this.$refs['enterpriseInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查
              let listResult = await IndustryEnterpriseModel.getList({
                name: $this.enterpriseInfo.edit.name,
                deleted: 0
              })
              if (listResult.length > 0) {
                msg_err("已存在同名企业，请修改后再试！")
                return
              }
              // 确认提交
              if (await msg_confirm('确认要新增该企业吗？')) {
                let result = await IndustryEnterpriseModel.addOrEdit($this.enterpriseInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['enterpriseInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查（排除自身）
              let listResult = await IndustryEnterpriseModel.getList({
                name: $this.enterpriseInfo.edit.name,
                enterpriseId: {"$ne": $this.enterpriseInfo.edit.enterpriseId},
                deleted: 0
              })
              if (listResult.length > 0) {
                msg_err("已存在同名企业，请修改后再试！")
                return
              }
              // 确认提交
              if (await msg_confirm('确认要编辑该企业吗？')) {
                let result = await IndustryEnterpriseModel.addOrEdit($this.enterpriseInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.enterpriseInfo.dialog = false
        }
      }
    },

        // 文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // 文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },

  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}

.dialog-container {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
</style>
