<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success" 
                     @click="ListMethods().clickAddBtn()" 
                     style="background-color: #67C23A;border-color:#67C23A">
            新增轮播图
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading" 
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column label="轮播图" align="center" width="100px">
        <template slot-scope="scope">
          <el-image
            style="width: 80px; height: 40px"
            :src="scope.row.image"
            fit="cover"
            :preview-src-list="[scope.row.image]">
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="标题" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column label="描述" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.description }}</span>
        </template>
      </el-table-column>
      <el-table-column label="链接" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.link }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" width="80px">
        <template slot-scope="scope">
          <span>{{ scope.row.sort }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" width="80px">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
            {{ scope.row.status === 'active' ? '启用' : '禁用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" width="140px">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="200"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑
          </el-button>
          <el-button type="danger" size="mini" round
                     @click="ListMethods().clickDeleteBtn(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background 
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number" 
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes" 
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--新增/编辑弹窗-->
    <el-dialog :title="bannerInfo.title"
               :visible.sync="bannerInfo.dialog"
               :close-on-click-modal="false"
               :append-to-body="true"
               width="800px"
               center
               v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="120px" ref="bannerInfoForm" 
                 :model="bannerInfo.edit" :rules="bannerInfo.formRules">
          <el-form-item label="轮播图标题:" prop="title">
            <el-input v-model.trim="bannerInfo.edit.title" placeholder="请输入轮播图标题">
              <div slot="suffix" v-if="bannerInfo.edit.title">
                <span class="el-input__count">
                  <span class="el-input__count-inner">
                    {{ bannerInfo.edit.title.length }} / 50
                  </span>
                </span>
              </div>
            </el-input>
          </el-form-item>
          <el-form-item label="轮播图描述:" prop="description">
            <el-input type="textarea" :rows="3" v-model.trim="bannerInfo.edit.description" 
                      placeholder="请输入轮播图描述">
            </el-input>
          </el-form-item>
          <el-form-item label="轮播图片:" prop="image">
            <erp-uploader-one-pic style="float:left;" :img-in="bannerInfo.edit.image"
                                  uploader-id="image"
                                  uploader-title="" :uploader-size="[200,100]" :pixel-limit="[1920,500]"
                                  :size-limit="2048"
                                  @uploadSuccess="data=>fileUpload(data,bannerInfo.edit,'')"
                                  @afterDelete="data=>fileDelete(data,bannerInfo.edit,'')"></erp-uploader-one-pic>
          </el-form-item>
          <el-form-item label="跳转链接:" prop="link">
            <el-input v-model.trim="bannerInfo.edit.link" placeholder="请输入跳转链接">
            </el-input>
          </el-form-item>
          <el-form-item label="排序序号:" prop="sort">
            <el-input-number v-model="bannerInfo.edit.sort" :min="1" :max="999" label="排序序号">
            </el-input-number>
            <span style="margin-left: 10px; color: #999; font-size: 12px;">数字越小排序越靠前</span>
          </el-form-item>
          <el-form-item label="状态:" prop="status">
            <el-radio-group v-model="bannerInfo.edit.status">
              <el-radio label="active">启用</el-radio>
              <el-radio label="inactive">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="default" @click="bannerInfo.dialog=false">取 消</el-button>
        <el-button type="success" @click="EntityMethods().clickAddBtn()" 
                   v-if="bannerInfo.type==='add'">提 交</el-button>
        <el-button type="success" @click="EntityMethods().clickEditBtn()"
                   v-if="bannerInfo.type==='edit'">提 交</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {IndustryBannerModel} from "@/model/IndustryModel";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";

export default {
  name: "industryBannerManage",
  components: {ListSearchFilter, erpUploaderOnePic},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    let $this = this;
    // 统一表单验证函数
    const validateTitle = (rule, value, callback) => {
      if (!(value === 0 || value)) {
        callback(new Error('请输入轮播图标题'))
        return
      }
      if (value.length > 50) {
        callback(new Error('最多输入50个字，当前已输入' + value.length + "个字"))
      }
      callback()
    }

    const validateDescription = (rule, value, callback) => {
      if (value && value.length > 200) {
        callback(new Error('最多输入200个字，当前已输入' + value.length + "个字"))
      }
      callback()
    }

    const validateImage = (rule, value, callback) => {
      if (!(value === 0 || value)) {
        callback(new Error('请上传轮播图片'))
        return
      }
      callback()
    }

    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,
      
      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {deleted: 0},
        sort: "sort,createTime",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '标题',
              key: 'title',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '启用', value: 'active'},
                {label: '禁用', value: 'inactive'}
              ]
            }
          ]
        }
      },

      // 轮播图信息相关
      bannerInfo: {
        dialog: false,
        title: "新增轮播图",
        type: "add",
        filter: {},
        edit: {
          title: "",
          description: "",
          image: "",
          link: "",
          sort: 1,
          status: "active"
        },
        formRules: {
          'title': {
            required: true, 
            validator: validateTitle, 
            trigger: 'blur'
          },
          'description': {
            validator: validateDescription, 
            trigger: 'blur'
          },
          'image': {
            required: true, 
            validator: validateImage, 
            trigger: 'blur'
          },
          'sort': {
            required: true, 
            message: '请输入排序序号', 
            trigger: 'blur'
          },
          'status': {
            required: true, 
            message: '请选择状态', 
            trigger: 'change'
          }
        }
      }
    }
  },

  async mounted() {
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // ERP通用-文件上传成功
    fileUpload(params, target, formRef) {
      let imgSrc = params[1] // 成功后文件地址
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, imgSrc)
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    // ERP通用-文件删除
    fileDelete(params, target, formRef) {
      let uploaderId = params[0] // 上传id
      this.$set(target, uploaderId, "")
      if (formRef) {
        this.$refs[formRef].validate()
      }
    },
    
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await IndustryBannerModel.getPageList(page, size, $this.lists.sort, query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.bannerInfo.dialog = true;
          $this.bannerInfo.type = "add"
          $this.bannerInfo.title = "新增轮播图"
          $this.bannerInfo.edit = {
            title: "",
            description: "",
            image: "",
            link: "",
            sort: 1,
            status: "active"
          };
          setTimeout(() => {
            $this.$refs['bannerInfoForm'].clearValidate()
          }, 300)
        },

        // 编辑按钮点击
        async clickEditBtn(banner) {
          banner = JSON.parse(JSON.stringify(banner))
          $this.bannerInfo.dialog = true;
          $this.$set($this.bannerInfo, "edit", banner);
          $this.bannerInfo.type = "edit"
          $this.bannerInfo.title = "编辑轮播图"
          setTimeout(() => {
            $this.$refs['bannerInfoForm'].clearValidate()
          }, 300)
        },

        // 删除按钮点击
        async clickDeleteBtn(banner) {
          if (await msg_confirm('确认要删除该轮播图吗？删除后无法恢复！')) {
            let result = await IndustryBannerModel.deleteOne(banner.bannerId);
            if (result !== false) {
              msg_success("删除成功");
              this.getList(0, $this.lists.pages.size, $this.lists.query);
            } else {
              msg_err("删除失败");
            }
          }
        }
      }
    },

    // 实体操作方法集合
    EntityMethods() {
      let $this = this;
      return {
        // 新增提交
        async clickAddBtn() {
          $this.$refs['bannerInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查
              let listResult = await IndustryBannerModel.getList({
                title: $this.bannerInfo.edit.title,
                deleted: 0
              })
              if (listResult.length > 0) {
                msg_err("已存在同名轮播图，请修改后再试！")
                return
              }
              // 确认提交
              if (await msg_confirm('确认要新增该轮播图吗？')) {
                let result = await IndustryBannerModel.addOrEdit($this.bannerInfo.edit)
                if (result.code === "000000") {
                  msg_success("新增成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 编辑提交
        async clickEditBtn() {
          $this.$refs['bannerInfoForm'].validate(async validate => {
            if (validate) {
              // 重名检查（排除自身）
              let listResult = await IndustryBannerModel.getList({
                title: $this.bannerInfo.edit.title,
                bannerId: {"$ne": $this.bannerInfo.edit.bannerId},
                deleted: 0
              })
              if (listResult.length > 0) {
                msg_err("已存在同名轮播图，请修改后再试！")
                return
              }
              // 确认提交
              if (await msg_confirm('确认要编辑该轮播图吗？')) {
                let result = await IndustryBannerModel.addOrEdit($this.bannerInfo.edit)
                if (result.code === "000000") {
                  msg_success("修改成功")
                  this.clickCancelBtn()
                  $this.ListMethods().getList(0, $this.lists.pages.size, $this.lists.query)
                }
              }
            }
          });
        },

        // 取消操作
        clickCancelBtn() {
          $this.bannerInfo.dialog = false
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}

.dialog-container {
  .el-form-item {
    margin-bottom: 20px;
  }
}
</style>