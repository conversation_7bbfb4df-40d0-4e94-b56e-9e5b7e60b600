<template>
  <div class="page-help content-container flex flex-between" style="align-items: start">
    <div class="nav-box">
      <div class="li active">常见问题</div>
    </div>
    <div class="content-box">
      <el-page-header @back="clickNavBackBtn" content="有关账号信息：如何获取账号？忘记用户名/密码怎么办？" class="nav-bar" v-show="showBox!==0">
        <template #title>
          返回
        </template>
      </el-page-header>
      <!--显示首页-->
      <div class="show-box main" v-show="showBox===0">
        <div class="title">
          <font-awesome-icon :icon="['fas', 'question-circle']" class="title-icon" />
          常见问题
        </div>
        <div class="des">
          <p>亲爱的老师、同学，欢迎来到“虚拟仿真实验平台”帮助中心！</p>
          <p>系统使用中的常见问题请参考下面列表，如有其他问题请联系相关负责老师。</p>
        </div>
        <el-divider></el-divider>
        <div class="question-list">
          <div class="title">
            <font-awesome-icon :icon="['fas', 'user-graduate']" class="section-icon" />
            学生用户：
          </div>
          <div class="list">
            <div class="li" v-for="item in questionList.student" @click="clickOneQuestion(item)">
              <font-awesome-icon :icon="['fas', 'angle-right']" class="item-icon" />
              {{ item.name }}
            </div>
          </div>
          <el-divider></el-divider>
          <div class="title">
            <font-awesome-icon :icon="['fas', 'chalkboard-teacher']" class="section-icon" />
            教师用户：
          </div>
          <div class="list">
            <div class="li" v-for="item in questionList.teacher" @click="clickOneQuestion(item)">
              <font-awesome-icon :icon="['fas', 'angle-right']" class="item-icon" />
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="show-box" v-show="showBox===1">
        <div class="des" v-html="questionInfo.content">

        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {HelpQuestionModel} from "../model/HelpQuestionModel";

export default {
  name: "Help",
  data() {
    return {
      showBox: 0,
      // 问题列表
      questionList: {
        student: [],
        teacher: []
      },
      // 问题详情
      questionInfo: {}
    }
  },
  mounted() {
    this.getStudentQuestionList()
    this.getTeacherQuestionList()
  },
  methods: {
    // 获取学生问题列表
    async getStudentQuestionList() {
      let list = await HelpQuestionModel.getList({
        type: "学生"
      });
      this.$set(this.questionList, "student", list)
    },
    // 获取教师问题列表
    async getTeacherQuestionList() {
      let list = await HelpQuestionModel.getList({
        type: "教师"
      });
      this.$set(this.questionList, "teacher", list)
    },
    // 点击某个问题
    clickOneQuestion(item) {
      this.questionInfo = item;
      this.showBox = 1;
    },
    // 回任务列表
    clickNavBackBtn() {
      this.showBox = 0;
    }
  }
}
</script>

<style scoped lang="less">
@import '../style/app.less';

.page-help {
  margin-top: 20px;
  margin-bottom: 20px;

  .nav-box {
    background-color: #fff;

    .li {
      padding: 10px 40px;
      font-size: 15px;
      cursor: pointer;
      border-right: 3px solid #fff;

      &:hover, &.active {
        color: #4093f9;
        font-weight: bold;
        border-color: #4093f9;
      }
    }
  }

  .content-box {
    width: 1020px;
    background-color: #fff;
    padding: 20px;

    .nav-bar {
      margin-bottom: 30px;
    }

    .show-box {
      font-size: 14px;

      &.main {
        .title {
          font-weight: bold;
          font-size: 15px;
        }

        .des {
          color: #666;
        }

        .question-list {
          .title {
            margin-bottom: 15px;
          }

          .li {
            cursor: pointer;
            padding-left: 20px;
            color: #4093f9;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }
}

// 图标样式
.title-icon {
  margin-right: 8px;
  color: #4093f9;
  font-size: 0.9em;
}

.section-icon {
  margin-right: 8px;
  color: #4093f9;
  font-size: 0.9em;
}

.item-icon {
  margin-right: 8px;
  color: #4093f9;
  font-size: 0.8em;
}

.back-icon {
  margin-right: 8px;
  font-size: 0.9em;
}
</style>
</style>
