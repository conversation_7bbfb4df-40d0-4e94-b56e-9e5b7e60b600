<template>
  <div class="app-container">
    <div class="info-container">
      <div class="info-card">
        <div class="card-header">
          <div class="user-avatar">
            <i class="el-icon-user"></i>
          </div>
          <div class="user-info">
            <h2 class="user-name">{{ user.name }}</h2>
            <p class="user-role">{{ user.roles | rolesNameFilter }}</p>
          </div>
        </div>
        
        <div class="card-content">
          <div class="info-item">
            <div class="info-label">
              <i class="el-icon-user-solid"></i>
              <span>账号</span>
            </div>
            <div class="info-value">{{ user.userName }}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">
              <i class="el-icon-postcard"></i>
              <span>姓名</span>
            </div>
            <div class="info-value">{{ user.name }}</div>
          </div>
          
          <div class="info-item">
            <div class="info-label">
              <i class="el-icon-s-custom"></i>
              <span>角色</span>
            </div>
            <div class="info-value">{{ user.roles | rolesNameFilter }}</div>
          </div>
        </div>
        
        <div class="card-footer">
          <el-button 
            type="primary" 
            size="medium" 
            @click="password.dialog=true"
            class="password-btn">
            <i class="el-icon-key"></i>
            修改密码
          </el-button>
        </div>
      </div>
    </div>
    
    <!--修改密码弹窗-->
    <el-dialog
      title="修改密码"
      :visible.sync="password.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      width="500px"
      center
      v-el-drag-dialog
      custom-class="password-dialog">
      <div class="dialog-container">
        <el-form 
          label-width="120px" 
          ref="passwordForm" 
          :model="password.edit" 
          :rules="password.formRules"
          class="password-form">
          <el-form-item label="新密码" prop="password1">
            <el-input 
              v-model.trim="password.edit.password1" 
              type="password" 
              placeholder="请输入至少6位密码"
              show-password>
              <i slot="prefix" class="el-icon-lock"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="确认新密码" prop="password2">
            <el-input 
              v-model="password.edit.password2" 
              type="password" 
              placeholder="请再次输入新密码"
              show-password>
              <i slot="prefix" class="el-icon-lock"></i>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
         <el-button @click="password.dialog=false">取 消</el-button>
         <el-button type="primary" @click="clickChangePasswordBtn()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {mapGetters, mapState} from 'vuex'
import enums from '@/enums/index'
import {dateFormat, rolesNameFilter} from "@/filters";
import {validateMaxLength} from "@/utils/validate";
import {msg_err, msg_success} from "@/utils/ele_component";
import {AdminUserModel} from "@/model/AdminUserModel";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";

export default {
  name: 'info',
  computed: {
    ...mapGetters([
      'user'
    ]),
    ...mapState({
      adminUserId: state => state.user.id,
    })
  },
  directives: {
    elDragDialog, permission
  },
  filters: {dateFormat, rolesNameFilter},
  data() {
    return {
      enums: enums,
      password: {
        dialog: false,
        userInfo: {},
        edit: {
          password1: "",
          password2: ""
        },
        // 输入检测
        formRules: {
          'password1': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
          'password2': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
        },
      }
    }
  },
  mounted() {

  },
  methods: {
    // 点击修改密码弹窗的提交按钮
    clickChangePasswordBtn() {
      this.$refs['passwordForm'].validate(async validate => {
        if (validate) {
          if (this.password.edit.password1 !== this.password.edit.password2) {
            msg_err("两次密码输入不一样！")
            return
          }
          let result = await AdminUserModel.changePassword(this.password.edit.password1, this.user["roles"][0])
          if (result.code === "000000") {
            msg_success("修改密码成功")
            this.password.dialog = false
          }
        }
      });
    }
  }
}
</script>

<style scoped lang="scss">
.info-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 20px;
}

.info-card {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  color: white;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.user-avatar {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  
  i {
    font-size: 36px;
    color: white;
  }
}

.user-info {
  flex: 1;
  
  .user-name {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .user-role {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
    font-weight: 300;
  }
}

.card-content {
  margin-bottom: 30px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  transition: background 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.15);
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.info-label {
  display: flex;
  align-items: center;
  min-width: 80px;
  font-weight: 500;
  
  i {
    margin-right: 8px;
    font-size: 18px;
  }
  
  span {
    font-size: 16px;
  }
}

.info-value {
  flex: 1;
  font-size: 16px;
  font-weight: 400;
  text-align: right;
}

.card-footer {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.password-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px 30px;
  font-size: 16px;
  border-radius: 25px;
  transition: all 0.3s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
  }
  
  i {
    margin-right: 8px;
  }
}

// 弹窗样式
.password-dialog {
  border-radius: 15px;
  overflow: hidden;
}

.dialog-container {
  padding: 20px 0;
}

.password-form {
  .el-form-item {
    margin-bottom: 25px;
  }
  
  .el-form-item__label {
    font-weight: 500;
    color: #333;
  }
  
  .el-input {
    .el-input__inner {
      border-radius: 8px;
      border: 2px solid #e0e0e0;
      padding-left: 40px;
      transition: all 0.3s ease;
      
      &:focus {
        border-color: #2196F3;
        box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
      }
    }
    
    .el-input__prefix {
      left: 12px;
      
      i {
        color: #999;
        font-size: 18px;
      }
    }
  }
}

.dialog-footer {
  .el-button {
    padding: 10px 25px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .info-container {
    padding: 15px;
  }
  
  .info-card {
    padding: 25px;
    border-radius: 15px;
  }
  
  .card-header {
    flex-direction: column;
    text-align: center;
    
    .user-avatar {
      margin-right: 0;
      margin-bottom: 15px;
    }
  }
  
  .info-item {
    flex-direction: column;
    text-align: center;
    
    .info-label {
      margin-bottom: 8px;
      min-width: auto;
    }
    
    .info-value {
      text-align: center;
    }
  }
  
  .password-dialog {
    width: 90% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .info-card {
    padding: 20px;
  }
  
  .user-avatar {
    width: 60px;
    height: 60px;
    
    i {
      font-size: 28px;
    }
  }
  
  .user-name {
    font-size: 24px;
  }
  
  .password-form {
    .el-form-item__label {
      float: none;
      display: block;
      text-align: center;
      margin-bottom: 8px;
    }
  }
}
</style>
