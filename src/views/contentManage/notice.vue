<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()"
                     style="background-color: #67C23A;border-color:#67C23A"
                     >
            新增通知公告
          </el-button>
          <el-button class="el-button" type="info"
                     @click="ListMethods().clickStatisticsBtn()"
                     >
            统计信息
          </el-button>
          <el-button class="el-button" type="warning"
                     @click="ListMethods().clickProcessExpiredBtn()"
                     >
            处理过期通知
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column label="标题" min-width="200" align="left">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center;">
            <el-tag v-if="scope.row.isTop" type="warning" size="mini" style="margin-right: 5px;">置顶</el-tag>
            <el-tag v-if="scope.row.urgency === 'high'" type="danger" size="mini" style="margin-right: 5px;">紧急</el-tag>
            <span style="cursor: pointer; color: #409EFF;" @click="ListMethods().clickViewBtn(scope.row)">
              {{ scope.row.title }}
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="类型" width="120" align="center">
        <template slot-scope="scope">
          <el-tag :type="getTypeTagType(scope.row.type)" size="small">
            {{ NoticeModel.formatType(scope.row.type) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)" size="small">
            {{ NoticeModel.formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="紧急程度" width="100" align="center">
        <template slot-scope="scope">
          <el-tag
            :type="getUrgencyTagType(scope.row.urgency)"
            size="small">
            {{ NoticeModel.formatUrgency(scope.row.urgency) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="浏览量" width="100" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.views || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="发布者" width="120" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.author || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="发布时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.publishTime | dateFormat }}</span>
        </template>
      </el-table-column>

      <el-table-column label="过期时间" width="160" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.expireTime"
                :class="{'expired-time': isExpired(scope.row.expireTime)}">
            {{ scope.row.expireTime | dateFormat }}
          </span>
          <span v-else style="color: #909399;">无期限</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="280" fixed="right"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)">编辑</el-button>

          <el-dropdown @command="(command) => ListMethods().handleDropdownCommand(command, scope.row)"
                       trigger="click">
            <el-button type="info" size="mini" round>
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="`view-${scope.row.noticeId}`">查看详情</el-dropdown-item>
              <el-dropdown-item :command="`publish-${scope.row.noticeId}`"
                                v-if="scope.row.status === 'draft'">发布</el-dropdown-item>
              <el-dropdown-item :command="`unpublish-${scope.row.noticeId}`"
                                v-if="scope.row.status === 'active'">撤回</el-dropdown-item>
              <el-dropdown-item :command="`setTop-${scope.row.noticeId}-${!scope.row.isTop}`">
                {{ scope.row.isTop ? '取消置顶' : '设为置顶' }}
              </el-dropdown-item>
              <el-dropdown-item :command="`extend-${scope.row.noticeId}`"
                                v-if="scope.row.expireTime && isExpired(scope.row.expireTime)">
                延期
              </el-dropdown-item>
              <el-dropdown-item :command="`delete-${scope.row.noticeId}`"
                                style="color: #F56C6C;">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number"
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes"
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--统计信息弹窗-->
    <el-dialog title="通知公告统计信息"
               :visible.sync="statisticsDialog.visible"
               width="600px"
               center>
      <div class="statistics-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.totalCount }}</div>
              <div class="stat-label">总数量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.topCount }}</div>
              <div class="stat-label">置顶通知</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.urgentCount }}</div>
              <div class="stat-label">紧急通知</div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 10px;">
          <el-col :span="12">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.expiringCount }}</div>
              <div class="stat-label">即将过期</div>
            </div>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <h4>类型统计</h4>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(count, type) in statisticsDialog.data.typeStats" :key="type">
            <div class="stat-item">
              <div class="stat-number">{{ count }}</div>
              <div class="stat-label">{{ NoticeModel.formatType(type) }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="statisticsDialog.visible=false">关 闭</el-button>
      </span>
    </el-dialog>

    <!--通知详情查看弹窗-->
    <el-dialog :title="noticeDetail.title"
               :visible.sync="noticeDetail.visible"
               width="80%"
               center>
      <div class="notice-detail-container" v-if="noticeDetail.data">
        <div class="notice-meta">
          <el-row>
            <el-col :span="12">
              <p><strong>类型：</strong>{{ NoticeModel.formatType(noticeDetail.data.type) }}</p>
              <p><strong>发布者：</strong>{{ noticeDetail.data.author }}</p>
              <p><strong>状态：</strong>{{ NoticeModel.formatStatus(noticeDetail.data.status) }}</p>
              <p><strong>紧急程度：</strong>{{ NoticeModel.formatUrgency(noticeDetail.data.urgency) }}</p>
            </el-col>
            <el-col :span="12">
              <p><strong>浏览量：</strong>{{ noticeDetail.data.views }}</p>
              <p><strong>发布时间：</strong>{{ noticeDetail.data.publishTime | dateFormat }}</p>
              <p><strong>过期时间：</strong>{{ noticeDetail.data.expireTime ? (noticeDetail.data.expireTime | dateFormat) : '无期限' }}</p>
              <p><strong>创建时间：</strong>{{ noticeDetail.data.createTime | dateFormat }}</p>
            </el-col>
          </el-row>
        </div>
        <el-divider></el-divider>
        <div class="notice-summary">
          <h4>摘要</h4>
          <p>{{ noticeDetail.data.summary }}</p>
        </div>
        <el-divider></el-divider>
        <div class="notice-content">
          <h4>内容</h4>
          <div v-html="noticeDetail.data.content"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="noticeDetail.visible=false">关 闭</el-button>
        <el-button type="primary" @click="ListMethods().clickEditBtn(noticeDetail.data)">编 辑</el-button>
      </span>
    </el-dialog>

    <!--延期弹窗-->
    <el-dialog title="延期通知"
               :visible.sync="extendDialog.visible"
               width="400px"
               center>
      <el-form :model="extendDialog.form" label-width="100px">
        <el-form-item label="新过期时间:">
          <el-date-picker
            v-model="extendDialog.form.expireTime"
            type="datetime"
            placeholder="选择新的过期时间"
            style="width: 100%;"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="timestamp">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="extendDialog.visible=false">取 消</el-button>
        <el-button type="primary" @click="confirmExtend">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {NoticeModel} from "@/model/NoticeModel";

export default {
  name: "noticeManage",
  components: {ListSearchFilter},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,
      NoticeModel: NoticeModel,

      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {
          deleted: 0
        },
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '标题',
              key: 'title',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '发布者',
              key: 'author',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '类型',
              key: 'type',
              value: '',
              data: [
                {label: '全部', value: ''}
              ]
            },
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                ...NoticeModel.getStatusOptions()
              ]
            },
            {
              type: 'select',
              label: '紧急程度',
              key: 'urgency',
              value: '',
              data: [
                {label: '全部', value: ''},
                ...NoticeModel.getUrgencyOptions()
              ]
            },
            {
              type: 'select',
              label: '置顶',
              key: 'isTop',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '是', value: true},
                {label: '否', value: false}
              ]
            }
          ]
        }
      },

      // 统计信息弹窗
      statisticsDialog: {
        visible: false,
        data: {
          totalCount: 0,
          topCount: 0,
          urgentCount: 0,
          typeStats: {},
          expiringCount: 0
        }
      },

      // 通知详情查看
      noticeDetail: {
        visible: false,
        title: '',
        data: null
      },

      // 延期弹窗
      extendDialog: {
        visible: false,
        noticeId: '',
        form: {
          expireTime: null
        }
      }
    }
  },

  async mounted() {
    // 加载类型配置
    await this.loadTypeOptions()
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 加载类型配置
    async loadTypeOptions() {
      try {
        const typeOptions = await NoticeModel.getTypeOptions()
        const typeFilter = this.lists.searchFilter.filter.find(item => item.key === 'type')
        if (typeFilter) {
          typeFilter.data = [
            {label: '全部', value: ''},
            ...typeOptions
          ]
        }
      } catch (error) {
        console.error('加载通知类型配置失败:', error)
      }
    },
    
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await NoticeModel.getPageList(page, size, "publishTime,desc", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.$router.push('/content/noticeEdit?type=add')
        },

        // 编辑按钮点击
        async clickEditBtn(notice) {
          $this.$router.push(`/content/noticeEdit?type=edit&id=${notice.noticeId}`)
        },

        // 查看按钮点击
        async clickViewBtn(notice) {
          $this.noticeDetail.data = notice
          $this.noticeDetail.title = notice.title
          $this.noticeDetail.visible = true
          // 增加浏览量
          NoticeModel.increaseViews(notice.noticeId)
        },

        // 统计信息按钮
        async clickStatisticsBtn() {
          $this.statisticsDialog.data = await NoticeModel.getStatistics()
          $this.statisticsDialog.visible = true
        },

        // 处理过期通知按钮
        async clickProcessExpiredBtn() {
          if (await msg_confirm('确认要处理所有过期通知吗？过期通知状态将更新为"已过期"。')) {
            const processedCount = await NoticeModel.processExpiredNotices()
            if (processedCount > 0) {
              this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
            }
          }
        },

        // 下拉菜单命令处理
        async handleDropdownCommand(command, row) {
          const [action, id, param] = command.split('-')

          switch (action) {
            case 'view':
              this.clickViewBtn(row)
              break
            case 'publish':
              if (await msg_confirm('确认要发布该通知吗？')) {
                if (await NoticeModel.publishNotice(id)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'unpublish':
              if (await msg_confirm('确认要撤回该通知吗？')) {
                if (await NoticeModel.unpublishNotice(id)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'setTop':
              const isTop = param === 'true'
              if (await msg_confirm(`确认要${isTop ? '设为置顶' : '取消置顶'}吗？`)) {
                if (await NoticeModel.setTopNotice(id, isTop)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'extend':
              $this.extendDialog.noticeId = id
              $this.extendDialog.form.expireTime = Date.now() + 30 * 24 * 60 * 60 * 1000 // 默认延期30天
              $this.extendDialog.visible = true
              break
            case 'delete':
              if (await msg_confirm('确认要删除该通知吗？此操作不可恢复！')) {
                if (await NoticeModel.deleteOne({noticeId: id})) {
                  msg_success("删除成功")
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                } else {
                  msg_err("删除失败")
                }
              }
              break
          }
        }
      }
    },

    // 确认延期
    async confirmExtend() {
      if (!this.extendDialog.form.expireTime) {
        msg_err("请选择新的过期时间")
        return
      }

      // 这里需要调用延期接口，由于后端没有专门的延期接口，
      // 可以通过编辑接口来更新过期时间
      try {
        const noticeData = await NoticeModel.getOne(this.extendDialog.noticeId)
        if (noticeData) {
          noticeData.expireTime = this.extendDialog.form.expireTime
          const result = await NoticeModel.addOrEdit(noticeData)
          if (result.code === "000000") {
            msg_success("延期成功")
            this.extendDialog.visible = false
            this.ListMethods().getList(this.lists.pages.number - 1, this.lists.pages.size, this.lists.query)
          } else {
            msg_err("延期失败")
          }
        }
      } catch (error) {
        msg_err("延期失败：" + error.message)
      }
    },

    // 判断是否过期
    isExpired(expireTime) {
      return expireTime && expireTime < Date.now()
    },

    // 获取类型标签类型
    getTypeTagType(type) {
      const typeMap = {
        'important': 'danger',
        'teaching': 'success',
        'system': 'info',
        'activity': 'warning'
      }
      return typeMap[type] || ''
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'active': 'success',
        'expired': 'warning',
        'archived': 'danger'
      }
      return typeMap[status] || ''
    },

    // 获取紧急程度标签类型
    getUrgencyTagType(urgency) {
      const typeMap = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      }
      return typeMap[urgency] || ''
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}

.statistics-container {
  .stat-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 10px;

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }

    .stat-label {
      color: #606266;
      font-size: 14px;
    }
  }
}

.notice-detail-container {
  .notice-meta {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;

    p {
      margin: 5px 0;
      line-height: 1.6;
    }
  }

  .notice-summary {
    p {
      line-height: 1.8;
      color: #606266;
    }
  }

  .notice-content {
    max-height: 400px;
    overflow-y: auto;

    ::v-deep img {
      max-width: 100%;
      height: auto;
    }
  }
}

.expired-time {
  color: #F56C6C;
  font-weight: bold;
}
</style>
