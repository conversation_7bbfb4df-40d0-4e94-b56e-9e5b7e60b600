<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-page-header @back="goBack" :content="pageTitle">
      </el-page-header>
    </div>

    <!-- 编辑表单 -->
    <el-card style="margin-top: 20px;">
      <el-form ref="noticeForm" :model="noticeForm" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="通知标题:" prop="title">
              <el-input v-model.trim="noticeForm.title" placeholder="请输入通知标题">
                <div slot="suffix" v-if="noticeForm.title">
                  <span class="el-input__count">
                    <span class="el-input__count-inner">
                      {{ noticeForm.title.length }} / 100
                    </span>
                  </span>
                </div>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="通知类型:" prop="type">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-select v-model="noticeForm.type" placeholder="请选择通知类型" style="flex: 1;">
                  <el-option
                    v-for="item in typeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-setting"
                  @click="openTypeDialog"
                  title="编辑类型">
                  编辑
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="发布者:" prop="author">
              <el-input v-model.trim="noticeForm.author" placeholder="请输入发布者姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态:" prop="status">
              <el-select v-model="noticeForm.status" placeholder="请选择状态" style="width: 100%;">
                <el-option
                  v-for="item in NoticeModel.getStatusOptions()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="紧急程度:" prop="urgency">
              <el-select v-model="noticeForm.urgency" placeholder="请选择紧急程度" style="width: 100%;">
                <el-option
                  v-for="item in NoticeModel.getUrgencyOptions()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="通知级别:">
              <el-select v-model="noticeForm.noticeLevel" placeholder="请选择通知级别" style="width: 100%;">
                <el-option
                  v-for="item in NoticeModel.getNoticeLevelOptions()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="目标范围:">
              <el-select v-model="noticeForm.targetScope" placeholder="请选择目标范围" style="width: 100%;">
                <el-option
                  v-for="item in NoticeModel.getTargetScopeOptions()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="置顶通知:">
              <el-switch v-model="noticeForm.isTop"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需要确认阅读:">
              <el-switch v-model="noticeForm.needConfirm"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="设置过期时间:">
              <el-switch v-model="noticeForm.hasExpire"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="noticeForm.hasExpire">
          <el-col :span="12">
            <el-form-item label="过期时间:">
              <el-date-picker
                v-model="expireTimeTemp"
                type="datetime"
                placeholder="选择过期时间"
                style="width: 100%;"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="timestamp">
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发布时间:" v-if="noticeForm.status === 'active'">
              <el-date-picker
                v-model="publishTimeTemp"
                type="datetime"
                placeholder="选择发布时间"
                style="width: 100%;"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="timestamp">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="封面图片:">
          <erp-uploader-one-pic
            :img-in="noticeForm.coverImage"
            uploader-id="coverImage"
            uploader-title="封面图片"
            :uploader-size="[200,100]"
            :pixel-limit="[800,400]"
            :size-limit="2048"
            @uploadSuccess="data=>handleCoverUpload(data)"
            @afterDelete="data=>handleCoverDelete(data)">
          </erp-uploader-one-pic>
          <div class="upload-tip" style="margin-top: 10px;">
            <p>建议尺寸：800×400px，支持jpg、png格式，最大2MB（可选）</p>
          </div>
        </el-form-item>

        <el-form-item label="通知摘要:" prop="summary">
          <el-input
            type="textarea"
            v-model="noticeForm.summary"
            :rows="4"
            placeholder="请输入通知摘要，建议150字以内"
            maxlength="300"
            show-word-limit>
          </el-input>
        </el-form-item>

        <el-form-item label="通知内容:" prop="content">
          <tinymce
            id="tinymce_noticeContent"
            ref="tinymce_noticeContent"
            v-model="noticeForm.content"
            :height="400"
          />
        </el-form-item>

        <el-form-item label="附件上传:">
          <el-upload
            class="attachment-upload"
            :http-request="handleCustomUpload"
            :file-list="attachmentList"
            :on-remove="handleAttachmentRemove"
            multiple>
            <el-button size="small" type="primary">点击上传附件</el-button>
            <div slot="tip" class="el-upload__tip">支持上传任意格式文件</div>
          </el-upload>
        </el-form-item>

        <el-form-item label="发送方式:" v-if="false">
          <el-checkbox-group v-model="sendMethods">
            <el-checkbox label="web">网站通知</el-checkbox>
            <el-checkbox label="email">邮件通知</el-checkbox>
            <el-checkbox label="sms">短信通知</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <div class="button-group">
            <el-button @click="goBack">取 消</el-button>
            <el-button type="info" @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
            <el-button type="success" @click="saveAndPublish" :loading="saving">保存并发布</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 类型编辑弹窗 -->
    <el-dialog
      title="编辑通知类型"
      :visible.sync="typeDialog.visible"
      width="800px"
      :close-on-click-modal="false">
      <div class="type-editor">
        <div class="type-list">
          <el-table :data="typeDialog.types" style="width: 100%" size="small">
            <el-table-column prop="label" label="类型名称" width="200">
              <template slot-scope="scope">
                <el-input v-if="scope.row.editing" v-model="scope.row.label" size="small"></el-input>
                <span v-else>{{ scope.row.label }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="类型值" width="150">
              <template slot-scope="scope">
                <el-input v-if="scope.row.editing" v-model="scope.row.value" size="small"></el-input>
                <span v-else>{{ scope.row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250">
              <template slot-scope="scope">
                <el-button v-if="scope.row.editing" size="mini" type="success" @click="saveType(scope.$index)">保存</el-button>
                <el-button v-if="scope.row.editing" size="mini" @click="cancelEditType(scope.$index)">取消</el-button>
                <el-button v-if="!scope.row.editing" size="mini" @click="editType(scope.$index)">编辑</el-button>
                <el-button size="mini" type="danger" @click="removeType(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="add-type" style="margin-top: 20px;">
          <el-button type="primary" @click="addType">添加类型</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="typeDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="saveTypeConfig" :loading="typeDialog.saving">保存配置</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {NoticeModel} from "@/model/NoticeModel";
import {ConfigModel} from "@/model/ConfigModel";
import {FileModel} from "@/model/FileModel";
import {msg_success, msg_err, msg_confirm} from "@/utils/ele_component";
import {getToken} from "@/utils/auth";
import {API_URL, FILE_URL} from "@/config/main";
import Tinymce from "@/components/Tinymce";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";

export default {
  name: "noticeEdit",
  components: {Tinymce,erpUploaderOnePic},
  data() {
    return {
      NoticeModel: NoticeModel,
      pageTitle: "新增通知公告",
      editType: "add", // add 或 edit
      noticeId: null,
      saving: false,

      // 类型选项
      typeOptions: [],

      // 类型编辑对话框
      typeDialog: {
        visible: false,
        types: [],
        saving: false
      },

      // 表单数据
      noticeForm: {
        title: "",
        type: "",
        author: "",
        status: "draft",
        summary: "",
        content: "",
        coverImage: "",
        urgency: "medium",
        noticeLevel: "normal",
        targetScope: "all",
        isTop: false,
        needConfirm: false,
        hasExpire: false,
        sendMethod: "web"
      },

      // 时间临时变量
      publishTimeTemp: null,
      expireTimeTemp: null,

      // 附件列表
      attachmentList: [],

      // 发送方式
      sendMethods: ['web'],

      // 富文本编辑器



      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入通知标题', trigger: 'blur' },
          { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择通知类型', trigger: 'change' }
        ],
        author: [
          { required: true, message: '请输入发布者姓名', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        urgency: [
          { required: true, message: '请选择紧急程度', trigger: 'change' }
        ],
        summary: [
          { required: true, message: '请输入通知摘要', trigger: 'blur' },
          { max: 300, message: '摘要长度不能超过300个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入通知内容', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    // 加载类型选项
    await this.loadTypeOptions()

    // 获取路由参数
    this.editType = this.$route.query.type || 'add'
    this.noticeId = this.$route.query.id

    if (this.editType === 'edit' && this.noticeId) {
      this.pageTitle = "编辑通知公告"
      await this.loadNoticeData()
    }

    // 初始化富文本编辑器
    this.$nextTick(() => {
      this.initTinymceEditor()
    })
  },

  beforeDestroy() {
    // 销毁编辑器
    if (this.tinymceEditor) {
      this.tinymceEditor.destroy()
    }
  },

  methods: {
    // 加载类型选项
    async loadTypeOptions() {
      try {
        this.typeOptions = await NoticeModel.getTypeOptions()
      } catch (error) {
        console.error('加载通知类型配置失败:', error)
        this.typeOptions = []
      }
    },

    // 打开类型编辑对话框
    openTypeDialog() {
      this.typeDialog.types = JSON.parse(JSON.stringify(this.typeOptions))
      this.typeDialog.visible = true
    },

    // 编辑类型
    editType(index) {
      this.$set(this.typeDialog.types[index], 'editing', true)
      this.$set(this.typeDialog.types[index], 'originalLabel', this.typeDialog.types[index].label)
      this.$set(this.typeDialog.types[index], 'originalValue', this.typeDialog.types[index].value)
    },

    // 保存单个类型编辑
    saveType(index) {
      const type = this.typeDialog.types[index]
      if (!type.label || !type.value) {
        msg_err('请填写完整的类型名称和值')
        return
      }
      this.$set(this.typeDialog.types[index], 'editing', false)
      this.$delete(this.typeDialog.types[index], 'originalLabel')
      this.$delete(this.typeDialog.types[index], 'originalValue')
    },

    // 取消编辑类型
    cancelEditType(index) {
      const type = this.typeDialog.types[index]
      type.label = type.originalLabel
      type.value = type.originalValue
      this.$set(this.typeDialog.types[index], 'editing', false)
      this.$delete(this.typeDialog.types[index], 'originalLabel')
      this.$delete(this.typeDialog.types[index], 'originalValue')
    },

    // 添加类型
    addType() {
      this.typeDialog.types.push({
        label: '',
        value: '',
        editing: true
      })
    },

    // 删除类型
    removeType(index) {
      this.typeDialog.types.splice(index, 1)
    },

    // 保存类型配置
    async saveTypeConfig() {
      // 验证所有类型是否填写完整
      for (const type of this.typeDialog.types) {
        if (!type.label || !type.value) {
          msg_err('请填写完整的类型名称和值')
          return
        }
      }

      this.typeDialog.saving = true
      try {
        const config = {
          types: JSON.stringify(this.typeDialog.types)
        }

        const result = await ConfigModel.editConfig("noticeConfig", config)
        if (result) {
          msg_success('类型配置保存成功')
          this.typeOptions = JSON.parse(JSON.stringify(this.typeDialog.types))
          this.typeDialog.visible = false
        } else {
          msg_err(result?.message || '类型配置保存失败')
        }
      } catch (error) {
        msg_err('类型配置保存失败：' + error.message)
      } finally {
        this.typeDialog.saving = false
      }
    },

    // 加载通知数据
    async loadNoticeData() {
      const noticeData = await NoticeModel.getOne(this.noticeId)
      if (noticeData) {
        this.noticeForm = {
          ...noticeData,
          hasExpire: !!noticeData.expireTime
        }
        this.publishTimeTemp = noticeData.publishTime
        this.expireTimeTemp = noticeData.expireTime
        // 处理附件
        if (noticeData.attachments) {
          try {
            const attachments = noticeData.attachments || []
            this.attachmentList = attachments.map(item => ({
              ...item,
              uid: Date.now() + Math.random(),
              status: 'success'
            }))
          } catch (e) {
            this.attachmentList = []
          }
        }

        // 处理发送方式
        if (noticeData.sendMethod) {
          this.sendMethods = noticeData.sendMethod.split(',')
        }

        // 设置编辑器内容
        if (this.tinymceEditor) {
          this.tinymceEditor.setContent(noticeData.content || '')
        }
      } else {
        msg_err("加载通知数据失败")
        this.goBack()
      }
    },

    // 初始化富文本编辑器
    initTinymceEditor() {
      // 这里需要集成 TinyMCE 编辑器
      // 由于项目可能已经有编辑器配置，这里使用简化版本
      const textarea = document.createElement('textarea')
      textarea.style.width = '100%'
      textarea.style.height = '400px'
      textarea.value = this.noticeForm.content
      textarea.addEventListener('input', (e) => {
        this.noticeForm.content = e.target.value
      })

      const container = document.getElementById('tinymceEditor')
      container.appendChild(textarea)

      this.tinymceEditor = {
        setContent: (content) => {
          textarea.value = content
          this.noticeForm.content = content
        },
        getContent: () => textarea.value,
        destroy: () => {
          if (container.contains(textarea)) {
            container.removeChild(textarea)
          }
        }
      }
    },

    // 封面图片上传成功（使用 erp-uploader-one-pic 组件的回调）
    handleCoverUpload(data) {
      let imgSrc = data[1] // 成功后文件地址
      let uploaderId = data[0] // 上传id
      this.noticeForm.coverImage = imgSrc
      msg_success('封面图片上传成功')
    },

    // 封面图片删除（使用 erp-uploader-one-pic 组件的回调）
    handleCoverDelete(data) {
      let uploaderId = data[0] // 上传id
      this.noticeForm.coverImage = ""
      msg_success('封面图片已删除')
    },

    // 自定义附件上传
    async handleCustomUpload(options) {
      try {
        const file = options.file
        const result = await FileModel.uploadOne(file)

        if (result) {
          const fileUrl = FILE_URL + result.location

          // 添加到附件列表
          this.attachmentList.push({
            name: result.name,
            url: fileUrl,
            uid: file.uid,
            status: 'success',
            fileId: result.fileId,
            size: result.byteSize
          })

          msg_success('附件上传成功')
        } else {
          msg_err('附件上传失败')
        }
      } catch (error) {
        msg_err('附件上传失败：' + error.message)
      }
    },

    // 移除附件
    handleAttachmentRemove(file, fileList) {
      this.attachmentList = fileList
    },

    // 保存为草稿
    async saveAsDraft() {
      this.noticeForm.status = 'draft'
      await this.saveNotice()
    },

    // 保存并发布
    async saveAndPublish() {
      if (await msg_confirm('确认要发布该通知吗？发布后用户将可以看到该通知。')) {
        this.noticeForm.status = 'active'
        this.noticeForm.publishTime = this.publishTimeTemp || Date.now()
        await this.saveNotice()
      }
    },

    // 保存通知
    async saveNotice() {
      // 获取编辑器内容
      if (this.tinymceEditor) {
        this.noticeForm.content = this.tinymceEditor.getContent()
      }

      // 表单验证
      let valid = false
      await this.$refs.noticeForm.validate(v => {
        valid = v
      })

      if (!valid) {
        return false
      }

      this.saving = true

      try {
        // 准备提交数据
        const submitData = {
          ...this.noticeForm,
          expireTime: this.noticeForm.hasExpire ? this.expireTimeTemp : null,
          attachments: JSON.stringify(this.attachmentList),
          sendMethod: this.sendMethods.join(',')
        }

        // 如果是编辑模式，添加ID
        if (this.editType === 'edit') {
          submitData.noticeId = this.noticeId
        }

        const result = await NoticeModel.addOrEdit(submitData)

        if (result.code === "000000") {
          msg_success(this.editType === 'add' ? "新增成功" : "修改成功")
          this.goBack()
        } else {
          msg_err(result.message || "保存失败")
        }
      } catch (error) {
        msg_err("保存失败：" + error.message)
      } finally {
        this.saving = false
      }
    },

    // 返回列表页
    goBack() {
      this.$router.push('/content/notice')
    }
  }
}
</script>

<style scoped lang="scss">
.page-header {
  margin-bottom: 20px;
}

.upload-tip {
  color: #606266;
  font-size: 12px;
  line-height: 1.5;

  p {
    margin: 0 0 5px 0;
  }
}

.button-group {
  text-align: center;
  padding: 20px 0;

  .el-button {
    margin: 0 10px;
    min-width: 120px;
  }
}

.attachment-upload {
  ::v-deep .el-upload-list {
    margin-top: 10px;
  }
}
</style>
