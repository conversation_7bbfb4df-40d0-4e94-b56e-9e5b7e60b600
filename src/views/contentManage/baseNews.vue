<template>
  <div class="app-container">
    <!--筛选区域-->
    <div class="header-container clearfix">
      <list-search-filter :search-filter="lists.searchFilter"
                          @clickSearchFilterBtn="query=>ListMethods().clickSearchFilterBtn(query)"
                          @clickCleanBtn="ListMethods().clickCleanBtn()">
      </list-search-filter>
      <!--操作按钮区域-->
      <div class="right">
        <div style="text-align: right">
          <el-button class="el-button" type="success"
                     @click="ListMethods().clickAddBtn()"
                     style="background-color: #67C23A;border-color:#67C23A"
                     >
            新增基地新闻
          </el-button>
          <el-button class="el-button" type="info"
                     @click="ListMethods().clickStatisticsBtn()"
                     >
            统计信息
          </el-button>
          <el-button class="el-button" type="warning"
                     @click="ListMethods().clickBatchProcessBtn()"
                     >
            批量操作
          </el-button>
        </div>
      </div>
    </div>

    <!--列表区域-->
    <el-table :data="lists.list" v-loading="lists.loading"
              element-loading-text="加载中" border fit style="width: 100%;">
      <el-table-column type="selection" width="55" align="center"></el-table-column>

      <el-table-column label="标题" min-width="200" align="left">
        <template slot-scope="scope">
          <div style="display: flex; align-items: center;">
            <el-tag v-if="scope.row.isHot" type="danger" size="mini" style="margin-right: 5px;">热门</el-tag>
            <el-tag v-if="scope.row.isTop" type="warning" size="mini" style="margin-right: 5px;">置顶</el-tag>
            <span style="cursor: pointer; color: #409EFF;" @click="ListMethods().clickViewBtn(scope.row)">
              {{ scope.row.title }}
            </span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="分类" width="120" align="center">
        <template slot-scope="scope">
          <el-tag :type="getCategoryTagType(scope.row.category)" size="small">
            {{ BaseNewsModel.formatCategory(scope.row.category) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.status)" size="small">
            {{ BaseNewsModel.formatStatus(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="浏览量" width="100" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.views || 0 }}</span>
        </template>
      </el-table-column>

      <el-table-column label="作者" width="120" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.author || '-' }}</span>
        </template>
      </el-table-column>

      <el-table-column label="发布时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.publishTime | dateFormat }}</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="160" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.createTime | dateFormat }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="280" fixed="right"
                       class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button type="primary" size="mini" round
                     @click="ListMethods().clickEditBtn(scope.row)"
                     >编辑</el-button>

          <el-dropdown @command="(command) => ListMethods().handleDropdownCommand(command, scope.row)"
                       trigger="click"
                       >
            <el-button type="info" size="mini" round>
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="`view-${scope.row.newsId}`">查看详情</el-dropdown-item>
              <el-dropdown-item :command="`publish-${scope.row.newsId}`"
                                v-if="scope.row.status === 'draft' && hasPermission('content:news:edit')">发布</el-dropdown-item>
              <el-dropdown-item :command="`unpublish-${scope.row.newsId}`"
                                v-if="scope.row.status === 'published' && hasPermission('content:news:edit')">撤回</el-dropdown-item>
              <el-dropdown-item :command="`setHot-${scope.row.newsId}-${!scope.row.isHot}`"
                                v-if="hasPermission('content:news:edit')">
                {{ scope.row.isHot ? '取消热门' : '设为热门' }}
              </el-dropdown-item>
              <el-dropdown-item :command="`setTop-${scope.row.newsId}-${!scope.row.isTop}`"
                                v-if="hasPermission('content:news:edit')">
                {{ scope.row.isTop ? '取消置顶' : '设为置顶' }}
              </el-dropdown-item>
              <el-dropdown-item :command="`delete-${scope.row.newsId}`"
                                style="color: #F56C6C;"
                                v-if="hasPermission('content:news:delete')">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <!--分页区域-->
    <div class="pagination-container">
      <el-pagination background
                     @current-change="(number)=>ListMethods().pageChange(number)"
                     :current-page.sync="lists.pages.number"
                     :page-size.sync="lists.pages.size"
                     layout="total,prev, pager, next,jumper,sizes"
                     :total="lists.pages.totalElements"
                     @size-change="(size)=>ListMethods().pageLimitChange(size)"
                     :page-count="lists.pages.totalPages">
      </el-pagination>
    </div>

    <!--统计信息弹窗-->
    <el-dialog title="基地新闻统计信息"
               :visible.sync="statisticsDialog.visible"
               width="600px"
               center>
      <div class="statistics-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.totalCount }}</div>
              <div class="stat-label">总数量</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.hotCount }}</div>
              <div class="stat-label">热门新闻</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-item">
              <div class="stat-number">{{ statisticsDialog.data.topCount }}</div>
              <div class="stat-label">置顶新闻</div>
            </div>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <h4>分类统计</h4>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(count, category) in statisticsDialog.data.categoryStats" :key="category">
            <div class="stat-item">
              <div class="stat-number">{{ count }}</div>
              <div class="stat-label">{{ BaseNewsModel.formatCategory(category) }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="statisticsDialog.visible=false">关 闭</el-button>
      </span>
    </el-dialog>

    <!--新闻详情查看弹窗-->
    <el-dialog :title="newsDetail.title"
               :visible.sync="newsDetail.visible"
               width="80%"
               center>
      <div class="news-detail-container" v-if="newsDetail.data">
        <div class="news-meta">
          <el-row>
            <el-col :span="12">
              <p><strong>分类：</strong>{{ BaseNewsModel.formatCategory(newsDetail.data.category) }}</p>
              <p><strong>作者：</strong>{{ newsDetail.data.author }}</p>
              <p><strong>状态：</strong>{{ BaseNewsModel.formatStatus(newsDetail.data.status) }}</p>
            </el-col>
            <el-col :span="12">
              <p><strong>浏览量：</strong>{{ newsDetail.data.views }}</p>
              <p><strong>发布时间：</strong>{{ newsDetail.data.publishTime | dateFormat }}</p>
              <p><strong>创建时间：</strong>{{ newsDetail.data.createTime | dateFormat }}</p>
            </el-col>
          </el-row>
        </div>
        <el-divider></el-divider>
        <div class="news-summary">
          <h4>摘要</h4>
          <p>{{ newsDetail.data.summary }}</p>
        </div>
        <el-divider></el-divider>
        <div class="news-content">
          <h4>内容</h4>
          <div v-html="newsDetail.data.content"></div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="newsDetail.visible=false">关 闭</el-button>
        <el-button type="primary" @click="ListMethods().clickEditBtn(newsDetail.data)">编 辑</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ListSearchFilter from "@/views/components/list/listSearchFilter";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";
import {mapState} from "vuex";
import {dateFormat} from "@/filters";
import {date_format, getQuery} from "@/utils/common";
import enums from "@/enums/index"
import {msg_confirm, msg_err, msg_success} from "@/utils/ele_component";
import {BaseNewsModel} from "@/model/BaseNewsModel";

export default {
  name: "baseNewsManage",
  components: {ListSearchFilter},
  directives: {elDragDialog, permission},
  computed: {
    ...mapState({
      adminRoles: state => state.user.roles,
      permissionArr: state => state.user.permission
    })
  },
  filters: {dateFormat},
  data() {
    return {
      enums: enums,
      getQuery: getQuery,
      date_format: date_format,
      BaseNewsModel: BaseNewsModel,

      // 列表相关数据
      lists: {
        list: [],
        loading: false,
        query: {},
        queryBase: {
          deleted: 0
        },
        sort: "",
        pages: {
          size: 20
        },
        searchFilter: {
          search: [
            {
              type: 'input',
              label: '标题',
              key: 'title',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            },
            {
              type: 'input',
              label: '作者',
              key: 'author',
              value: '',
              format: function (v) {
                return {'$regex': `.*${v}.*`}
              }
            }
          ],
          filter: [
            {
              type: 'select',
              label: '分类',
              key: 'category',
              value: '',
              data: [
                {label: '全部', value: ''}
              ]
            },
            {
              type: 'select',
              label: '状态',
              key: 'status',
              value: '',
              data: [
                {label: '全部', value: ''},
                ...BaseNewsModel.getStatusOptions()
              ]
            },
            {
              type: 'select',
              label: '热门',
              key: 'isHot',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '是', value: true},
                {label: '否', value: false}
              ]
            },
            {
              type: 'select',
              label: '置顶',
              key: 'isTop',
              value: '',
              data: [
                {label: '全部', value: ''},
                {label: '是', value: true},
                {label: '否', value: false}
              ]
            }
          ]
        }
      },

      // 统计信息弹窗
      statisticsDialog: {
        visible: false,
        data: {
          totalCount: 0,
          hotCount: 0,
          topCount: 0,
          categoryStats: {}
        }
      },

      // 新闻详情查看
      newsDetail: {
        visible: false,
        title: '',
        data: null
      }
    }
  },

  async mounted() {
    // 加载分类配置
    await this.loadCategoryOptions()
    // 获取列表数据
    this.ListMethods().getList(0, 20, {})
  },

  methods: {
    // 加载分类配置
    async loadCategoryOptions() {
      try {
        const categoryOptions = await BaseNewsModel.getCategoryOptions()
        const categoryFilter = this.lists.searchFilter.filter.find(item => item.key === 'category')
        if (categoryFilter) {
          categoryFilter.data = [
            {label: '全部', value: ''},
            ...categoryOptions
          ]
        }
      } catch (error) {
        console.error('加载分类配置失败:', error)
      }
    },
    
    // 列表相关方法集合
    ListMethods() {
      let $this = this
      return {
        // 获取列表数据
        async getList(page, size, query) {
          $this.lists.loading = true;
          let list = [];
          query = Object.assign(query, $this.lists.queryBase);
          [list, $this.lists.pages] = await BaseNewsModel.getPageList(page, size, "publishTime,desc", query)
          $this.$set($this.lists, "list", list)
          $this.lists.loading = false
        },

        // 分页-页码变化
        async pageChange(page) {
          this.getList(page - 1, $this.lists.pages.size, $this.lists.query)
        },

        // 分页-每页数量变化
        async pageLimitChange(size) {
          this.getList(0, size, $this.lists.query)
        },

        // 搜索按钮点击
        clickSearchFilterBtn(query) {
          $this.lists.query = query
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 清空搜索
        clickCleanBtn() {
          $this.lists.query = {}
          this.getList(0, $this.lists.pages.size, $this.lists.query)
        },

        // 新增按钮点击
        async clickAddBtn() {
          $this.$router.push('/content/baseNewsEdit?type=add')
        },

        // 编辑按钮点击
        async clickEditBtn(news) {
          $this.$router.push(`/content/baseNewsEdit?type=edit&id=${news.newsId}`)
        },

        // 查看按钮点击
        async clickViewBtn(news) {
          $this.newsDetail.data = news
          $this.newsDetail.title = news.title
          $this.newsDetail.visible = true
          // 增加浏览量
          BaseNewsModel.increaseViews(news.newsId)
        },

        // 统计信息按钮
        async clickStatisticsBtn() {
          $this.statisticsDialog.data = await BaseNewsModel.getStatistics()
          $this.statisticsDialog.visible = true
        },

        // 批量操作按钮
        clickBatchProcessBtn() {
          msg_err("批量操作功能开发中...")
        },

        // 下拉菜单命令处理
        async handleDropdownCommand(command, row) {
          const [action, id, param] = command.split('-')

          switch (action) {
            case 'view':
              this.clickViewBtn(row)
              break
            case 'publish':
              if (await msg_confirm('确认要发布该新闻吗？')) {
                if (await BaseNewsModel.publishNews(id)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'unpublish':
              if (await msg_confirm('确认要撤回该新闻吗？')) {
                if (await BaseNewsModel.unpublishNews(id)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'setHot':
              const isHot = param === 'true'
              if (await msg_confirm(`确认要${isHot ? '设为热门' : '取消热门'}吗？`)) {
                if (await BaseNewsModel.setHotNews(id, isHot)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'setTop':
              const isTop = param === 'true'
              if (await msg_confirm(`确认要${isTop ? '设为置顶' : '取消置顶'}吗？`)) {
                if (await BaseNewsModel.setTopNews(id, isTop)) {
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                }
              }
              break
            case 'delete':
              if (await msg_confirm('确认要删除该新闻吗？此操作不可恢复！')) {
                if (await BaseNewsModel.deleteOne({newsId: id})) {
                  msg_success("删除成功")
                  this.getList($this.lists.pages.number - 1, $this.lists.pages.size, $this.lists.query)
                } else {
                  msg_err("删除失败")
                }
              }
              break
          }
        }
      }
    },

    // 获取分类标签类型
    getCategoryTagType(category) {
      const typeMap = {
        'base_news': '',
        'activity': 'success',
        'academic': 'info',
        'achievement': 'warning'
      }
      return typeMap[category] || ''
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const typeMap = {
        'draft': 'info',
        'published': 'success',
        'archived': 'warning'
      }
      return typeMap[status] || ''
    },

    // 检查权限
    hasPermission(permission) {
      const userRoles = this.adminRoles || []
      const userPermissions = this.permissionArr || []

      // 检查是否有管理员角色
      if (userRoles.includes('administrator') || userRoles.includes('super_admin')) {
        return true
      }

      // 检查具体权限
      return userPermissions.includes(permission)
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  margin-bottom: 15px;
}

.pagination-container {
  margin-top: 20px;
}

.statistics-container {
  .stat-item {
    text-align: center;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    margin-bottom: 10px;

    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #409EFF;
      margin-bottom: 5px;
    }

    .stat-label {
      color: #606266;
      font-size: 14px;
    }
  }
}

.news-detail-container {
  .news-meta {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;

    p {
      margin: 5px 0;
      line-height: 1.6;
    }
  }

  .news-summary {
    p {
      line-height: 1.8;
      color: #606266;
    }
  }

  .news-content {
    max-height: 400px;
    overflow-y: auto;

    ::v-deep img {
      max-width: 100%;
      height: auto;
    }
  }
}
</style>
