<template>
  <div class="app-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-page-header @back="goBack" :content="pageTitle">
      </el-page-header>
    </div>

    <!-- 编辑表单 -->
    <el-card style="margin-top: 20px;">
      <el-form ref="newsForm" :model="newsForm" :rules="formRules" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="新闻标题:" prop="title">
              <el-input v-model.trim="newsForm.title" placeholder="请输入新闻标题">
                <div slot="suffix" v-if="newsForm.title">
                  <span class="el-input__count">
                    <span class="el-input__count-inner">
                      {{ newsForm.title.length }} / 100
                    </span>
                  </span>
                </div>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="新闻分类:" prop="category">
              <div style="display: flex; align-items: center; gap: 10px;">
                <el-select v-model="newsForm.category" placeholder="请选择新闻分类" style="flex: 1;">
                  <el-option
                    v-for="item in categoryOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-setting"
                  @click="openCategoryDialog"
                  title="编辑分类">
                  编辑
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="作者:" prop="author">
              <el-input v-model.trim="newsForm.author" placeholder="请输入作者姓名"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态:" prop="status">
              <el-select v-model="newsForm.status" placeholder="请选择状态" style="width: 100%;">
                <el-option
                  v-for="item in BaseNewsModel.getStatusOptions()"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="热门新闻:">
              <el-switch v-model="newsForm.isHot"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="置顶新闻:">
              <el-switch v-model="newsForm.isTop"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发布时间:" v-if="newsForm.status === 'published'">
              <el-date-picker
                v-model="publishTimeTemp"
                type="datetime"
                placeholder="选择发布时间"
                style="width: 100%;"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="timestamp">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="封面图片:">
          <erp-uploader-one-pic
            :img-in="newsForm.coverImage"
            uploader-id="coverImage"
            uploader-title="封面图片"
            :uploader-size="[200,100]"
            :pixel-limit="[800,400]"
            :size-limit="2048"
            @uploadSuccess="data=>handleCoverUpload(data)"
            @afterDelete="data=>handleCoverDelete(data)">
          </erp-uploader-one-pic>
          <div class="upload-tip" style="margin-top: 10px;">
            <p>建议尺寸：800×400px，支持jpg、png格式，最大2MB</p>
          </div>
        </el-form-item>

        <el-form-item label="新闻摘要:" prop="summary">
          <el-input
            type="textarea"
            v-model="newsForm.summary"
            :rows="4"
            placeholder="请输入新闻摘要，建议150字以内"
            maxlength="300"
            show-word-limit>
          </el-input>
        </el-form-item>

        <el-form-item label="标签:">
          <el-tag
            v-for="tag in newsForm.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 10px;">
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            v-model="tagInputValue"
            ref="tagInput"
            size="small"
            @keyup.enter.native="addTag"
            @blur="addTag"
            style="width: 90px;">
          </el-input>
          <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
        </el-form-item>

        <el-form-item label="新闻内容:" prop="content">
          <tinymce
            id="tinymce_newsContent"
            ref="tinymce_newsContent"
            v-model="newsForm.content"
            :height="400"
          />
        </el-form-item>

        <el-form-item label="来源信息:">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-input v-model="newsForm.sourceFrom" placeholder="信息来源（可选）"></el-input>
            </el-col>
            <el-col :span="12">
              <el-input v-model="newsForm.sourceUrl" placeholder="来源链接（可选）"></el-input>
            </el-col>
          </el-row>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <div class="button-group">
            <el-button @click="goBack">取 消</el-button>
            <el-button type="info" @click="saveAsDraft" :loading="saving">保存为草稿</el-button>
            <el-button type="success" @click="saveAndPublish" :loading="saving">保存并发布</el-button>
          </div>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 分类编辑弹窗 -->
    <el-dialog
      title="编辑新闻分类"
      :visible.sync="categoryDialog.visible"
      width="700px"
      :close-on-click-modal="false">
      <div class="category-editor">
        <div class="category-list">
          <el-table :data="categoryDialog.categories" style="width: 100%" size="small">
            <el-table-column prop="label" label="分类名称" width="200">
              <template slot-scope="scope">
                <el-input v-if="scope.row.editing" v-model="scope.row.label" size="small"></el-input>
                <span v-else>{{ scope.row.label }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="value" label="分类值" width="150">
              <template slot-scope="scope">
                <el-input v-if="scope.row.editing" v-model="scope.row.value" size="small"></el-input>
                <span v-else>{{ scope.row.value }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="250">
              <template slot-scope="scope">
                <el-button v-if="scope.row.editing" size="mini" type="success" @click="saveCategory(scope.$index)">保存</el-button>
                <el-button v-if="scope.row.editing" size="mini" @click="cancelEditCategory(scope.$index)">取消</el-button>
                <el-button v-if="!scope.row.editing" size="mini" @click="editCategory(scope.$index)">编辑</el-button>
                <el-button size="mini" type="danger" @click="removeCategory(scope.$index)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="add-category" style="margin-top: 20px;">
          <el-button type="primary" @click="addCategory">添加分类</el-button>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="categoryDialog.visible = false">取 消</el-button>
        <el-button type="primary" @click="saveCategoryConfig" :loading="categoryDialog.saving">保存配置</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {BaseNewsModel} from "@/model/BaseNewsModel";
import {ConfigModel} from "@/model/ConfigModel";
import {msg_success, msg_err, msg_confirm} from "@/utils/ele_component";
import {getToken} from "@/utils/auth";
import {API_URL} from "@/config/main";
import Tinymce from "@/components/Tinymce";
import erpUploaderOnePic from "@/views/components/erpUploaderOnePic";

export default {
  name: "baseNewsEdit",
  components: {Tinymce,erpUploaderOnePic},
  data() {
    return {
      BaseNewsModel: BaseNewsModel,
      pageTitle: "新增基地新闻",
      editType: "add", // add 或 edit
      newsId: null,
      saving: false,

      // 分类选项
      categoryOptions: [],

      // 分类编辑对话框
      categoryDialog: {
        visible: false,
        categories: [],
        saving: false
      },

      // 表单数据
      newsForm: {
        title: "",
        category: "",
        author: "",
        status: "draft",
        summary: "",
        content: "",
        coverImage: "",
        isHot: false,
        isTop: false,
        tags: [],
        sourceFrom: "",
        sourceUrl: ""
      },

      // 发布时间临时变量
      publishTimeTemp: null,

      // 标签输入
      tagInputVisible: false,
      tagInputValue: "",

      // 富文本编辑器


      // 文件上传
      uploadAction: API_URL + "v1/file/upload",
      uploadHeaders: {
        'Authorization': 'Bearer ' + getToken()
      },

      // 表单验证规则
      formRules: {
        title: [
          { required: true, message: '请输入新闻标题', trigger: 'blur' },
          { max: 100, message: '标题长度不能超过100个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择新闻分类', trigger: 'change' }
        ],
        author: [
          { required: true, message: '请输入作者姓名', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ],
        summary: [
          { required: true, message: '请输入新闻摘要', trigger: 'blur' },
          { max: 300, message: '摘要长度不能超过300个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入新闻内容', trigger: 'blur' }
        ]
      }
    }
  },

  async mounted() {
    // 加载分类选项
    await this.loadCategoryOptions()

    // 获取路由参数
    this.editType = this.$route.query.type || 'add'
    this.newsId = this.$route.query.id

    if (this.editType === 'edit' && this.newsId) {
      this.pageTitle = "编辑基地新闻"
      await this.loadNewsData()
    }

    // 初始化富文本编辑器
    this.$nextTick(() => {
      this.initTinymceEditor()
    })
  },

  beforeDestroy() {
    // 销毁编辑器
    if (this.tinymceEditor) {
      this.tinymceEditor.destroy()
    }
  },

  methods: {
    // 加载分类选项
    async loadCategoryOptions() {
      try {
        this.categoryOptions = await BaseNewsModel.getCategoryOptions()
      } catch (error) {
        console.error('加载分类配置失败:', error)
        this.categoryOptions = []
      }
    },

    // 打开分类编辑对话框
    openCategoryDialog() {
      this.categoryDialog.categories = JSON.parse(JSON.stringify(this.categoryOptions))
      this.categoryDialog.visible = true
    },

    // 编辑分类
    editCategory(index) {
      this.$set(this.categoryDialog.categories[index], 'editing', true)
      this.$set(this.categoryDialog.categories[index], 'originalLabel', this.categoryDialog.categories[index].label)
      this.$set(this.categoryDialog.categories[index], 'originalValue', this.categoryDialog.categories[index].value)
    },

    // 保存单个分类编辑
    saveCategory(index) {
      const category = this.categoryDialog.categories[index]
      if (!category.label || !category.value) {
        msg_err('请填写完整的分类名称和值')
        return
      }
      this.$set(this.categoryDialog.categories[index], 'editing', false)
      this.$delete(this.categoryDialog.categories[index], 'originalLabel')
      this.$delete(this.categoryDialog.categories[index], 'originalValue')
    },

    // 取消编辑分类
    cancelEditCategory(index) {
      const category = this.categoryDialog.categories[index]
      category.label = category.originalLabel
      category.value = category.originalValue
      this.$set(this.categoryDialog.categories[index], 'editing', false)
      this.$delete(this.categoryDialog.categories[index], 'originalLabel')
      this.$delete(this.categoryDialog.categories[index], 'originalValue')
    },

    // 添加分类
    addCategory() {
      this.categoryDialog.categories.push({
        label: '',
        value: '',
        editing: true
      })
    },

    // 删除分类
    removeCategory(index) {
      this.categoryDialog.categories.splice(index, 1)
    },

    // 保存分类配置
    async saveCategoryConfig() {
      // 验证所有分类是否填写完整
      for (const category of this.categoryDialog.categories) {
        if (!category.label || !category.value) {
          msg_err('请填写完整的分类名称和值')
          return
        }
      }

      this.categoryDialog.saving = true
      try {
        const config = {
          categories: JSON.stringify(this.categoryDialog.categories)
        }

        const result = await ConfigModel.editConfig("newsConfig", config)
        console.log(result)
        if (result) {
          msg_success('分类配置保存成功')
          this.categoryOptions = JSON.parse(JSON.stringify(this.categoryDialog.categories))
          this.categoryDialog.visible = false
        } else {
          msg_err(result?.message || '分类配置保存失败')
        }
      } catch (error) {
        msg_err('分类配置保存失败：' + error.message)
      } finally {
        this.categoryDialog.saving = false
      }
    },

    // 加载新闻数据
    async loadNewsData() {
      const newsData = await BaseNewsModel.getOne(this.newsId)
      if (newsData) {
        this.newsForm = {
          ...newsData,
          tags: newsData.tags || []
        }
        this.publishTimeTemp = newsData.publishTime

        // 设置编辑器内容
        if (this.tinymceEditor) {
          this.tinymceEditor.setContent(newsData.content || '')
        }
      } else {
        msg_err("加载新闻数据失败")
        this.goBack()
      }
    },

    // 初始化富文本编辑器
    initTinymceEditor() {
      // 这里需要集成 TinyMCE 编辑器
      // 由于项目可能已经有编辑器配置，这里使用简化版本
      const textarea = document.createElement('textarea')
      textarea.style.width = '100%'
      textarea.style.height = '400px'
      textarea.value = this.newsForm.content
      textarea.addEventListener('input', (e) => {
        this.newsForm.content = e.target.value
      })

      const container = document.getElementById('tinymceEditor')
      container.appendChild(textarea)

      this.tinymceEditor = {
        setContent: (content) => {
          textarea.value = content
          this.newsForm.content = content
        },
        getContent: () => textarea.value,
        destroy: () => {
          if (container.contains(textarea)) {
            container.removeChild(textarea)
          }
        }
      }
    },

    // 封面图片上传成功（使用 erp-uploader-one-pic 组件的回调）
    handleCoverUpload(data) {
      let imgSrc = data[1] // 成功后文件地址
      let uploaderId = data[0] // 上传id
      this.newsForm.coverImage = imgSrc
      msg_success('封面图片上传成功')
    },

    // 封面图片删除（使用 erp-uploader-one-pic 组件的回调）
    handleCoverDelete(data) {
      let uploaderId = data[0] // 上传id
      this.newsForm.coverImage = ""
      msg_success('封面图片已删除')
    },

    // 显示标签输入框
    showTagInput() {
      this.tagInputVisible = true
      this.$nextTick(_ => {
        this.$refs.tagInput.$refs.input.focus()
      })
    },

    // 添加标签
    addTag() {
      let inputValue = this.tagInputValue
      if (inputValue && !this.newsForm.tags.includes(inputValue)) {
        this.newsForm.tags.push(inputValue)
      }
      this.tagInputVisible = false
      this.tagInputValue = ''
    },

    // 删除标签
    removeTag(tag) {
      this.newsForm.tags.splice(this.newsForm.tags.indexOf(tag), 1)
    },

    // 保存为草稿
    async saveAsDraft() {
      this.newsForm.status = 'draft'
      await this.saveNews()
    },

    // 保存并发布
    async saveAndPublish() {
      if (await msg_confirm('确认要发布该新闻吗？发布后用户将可以看到该新闻。')) {
        this.newsForm.status = 'published'
        this.newsForm.publishTime = this.publishTimeTemp || Date.now()
        await this.saveNews()
      }
    },

    // 保存新闻
    async saveNews() {
      // 获取编辑器内容
      if (this.tinymceEditor) {
        this.newsForm.content = this.tinymceEditor.getContent()
      }

      // 表单验证
      let valid = false
      await this.$refs.newsForm.validate(v => {
        valid = v
      })

      if (!valid) {
        return false
      }

      this.saving = true

      try {
        // 准备提交数据
        const submitData = {
          ...this.newsForm,
          tags: JSON.stringify(this.newsForm.tags)
        }

        // 如果是编辑模式，添加ID
        if (this.editType === 'edit') {
          submitData.newsId = this.newsId
        }

        const result = await BaseNewsModel.addOrEdit(submitData)

        if (result.code === "000000") {
          msg_success(this.editType === 'add' ? "新增成功" : "修改成功")
          this.goBack()
        } else {
          msg_err(result.message || "保存失败")
        }
      } catch (error) {
        msg_err("保存失败：" + error.message)
      } finally {
        this.saving = false
      }
    },

    // 返回列表页
    goBack() {
      this.$router.push('/content/baseNews')
    }
  }
}
</script>

<style scoped lang="scss">
.page-header {
  margin-bottom: 20px;
}

.upload-tip {
  color: #606266;
  font-size: 12px;
  line-height: 1.5;

  p {
    margin: 0 0 5px 0;
  }
}

.button-group {
  text-align: center;
  padding: 20px 0;

  .el-button {
    margin: 0 10px;
    min-width: 120px;
  }
}

::v-deep .el-tag {
  margin-bottom: 5px;
}
</style>
