<template>
  <div class="feedback-container">
    <div class="feedback-card">
      <div class="header-section">
        <h1 class="main-title">
          <font-awesome-icon :icon="['fas', 'comments']" class="title-icon" />
          教学反馈
        </h1>
        <p class="subtitle">感谢您的反馈，我们将尽快解决您的相关问题。</p>
      </div>
      
      <div class="content-section">
        <div class="input-wrapper">
          <label class="input-label">
            <font-awesome-icon :icon="['fas', 'edit']" class="label-icon" />
            反馈内容
          </label>
          <el-input 
            class="feedback-input" 
            type="textarea" 
            placeholder="请输入您的反馈内容（10-200字）" 
            :rows="6"
            v-model="content"
            maxlength="200"
            show-word-limit
          ></el-input>
        </div>
        
        <div class="action-section">
          <el-button 
            class="submit-btn" 
            type="primary" 
            size="large" 
            @click="clickFeedBtn"
            :disabled="content.length < 10"
          >
            <font-awesome-icon :icon="['fas', 'paper-plane']" class="btn-icon" />
            提交反馈
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {msg_confirm_choose, msg_err, msg_success} from "../utils/ele_component";
import {FeedbackModel} from "../model/FeedbackModel";
import {mapState} from "vuex";

export default {
  name: "Feedback",
  computed: {
    ...mapState({
      userInfo: state => state.userInfo,
    })
  },
  data() {
    return {
      content: ""
    }
  },
  mounted() {
    // 判断是否已登录
    if (!this.userInfo.hasOwnProperty("userId")) {
      msg_confirm_choose("您尚未登录，请先登录！", "请先登录", "取消", "去登录").then(res => {
        if (res === "right") {
          this.$router.push("/login")
        }else{
          this.$router.push("/")
        }
      })

    }
  },
  methods: {
    // 点击反馈按钮
    async clickFeedBtn() {
      if (this.content.length < 10) {
        msg_err("最少输入10字！")
        return
      }
      if (this.content.length > 200) {
        msg_err("最多输入200字！")
        return
      }
      if (await FeedbackModel.post(this.content)) {
        msg_success("您的反馈已提交成功！")
        this.$router.push("/")
      }
    }
  }
}
</script>

<style scoped lang="less">
.feedback-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #588cd4 0%, #4a7bc8 100%);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 140px 20px 20px;
  box-sizing: border-box;
}

.feedback-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  padding: 48px;
  width: 100%;
  max-width: 600px;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  }
}

.header-section {
  text-align: center;
  margin-bottom: 40px;
}

.main-title {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 16px 0;
  background: linear-gradient(135deg, #588cd4, #4a7bc8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.subtitle {
  font-size: 16px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.6;
  font-weight: 400;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-label {
  font-size: 16px;
  font-weight: 600;
  color: #34495e;
  margin: 0;
}

.feedback-input {
  :deep(.el-textarea__inner) {
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    padding: 16px;
    font-size: 15px;
    line-height: 1.6;
    transition: all 0.3s ease;
    resize: none;
    background: #fafbfc;
    
    &:focus {
      border-color: #588cd4;
      box-shadow: 0 0 0 3px rgba(88, 140, 212, 0.1);
      background: #ffffff;
    }
    
    &::placeholder {
      color: #95a5a6;
    }
  }
  
  :deep(.el-input__count) {
    background: transparent;
    color: #7f8c8d;
    font-size: 13px;
    right: 12px;
    bottom: 8px;
  }
}

.action-section {
  display: flex;
  justify-content: center;
  align-items: center;
}

.submit-btn {
  :deep(.el-button) {
    background: linear-gradient(135deg, #588cd4, #4a7bc8);
    border: none;
    border-radius: 25px;
    padding: 14px 40px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(88, 140, 212, 0.3);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(88, 140, 212, 0.4);
    }
    
    &:active {
      transform: translateY(0);
    }
    
    &:disabled {
      background: #bdc3c7;
      transform: none;
      box-shadow: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .feedback-container {
    padding: 120px 16px 16px;
  }
  
  .feedback-card {
    padding: 32px 24px;
  }
  
  .main-title {
    font-size: 28px;
  }
  
  .subtitle {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .feedback-container {
    padding: 110px 16px 16px;
  }
  
  .feedback-card {
    padding: 24px 20px;
  }
  
  .main-title {
    font-size: 24px;
  }
  
  .content-section {
    gap: 24px;
  }
}

// 图标样式
.title-icon {
  margin-right: 12px;
  color: #588cd4;
  font-size: 0.9em;
}

.label-icon {
  margin-right: 8px;
  color: #588cd4;
  font-size: 0.9em;
}

.btn-icon {
  margin-right: 8px;
  font-size: 0.9em;
}
</style>
