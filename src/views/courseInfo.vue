<template>
  <div class="page-experimentInfo">
    <div class="header-container">
      <div class="header-box content-container flex flex-around">
        <div class="left-box">
          <img class="avatar"
               :src="course.avatarUrl"
               alt="">
        </div>
        <div class="right-box">
          <div class="name">{{ course.name }}</div>
          <div class="des-info flex flex-start">
            <span><font-awesome-icon icon="graduation-cap" class="info-icon" />所属学科：{{ course.courseSubjectEntity[0]["name"] }}</span>
            <span><font-awesome-icon icon="university" class="info-icon" />所属学院：{{ course.collegeEntity[0]["name"] }}</span>
          </div>
          <div class="des-text" v-for="item in course.shortDesText.split('\n')">
            <div class="text">{{ item }}</div>
          </div>
          <div class="button-box">
            <el-button type="primary" @click="clickStartExperimentBtn">
              <font-awesome-icon icon="rocket" class="button-icon" />
              开始实验
            </el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="info-container content-container flex flex-between">
      <el-card class="left-container">
        <el-tabs v-model="desTabShow">
          <el-tab-pane name="first">
            <span slot="label">
              <font-awesome-icon icon="book-open" class="tab-icon" />
              实验介绍
            </span>
            <div class="html-view" v-html="course.desText"></div>
          </el-tab-pane>
          <el-tab-pane name="second">
            <span slot="label">
              <font-awesome-icon icon="lightbulb" class="tab-icon" />
              实验帮助
            </span>
            <div class="html-view" v-html="course.helpText"></div>
          </el-tab-pane>
          <el-tab-pane name="third">
            <span slot="label">
              <font-awesome-icon icon="comments" class="tab-icon" />
              联系方式
            </span>
            <div class="html-view" v-html="course.contactText"></div>
          </el-tab-pane>
          <el-tab-pane name="fourth">
            <span slot="label">
              <font-awesome-icon icon="cog" class="tab-icon" />
              软硬件要求
            </span>
            <div class="html-view" v-html="course.softHardwareText"></div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
      <div class="right-container">
        <el-card class="statistics-info right-li">
          <div class="title flex flex-between">
            <span>实验统计</span>
            <span class="remark">更新时间 {{ statisticRealTime.info.updateTime | dateFormat }}</span>
          </div>
          <el-divider></el-divider>
          <div class="number-info">
            <div class="li flex flex-between">
              <span>
                <font-awesome-icon icon="eye" class="stat-icon" />
                实验浏览量
                <el-tooltip class="item" effect="light" content="用户进入实验的次数" placement="top-start">
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
              </span>
              <span>{{ statisticRealTime.info.viewNumber }}</span>
            </div>
            <div class="li flex flex-between">
              <span>
                <font-awesome-icon icon="users" class="stat-icon" />
                实验人次
                 <el-tooltip class="item" effect="light" content="“实验人次”是指用户参与并完成实验的总次数,同一个用户可以做多次实验。"
                             placement="top-start">
                   <i class="el-icon-warning-outline"></i>
                 </el-tooltip>
              </span>
              <span>{{ statisticRealTime.info.scoreNumber }}</span>
            </div>
            <div class="li flex flex-between">
              <span>
                <font-awesome-icon icon="user" class="stat-icon" />
                实验人数
               <el-tooltip class="item" effect="light" content="“实验人数”是指参与实验的用户总人数。同一个用户多次实验，实验人数只统计为1人。"
                           placement="top-start">
                 <i class="el-icon-warning-outline"></i>
               </el-tooltip>
              </span>
              <span>{{ statisticRealTime.info.userNumber }}</span>
            </div>
            <div class="li flex flex-between">
              <span><font-awesome-icon icon="clock" class="stat-icon" />实验平均用时</span>
              <span>{{ statisticRealTime.info.averageUsedTime | scoreUseTimeFilter }}</span>
            </div>
            <div class="li flex flex-between">
              <span>
                <font-awesome-icon icon="check-circle" class="stat-icon" />
                实验完成率
                <el-tooltip class="item" effect="light" content="“实验完成率”是指完成实验的次数占参与实验总次数的百分比。"
                            placement="top-start">
                 <i class="el-icon-warning-outline"></i>
               </el-tooltip>
              </span>
              <span>{{ (statisticRealTime.info.completePoint * 100).toFixed(2) }}%</span>
            </div>
            <div class="li flex flex-between">
              <span>
                <font-awesome-icon icon="trophy" class="stat-icon" />
                实验通过率
                 <el-tooltip class="item" effect="light" content="“实验通过率”是指实验成绩60分及以上的次数占实验总次数的百分比。"
                             placement="top-start">
                 <i class="el-icon-warning-outline"></i>
               </el-tooltip>
              </span>
              <span>{{ (statisticRealTime.info.passPoint * 100).toFixed(2) }}%</span>
            </div>
          </div>
          <div class="chart-info flex flex-between">
            <div class="number-box">
              <el-tooltip class="item" effect="light" content="成绩≥85"
                          placement="top-start">
                <div class="li flex flex-start">
                  <i></i>
                  <span>优秀</span>
                  <span>{{ statisticRealTime.info.excellentNumber }}人次</span>
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="light" content="60≤成绩<85"
                          placement="top-start">
                <div class="li flex flex-start">
                  <i></i>
                  <span>达标</span>
                  <span>{{ statisticRealTime.info.standardNumber }}人次</span>
                </div>
              </el-tooltip>
              <el-tooltip class="item" effect="light" content="成绩<60"
                          placement="top-start">
                <div class="li flex flex-start">
                  <i></i>
                  <span>不达标</span>
                  <span>{{ statisticRealTime.info.failNumber }}人次</span>
                </div>
              </el-tooltip>
            </div>
            <div id="statistic-user-number" style="height: 120px;width: 120px;margin-top: 15px;"></div>
          </div>
        </el-card>
        <el-card class="task-info right-li">
          <div class="title">
            <font-awesome-icon icon="tasks" class="title-icon-small" />
            任务安排
          </div>
          <el-divider></el-divider>
          <div class="task-list" v-if="taskList.shortList.length>0">
            <div class="li" v-for="item in taskList.shortList">
              <div class="name">{{ item.name }}</div>
              <div class="college">{{ item.collegeName }}</div>
            </div>
          </div>
          <el-empty description="暂无数据" v-if="taskList.shortList.length===0" :image-size="80"></el-empty>
          <div class="button">
            <el-button type="primary" size="small" @click="clickTaskMoreBtn" v-if="taskList.shortList.length>0">
              <font-awesome-icon icon="chevron-right" class="btn-icon" />
              查看更多
            </el-button>
          </div>
        </el-card>
        <el-card class="comment-info right-li">
          <div class="title flex flex-between">
            <span><font-awesome-icon icon="star" class="title-icon-small" />课程评价</span>
            <span class="number">总数：{{ course.commentNumber }}</span>
          </div>
          <el-divider></el-divider>
          <div class="comment-list">
            <div class="li" v-if="comment.info.content">
              <div class="comment-text">
                <div class="text" v-for="item in comment.info.content.split('\n')">{{ item }}</div>
              </div>
              <div class="user flex flex-between">
                <span class="name">{{ comment.info.userName }}</span>
                <span class="date">{{ comment.info.createTime |dateFormat }}</span>
              </div>
            </div>
            <div class="button" v-if="comment.info.content">
              <el-button type="primary" size="small" @click="clickCommentMoreBtn">
                <font-awesome-icon icon="chevron-right" class="btn-icon" />
                查看更多
              </el-button>
            </div>
            <el-empty description="暂无数据" v-if="!comment.info.content" :image-size="80"></el-empty>
          </div>
        </el-card>
      </div>
    </div>
    <!--任务列表弹窗-->
    <el-dialog
        title="任务安排"
        :visible.sync="taskList.dialog"
        width="980px"
        center
        :close-on-click-modal="false"
        v-el-drag-dialog>
      <div class="dialog-container">
        <el-table :data="taskList.list" v-loading="taskList.loading"
                  element-loading-text="加载中" fit border
                  style="width: 100%;">
          <el-table-column label="任务名称" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所属学院" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.collegeName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开放状态" align="center" width="80px">
            <template slot-scope="scope">
              <span>{{ scope.row.opened ? '开放' : '关闭' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="任务时间" align="center">
            <template slot-scope="scope">
              <span>{{ scope.row.startTime | dateFormat }} - {{ scope.row.endTime | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" width="150px">
            <template slot-scope="scope">
              <span>{{ scope.row.createTime | dateFormat }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="courseName" align="center" width="80px">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="clickTaskDesBtn(scope.row)">任务介绍</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
    <!--任务介绍弹窗-->
    <el-dialog
        title="任务介绍"
        :visible.sync="taskDes.dialog"
        width="980px"
        center
        :close-on-click-modal="false"
        v-el-drag-dialog>
      <div class="dialog-container html-view" v-html="taskDes.info.desText">
      </div>
    </el-dialog>
    <!--开始实验弹窗-->
    <el-dialog
        title="开始课程实验"
        :visible.sync="startDialog.dialog"
        width="700px"
        center
        :close-on-click-modal="false"
        v-el-drag-dialog>
      <div class="dialog-container">
        <!--网页版-->
        <div v-if="course.courseType==='网页版'">
          <div style="font-size:18px;font-weight: bold;margin-bottom: 15px;text-align: center;">请点击下面链接进入课程</div>
          <div class="url" style="text-align: center">
            <a :href="startDialog.jumpUrl" target="_blank" @click="startDialog.dialog=false">{{
                startDialog.jumpUrl
              }}</a>
          </div>
        </div>
        <div v-if="course.courseType==='客户端版'">
          <div style="font-size:18px;font-weight: bold;margin-bottom: 15px;text-align: center;">请按照下面提示获取课程的客户端</div>
          <div class="html-view limit-height" v-html="course.clientText"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {date_format, getQuery} from "../utils/common";
import {CourseModel} from "../model/CourseModel";
import {msg_confirm_choose, msg_err} from "../utils/ele_component";
import {CacheModel} from "../model/CacheModel";
import {dateFormat, scoreUseTimeFilter} from "../filters";
import elDragDialog from "@/directive/el-drag-dialog";
import {CourseTaskModel} from "../model/CourseTaskModel";
import {CourseCommentModel} from "../model/CourseCommentModel";
import * as echarts from 'echarts';
import {OpenModel} from "../model/OpenModel";
import {StatisticModel} from "../model/StatisticModel";
import {mapState} from "vuex";

export default {
  name: "CourseInfo",
  directives: {
    elDragDialog
  },
  filters: {
    dateFormat: dateFormat,
    scoreUseTimeFilter,
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo,
    })
  },
  data() {
    return {
      window: window,
      courseId: getQuery("id"),
      desTabShow: "first",
      course: {
        courseSubjectEntity: [{}],
        collegeEntity: [{}],
        shortDesText: ""
      },
      statisticCache: {
        info: {}
      },
      statisticRealTime: {
        info: {}
      },
      // 任务列表
      taskList: {
        dialog: false,
        // 页面中列表
        shortList: [],
        // 弹窗列表
        list: [{}, {}],
        listLoading: false,
      },
      // 任务介绍弹窗
      taskDes: {
        dialog: false,
        info: {}
      },
      // 评价列表
      comment: {
        list: [],
        timeInterval: undefined,
        index: 0,
        info: {
          content: ""
        }
      },
      // 开始实验跳转和介绍弹窗
      startDialog: {
        dialog: false,
        clientText: ""
      },
    }
  },
  async mounted() {
    await this.getCourseInfo()

  },
  beforeDestroy() {
    // 退出前清理
    clearInterval(this.comment.timeInterval)
  },
  methods: {
    // 点击开始实验按钮
    async clickStartExperimentBtn() {
      // 判断是否已登录
      if (!this.userInfo.hasOwnProperty("userId")) {
        msg_confirm_choose("您尚未登录，请先登录！", "请先登录", "取消", "去登录").then(res => {
          if (res === "right") {
            this.$router.push("/login")
          }
        })
        return
      }
      if (this.course.courseType === "网页版") {
        // 获取ticket
        let ticket = await OpenModel.getTicketByWeb(this.course.appId)
        if (ticket) {
          this.startDialog.jumpUrl = this.course.webUrl + "?ticket=" + ticket
        } else {
          msg_err("获取ticket失败，请联系管理员！")
        }
      }
      if (this.course.courseType === "客户端") {

      }
      this.startDialog.dialog = true
    },
    // 获取课程详情
    async getCourseInfo() {
      this.course = await CourseModel.getOne(this.courseId).catch(res => {
        msg_err("获取课程详情失败！")
      })
      if (this.course.opened === false) {
        msg_err("该课程已下线")
        this.$router.push({
          name: "Home"
        })
        return
      }
      (async () => {
        // 获取课程实时统计数据
        this.statisticRealTime.info = await StatisticModel.getCourseRealTimeValue(this.courseId)
        setTimeout(() => {
          // 绘制统计图
          // 基于准备好的dom，初始化echarts实例
          let myChart = echarts.init(document.getElementById('statistic-user-number'));
          // 绘制图表
          myChart.setOption({
            series: [
              {
                type: 'pie',
                label: {
                  normal: {
                    show: false
                  }
                },
                color: ["#dd5303", "#46b4e9", "#762983"],
                data: [
                  {value: this.statisticRealTime.info.excellentNumber},
                  {value: this.statisticRealTime.info.standardNumber},
                  {value: this.statisticRealTime.info.failNumber},
                ],
                emphasis: {
                  itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                  }
                }
              }
            ]
          });
        }, 1000)
      })().then();
      (async () => {
        // 获取任务安排列表
        let courseTaskList = await CourseTaskModel.getList({
          courseId: this.courseId
        })
        // 获取前3个安排用于在页面显示
        let courseTaskShortList = []
        courseTaskShortList = courseTaskList.slice(0, 3)
        this.$set(this.taskList, "list", courseTaskList)
        this.$set(this.taskList, "shortList", courseTaskShortList)
      })().then();
      (async () => {
        // 获取最新的5条评论
        let [commentList,] = await CourseCommentModel.getPageList(0, 5, "", {
          courseId: this.courseId
        })
        this.$set(this.comment, "list", commentList)
        this.$set(this.comment, "index", 0)
        if (commentList.length > 0) {// 评论轮播
          this.$set(this.comment, "info", commentList[0])
          this.comment.timeInterval = setInterval(this.commentShow, 4000)
        }
      })().then();
    },
    // 评价循环显示
    commentShow() {
      this.$set(this.comment, "info", this.comment.list[this.comment.index])
      this.comment.index++
      if (this.comment.index === this.comment.list.length) {
        this.comment.index = 0;
      }
    },
    // 点击任务-查看更多按钮
    clickTaskMoreBtn() {
      this.taskList.dialog = true
    },
    // 点击任务详情按钮
    clickTaskDesBtn(task) {
      this.taskDes.dialog = true
      this.taskDes.info = task
    },
    // 点击评价查看更多按钮
    clickCommentMoreBtn() {
      this.$router.push({
        name: "CommentList",
        query: {
          id: this.courseId
        }
      })
    }
  }
}
</script>

<style scoped lang="less">
@import '../style/app.less';

.page-experimentInfo {
  margin-bottom: 20px;
}

// FontAwesome图标样式
.info-icon {
  margin-right: 6px;
  color: #4093f9;
  font-size: 12px;
}

.button-icon {
  margin-right: 6px;
  font-size: 14px;
}

.tab-icon {
  margin-right: 6px;
  font-size: 13px;
  color: #4093f9;
}

.title-icon-small {
  margin-right: 6px;
  color: #4093f9;
  font-size: 14px;
}

.stat-icon {
  margin-right: 6px;
  color: #fff;
  font-size: 12px;
}

.btn-icon {
  margin-right: 4px;
  font-size: 11px;
}

.header-container {
  width: 100%;
  overflow: hidden;
  position: relative;
  background-color: #2f3a59;

  .header-box {
    color: #fff;
    padding: 40px 0px;
    align-items: start;

    .right-box {
      width: 700px;

      .name {
        font-weight: 400;
        font-size: 24px;
        letter-spacing: 1px;
        line-height: 1.5;
        margin-bottom: 10px;
      }

      .des-info {
        margin-bottom: 10px;

        span {
          font-size: 14px;
          color: #fff;
          opacity: .5;
          margin-right: 20px;
          line-height: 20px;
        }
      }

      .des-text {
        font-size: 14px;
        line-height: 22px;

        .text { // 简介分段

        }
      }

      .button-box {
        margin-top: 30px;
        text-align: center;

        .el-button {
          padding: 12px 40px;
        }
      }
    }

    .left-box {
      padding-top: 5px;

      img.avatar {
        width: 350px;
        height: 210px;
      }
    }
  }
}

.info-container {
  margin-top: 20px;
  position: relative;
  align-items: start;

  .left-container {
    width: 900px;
  }

  .right-container {
    width: 280px;
    color: #333;

    .right-li {
      margin-bottom: 20px;

      div.title {
        font-weight: 500;
        font-size: 15px;
        border-left: 4px solid #4093f9;
        padding-left: 10px;
      }

      .el-divider--horizontal {
        margin: 8px 0px;
      }
    }

    .statistics-info {
      .title {
        .remark {
          font-size: 12px;
          color: #999;
        }
      }

      .number-info {
        .li {
          color: #fff;
          font-size: 13px;
          padding: 6px 20px;
          border-radius: 5px;
          margin-bottom: 5px;
        }

        .li:nth-child(1) {
          background-color: #d97408;
        }

        .li:nth-child(2) {
          background-color: #a621c2;
        }

        .li:nth-child(3) {
          background-color: #4976e8;
        }

        .li:nth-child(4) {
          background-color: #21a6c2;
        }

        .li:nth-child(5) {
          background-color: #eba705;
        }

        .li:nth-child(6) {
          background-color: #339900;
        }
      }

      .chart-info {
        padding-left: 5px;

        .li {
          color: #8b8b8b;
          font-size: 12px;
          margin-top: 15px;

          i {
            width: 15px;
            height: 15px;
            border-radius: 4px;
            margin-right: 5px;
          }

          span {
          }

          span:last-child {
            margin-left: 5px;
          }
        }

        .li:nth-child(1) i {
          background-color: #dd5303;
        }

        .li:nth-child(2) i {
          background-color: #46b4e9;
        }

        .li:nth-child(3) i {
          background-color: #762983;
        }
      }
    }

    .task-info {
      .li {
        .name {
          font-size: 14px;
          color: #333;
          white-space: nowrap;
          word-break: keep-all;
          text-overflow: ellipsis;
          margin-bottom: 5px;
          overflow: hidden;
          display: block;
          cursor: pointer;
        }

        .college {
          text-align: right;
          color: #999;
          font-size: 13px;
          white-space: nowrap;
          word-break: keep-all;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .button {
        margin-top: 20px;
        text-align: center;
      }
    }

    .comment-info {
      .title {
        span.number {
          font-size: 12px;
          color: #999;
        }
      }

      .comment-list {
        .li {
          color: #888;

          .comment-text {
            font-size: 13px;
            color: #555;
            line-height: 19px;
            margin-bottom: 10px;

            .text {
              margin-bottom: 3px;
            }
          }

          .user {
            font-size: 12px;

            .name {
              margin-right: 15px;
            }

            .date {

            }
          }
        }

        .button {
          margin-top: 20px;
          text-align: center;
        }
      }
    }
  }

}
</style>
