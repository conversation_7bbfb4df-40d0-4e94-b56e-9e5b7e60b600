<template>
  <div class="page-commentList content-container">
    <div class="header-container flex flex-between">
      <div class="flex flex-start">
        <img :src="course.avatarUrl" alt="">
        <div class="info">
          <div class="name">{{ course.name }}</div>
          <div class="des flex flex-start">
            <span>{{ course.collegeEntity[0]["name"] }}</span>
          </div>
        </div>
      </div>
      <div class="button">
<!--        <el-button type="primary" @click="clickGoToCommentBtn">去评价</el-button>-->
      </div>
    </div>
    <el-card class="list-container">
      <div slot="header" class="clearfix">
        <span>评价列表</span>
      </div>
      <div class="container">
        <div class="li" v-for="item in comment.list">
          <div class="flex flex-between">
            <div class="flex flex-start">
              <img src="@/assets/imgs/user.png" alt="" class="avatar">
              <div>
                <div class="name">{{ item.userName }}</div>
                <div class="collage">{{ item.userCollegeName }}</div>
              </div>
            </div>
            <div class="date">
              发布于 {{ item.createTime | dateFormat }}
            </div>
          </div>

          <div class="comment">
            <div class="text" v-for="text in item.content.split('\n')">{{ text }}</div>
          </div>
        </div>
      </div>
      <div class="buttons flex flex-center">
        <el-button type="primary" size="medium" v-if="comment.list.length<course.commentNumber">查看更多</el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import {getQuery} from "../utils/common";
import {CourseModel} from "../model/CourseModel";
import {CourseCommentModel} from "../model/CourseCommentModel";
import {dateFormat} from "../filters";

export default {
  name: "CommentList",
  filters: {
    dateFormat
  },
  data() {
    return {
      courseId: getQuery("id"),
      course: {
        collegeEntity: [{}]
      },
      comment: {
        list: [],
        pageNumber: 0,
      }
    }
  },
  async mounted() {
    // 获取课程详情
    this.course = await CourseModel.getOne(this.courseId)
    // 获取列表
    this.getCommentList();
  },
  methods: {
    // 获取评价列表
    async getCommentList() {
      let [list, pages] = await CourseCommentModel.getPageList(this.comment.pageNumber, 10, "", {
        courseId: this.courseId
      })
      list.forEach(li => {
        this.comment.list.push(li)
      })
      this.comment.pageNumber = pages.number
    },
  }
}
</script>

<style scoped lang="less">
@import '../style/app.less';

.page-commentList {
  margin-bottom: 20px;
}

.header-container {
  padding: 20px 0px;

  img {
    width: 200px;
    height: 120px;
    margin-right: 20px;
  }

  .info {
    .name {
      font-size: 24px;
      color: #3a3a3a;
      margin: 20px 0;
    }

    .des {
      color: #3a3a3a;
      font-size: 14px;

      span {
        margin-right: 20px;
      }
    }
  }

  .button {
    padding-right: 150px;
  }
}

.list-container {
  .li {
    border-bottom: 1px solid #f2f2f2;
    padding-bottom: 10px;
    margin-bottom: 10px;

    &:last-child {
      border-bottom: none;
    }

    img.avatar {
      width: 50px;
      height: 50px;
      border-radius: 50px;
      margin-right: 10px;
    }

    .name {
      color: #333;
      font-size: 14px;
      margin-bottom: 5px;
    }

    .collage {
      color: #777;
      font-size: 13px;
    }

    .date {
      color: #999;
      font-size: 13px;
    }

    .comment {
      margin-top: 15px;
      color: #555;
      font-size: 14px;
      line-height: 20px;
    }
  }
}
</style>
