<template>
  <div class="login-container">
    <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" autocomplete="off"
             label-position="left"
    >

      <div class="title-container">
        <h3 class="title-img">
          <img src="../../../public/logo_round.png" alt="">
        </h3>
        <h3 class="title">
          成都智云鸿道信息技术有限公司
        </h3>
        <h4 class="sub-title">
          虚拟仿真实验平台管理系统
        </h4>
      </div>

      <el-form-item prop="account">
        <span class="svg-container">
          <svg-icon icon-class="user"/>
        </span>
        <el-input
          ref="account"
          v-model="loginForm.account"
          :placeholder="$t('login.account')"
          name="account"
          type="text"
          tabindex="1"
          autocomplete="off"
        />
      </el-form-item>

      <el-tooltip v-model="capsTooltip" content="Caps lock is On" placement="right" manual>
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password"/>
          </span>
          <el-input
            :key="passwordType"
            ref="password"
            v-model="loginForm.password"
            :type="passwordType"
            placeholder="密码"
            name="password"
            tabindex="2"
            autocomplete="off"
            @keyup.native="checkCapslock"
            @blur="capsTooltip = false"
            @keyup.enter.native="handleLogin"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"/>
          </span>
        </el-form-item>
      </el-tooltip>

      <div>
        <el-button round :loading="loading" type="primary"
                   style="display:block;width:60%;margin:0 auto;margin-bottom:30px;font-size: 16px;height: 50px;border-radius: 25px"
                   @click.native.prevent="handleLogin"
        >
          {{ $t('login.logIn') }}
        </el-button>
      </div>
    </el-form>
    <!--    <ul class="cb-slideshow">-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--      <li></li>-->
    <!--    </ul>-->

    <!--修改密码弹窗-->
    <el-dialog
      title="请修改默认密码"
      :visible.sync="password.dialog"
      :close-on-click-modal="false"
      :append-to-body="true"
      :show-close="false"
      width="600px"
      center
      v-el-drag-dialog>
      <div class="dialog-container">
        <el-form label-width="140px" ref="passwordForm" :model="password.edit" :rules="password.formRules">
          <el-form-item label="请输入新密码:" prop="password1">
            <el-input v-model.trim="password.edit.password1" type="password">
            </el-input>
          </el-form-item>
          <el-form-item label="请再次输入新密码:" prop="password2">
            <el-input v-model="password.edit.password2" type="password"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="success" @click="clickChangePasswordBtn()">提 交</el-button>
      </span>
    </el-dialog>

    <div class="animated-bg"></div>

    <div class="bottomTips">
      <div>成都智云鸿道信息技术有限公司版权所有</div>
      <div>推荐使用谷歌、火狐浏览器访问本系统</div>
      <div>蜀ICP备15014907号-2</div>
    </div>
  </div>
</template>

<script>
import {validUsername} from '@/utils/validate'
import {AdminUserModel} from '@/model/AdminUserModel'
import {getToken, setToken} from '@/utils/auth'
import {msg_err, msg_success} from "@/utils/ele_component";
import elDragDialog from "@/directive/el-drag-dialog";
import permission from "@/directive/permission";

export default {
  name: 'Login',
  components: {},
  directives: {
    elDragDialog, permission
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      callback()
    }
    const validatePassword = (rule, value, callback) => {
      callback()
    }
    return {
      directToken: this.$route.query['directToken'],
      loginForm: {
        account: '',
        password: ''
      },
      loginRules: {
        account: [{required: true, trigger: 'blur', message: '请输入账号'}],
        password: [{required: true, trigger: 'blur', message: '请输入密码'}]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {},
      password: {
        dialog: false,
        userInfo: {},
        edit: {
          password1: "",
          password2: ""
        },
        // 输入检测
        formRules: {
          'password1': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
          'password2': {required: true, pattern: /^\w{6,}$/, message: '密码至少6位'},
        },
      }
    }
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    // window.addEventListener('storage', this.afterQRScan)
  },
  async mounted() {
    // 检测地址栏是否有token,有就直接登录
    if (this.directToken) {
      this.loginDirectByToken(this.directToken)
    }
    if (this.loginForm.account === '') {
      this.$refs.account.focus()
    } else if (this.loginForm.password === '') {
      this.$refs.password.focus()
    }
  },
  destroyed() {
    // window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    // 通过token直接登录系统
    loginDirectByToken(token) {
      this.$store.dispatch('user/login', {
        token: token
      }).then(() => {
        this.$router.push({path: '/'})
      })
    },
    checkCapslock(e) {
      const {key} = e
      this.capsTooltip = key && key.length === 1 && (key >= 'A' && key <= 'Z')
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          let result = await AdminUserModel.userLogin(this.loginForm.account, this.loginForm.password)
          if (result) {
            this.$store.dispatch('user/login', result).then(() => {
              this.$router.push({path: '/'})
            })
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    // 点击修改密码弹窗的提交按钮
    clickChangePasswordBtn() {
      this.$refs['passwordForm'].validate(async validate => {
        if (validate) {
          if (this.password.edit.password1 !== this.password.edit.password2) {
            msg_err("两次密码输入不一样！")
            return
          }
          let result = await AdminUserModel.changePassword(this.password.edit.password1, this.password.userInfo.user.adminUserId)
          if (result.code === "000000") {
            msg_success("修改密码成功")
            // 设置extraInfo已登录
            let user = result.data
            user.extraInfo.hasLogin = true
            await AdminUserModel.edit(user)
            this.password.dialog = false
            this.$router.push({path: '/'})
          }
        }
      });
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #f9f9f9;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 50px;
    width: 75%;
    margin-left: 15px;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 13px;
      color: #333;
      height: 50px;
      font-size: 16px;

      &:-webkit-autofill {
        box-shadow: inset 0px 60px 0px transparent;
        -webkit-text-fill-color: #333 !important;
        color: #222;
        transition: background-color 5000s ease-in-out 0s;
        background-color: transparent !important;
        background-image: none !important;
        caret-color: #333;
      }

      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        box-shadow: inset 0px 60px 0px transparent;
        -webkit-text-fill-color: #333 !important;
        transition: background-color 5000s ease-in-out 0s;
        background-color: transparent !important;
        background-image: none !important;
      }

      &::placeholder {
        color: #999;
        font-size: 15px;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    color: #454545;
    margin-bottom: 25px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
    }

    &:focus-within {
      border-color: #409eff;
      box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    }
  }
}
</style>

<style lang="scss" scoped>
.animated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 30%, #3d5a80 60%, #546e7a 100%);
  z-index: -999;
  overflow: hidden;
}

.animated-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: floatingBubbles 40s ease-in-out infinite;
}

.animated-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: floatingBubbles 40s ease-in-out infinite;
  animation-delay: 10s;
}

@keyframes floatingBubbles {
  0% {
    transform: translate(0, 0) scale(0.8);
  }
  25% {
    transform: translate(-30px, -20px) scale(1.2);
  }
  50% {
    transform: translate(20px, 30px) scale(0.9);
  }
  75% {
    transform: translate(40px, -10px) scale(1.1);
  }
  100% {
    transform: translate(0, 0) scale(0.8);
  }
}

/* 新增多个小圆点动画 */
.animated-bg .circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: circleFloat 15s infinite ease-in-out;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2);
}

@keyframes circleFloat {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 0.8;
  }
  100% {
    transform: translate(var(--tx), var(--ty)) scale(var(--scale));
    opacity: 0;
  }
}

/* 圆点配置 */
.circle:nth-child(1) {
  width: 60px;
  height: 60px;
  top: 20%;
  left: 15%;
  --tx: 100px;
  --ty: -80px;
  --scale: 1.5;
  animation-delay: 0s;
}

.circle:nth-child(2) {
  width: 40px;
  height: 40px;
  top: 70%;
  left: 85%;
  --tx: -120px;
  --ty: 120px;
  --scale: 0.8;
  animation-delay: 3s;
}

.circle:nth-child(3) {
  width: 40px;
  height: 40px;
  top: 60%;
  left: 75%;
  --tx: -120px;
  --ty: 60px;
  --scale: 1.2;
  animation-delay: 6s;
}

.circle:nth-child(4) {
  width: 80px;
  height: 80px;
  top: 45%;
  left: 85%;
  --tx: -150px;
  --ty: 100px;
  --scale: 2;
  animation-delay: 9s;
}

.circle:nth-child(5) {
  width: 30px;
  height: 30px;
  top: 80%;
  left: 70%;
  --tx: -60px;
  --ty: 120px;
  --scale: 1.2;
  animation-delay: 12s;
}

.circle:nth-child(6) {
  width: 50px;
  height: 50px;
  top: 30%;
  left: 85%;
  --tx: -90px;
  --ty: 70px;
  --scale: 1.8;
  animation-delay: 2s;
}

.circle:nth-child(7) {
  width: 40px;
  height: 40px;
  top: 65%;
  left: 5%;
  --tx: -120px;
  --ty: 90px;
  --scale: 1.3;
  animation-delay: 7s;
}

.circle:nth-child(8) {
  width: 35px;
  height: 35px;
  top: 10%;
  left: 90%;
  --tx: -60px;
  --ty: 120px;
  --scale: 1.2;
  animation-delay: 4s;
}

.circle:nth-child(9) {
  width: 70px;
  height: 70px;
  top: 85%;
  left: 25%;
  --tx: -120px;
  --ty: -60px;
  --scale: 2;
  animation-delay: 11s;
}

.circle:nth-child(10) {
  width: 45px;
  height: 45px;
  top: 50%;
  left: 35%;
  --tx: 80px;
  --ty: 100px;
  --scale: 1.2;
  animation-delay: 8s;
}

.bottomTips {
  position: fixed;
  right: 20px;
  bottom: 20px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  text-align: right;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #fff;

.login-container {
  min-height: 100%;
  width: 100%;

  overflow: hidden;

  .login-form {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 480px;
    max-width: 90%;
    padding: 40px;
    margin: 0 auto;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 12px 5px 12px 20px;
    color: #606266;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
    font-size: 18px;
  }

  .title-container {
    position: relative;

    .title-img {
      text-align: center;
      margin-bottom: 20px;

      img {
        width: 100px;
        height: 100px;
        border:1px solid #cecece;
        border-radius: 50%;
        object-fit: cover;
      }
    }

    .title {
      font-size: 28px;
      color: #2c3e50;
      margin: 0px auto 15px auto;
      text-align: center;
      font-weight: bold;
      line-height: 1.4;
    }

    .sub-title {
      font-size: 20px;
      color: #546e7a;
      margin: 0px auto 40px auto;
      text-align: center;
      font-weight: 500;
      line-height: 1.4;
    }

    .set-language {
      color: #fff;
      position: absolute;
      top: 3px;
      font-size: 18px;
      right: 0px;
      cursor: pointer;
    }
  }

  .show-pwd {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 18px;
    color: #606266;
    cursor: pointer;
    user-select: none;
    transition: color 0.3s ease;

    &:hover {
      color: #409eff;
    }
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}
</style>
