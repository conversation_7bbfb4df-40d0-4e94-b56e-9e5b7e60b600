<template>
  <!--页面顶部-->
  <div class="page-header">
    <div class="header-container">
      <!-- 左侧logo区域 -->
      <div class="left-section">
        <div class="web-title flex flex-start">
          <img :src="webConfig.logoUrl" alt="" class="logo">
          <span class="title">{{webConfig.webName}}</span>
        </div>
      </div>
      
      <!-- 中间导航区域 -->
      <div class="center-section">
        <el-menu :default-active="navIndex" class="el-menu-page-header" mode="horizontal" text-color="#666666"
                 active-text-color="#000000" @select="selectNav">
          <el-menu-item index="0" v-if="systemConfig.platformFor==='college'">首页</el-menu-item>
          <el-submenu index="1" v-if="systemConfig.platformFor==='school'">
            <template slot="title">首页</template>
            <template>
              <el-menu-item index="1-0">学校首页</el-menu-item>
              <el-menu-item :index="'1-'+item.collegeId" v-for="(item,index) in navCollegeList">{{ item.name }}
              </el-menu-item>
            </template>
          </el-submenu>`
          <el-submenu index="2">
            <template slot="title">{{ introduceConfig.name }}</template>
            <el-menu-item :index="'2-'+item.id" v-for="(item,index) in introduceConfig.tabs" v-if="item.showNav">
              {{ item.name }}
            </el-menu-item>
          </el-submenu>
          <el-menu-item index="3">课程</el-menu-item>
          <el-menu-item index="9">资源中心</el-menu-item>
          <el-submenu index="7">
            <template slot="title">基地动态</template>
            <el-menu-item index="7-1">基地新闻</el-menu-item>
            <el-menu-item index="7-2">通知公告</el-menu-item>
          </el-submenu>
          <el-submenu index="8">
            <template slot="title">产教融合</template>
            <el-menu-item index="8-1">融合首页</el-menu-item>
            <el-menu-item index="8-2">合作企业</el-menu-item>
            <el-menu-item index="8-3">企业招聘</el-menu-item>
            <el-menu-item index="8-4">优秀学生</el-menu-item>
          </el-submenu>
          <el-menu-item index="4">帮助</el-menu-item>
          <el-menu-item index="5">教学反馈</el-menu-item>
          <el-menu-item index="6" v-if="$route.path==='/'">友情链接</el-menu-item>
        </el-menu>
      </div>
      
      <!-- 右侧用户信息区域 -->
      <div class="right-section flex flex-start">
        <div class="avatar">
          <i class="el-icon-user-solid avatar"></i>
        </div>
        <template v-if="userInfo.hasOwnProperty('userId')">
          <div class="realName cursor-pointer" @click="clickUserAvatar">{{ userInfo.name }}</div>
          <div class="tools flex flex-start">
            <span class="exitBtn cursor-pointer" @click="clickLoginOutBtn">退出</span>
          </div>
        </template>
        <template v-if="!userInfo.hasOwnProperty('userId')">
          <div class="realName cursor-pointer"></div>
          <div class="tools flex flex-start">
            <span class="cursor-pointer realName" @click="clickLoginBtn">登录</span>
          </div>
        </template>
      </div>
    </div>
    <!--回到顶部按钮-->
    <div class="back-top" style="display: none;cursor: pointer" @click="clickBackTopBtn">
      <img src="@/assets/icons/tools-backTop.svg" alt="回到顶部">
    </div>
  </div>
</template>

<script>
import {mapState} from "vuex";
import {UserModel} from "../../model/UserModel";
import {CollegeModel} from "../../model/CollegeModel";
import store from "../../store";
import $ from "jquery"

export default {
  name: "pageHeader",
  computed: {
    ...mapState({
      webConfig: state => state.webConfig,
      isLoginPage: state => state.isLoginPage,
      userInfo: state => state.userInfo,
      systemConfig: state => state.systemConfig,
      introduceConfig: state => state.introduceConfig,
      navCollegeList: state => state.navCollegeList,
    })
  },
  data() {
    return {
      navIndex: '1',

    }
  },
  mounted() {
  },
  async created() {
    // 该页会最先加载，所以在此获取系统各项配置

    // 检测系统配置是否已加载 获取系统配置
    if (!store.state.webConfig.hasOwnProperty("logoUrl")) {
      await store.dispatch("getWebConfig")
      store.dispatch("getSystemConfig").then((systemConfig) => {
        if (systemConfig.platformFor === "school") {// 如果是校级平台，就获取显示到首页的学院列表
          this.getNavCollegeList()
        }
      })
      await store.dispatch("getIntroduceConfig")
    }
  },
  methods: {
    // 获取导航-首页显示的学院列表
    async getNavCollegeList() {
      let navCollegeList = await CollegeModel.getList({
        showIndex: true
      });
      await store.dispatch("setNavCollegeList", navCollegeList)
    },
    // 选择了某个导航
    selectNav(navIndex) {
      console.log(navIndex)
      if (navIndex.indexOf("0") === 0) { // 首页
        this.$router.push("/")
      }
      if (navIndex.indexOf("1-") === 0) { // 首页
        let collegeId = navIndex.replace("1-", "")
        this.$router.push("/")
        store.dispatch("setCollegeId", collegeId)
      }
      if (navIndex.indexOf("2-") === 0) { // 介绍
        let introduceId = navIndex.replace("2-", "")
        this.$router.push("/introduce")
        store.dispatch("setIntroduceId", introduceId)
      }
      if (navIndex.indexOf("3") === 0) {
        this.$router.push("/course")
      }
      if (navIndex.indexOf("4") === 0) {
        this.$router.push("/help")
      }
      if (navIndex.indexOf("5") === 0) {
        this.$router.push("/feedback")
      }
      if (navIndex.indexOf("6") === 0) {
        this.$router.push("/#link")
        // 跳转到第一个错误处
        let firstErrEl = $(".link-container")
        let top = firstErrEl.offset().top - 200
        // 跳转到指定位置
        document.body.scrollTop = top;
        document.documentElement.scrollTop = top;
      }
      if (navIndex.indexOf("7-1") === 0) { // 基地新闻
        this.$router.push("/news")
      }
      if (navIndex.indexOf("7-2") === 0) { // 通知公告
        this.$router.push("/notice")
      }
      if (navIndex.indexOf("8-1") === 0) { // 产教融合首页
        this.$router.push("/industry")
      }
      if (navIndex.indexOf("8-2") === 0) { // 合作企业
        this.$router.push("/industry/enterprise")
      }
      if (navIndex.indexOf("8-3") === 0) { // 企业招聘
        this.$router.push("/industry/recruitment")
      }
      if (navIndex.indexOf("8-4") === 0) { // 优秀学生
        this.$router.push("/industry/students")
      }
      if (navIndex.indexOf("9") === 0) { // 资源中心
        this.$router.push("/resource")
      }
    },
    // 点击回到顶部按钮
    clickBackTopBtn() {
      document.body.scrollTop = 0;
      document.documentElement.scrollTop = 0;
    },
    // 点击退出登录按钮
    clickLoginOutBtn() {
      UserModel.loginOut()
    },
    // 点击用户头像
    clickUserAvatar() {
      this.$router.push({name: "UserInfo"})
    },
    gotoUrl(url) {
      if (this.$route.path !== url) {
        this.$router.push(url)
      }
    },
    // 点击登录按钮
    clickLoginBtn() {
      this.$router.push("/login")
    }
  }
}
</script>
<style>
.el-menu--horizontal > .el-menu-item {
  margin-right: 20px !important;
  font-size: 16px !important;
  letter-spacing: 2px;
}

.el-menu--horizontal > .el-submenu .el-submenu__title {
  font-size: 16px !important;
  letter-spacing: 2px;
}

.el-menu--horizontal .el-menu .el-menu-item {
  font-size: 14px !important;
  text-align: center;
}
</style>
<style scoped lang="less">
@import '../../style/app.less';

.page-header {
  width: 100%;
  background-color: #fff;
  z-index: 1999;
  position: fixed;
  top: 0;
  left: 0;

  .header-container {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 72px;
    box-sizing: border-box;
  }

  .left-section {
    flex: 0 0 auto;
    
    .web-title {
      .logo {
        width: 50px;
        height: 50px;
        margin-right: 10px;
        cursor: pointer;
      }

      span.title {
        font-size: 16px;
        color: #555;
      }
    }
  }

  .center-section {
    flex: 1;
    display: flex;
    justify-content: center;
    
    .el-menu-page-header {
      border-bottom: none !important;
    }
  }

  .right-section {
    flex: 0 0 auto;
    
    .realName {
      margin-right: 15px;
      color: #4093f9;
      font-size: 14px;
    }

    .tools {
      .exitBtn {
        color: #888;
        font-size: 14px;
      }
    }

    div.avatar {
      margin-right: 5px;

      i.avatar {
        color: @remark-color;
        font-size: 22px;
      }
    }
  }
  
  // 响应式设计
  @media (max-width: 1200px) {
    .header-container {
      padding: 0 15px;
    }
    
    .left-section .web-title span.title {
      display: none; // 小屏幕隐藏网站标题文字
    }
  }
  
  @media (max-width: 768px) {
    .header-container {
      padding: 0 10px;
      height: 60px;
    }
    
    .left-section .web-title .logo {
      width: 40px;
      height: 40px;
    }
    
    .center-section {
      .el-menu-page-header {
        .el-menu-item, .el-submenu__title {
          padding: 0 10px !important;
          font-size: 14px !important;
        }
      }
    }
    
    .right-section {
      .realName, .tools .exitBtn {
        font-size: 12px;
      }
      
      div.avatar i.avatar {
        font-size: 18px;
      }
    }
  }
}

.back-top {
  position: fixed;
  bottom: 40px;
  right: 40px;
  z-index: 99999;

  img {
    width: 40px;
    height: 40px;
  }
}
</style>
