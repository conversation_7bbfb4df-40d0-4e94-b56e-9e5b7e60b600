<template>
  <div class="page-footer">
    <div class="info-container content-container">
      <div class="infos">
        <img :src="webConfig.logoUrl" alt="" class="logo">
        <p>{{webConfig.webName}}</p>
        <span>技术支持:成都智云鸿道信息技术有限公司</span>
        <span>推荐使用:谷歌浏览器、火狐浏览器</span>
        <span>{{webConfig.beiAnText}}</span>
      </div>
    </div>
  </div>
</template>

<script>
import {mapState} from "vuex";

export default {
  name: "pageFooter",
  computed: {
    ...mapState({
      webConfig: state => state.webConfig,
    })
  },
}
</script>

<style scoped lang="less">
@import '../../style/app.less';

.page-footer {
  background-color: #2d2d2d;
  padding: 5px 0px 12px 0px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;

  .info-container {
    .logo{
      width: 70px;
      height: 70px;
      cursor: pointer;
      display: none;
    }
    .infos {
      text-align: center;
      color: @remark-color;
      font-size: 14px;

      span {
        margin-right: 10px;
      }
    }
  }
}
</style>
