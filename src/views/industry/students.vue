<template>
  <div class="students-page">
    <div class="content-container">
      <div class="page-header">
        <div class="header-bg">
          <div class="bg-circle bg-circle-1"></div>
          <div class="bg-circle bg-circle-2"></div>
          <div class="bg-circle bg-circle-3"></div>
        </div>
        <div class="header-content">
          <div class="title-wrapper">
            <div class="title-icon">
              <font-awesome-icon icon="user-graduate" />
            </div>
            <h1 class="page-title">优秀学生</h1>
          </div>
          <div class="page-subtitle">榜样力量，传承精神，激励前行成长</div>
<!--          <div class="header-stats">-->
<!--            <div class="stat-item">-->
<!--              <div class="stat-number">500+</div>-->
<!--              <div class="stat-label">优秀学生</div>-->
<!--            </div>-->
<!--            <div class="stat-divider"></div>-->
<!--            <div class="stat-item">-->
<!--              <div class="stat-number">95%</div>-->
<!--              <div class="stat-label">满意度</div>-->
<!--            </div>-->
<!--            <div class="stat-divider"></div>-->
<!--            <div class="stat-item">-->
<!--              <div class="stat-number">100+</div>-->
<!--              <div class="stat-label">获奖项目</div>-->
<!--            </div>-->
<!--          </div>-->
        </div>
      </div>

      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索学生姓名"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-tabs">
          <el-radio-group v-model="selectedCategory" @change="handleCategoryChange">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="outstanding">优秀毕业生</el-radio-button>
            <el-radio-button label="scholarship">奖学金获得者</el-radio-button>
            <el-radio-button label="competition">竞赛获奖者</el-radio-button>
            <el-radio-button label="innovation">创新创业</el-radio-button>
            <el-radio-button label="academic">学术研究</el-radio-button>
            <el-radio-button label="other">其它</el-radio-button>
          </el-radio-group>
        </div>

      </div>

      <!-- 学生列表 -->
      <div class="students-list-section" v-loading="loading">
        <div class="students-grid" v-if="studentList.length > 0">
          <div class="student-card" v-for="student in studentList" :key="student.studentId" @click="openStudentDetail(student.studentId)">
            <div class="student-photo">
              <img :src="student.photo" :alt="student.name" />
              <div class="student-category">{{ student.categoryName }}</div>
            </div>
            
            <div class="student-info">
              <h3 class="student-name">{{ student.name }}</h3>
              <p class="student-major">{{ student.major }} · {{ student.graduationYear }}届</p>
              <div class="student-details">
                <p class="student-company">
                  <font-awesome-icon :icon="['fas', 'building']" />
                  <span>{{ student.company }}</span>
                </p>
                <p class="student-position">
                  <font-awesome-icon :icon="['fas', 'briefcase']" class="position-icon" />
                  <span>{{ student.position }}</span>
                </p>
              </div>
              
              <div class="student-achievements">
                <div class="achievement-item" v-for="achievement in student.achievements" :key="achievement.id">
                  <el-tag :type="getAchievementType(achievement.type)" size="small">
                    {{ achievement.title }}
                  </el-tag>
                </div>
              </div>
              
              <div class="student-stats">
                <div class="stat-item">
                  <font-awesome-icon :icon="['fas', 'laptop-code']" class="stat-icon" />
                  <span class="stat-label">项目经验:</span>
                  <span class="stat-value">{{ student.projectCount }}个</span>
                </div>
              </div>
            </div>
            
            <div class="student-footer">
              <div class="student-quote">
                <font-awesome-icon :icon="['fas', 'quote-left']" />
                <span>{{ student.quote }}</span>
                <font-awesome-icon :icon="['fas', 'quote-right']" />
              </div>
            </div>
          </div>
        </div>
        
        <!-- 空数据状态 -->
        <div class="empty-state" v-else-if="!loading">
          <font-awesome-icon :icon="['fas', 'user-graduate']" />
          <p>暂无学生数据</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    

  </div>
</template>

<script>
import {IndustryModel} from '@/model/IndustryModel'

export default {
  name: 'StudentsPage',
  data() {
    return {
      // 筛选条件
      selectedCategory: '',
      selectedMajor: '',
      selectedYear: '',
      searchKeyword: '',
      
      // 分页
      currentPage: 0,
      pageSize: 12,
      total: 0,
      
      // 数据
      studentList: [],
      loading: false
    }
  },
  
  created() {
    this.fetchStudentList()
  },
  
  methods: {
    // 获取学生列表
    async fetchStudentList() {
      this.loading = true
      try {
        const params = {}
        
        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }
        
        if (this.searchKeyword) {
          params.name = { $regex: this.searchKeyword, $options: 'i' }
        }
        
        const response = await IndustryModel.getStudentPageList(this.currentPage, this.pageSize, params)
        console.log(response)
        
        this.studentList = response.content
        this.total = response.totalElements
      } catch (error) {
        this.$message.error('获取学生列表失败')
        console.error('获取学生列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 0
      this.fetchStudentList()
    },
    
    // 类别筛选
    handleCategoryChange() {
      this.currentPage = 0
      this.fetchStudentList()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchStudentList()
      // 滚动到顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    
    // 跳转到学生详情页
    openStudentDetail(id) {
      window.open(`/industry/student/${id}`, '_blank')
    },
    
    // 获取成就类型样式
    getAchievementType(type) {
      const typeMap = {
        'scholarship': 'warning',
        'competition': 'success',
        'honor': 'primary',
        'innovation': 'danger',
        'academic': 'info'
      }
      return typeMap[type] || 'info'
    }
  }
}
</script>

<style lang="less" scoped>


.students-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .page-header {
    position: relative;
    background: linear-gradient(135deg, #27ae60 0%, #16a085 50%, #2980b9 100%);
    border-radius: 24px;
    margin-bottom: 40px;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 20px 40px rgba(39, 174, 96, 0.3);
    
    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      
      .bg-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        animation: float 6s ease-in-out infinite;
        
        &.bg-circle-1 {
          width: 120px;
          height: 120px;
          top: -60px;
          right: -60px;
          animation-delay: 0s;
        }
        
        &.bg-circle-2 {
          width: 80px;
          height: 80px;
          bottom: -40px;
          left: 20%;
          animation-delay: 2s;
        }
        
        &.bg-circle-3 {
          width: 100px;
          height: 100px;
          top: 30%;
          left: -50px;
          animation-delay: 4s;
        }
      }
    }
    
    .header-content {
      position: relative;
      z-index: 2;
      text-align: center;
      color: white;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        animation: slideInDown 1s ease-out;
        
        .title-icon {
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          backdrop-filter: blur(10px);
          border: 2px solid rgba(255, 255, 255, 0.3);
          
          svg {
            font-size: 24px;
            color: white;
          }
        }
        
        .page-title {
          font-size: 36px;
          font-weight: 700;
          margin: 0;
          background: linear-gradient(45deg, #ffffff, #f0f8ff);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
      
      .page-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 30px;
        font-weight: 400;
        letter-spacing: 0.5px;
        animation: slideInUp 1s ease-out 0.3s both;
      }
      
      .header-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0;
        animation: fadeInUp 1s ease-out 0.6s both;
        
        .stat-item {
          text-align: center;
          padding: 0 30px;
          
          .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          
          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            letter-spacing: 0.5px;
          }
        }
        
        .stat-divider {
          width: 1px;
          height: 40px;
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  .filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .filter-tabs {
      margin-bottom: 20px;
      text-align: center;
      
      ::v-deep .el-radio-group {
        .el-radio-button__inner {
          padding: 12px 20px;
          border-radius: 20px;
          margin: 0 5px;
          border: 1px solid #dcdfe6;
        }
        
        .el-radio-button:first-child .el-radio-button__inner {
          border-radius: 20px;
        }
        
        .el-radio-button:last-child .el-radio-button__inner {
          border-radius: 20px;
        }
        
        .el-radio-button__orig-radio:checked + .el-radio-button__inner {
          background-color: #4093f9;
          border-color: #4093f9;
          color: white;
        }
      }
    }
    
    .search-area {
      margin-bottom: 20px;
      text-align: center;
      
      .search-input {
        max-width: 500px;
        
        ::v-deep .el-input__inner {
          border-radius: 25px 0 0 25px;
          height: 45px;
          line-height: 45px;
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 25px 25px 0;
          
          .el-button {
            border-radius: 0 25px 25px 0;
            height: 45px;
            padding: 0 20px;
            background-color: #4093f9;
            border-color: #4093f9;
            color: white;
            
            &:hover {
              background-color: darken(#4093f9, 10%);
              border-color: darken(#4093f9, 10%);
            }
          }
        }
      }
    }
    
    .filter-options {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
      
      .el-select {
        min-width: 180px;
      }
    }
  }
  
  .students-list-section {
    min-height: 400px;
    
    .students-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 25px;
      
      .student-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .student-photo {
          position: relative;
          text-align: center;
          padding: 25px 25px 15px;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          
          img {
            width: 120px;
            height: 120px;
            object-fit: cover;
            border-radius: 50%;
            border: 4px solid white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
          
          .student-category {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(64, 147, 249, 0.9);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
          }
        }
        
        .student-info {
          padding: 20px 25px;
          
          .student-name {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
            text-align: center;
          }
          
          .student-major {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
            text-align: center;
          }
          
          .student-company {
            font-size: 15px;
            color: #4093f9;
            margin-bottom: 8px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            font-weight: 500;
          }
          
          .student-position {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            
            .position-icon {
              color: #4093f9;
              font-size: 12px;
            }
          }
          
          .student-achievements {
            margin-bottom: 15px;
            text-align: center;
            
            .achievement-item {
              display: inline-block;
              margin: 3px;
            }
          }
          
          .student-stats {
            display: flex;
            justify-content: space-around;
            padding: 15px 0;
            border-top: 1px solid #f0f0f0;
            border-bottom: 1px solid #f0f0f0;
            
            .stat-item {
              text-align: center;
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 5px;
              
              .stat-icon {
                color: #4093f9;
                font-size: 18px;
                margin-bottom: 5px;
              }
              
              .stat-label {
                font-size: 12px;
                color: #999;
                display: block;
              }
              
              .stat-value {
                font-size: 16px;
                font-weight: bold;
                color: #4093f9;
              }
            }
          }
        }
        
        .student-footer {
          padding: 20px 25px;
          background: #f8f9fa;
          
          .student-quote {
            text-align: center;
            font-style: italic;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
            
            svg {
              color: #4093f9;
              margin-right: 8px;
              margin-left: 8px;
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      
      svg {
        font-size: 64px;
        margin-bottom: 16px;
        color: #ccc;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
  
  .pagination-container {
    text-align: center;
    margin-top: 40px;
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .students-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
    
    .page-header {
      min-height: 220px;
      border-radius: 16px;
      margin-bottom: 30px;
      
      .header-content {
        .title-wrapper {
          flex-direction: column;
          
          .title-icon {
            margin-right: 0;
            margin-bottom: 15px;
            width: 50px;
            height: 50px;
            
            svg {
              font-size: 20px;
            }
          }
          
          .page-title {
            font-size: 28px;
          }
        }
        
        .page-subtitle {
          font-size: 16px;
          margin-bottom: 25px;
        }
        
        .header-stats {
          .stat-item {
            padding: 0 20px;
            
            .stat-number {
              font-size: 24px;
            }
            
            .stat-label {
              font-size: 12px;
            }
          }
        }
      }
    }
    
    .filter-section {
      padding: 15px;
      
      .filter-tabs {
        ::v-deep .el-radio-group {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 5px;
          
          .el-radio-button__inner {
            padding: 8px 15px;
            font-size: 14px;
          }
        }
      }
      
      .filter-options {
        flex-direction: column;
        align-items: center;
        
        .el-select {
          width: 100%;
          max-width: 300px;
        }
      }
    }
    
    .students-list-section {
      .students-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        
        .student-card {
          .student-photo {
            padding: 20px 20px 12px;
            
            img {
              width: 100px;
              height: 100px;
            }
          }
          
          .student-info {
            padding: 15px 20px;
            
            .student-name {
              font-size: 18px;
            }
          }
          
          .student-footer {
            padding: 15px 20px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .students-page {
    .page-header {
      min-height: 180px;
      border-radius: 12px;
      
      .header-content {
        .title-wrapper {
          .title-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 10px;
            
            svg {
              font-size: 16px;
            }
          }
          
          .page-title {
            font-size: 24px;
          }
        }
        
        .page-subtitle {
          font-size: 14px;
          margin-bottom: 20px;
        }
        
        .header-stats {
          flex-direction: column;
          gap: 15px;
          
          .stat-item {
            padding: 0;
            
            .stat-number {
              font-size: 20px;
            }
            
            .stat-label {
              font-size: 11px;
            }
          }
          
          .stat-divider {
            display: none;
          }
        }
      }
    }
    
    .filter-section {
      .filter-tabs {
        ::v-deep .el-radio-group {
          .el-radio-button__inner {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }
    
    .students-list-section {
      .students-grid {
        grid-template-columns: 1fr;
        
        .student-card {
          .student-photo {
            padding: 15px 15px 10px;
            
            img {
              width: 80px;
              height: 80px;
            }
            
            .student-category {
              font-size: 11px;
              padding: 4px 10px;
            }
          }
          
          .student-info {
            padding: 12px 15px;
            
            .student-name {
              font-size: 16px;
            }
            
            .student-major, .student-position {
              font-size: 13px;
            }
            
            .student-company {
              font-size: 14px;
            }
            
            .student-stats {
              .stat-item {
                .stat-icon {
                  font-size: 16px;
                }
                
                .stat-value {
                  font-size: 14px;
                }
              }
            }
          }
          
          .student-footer {
            padding: 12px 15px;
            
            .student-quote {
              font-size: 13px;
            }
          }
        }
      }
    }
  }
}
</style>