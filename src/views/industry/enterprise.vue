<template>
  <div class="enterprise-list-page">
    <div class="content-container">
      <div class="page-header">
        <div class="header-bg">
          <div class="bg-circle bg-circle-1"></div>
          <div class="bg-circle bg-circle-2"></div>
          <div class="bg-circle bg-circle-3"></div>
        </div>
        <div class="header-content">
          <div class="title-wrapper">
            <div class="title-icon">
              <font-awesome-icon icon="handshake" />
            </div>
            <h1 class="page-title">合作企业</h1>
          </div>
          <div class="page-subtitle">深度合作，共创未来，携手共赢</div>
        </div>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索企业名称或关键词"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
          </el-input>
        </div>
        
        <div class="filter-area">
          <el-select v-model="selectedType" placeholder="企业类型" @change="handleTypeChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="互联网" value="internet"></el-option>
            <el-option label="金融" value="finance"></el-option>
            <el-option label="制造业" value="manufacturing"></el-option>
            <el-option label="教育" value="education"></el-option>
            <el-option label="医疗" value="healthcare"></el-option>
            <el-option label="其他" value="other"></el-option>
          </el-select>
          
          <el-select v-model="selectedScale" placeholder="企业规模" @change="handleScaleChange">
            <el-option label="全部" value=""></el-option>
            <el-option label="大型企业" value="large"></el-option>
            <el-option label="中型企业" value="medium"></el-option>
            <el-option label="小型企业" value="small"></el-option>
          </el-select>
        </div>
      </div>

      <!-- 企业列表 -->
      <div class="enterprise-list-section" v-loading="loading">
        <div class="enterprise-grid" v-if="enterpriseList.length > 0">
          <div class="enterprise-card" v-for="enterprise in enterpriseList" :key="enterprise.enterpriseId" @click="goToDetail(enterprise.enterpriseId)">
            <div class="enterprise-header">
              <div class="enterprise-logo">
                <img :src="enterprise.logo" :alt="enterprise.name" v-if="enterprise.logo" />
                <div v-else class="logo-placeholder">
                  <font-awesome-icon icon="building" />
                </div>
              </div>
              <div class="enterprise-basic">
                <h3 class="enterprise-name">{{ enterprise.name }}</h3>
                <div class="enterprise-meta">
                  <div class="enterprise-type">
                    <font-awesome-icon icon="industry" />
                    <span>{{ enterprise.typeName }}</span>
                  </div>
                  <div class="enterprise-location">
                    <font-awesome-icon icon="map-marker-alt" />
                    <span>{{ enterprise.location }}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="enterprise-content">
              <p class="enterprise-description">{{ enterprise.description }}</p>
              
              <div class="enterprise-info">
                <div class="info-item">
                  <div class="info-icon">
                    <font-awesome-icon icon="calendar-alt" />
                  </div>
                  <div class="info-content">
                    <span class="info-label">成立时间</span>
                    <span class="info-value">{{ enterprise.foundYear }}</span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <font-awesome-icon icon="users" />
                  </div>
                  <div class="info-content">
                    <span class="info-label">企业规模</span>
                    <span class="info-value">{{ enterprise.scaleName }}</span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <font-awesome-icon icon="project-diagram" />
                  </div>
                  <div class="info-content">
                    <span class="info-label">合作项目</span>
                    <span class="info-value">{{ enterprise.projectCount }}个</span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <font-awesome-icon icon="handshake" />
                  </div>
                  <div class="info-content">
                    <span class="info-label">合作时长</span>
                    <span class="info-value">{{ enterprise.cooperationYears }}年</span>
                  </div>
                </div>
                <div class="info-item">
                  <div class="info-icon">
                    <font-awesome-icon icon="graduation-cap" />
                  </div>
                  <div class="info-content">
                    <span class="info-label">培养学生</span>
                    <span class="info-value">{{ enterprise.studentCount }}人</span>
                  </div>
                </div>
              </div>
              
              <div class="enterprise-tags" v-if="enterprise.tags && enterprise.tags.length > 0">
                <el-tag v-for="tag in enterprise.tags" :key="tag" size="small" type="info">
                  <font-awesome-icon icon="tag" />
                  {{ tag }}
                </el-tag>
              </div>
            </div>
            
            <div class="enterprise-footer">
              <el-button type="primary" size="small" class="detail-btn">
                <font-awesome-icon icon="eye" />
                查看详情
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 空数据状态 -->
        <div class="empty-state" v-else-if="!loading">
          <font-awesome-icon icon="building" />
          <p>暂无企业数据</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    

  </div>
</template>

<script>
import {IndustryModel} from '@/model/IndustryModel'

export default {
  name: 'EnterpriseListPage',
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedType: '',
      selectedScale: '',
      selectedLocation: '',
      
      // 分页
      currentPage: 0,
      pageSize: 12,
      total: 0,
      
      // 数据
      enterpriseList: [],
      loading: false
    }
  },
  
  created() {
    this.fetchEnterpriseList()
  },
  
  methods: {
    // 获取企业列表
    async fetchEnterpriseList() {
      this.loading = true
      try {
        const params = {}
        
        if (this.searchKeyword) {
          params.name = { $regex: this.searchKeyword }
        }
        
        if (this.selectedType) {
          params.type = this.selectedType
        }
        
        if (this.selectedScale) {
          params.scale = this.selectedScale
        }
        
        const response = await IndustryModel.getEnterprisePageList(this.currentPage, this.pageSize, params)
        console.log(response)
        
        this.enterpriseList = response.content
        this.total = response.totalElements
      } catch (error) {
        this.$message.error('获取企业列表失败')
        console.error('获取企业列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 搜索
    handleSearch() {
      this.currentPage = 0
      this.fetchEnterpriseList()
    },
    
    // 类型筛选
    handleTypeChange() {
      this.currentPage = 0
      this.fetchEnterpriseList()
    },
    
    // 规模筛选
    handleScaleChange() {
      this.currentPage = 0
      this.fetchEnterpriseList()
    },
    
    // 地区筛选
    handleLocationChange() {
      this.currentPage = 0
      this.fetchEnterpriseList()
    },
    
    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchEnterpriseList()
      // 滚动到顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },
    
    // 跳转到企业详情页
    goToDetail(id) {
      window.open(`/industry/enterprise/${id}`, '_blank')
    }
  }
}
</script>

<style lang="less" scoped>


.enterprise-list-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .page-header {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24px;
    margin-bottom: 40px;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    
    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      
      .bg-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.1);
        animation: float 6s ease-in-out infinite;
        
        &.bg-circle-1 {
          width: 120px;
          height: 120px;
          top: -60px;
          right: -60px;
          animation-delay: 0s;
        }
        
        &.bg-circle-2 {
          width: 80px;
          height: 80px;
          bottom: -40px;
          left: 20%;
          animation-delay: 2s;
        }
        
        &.bg-circle-3 {
          width: 100px;
          height: 100px;
          top: 30%;
          left: -50px;
          animation-delay: 4s;
        }
      }
    }
    
    .header-content {
      position: relative;
      z-index: 2;
      text-align: center;
      color: white;
      
      .title-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        animation: slideInDown 1s ease-out;
        
        .title-icon {
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          backdrop-filter: blur(10px);
          border: 2px solid rgba(255, 255, 255, 0.3);
          
          svg {
            font-size: 24px;
            color: white;
          }
        }
        
        .page-title {
          font-size: 36px;
          font-weight: 700;
          margin: 0;
          background: linear-gradient(45deg, #ffffff, #f0f8ff);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }
      
      .page-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 30px;
        font-weight: 400;
        letter-spacing: 0.5px;
        animation: slideInUp 1s ease-out 0.3s both;
      }
      
      .header-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0;
        animation: fadeInUp 1s ease-out 0.6s both;
        
        .stat-item {
          text-align: center;
          padding: 0 30px;
          
          .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
          
          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            letter-spacing: 0.5px;
          }
        }
        
        .stat-divider {
          width: 1px;
          height: 40px;
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }
  
  .search-filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .search-area {
      margin-bottom: 20px;
      text-align: center;
      
      .search-input {
        max-width: 500px;
        
        ::v-deep .el-input__inner {
          border-radius: 25px 0 0 25px;
          height: 45px;
          line-height: 45px;
        }
        
        ::v-deep .el-input-group__append {
          border-radius: 0 25px 25px 0;
          
          .el-button {
            border-radius: 0 25px 25px 0;
            height: 45px;
            padding: 0 20px;
            background-color: #4093f9;
            border-color: #4093f9;
            color: white;
            
            &:hover {
              background-color: darken(#4093f9, 10%);
              border-color: darken(#4093f9, 10%);
            }
          }
        }
      }
    }
    
    .filter-area {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
      
      .el-select {
        min-width: 150px;
      }
    }
  }
  
  .enterprise-list-section {
    min-height: 400px;
    
    .enterprise-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 25px;
      
      .enterprise-card {
        background: white;
        border-radius: 16px;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        border: 1px solid rgba(0, 0, 0, 0.04);
        
        &:hover {
          transform: translateY(-8px);
          box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
          border-color: rgba(64, 147, 249, 0.2);
        }
        
        .enterprise-header {
          display: flex;
          align-items: center;
          padding: 24px;
          background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
          border-bottom: 1px solid rgba(0, 0, 0, 0.06);
          
          .enterprise-logo {
            width: 72px;
            height: 72px;
            margin-right: 20px;
            flex-shrink: 0;
            position: relative;
            
            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              border-radius: 12px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }
            
            .logo-placeholder {
              width: 100%;
              height: 100%;
              background: linear-gradient(135deg, #4093f9, #66b3ff);
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-size: 28px;
              box-shadow: 0 2px 8px rgba(64, 147, 249, 0.3);
            }
          }
          
          .enterprise-basic {
            flex: 1;
            
            .enterprise-name {
              font-size: 20px;
              font-weight: 600;
              color: #1a1a1a;
              margin-bottom: 12px;
              line-height: 1.3;
            }
            
            .enterprise-meta {
              display: flex;
              align-items: center;
              gap: 20px;
              
              .enterprise-type {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 14px;
                color: #4093f9;
                font-weight: 500;
                
                svg {
                  font-size: 14px;
                }
              }
              
              .enterprise-location {
                display: flex;
                align-items: center;
                gap: 6px;
                font-size: 14px;
                color: #666;
                
                svg {
                  color: #ff6b6b;
                  font-size: 14px;
                }
              }
            }
          }
        }
        
        .enterprise-content {
          padding: 24px;
          
          .enterprise-description {
            font-size: 14px;
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-align: justify;
          }
          
          .enterprise-info {
            margin-bottom: 20px;
            background: #f8f9fb;
            border-radius: 12px;
            padding: 16px;
            
            .info-item {
              display: flex;
              align-items: center;
              margin-bottom: 12px;
              
              &:last-child {
                margin-bottom: 0;
              }
              
              .info-icon {
                width: 32px;
                height: 32px;
                background: linear-gradient(135deg, #4093f9, #66b3ff);
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
                flex-shrink: 0;
                
                svg {
                  color: white;
                  font-size: 14px;
                }
              }
              
              .info-content {
                flex: 1;
                display: flex;
                justify-content: space-between;
                align-items: center;
                
                .info-label {
                  color: #666;
                  font-size: 14px;
                }
                
                .info-value {
                  color: #1a1a1a;
                  font-weight: 600;
                  font-size: 14px;
                }
              }
            }
          }
          
          .enterprise-tags {
            .el-tag {
              margin-right: 8px;
              margin-bottom: 8px;
              border-radius: 20px;
              padding: 4px 12px;
              font-weight: 500;
              border: none;
              background: rgba(64, 147, 249, 0.1);
              color: #4093f9;
              
              svg {
                margin-right: 4px;
                font-size: 12px;
              }
            }
          }
        }
        
        .enterprise-footer {
          display: flex;
          justify-content: center;
          align-items: center;
          padding: 20px 24px;
          background: linear-gradient(135deg, #f8f9fb 0%, #ffffff 100%);
          border-top: 1px solid rgba(0, 0, 0, 0.06);
          
          .detail-btn {
            border-radius: 20px;
            padding: 10px 20px;
            font-weight: 500;
            transition: all 0.3s ease;
            
            svg {
              margin-right: 6px;
            }
            
            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(64, 147, 249, 0.3);
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;
      
      svg {
        font-size: 64px;
        margin-bottom: 16px;
      }
      
      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
  
  .pagination-container {
    text-align: center;
    margin-top: 40px;
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .enterprise-list-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
    
    .page-header {
      min-height: 220px;
      border-radius: 16px;
      margin-bottom: 30px;
      
      .header-content {
        .title-wrapper {
          flex-direction: column;
          
          .title-icon {
            margin-right: 0;
            margin-bottom: 15px;
            width: 50px;
            height: 50px;
            
            svg {
              font-size: 20px;
            }
          }
          
          .page-title {
            font-size: 28px;
          }
        }
        
        .page-subtitle {
          font-size: 16px;
          margin-bottom: 25px;
        }
        
        .header-stats {
          .stat-item {
            padding: 0 20px;
            
            .stat-number {
              font-size: 24px;
            }
            
            .stat-label {
              font-size: 12px;
            }
          }
        }
      }
    }
    
    .search-filter-section {
      padding: 15px;
      
      .search-area {
        .search-input {
          max-width: 100%;
        }
      }
      
      .filter-area {
        flex-direction: column;
        align-items: center;
        
        .el-select {
          width: 100%;
          max-width: 300px;
        }
      }
    }
    
    .enterprise-list-section {
      .enterprise-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        
        .enterprise-card {
          .enterprise-header {
            flex-direction: column;
            text-align: center;
            
            .enterprise-logo {
              margin-right: 0;
              margin-bottom: 15px;
            }
          }
          
          .enterprise-footer {
            flex-direction: column;
            gap: 15px;
            text-align: center;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .enterprise-list-page {
    .page-header {
      min-height: 180px;
      border-radius: 12px;
      
      .header-content {
        .title-wrapper {
          .title-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 10px;
            
            svg {
              font-size: 16px;
            }
          }
          
          .page-title {
            font-size: 24px;
          }
        }
        
        .page-subtitle {
          font-size: 14px;
          margin-bottom: 20px;
        }
        
        .header-stats {
          flex-direction: column;
          gap: 15px;
          
          .stat-item {
            padding: 0;
            
            .stat-number {
              font-size: 20px;
            }
            
            .stat-label {
              font-size: 11px;
            }
          }
          
          .stat-divider {
            display: none;
          }
        }
      }
    }
    
    .enterprise-list-section {
      .enterprise-grid {
        .enterprise-card {
          .enterprise-header {
            padding: 15px;
            
            .enterprise-logo {
              width: 60px;
              height: 60px;
            }
            
            .enterprise-basic {
              .enterprise-name {
                font-size: 18px;
              }
            }
          }
          
          .enterprise-content {
            padding: 15px;
            
            .enterprise-description {
              font-size: 13px;
            }
            
            .enterprise-info {
              .info-item {
                font-size: 13px;
              }
            }
          }
          
          .enterprise-footer {
            padding: 12px 15px;
          }
        }
      }
    }
  }
}
</style>