<template>
  <div class="industry-page">
    <div class="content-container">
      <!-- 轮播图区域 -->
      <div class="banner-section">
        <el-carousel :interval="4000" type="card" height="350px" v-if="bannerList.length > 0">
          <el-carousel-item v-for="banner in bannerList" :key="banner.id">
            <div class="banner-item" @click="goToBannerLink(banner.link)">
              <img :src="banner.image" :alt="banner.title" />
              <div class="banner-overlay">
                <h3 class="banner-title">{{ banner.title }}</h3>
                <p class="banner-description">{{ banner.description }}</p>
              </div>
            </div>
          </el-carousel-item>
        </el-carousel>
      </div>

      <!-- 数据统计区域 -->
      <div class="stats-section">
        <div class="stats-container">
          <div class="stat-item">
            <div class="stat-icon">
              <font-awesome-icon icon="handshake" />
            </div>
            <div class="stat-number">{{ stats.enterpriseCount }}</div>
            <div class="stat-label">合作企业</div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <font-awesome-icon icon="project-diagram" />
            </div>
            <div class="stat-number">{{ stats.projectCount }}</div>
            <div class="stat-label">合作项目</div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <font-awesome-icon icon="graduation-cap" />
            </div>
            <div class="stat-number">{{ stats.studentCount }}</div>
            <div class="stat-label">优秀学生</div>
          </div>
          <div class="stat-item">
            <div class="stat-icon">
              <font-awesome-icon icon="briefcase" />
            </div>
            <div class="stat-number">{{ stats.jobCount }}</div>
            <div class="stat-label">招聘岗位</div>
          </div>

        </div>
      </div>

      <!-- 合作企业展示区域 -->
      <div class="enterprise-section">
        <div class="section-header enterprise-header">
          <div class="header-bg">
            <div class="bg-circle bg-circle-1"></div>
            <div class="bg-circle bg-circle-2"></div>
          </div>
          <div class="header-content">
            <div class="title-wrapper">
              <div class="title-icon">
                <font-awesome-icon icon="handshake" />
              </div>
              <h2 class="section-title">合作企业</h2>
            </div>
            <p class="section-subtitle">深度合作，共创未来，携手共赢</p>
          </div>
        </div>

        <div class="enterprise-grid" v-loading="enterpriseLoading">
          <div class="enterprise-card" v-for="enterprise in displayEnterprises" :key="enterprise.enterpriseId"
            @click="goToEnterpriseDetail(enterprise.enterpriseId)">
            <div class="enterprise-logo">
              <img :src="enterprise.logo" :alt="enterprise.name" />
            </div>
            <div class="enterprise-info">
              <h3 class="enterprise-name">{{ enterprise.name }}</h3>
              <p class="enterprise-type">{{ enterprise.typeName }}</p>
              <div class="enterprise-tags">
                <el-tag v-for="tag in enterprise.tags" :key="tag" size="small">{{ tag }}</el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部查看更多按钮 -->
        <div class="section-footer">
          <el-button type="primary" plain size="large" @click="goToEnterpriseList" class="footer-more-button">
            <font-awesome-icon icon="building" class="button-icon" />
            查看更多合作企业
            <font-awesome-icon icon="arrow-right" class="button-arrow" />
          </el-button>
        </div>
      </div>

      <!-- 优秀学生展示区域 -->
      <div class="students-section">
        <div class="section-header students-header">
          <div class="header-bg">
            <div class="bg-circle bg-circle-1"></div>
            <div class="bg-circle bg-circle-2"></div>
          </div>
          <div class="header-content">
            <div class="title-wrapper">
              <div class="title-icon">
                <font-awesome-icon icon="graduation-cap" />
              </div>
              <h2 class="section-title">优秀学生</h2>
            </div>
            <p class="section-subtitle">榜样力量，传承精神，激励前行成长</p>
          </div>
        </div>

        <div class="students-grid" v-loading="studentsLoading">
          <div class="student-card" v-for="student in displayStudents" :key="student.studentId"
            @click="goToStudentDetail(student.studentId)">
            <div class="student-photo">
              <img :src="student.photo" :alt="student.name" />
            </div>
            <div class="student-info">
              <h3 class="student-name">{{ student.name }}</h3>
              <p class="student-major">{{ student.major }}</p>
              <p class="student-company">就职于：{{ student.company }}</p>
              <div class="student-achievements">
                <el-tag v-for="achievement in student.achievements" :key="achievement.id" type="success" size="small">
                  {{ achievement.title }}
                </el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部查看更多按钮 -->
        <div class="section-footer">
          <el-button type="primary" plain size="large" @click="goToStudentsList" class="footer-more-button">
            <font-awesome-icon icon="user-graduate" class="button-icon" />
            查看更多优秀学生
            <font-awesome-icon icon="arrow-right" class="button-arrow" />
          </el-button>
        </div>
      </div>

      <!-- 招聘信息区域 -->
      <div class="recruitment-section">
        <div class="section-header recruitment-header">
          <div class="header-bg">
            <div class="bg-circle bg-circle-1"></div>
            <div class="bg-circle bg-circle-2"></div>
          </div>
          <div class="header-content">
            <div class="title-wrapper">
              <div class="title-icon">
                <font-awesome-icon icon="briefcase" />
              </div>
              <h2 class="section-title">最新招聘</h2>
            </div>
            <p class="section-subtitle">优质岗位，成就职业梦想，共创美好未来</p>
          </div>
        </div>

        <div class="recruitment-list" v-loading="jobsLoading">
          <div class="recruitment-item" v-for="job in displayJobs" :key="job.jobId" @click="goToJobDetail(job.jobId)">
            <div class="job-info">
              <h3 class="job-title">{{ job.title }}</h3>
              <p class="job-company">{{ job.companyName }}</p>
              <div class="job-details">
                <span class="job-salary">{{ job.salary }}</span>
                <span class="job-location">{{ job.location }}</span>
                <span class="job-experience">{{ job.experience }}</span>
              </div>
            </div>
            <div class="job-status">
              <el-tag :type="job.urgent ? 'danger' : 'primary'" size="small">
                {{ job.urgent ? '急招' : '在招' }}
              </el-tag>
              <div class="job-date">{{ formatDate(job.publishTime) }}</div>
            </div>
          </div>
        </div>

        <!-- 底部查看更多按钮 -->
        <div class="section-footer">
          <el-button type="primary" plain size="large" @click="goToRecruitmentList" class="footer-more-button">
            <font-awesome-icon icon="search" class="button-icon" />
            查看更多招聘信息
            <font-awesome-icon icon="arrow-right" class="button-arrow" />
          </el-button>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import { IndustryModel } from '@/model/IndustryModel'

export default {
  name: 'IndustryPage',
  data() {
    return {
      // 轮播图数据
      bannerList: [],

      // 统计数据
      stats: {
        enterpriseCount: 0,
        studentCount: 0,
        jobCount: 0,
        projectCount: 0
      },

      // 企业数据
      displayEnterprises: [],
      enterpriseLoading: false,

      // 学生数据
      displayStudents: [],
      studentsLoading: false,

      // 招聘数据
      displayJobs: [],
      jobsLoading: false
    }
  },

  created() {
    this.fetchBannerList()
    this.fetchStats()
    this.fetchEnterprises()
    this.fetchStudents()
    this.fetchJobs()
  },

  methods: {
    // 获取轮播图数据
    async fetchBannerList() {
      try {
        this.bannerList = await IndustryModel.getBannerList()
        console.log(this.bannerList)
      } catch (error) {
        console.error('获取轮播图数据失败:', error)
      }
    },

    // 获取统计数据
    async fetchStats() {
      try {
        this.stats = await IndustryModel.getIndustryStats()
      } catch (error) {
        console.error('获取统计数据失败:', error)
      }
    },

    // 获取企业数据
    async fetchEnterprises() {
      this.enterpriseLoading = true
      try {
        this.displayEnterprises = await IndustryModel.getEnterpriseList()
      } catch (error) {
        this.$message.error('获取企业数据失败')
        console.error('获取企业数据失败:', error)
      } finally {
        this.enterpriseLoading = false
      }
    },

    // 获取学生数据
    async fetchStudents() {
      this.studentsLoading = true
      try {
        this.displayStudents = await IndustryModel.getStudentList()
      } catch (error) {
        this.$message.error('获取学生数据失败')
        console.error('获取学生数据失败:', error)
      } finally {
        this.studentsLoading = false
      }
    },

    // 获取招聘数据
    async fetchJobs() {
      this.jobsLoading = true
      try {
        this.displayJobs = await IndustryModel.getJobList()
      } catch (error) {
        this.$message.error('获取招聘数据失败')
        console.error('获取招聘数据失败:', error)
      } finally {
        this.jobsLoading = false
      }
    },

    // 轮播图点击事件
    goToBannerLink(link) {
      if (link) {
        window.open(link)
      }
    },

    // 跳转到企业列表
    goToEnterpriseList() {
      this.$router.push('/industry/enterprise')
    },

    // 跳转到企业详情页
    goToEnterpriseDetail(id) {
      window.open(`/industry/enterprise/${id}`, '_blank')
    },

    // 跳转到学生列表
    goToStudentsList() {
      this.$router.push('/industry/students')
    },

    // 跳转到学生详情页
    goToStudentDetail(id) {
      window.open(`/industry/student/${id}`, '_blank')
    },

    // 跳转到招聘列表
    goToRecruitmentList() {
      this.$router.push('/industry/recruitment')
    },

    // 跳转到招聘详情页
    goToJobDetail(id) {
      window.open(`/industry/recruitment/${id}`, '_blank')
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return IndustryModel.formatDate(date, 'MM-DD')
    }
  }
}
</script>

<style lang="less" scoped>


.industry-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;

  .banner-section {
    margin-bottom: 40px;

    .banner-item {
      position: relative;
      height: 100%;
      border-radius: 12px;
      overflow: hidden;
      cursor: pointer;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .banner-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
        padding: 30px;
        color: white;

        .banner-title {
          font-size: 24px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .banner-description {
          font-size: 16px;
          opacity: 0.9;
        }
      }
    }
  }

  .stats-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .stats-container {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 30px;

      .stat-item {
        text-align: center;
        position: relative;
        padding: 20px;
        border-radius: 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        border: 1px solid rgba(64, 147, 249, 0.1);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-5px);
          box-shadow: 0 8px 25px rgba(64, 147, 249, 0.15);
          border-color: rgba(64, 147, 249, 0.3);
        }

        .stat-icon {
          width: 50px;
          height: 50px;
          margin: 0 auto 15px;
          background: linear-gradient(135deg, #4093f9 0%, #667eea 100%);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 15px rgba(64, 147, 249, 0.3);

          svg {
            font-size: 22px;
            color: white;
          }
        }

        .stat-number {
          font-size: 42px;
          font-weight: bold;
          color: #4093f9;
          margin-bottom: 8px;
          text-shadow: 0 2px 4px rgba(64, 147, 249, 0.1);
        }

        .stat-label {
          font-size: 16px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }

  .enterprise-section,
  .students-section,
  .recruitment-section {
    background: white;
    border-radius: 12px;
    padding: 30px;
    margin-bottom: 40px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 25px;

      .header-left {
        flex: 1;

        .section-title {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin-bottom: 5px;
        }

        .section-subtitle {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }

      // 新的header样式
      &.enterprise-header,
      &.students-header,
      &.recruitment-header {
        position: relative;
        border-radius: 16px;
        padding: 20px 25px;
        margin-bottom: 30px;
        overflow: hidden;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .header-bg {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;

          .bg-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.15);
            animation: float 6s ease-in-out infinite;

            &.bg-circle-1 {
              width: 80px;
              height: 80px;
              top: -40px;
              right: -40px;
              animation-delay: 0s;
            }

            &.bg-circle-2 {
              width: 60px;
              height: 60px;
              bottom: -30px;
              left: 15%;
              animation-delay: 3s;
            }
          }
        }

        .header-content {
          position: relative;
          z-index: 2;
          color: white;
          flex: 1;

          .title-wrapper {
            display: flex;
            align-items: center;
            margin-bottom: 10px;

            .title-icon {
              width: 40px;
              height: 40px;
              background: rgba(255, 255, 255, 0.2);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 15px;
              backdrop-filter: blur(10px);
              border: 2px solid rgba(255, 255, 255, 0.3);

              svg {
                font-size: 18px;
                color: white;
              }
            }

            .section-title {
              font-size: 24px;
              font-weight: 700;
              margin: 0;
              color: white;
              background: linear-gradient(45deg, #ffffff, #f0f8ff);
              -webkit-background-clip: text;
              -webkit-text-fill-color: transparent;
              background-clip: text;
            }
          }

          .section-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
            font-weight: 400;
            letter-spacing: 0.3px;
          }
        }
      }

      &.enterprise-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }

      &.students-header {
        background: linear-gradient(135deg, #27ae60 0%, #16a085 50%, #2980b9 100%);
        box-shadow: 0 8px 20px rgba(39, 174, 96, 0.3);
      }

      &.recruitment-header {
        background: linear-gradient(135deg, #e74c3c 0%, #c0392b 50%, #8e44ad 100%);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
      }
    }


      .header-right {
        .more-button {
          padding: 10px 20px;
          border-radius: 20px;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
          }

          i {
            margin-left: 5px;
            transition: transform 0.3s ease;
          }

          &:hover i {
            transform: translateX(3px);
          }
        }
      }
    }

    .section-footer {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #f0f0f0;

      .footer-more-button {
        padding: 12px 30px;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 500;
        min-width: 200px;
        transition: all 0.3s ease;
        gap: 8px;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.2);
        }

        .button-icon {
          font-size: 14px;
          transition: all 0.3s ease;
        }

        .button-arrow {
          font-size: 14px;
          transition: transform 0.3s ease;
        }

        &:hover .button-icon {
          transform: scale(1.1);
        }

        &:hover .button-arrow {
          transform: translateX(5px);
        }
      }
    }


  .enterprise-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;

    .enterprise-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      }

      .enterprise-logo {
        text-align: center;
        margin-bottom: 15px;

        img {
          width: 80px;
          height: 80px;
          object-fit: contain;
          border-radius: 8px;
        }
      }

      .enterprise-info {
        text-align: center;

        .enterprise-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .enterprise-type {
          font-size: 14px;
          color: #666;
          margin-bottom: 12px;
        }

        .enterprise-tags {
          .el-tag {
            margin: 2px;
          }
        }
      }
    }
  }

  .students-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;

    .student-card {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
      }

      .student-photo {
        text-align: center;
        margin-bottom: 15px;

        img {
          width: 100px;
          height: 100px;
          object-fit: cover;
          border-radius: 50%;
        }
      }

      .student-info {
        text-align: center;

        .student-name {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .student-major {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .student-company {
          font-size: 14px;
          color: #4093f9;
          margin-bottom: 12px;
        }

        .student-achievements {
          .el-tag {
            margin: 2px;
          }
        }
      }
    }
  }

  .recruitment-list {
    .recruitment-item {
      display: flex;
      align-items: center;
      padding: 20px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #f8f9fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .company-logo {
        width: 60px;
        height: 60px;
        margin-right: 20px;

        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 8px;
        }
      }

      .job-info {
        flex: 1;

        .job-title {
          font-size: 18px;
          font-weight: bold;
          color: #333;
          margin-bottom: 8px;
        }

        .job-company {
          font-size: 14px;
          color: #666;
          margin-bottom: 10px;
        }

        .job-details {
          display: flex;
          gap: 20px;
          font-size: 14px;

          .job-salary {
            color: #f56c6c;
            font-weight: bold;
          }

          .job-location,
          .job-experience {
            color: #666;
          }
        }
      }

      .job-status {
        text-align: center;

        .job-date {
          font-size: 12px;
          color: #999;
          margin-top: 8px;
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .industry-page {
    padding: 20px 0;

    .content-container {
      padding: 0 15px;
    }

    .banner-section {
      ::v-deep .el-carousel {
        height: 250px !important;
      }
    }

    .stats-section {
      .stats-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;

        .stat-item {
          padding: 15px;

          .stat-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 12px;

            svg {
              font-size: 18px;
            }
          }

          .stat-number {
            font-size: 32px;
          }
        }
      }
    }

    .enterprise-section,
    .students-section,
    .recruitment-section {
      .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

        .header-left {
          .section-title {
            font-size: 20px;
          }
        }

        // 新header样式的响应式设计
        &.enterprise-header,
        &.students-header,
        &.recruitment-header {
          min-height: 100px;
          padding: 15px 20px;
          margin-bottom: 25px;

          .header-content {
            .title-wrapper {
              .title-icon {
                width: 35px;
                height: 35px;
                margin-right: 12px;

                svg {
                  font-size: 16px;
                }
              }

              .section-title {
                font-size: 20px;
              }
            }

            .section-subtitle {
              font-size: 13px;
            }
          }
        }

        .header-right {
          align-self: stretch;

          .more-button {
            width: 100%;
            justify-content: center;
          }
        }
      }

      .section-footer {
        .footer-more-button {
          width: 100%;
          min-width: auto;
        }
      }
    }

    .enterprise-grid,
    .students-grid {
      grid-template-columns: 1fr;
    }

    .recruitment-list {
      .recruitment-item {
        flex-direction: column;
        text-align: center;

        .company-logo {
          margin-right: 0;
          margin-bottom: 15px;
        }

        .job-info {
          margin-bottom: 15px;

          .job-details {
            justify-content: center;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .industry-page {
    .stats-section {
      padding: 20px;

      .stats-container {
        gap: 15px;

        .stat-item {
          padding: 12px;

          .stat-icon {
            width: 35px;
            height: 35px;
            margin-bottom: 10px;

            svg {
              font-size: 16px;
            }
          }

          .stat-number {
            font-size: 28px;
          }

          .stat-label {
            font-size: 14px;
          }
        }
      }
    }

    .enterprise-section,
    .students-section,
    .recruitment-section {
      padding: 20px;

      .section-header {
        .section-title {
          font-size: 20px;
        }

        // 小屏幕新header样式
        &.enterprise-header,
        &.students-header,
        &.recruitment-header {
          min-height: 80px;
          padding: 12px 15px;
          margin-bottom: 20px;
          border-radius: 12px;

          .header-content {
            .title-wrapper {
              .title-icon {
                width: 30px;
                height: 30px;
                margin-right: 10px;

                svg {
                  font-size: 14px;
                }
              }

              .section-title {
                font-size: 18px;
              }
            }

            .section-subtitle {
              font-size: 12px;
            }
          }
        }
      }
    }

    .enterprise-grid {
      .enterprise-card {
        padding: 15px;

        .enterprise-logo img {
          width: 60px;
          height: 60px;
        }

        .enterprise-info {
          .enterprise-name {
            font-size: 16px;
          }
        }
      }
    }

    .students-grid {
      .student-card {
        padding: 15px;

        .student-photo img {
          width: 80px;
          height: 80px;
        }

        .student-info {
          .student-name {
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>