<template>
  <div class="recruitment-list-page">
    <div class="content-container">
      <div class="page-header">
        <div class="header-bg">
          <div class="bg-circle bg-circle-1"></div>
          <div class="bg-circle bg-circle-2"></div>
          <div class="bg-circle bg-circle-3"></div>
        </div>
        <div class="header-content">
          <div class="title-wrapper">
            <div class="title-icon">
              <font-awesome-icon icon="briefcase" />
            </div>
            <h1 class="page-title">企业招聘</h1>
          </div>
          <div class="page-subtitle">优质岗位，成就职业梦想，共创美好未来</div>
<!--          <div class="header-stats">-->
<!--            <div class="stat-item">-->
<!--              <div class="stat-number">200+</div>-->
<!--              <div class="stat-label">招聘职位</div>-->
<!--            </div>-->
<!--            <div class="stat-divider"></div>-->
<!--            <div class="stat-item">-->
<!--              <div class="stat-number">80%</div>-->
<!--              <div class="stat-label">就业率</div>-->
<!--            </div>-->
<!--            <div class="stat-divider"></div>-->
<!--            <div class="stat-item">-->
<!--              <div class="stat-number">15K</div>-->
<!--              <div class="stat-label">平均薪资</div>-->
<!--            </div>-->
<!--          </div>-->
        </div>
      </div>

      <!-- 搜索筛选区域 -->
      <div class="search-filter-section">
        <div class="search-area">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索职位名称或公司名称"
            class="search-input"
            @keyup.enter.native="handleSearch"
          >
            <el-button slot="append" @click="handleSearch">
            <font-awesome-icon icon="search" />
          </el-button>
          </el-input>
        </div>

        <div class="filter-area">
          <div class="filter-item">
            <font-awesome-icon icon="tags" class="filter-icon" />
            <el-select v-model="selectedCategory" placeholder="职位类别" @change="handleCategoryChange">
              <el-option label="全部" value=""></el-option>
              <el-option label="技术开发" value="development"></el-option>
              <el-option label="产品设计" value="design"></el-option>
              <el-option label="市场营销" value="marketing"></el-option>
              <el-option label="运营管理" value="operation"></el-option>
              <el-option label="人力资源" value="hr"></el-option>
              <el-option label="数据分析" value="analysis"></el-option>
              <el-option label="产品管理" value="product"></el-option>
              <el-option label="其它" value="other"></el-option>
            </el-select>
          </div>
<!--          <div class="filter-item">-->
<!--            <font-awesome-icon icon="user-graduate" class="filter-icon" />-->
<!--            <el-select v-model="selectedExperience" placeholder="经验要求" @change="handleExperienceChange">-->
<!--              <el-option label="全部" value=""></el-option>-->
<!--              <el-option label="应届生" value="fresh"></el-option>-->
<!--              <el-option label="1-3年" value="1-3"></el-option>-->
<!--              <el-option label="3-5年" value="3-5"></el-option>-->
<!--              <el-option label="5年以上" value="5+"></el-option>-->
<!--            </el-select>-->
<!--          </div>-->
<!--          <div class="filter-item">-->
<!--            <font-awesome-icon icon="map-marker-alt" class="filter-icon" />-->
<!--            <el-select v-model="selectedLocation" placeholder="工作地点" @change="handleLocationChange">-->
<!--              <el-option label="全部" value=""></el-option>-->
<!--              <el-option label="北京" value="beijing"></el-option>-->
<!--              <el-option label="上海" value="shanghai"></el-option>-->
<!--              <el-option label="深圳" value="shenzhen"></el-option>-->
<!--              <el-option label="杭州" value="hangzhou"></el-option>-->
<!--              <el-option label="成都" value="chengdu"></el-option>-->
<!--            </el-select>-->
<!--          </div>-->
        </div>
      </div>

      <!-- 招聘列表 -->
      <div class="recruitment-list-section" v-loading="loading">
        <div class="job-list" v-if="jobList.length > 0">
          <div class="job-item" v-for="job in jobList" :key="job.jobId" @click="openJobDetail(job.jobId)">
            <div class="job-header">
              <div class="company-logo">
                <font-awesome-icon icon="building" v-if="!job.companyLogo" />
                <img v-else :src="job.companyLogo" :alt="job.companyName" />
              </div>
              <div class="job-basic">
                <h3 class="job-title">
                  {{ job.title }}
                  <el-tag v-if="job.urgent" type="danger" size="mini">急招</el-tag>
                  <el-tag v-if="job.hot" type="warning" size="mini">热门</el-tag>
                </h3>
                <div class="job-meta-info">
                  <span class="job-company">{{ job.companyName }}</span>
                  <span class="job-location">
                    <font-awesome-icon icon="map-marker-alt" />
                    {{ job.location }}
                  </span>
                  <span class="publish-time">
                    <font-awesome-icon icon="clock" class="time-icon" />
                    发布时间：{{ formatDate(job.publishTime) }}
                  </span>
                </div>
              </div>
              <div class="job-salary">薪资范围：{{ job.salary }}</div>
            </div>

            <div class="job-content">
              <div class="job-requirements">
                <div class="requirement-item">
                  <font-awesome-icon icon="user-clock" class="requirement-icon" />
                  <span class="requirement-label">经验要求：</span>
                  <span class="requirement-value">{{ job.experience }}</span>
                </div>
                <div class="requirement-item">
                  <font-awesome-icon icon="graduation-cap" class="requirement-icon" />
                  <span class="requirement-label">学历要求：</span>
                  <span class="requirement-value">{{ job.education }}</span>
                </div>
                <div class="requirement-item">
                  <font-awesome-icon icon="briefcase" class="requirement-icon" />
                  <span class="requirement-label">工作性质：</span>
                  <span class="requirement-value">{{ job.jobType }}</span>
                </div>
              </div>

              <div class="job-description">
                <p>{{ job.description }}</p>
              </div>

              <div class="job-skills">
                <div class="skills-header">
                  <font-awesome-icon icon="tools" class="skills-icon" />
                  <span class="skills-label">技能要求：</span>
                </div>
                <div class="skills-tags">
                  <el-tag v-for="skill in job.skills" :key="skill" size="small">
                    <font-awesome-icon icon="code" class="skill-tag-icon" />
                    {{ skill }}
                  </el-tag>
                </div>
              </div>
            </div>

            <div class="job-footer">
            </div>
          </div>
        </div>

        <!-- 空数据状态 -->
        <div class="empty-state" v-else-if="!loading">
          <font-awesome-icon icon="briefcase" />
          <p>暂无招聘信息</p>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="total > 0">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-size="pageSize"
          layout="total, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </div>


  </div>
</template>

<script>
import {IndustryModel} from '@/model/IndustryModel'

export default {
  name: 'RecruitmentListPage',
  data() {
    return {
      // 搜索和筛选
      searchKeyword: '',
      selectedCategory: '',
      selectedExperience: '',
      selectedLocation: '',

      // 分页
      currentPage: 0,
      pageSize: 10,
      total: 0,

      // 数据
      jobList: [],
      loading: false
    }
  },

  created() {
    this.fetchJobList()
  },

  methods: {
    // 获取招聘列表
    async fetchJobList() {
      this.loading = true
      try {
        const params = {}

        if (this.searchKeyword) {
          params.$or = [
            { title: { $regex: this.searchKeyword } },
            { companyName: { $regex: this.searchKeyword } }
          ]
        }

        if (this.selectedCategory) {
          params.category = this.selectedCategory
        }

        const response = await IndustryModel.getJobPageList(this.currentPage, this.pageSize, params)

        this.jobList = response.content
        this.total = response.totalElements
      } catch (error) {
        this.$message.error('获取招聘列表失败')
        console.error('获取招聘列表失败:', error)
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.currentPage = 0
      this.fetchJobList()
    },

    // 类别筛选
    handleCategoryChange() {
      this.currentPage = 0
      this.fetchJobList()
    },

    // 经验筛选
    handleExperienceChange() {
      this.currentPage = 0
      this.fetchJobList()
    },

    // 地点筛选
    handleLocationChange() {
      this.currentPage = 0
      this.fetchJobList()
    },

    // 分页
    handleCurrentChange(page) {
      this.currentPage = page
      this.fetchJobList()
      // 滚动到顶部
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      })
    },

    // 跳转到招聘详情页
    openJobDetail(id) {
      window.open(`/industry/recruitment/${id}`, '_blank')
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return IndustryModel.formatDate(date, 'YYYY-MM-DD')
    }
  }
}
</script>

<style lang="less" scoped>


.recruitment-list-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;

  .page-header {
    position: relative;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 50%, #8e44ad 100%);
    border-radius: 24px;
    margin-bottom: 40px;
    overflow: hidden;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 20px 40px rgba(231, 76, 60, 0.3);

    .header-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      .bg-circle {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        animation: float 6s ease-in-out infinite;

        &.bg-circle-1 {
          width: 120px;
          height: 120px;
          top: -60px;
          right: -60px;
          animation-delay: 0s;
        }

        &.bg-circle-2 {
          width: 80px;
          height: 80px;
          bottom: -40px;
          left: 20%;
          animation-delay: 2s;
        }

        &.bg-circle-3 {
          width: 100px;
          height: 100px;
          top: 30%;
          left: -50px;
          animation-delay: 4s;
        }
      }
    }

    .header-content {
      position: relative;
      z-index: 2;
      text-align: center;
      color: white;

      .title-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        animation: slideInDown 1s ease-out;

        .title-icon {
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;
          backdrop-filter: blur(10px);
          border: 2px solid rgba(255, 255, 255, 0.3);

          svg {
            font-size: 24px;
            color: white;
          }
        }

        .page-title {
          font-size: 36px;
          font-weight: 700;
          margin: 0;
          background: linear-gradient(45deg, #ffffff, #f0f8ff);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
          text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
      }

      .page-subtitle {
        font-size: 18px;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 30px;
        font-weight: 400;
        letter-spacing: 0.5px;
        animation: slideInUp 1s ease-out 0.3s both;
      }

      .header-stats {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0;
        animation: fadeInUp 1s ease-out 0.6s both;

        .stat-item {
          text-align: center;
          padding: 0 30px;

          .stat-number {
            font-size: 32px;
            font-weight: 700;
            color: white;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }

          .stat-label {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            letter-spacing: 0.5px;
          }
        }

        .stat-divider {
          width: 1px;
          height: 40px;
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  .search-filter-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .search-area {
      margin-bottom: 20px;
      text-align: center;

      .search-input {
        max-width: 500px;

        ::v-deep .el-input__inner {
          border-radius: 25px 0 0 25px;
          height: 45px;
          line-height: 45px;
        }

        ::v-deep .el-input-group__append {
          border-radius: 0 25px 25px 0;

          .el-button {
            border-radius: 0 25px 25px 0;
            height: 45px;
            padding: 0 20px;
            background-color: #4093f9;
            border-color: #4093f9;
            color: white;

            &:hover {
              background-color: darken(#4093f9, 10%);
              border-color: darken(#4093f9, 10%);
            }
          }
        }
      }
    }

    .filter-area {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;

      .filter-item {
        display: flex;
        align-items: center;
        gap: 8px;

        .filter-icon {
          color: #4093f9;
          font-size: 16px;
        }

        .el-select {
          min-width: 150px;
        }
      }
    }
  }

  .recruitment-list-section {
    min-height: 400px;

    .job-list {
      .job-item {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .job-header {
          display: flex;
          align-items: flex-start;
          margin-bottom: 20px;
          gap: 20px;

          .company-logo {
            width: 70px;
            height: 70px;
            margin-right: 20px;
            flex-shrink: 0;
            background: #f8f9fa;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e9ecef;

            svg {
              font-size: 24px;
              color: #6c757d;
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: contain;
              border-radius: 8px;
            }
          }

          .job-basic {
            flex: 1;

            .job-title {
              font-size: 20px;
              font-weight: bold;
              color: #333;
              margin-bottom: 8px;
              line-height: 1.3;
              display: flex;
              align-items: center;
              gap: 10px;

              &:hover {
                color: #4093f9;
              }
            }

            .job-meta-info {
              display: flex;
              align-items: center;
              gap: 15px;
              flex-wrap: wrap;

              .job-company {
                font-size: 16px;
                color: #666;
                font-weight: 500;
              }

              .job-location {
                font-size: 14px;
                color: #999;
                display: flex;
                align-items: center;
                gap: 5px;
              }

              .publish-time {
                font-size: 14px;
                color: #999;
                display: flex;
                align-items: center;
                gap: 5px;

                .time-icon {
                  font-size: 12px;
                  color: #4093f9;
                }
              }
            }
          }

          .job-salary {
            font-size: 24px;
            font-weight: bold;
            color: #f56c6c;
            text-align: right;
          }
        }

        .job-content {
          margin-bottom: 20px;

          .job-requirements {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            flex-wrap: wrap;

            .requirement-item {
              font-size: 14px;
              display: flex;
              align-items: center;
              gap: 8px;

              .requirement-icon {
                color: #4093f9;
                font-size: 14px;
                width: 16px;
                text-align: center;
              }

              .requirement-label {
                color: #666;
              }

              .requirement-value {
                color: #333;
                font-weight: 500;
              }
            }
          }

          .job-description {
            margin-bottom: 15px;

            p {
              font-size: 15px;
              color: #666;
              line-height: 1.6;
              margin: 0;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }
          }

          .job-skills {
            .skills-header {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 10px;

              .skills-icon {
                color: #4093f9;
                font-size: 14px;
              }

              .skills-label {
                font-size: 14px;
                color: #666;
                white-space: nowrap;
              }
            }

            .skills-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;

              .el-tag {
                margin: 0;
                display: flex;
                align-items: center;
                gap: 4px;

                .skill-tag-icon {
                  font-size: 12px;
                }
              }
            }
          }
        }

        .job-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-top: 15px;
          border-top: 1px solid #f0f0f0;

          .job-meta {
            .publish-time {
              display: flex;
              align-items: center;
              gap: 6px;
              font-size: 13px;
              color: #999;

              .time-icon {
                font-size: 12px;
                color: #4093f9;
              }
            }
          }
        }
      }
    }

    .empty-state {
      text-align: center;
      padding: 60px 20px;
      color: #999;

      svg {
        font-size: 64px;
        margin-bottom: 16px;
        color: #ccc;
      }

      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }

  .pagination-container {
    text-align: center;
    margin-top: 40px;
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .recruitment-list-page {
    padding: 20px 0;

    .content-container {
      padding: 0 15px;
    }

    .page-header {
      min-height: 220px;
      border-radius: 16px;
      margin-bottom: 30px;

      .header-content {
        .title-wrapper {
          flex-direction: column;

          .title-icon {
            margin-right: 0;
            margin-bottom: 15px;
            width: 50px;
            height: 50px;

            svg {
              font-size: 20px;
            }
          }

          .page-title {
            font-size: 28px;
          }
        }

        .page-subtitle {
          font-size: 16px;
          margin-bottom: 25px;
        }

        .header-stats {
          .stat-item {
            padding: 0 20px;

            .stat-number {
              font-size: 24px;
            }

            .stat-label {
              font-size: 12px;
            }
          }
        }
      }
    }

    .search-filter-section {
      padding: 15px;

      .search-area {
        .search-input {
          max-width: 100%;
        }
      }

      .filter-area {
        flex-direction: column;
        align-items: center;

        .el-select {
          width: 100%;
          max-width: 300px;
        }
      }
    }

    .recruitment-list-section {
      .job-list {
        .job-item {
          padding: 20px;

          .job-header {
            flex-direction: column;
            text-align: center;

            .company-logo {
              margin-right: 0;
              margin-bottom: 15px;
              align-self: center;
            }

            .job-basic {
              margin-bottom: 15px;
            }

            .job-salary {
              text-align: center;
            }
          }

          .job-content {
            .job-requirements {
              flex-direction: column;
              gap: 10px;
              text-align: center;
            }
          }

          .job-footer {
            flex-direction: column;
            gap: 15px;
            text-align: center;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .recruitment-list-page {
    .page-header {
      min-height: 180px;
      border-radius: 12px;

      .header-content {
        .title-wrapper {
          .title-icon {
            width: 40px;
            height: 40px;
            margin-bottom: 10px;

            svg {
              font-size: 16px;
            }
          }

          .page-title {
            font-size: 24px;
          }
        }

        .page-subtitle {
          font-size: 14px;
          margin-bottom: 20px;
        }

        .header-stats {
          flex-direction: column;
          gap: 15px;

          .stat-item {
            padding: 0;

            .stat-number {
              font-size: 20px;
            }

            .stat-label {
              font-size: 11px;
            }
          }

          .stat-divider {
            display: none;
          }
        }
      }
    }

    .recruitment-list-section {
      .job-list {
        .job-item {
          padding: 15px;

          .job-header {
            .company-logo {
              width: 60px;
              height: 60px;
            }

            .job-basic {
              .job-title {
                font-size: 18px;
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
              }

              .job-meta-info {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .job-company {
                  font-size: 14px;
                }

                .job-location,
                .publish-time {
                  font-size: 13px;
                }
              }
            }

            .job-salary {
              font-size: 20px;
            }
          }

          .job-content {
            .job-description p {
              font-size: 14px;
            }

            .job-skills {
              .skills-label {
                display: block;
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }
  }
}
</style>