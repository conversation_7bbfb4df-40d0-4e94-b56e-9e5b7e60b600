<template>
    <div class="enterprise-detail-page">
        <div class="content-container">

            <div class="enterprise-detail" v-if="enterprise.enterpriseId && enterprise.status === 'active'" v-loading="loading">
                <!-- 企业基本信息 -->
                <div class="enterprise-header">
                    <div class="enterprise-logo">
                        <img :src="enterprise.logo" :alt="enterprise.name" />
                    </div>
                    <div class="enterprise-basic">
                        <h2 class="enterprise-name">{{ enterprise.name }}</h2>
                        <div class="enterprise-meta">
                            <span class="meta-item">
                                <i class="el-icon-location"></i>
                                {{ enterprise.location }}
                            </span>
                            <span class="meta-item">
                                <i class="el-icon-office-building"></i>
                                {{ enterprise.typeName }}
                            </span>
                            <span class="meta-item">
                                <i class="el-icon-time"></i>
                                成立于{{ enterprise.foundYear }}年
                            </span>
                            <span class="meta-item">
                                <i class="el-icon-s-grid"></i>
                                {{ enterprise.scaleName }}
                            </span>
                        </div>
                        <div class="enterprise-tags">
                            <el-tag v-for="tag in enterprise.tags" :key="tag" size="small">{{ tag }}</el-tag>
                        </div>
                    </div>
                </div>

                <!-- 企业详细描述 -->
                <div class="enterprise-content">
                    <div class="section-title">企业介绍</div>
                    <div class="enterprise-description" v-html="enterprise.fullDescription || enterprise.description">
                    </div>
                </div>

                <!-- 合作详情 -->
                <div class="cooperation-detail-content" v-if="enterprise.cooperationDetail">
                    <div class="section-title">校企合作详情</div>
                    <div class="cooperation-detail-description" v-html="enterprise.cooperationDetail">
                    </div>
                </div>

                <!-- 合作统计 -->
                <div class="cooperation-stats">
                    <div class="section-title">合作概况</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">{{ enterprise.cooperationYears }}</div>
                            <div class="stat-label">合作年限</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ enterprise.studentCount }}</div>
                            <div class="stat-label">培养学生</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">{{ enterprise.projectCount }}</div>
                            <div class="stat-label">合作项目</div>
                        </div>
                        <div class="stat-item" v-if="enterprise.employeeCount">
                            <div class="stat-number">{{ enterprise.employeeCount }}</div>
                            <div class="stat-label">员工规模</div>
                        </div>
                    </div>
                </div>

                <!-- 合作项目 -->
                <div class="cooperation-projects"
                    v-if="enterprise.cooperationProjects && enterprise.cooperationProjects.length > 0">
                    <div class="section-title">合作项目</div>
                    <div class="project-list">
                        <div class="project-item" v-for="project in enterprise.cooperationProjects" :key="project.id">
                            <div class="project-header">
                                <h4 class="project-name">{{ project.name }}</h4>
                                <el-tag :type="project.status === '进行中' ? 'primary' : 'success'" size="small">
                                    {{ project.status }}
                                </el-tag>
                            </div>
                            <p class="project-description">{{ project.description }}</p>
                            <div class="project-meta">
                                <span class="start-time">开始时间：{{ formatDate(project.startTime) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 联系方式 -->
                <div class="contact-info">
                    <div class="section-title">联系信息</div>
                    <div class="contact-grid">
                        <div class="contact-item" v-if="enterprise.address">
                            <span class="contact-label">详细地址：</span>
                            <span class="contact-value">{{ enterprise.address }}</span>
                        </div>
                        <div class="contact-item" v-if="enterprise.contactInfo && enterprise.contactInfo.contact">
                            <span class="contact-label">联系人：</span>
                            <span class="contact-value">{{ enterprise.contactInfo.contact }}</span>
                        </div>
                        <div class="contact-item" v-if="enterprise.contactInfo && enterprise.contactInfo.phone">
                            <span class="contact-label">联系电话：</span>
                            <span class="contact-value">{{ enterprise.contactInfo.phone }}</span>
                        </div>
                        <div class="contact-item" v-if="enterprise.contactInfo && enterprise.contactInfo.email">
                            <span class="contact-label">邮箱：</span>
                            <span class="contact-value">{{ enterprise.contactInfo.email }}</span>
                        </div>
                        <div class="contact-item" v-if="enterprise.website">
                            <span class="contact-label">官网：</span>
                            <a :href="enterprise.website" target="_blank" class="contact-link">{{ enterprise.website
                            }}</a>
                        </div>
                    </div>
                </div>

            </div>

            <!-- 企业已下架状态 -->
            <div class="error-state" v-else-if="enterprise.enterpriseId && enterprise.status !== 'active' && !loading">
                <i class="el-icon-circle-close"></i>
                <p>该企业信息已下架，暂时无法查看</p>
            </div>

            <!-- 加载失败状态 -->
            <div class="error-state" v-else-if="!loading">
                <i class="el-icon-warning"></i>
                <p>企业信息不存在或加载失败</p>
            </div>
        </div>
    </div>
</template>

<script>
import { IndustryModel } from '@/model/IndustryModel'
import enterprise from "../enterprise.vue";

export default {
    name: 'EnterpriseDetailPage',
    data() {
        return {
            enterprise: {},
            loading: false
        }
    },
    created() {
        this.fetchEnterpriseDetail()
    },
    watch: {
        '$route.params.id'() {
            this.fetchEnterpriseDetail()
        }
    },
    methods: {
        // 获取企业详情
        async fetchEnterpriseDetail() {
            const enterpriseId = this.$route.params.id
            if (!enterpriseId) {
                this.$message.error('企业ID不存在')
                this.goBack()
                return
            }

            this.loading = true
            try {
                this.enterprise = await IndustryModel.getEnterpriseDetail(enterpriseId)
                if (!this.enterprise) {
                    this.$message.error('企业信息不存在')
                } else if (this.enterprise.status !== 'active') {
                    this.$message.warning('该企业信息已下架')
                }
            } catch (error) {
                this.$message.error('获取企业详情失败')
                console.error('获取企业详情失败:', error)
            } finally {
                this.loading = false
            }
        },

        // 返回上一页
        goBack() {
            this.$router.go(-1)
        },

        // 访问官网
        visitWebsite() {
            if (this.enterprise.website) {
                window.open(this.enterprise.website, '_blank')
            }
        },

        // 格式化日期
        formatDate(date) {
            if (!date) return ''
            return IndustryModel.formatDate(date, 'YYYY-MM-DD')
        }
    }
}
</script>

<style lang="less" scoped>


.enterprise-detail-page {
    background-color: #f2f2f2;
    min-height: 100vh;
    padding: 30px 0;

    .back-button {
        margin-bottom: 20px;
    }
}

.enterprise-detail {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .enterprise-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;

        .enterprise-logo {
            width: 100px;
            height: 100px;
            margin-right: 20px;
            flex-shrink: 0;

            img {
                width: 100%;
                height: 100%;
                object-fit: contain;
                border-radius: 8px;
                border: 1px solid #f0f0f0;
            }
        }

        .enterprise-basic {
            flex: 1;

            .enterprise-name {
                font-size: 24px;
                font-weight: bold;
                color: #333;
                margin-bottom: 15px;
            }

            .enterprise-meta {
                display: flex;
                flex-wrap: wrap;
                gap: 20px;
                margin-bottom: 10px;

                .meta-item {
                    font-size: 14px;
                    color: #666;
                    display: flex;
                    align-items: center;
                    gap: 5px;

                    i {
                        color: #4093f9;
                    }
                }
            }

            .enterprise-scale {
                font-size: 14px;
                color: #666;
                margin-bottom: 15px;
            }

            .enterprise-tags {
                .el-tag {
                    margin-right: 8px;
                    margin-bottom: 8px;
                }
            }
        }
    }

    .section-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
        position: relative;
        padding-left: 15px;

        &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #4093f9;
            border-radius: 2px;
        }
    }

    .enterprise-content {
        margin-bottom: 30px;

        .enterprise-description {
            font-size: 15px;
            line-height: 1.8;
            color: #666;

            ::v-deep h3 {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin: 20px 0 10px 0;
            }

            ::v-deep p {
                margin-bottom: 15px;
            }

            ::v-deep ul {
                padding-left: 20px;
                margin-bottom: 15px;

                li {
                    margin-bottom: 8px;
                }
            }
        }
    }

    .cooperation-detail-content {
        margin-bottom: 30px;

        .cooperation-detail-description {
            font-size: 15px;
            line-height: 1.8;
            color: #666;

            ::v-deep h3 {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin: 20px 0 10px 0;
            }

            ::v-deep p {
                margin-bottom: 15px;
            }

            ::v-deep ul {
                padding-left: 20px;
                margin-bottom: 15px;

                li {
                    margin-bottom: 8px;
                }
            }

            ::v-deep table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 15px;

                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }

                th {
                    background-color: #f8f9fa;
                    font-weight: bold;
                }
            }
        }
    }

    .cooperation-stats {
        margin-bottom: 30px;

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;

            .stat-item {
                text-align: center;
                padding: 20px;
                background: #f8f9fa;
                border-radius: 8px;

                .stat-number {
                    font-size: 28px;
                    font-weight: bold;
                    color: #4093f9;
                    margin-bottom: 8px;
                }

                .stat-label {
                    font-size: 14px;
                    color: #666;
                }
            }
        }
    }

    .cooperation-projects {
        margin-bottom: 30px;

        .project-list {
            .project-item {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 20px;
                margin-bottom: 15px;

                &:last-child {
                    margin-bottom: 0;
                }

                .project-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;

                    .project-name {
                        font-size: 16px;
                        font-weight: bold;
                        color: #333;
                        margin: 0;
                    }
                }

                .project-description {
                    font-size: 14px;
                    color: #666;
                    line-height: 1.6;
                    margin-bottom: 10px;
                }

                .project-meta {
                    font-size: 13px;
                    color: #999;
                }
            }
        }
    }

    .contact-info {
        margin-bottom: 30px;

        .contact-grid {
            .contact-item {
                display: flex;
                align-items: center;
                margin-bottom: 12px;
                font-size: 14px;

                .contact-label {
                    color: #666;
                    min-width: 80px;
                }

                .contact-value {
                    color: #333;
                }

                .contact-link {
                    color: #4093f9;
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }
        }
    }

    .action-buttons {
        text-align: center;
        padding-top: 20px;
        border-top: 1px solid #f0f0f0;

        .el-button {
            margin: 0 10px;
        }
    }
}

.error-state {
    text-align: center;
    padding: 60px 20px;
    background: white;
    border-radius: 12px;
    color: #999;

    i {
        font-size: 64px;
        margin-bottom: 16px;
        color: #f56c6c;
    }

    p {
        font-size: 16px;
        margin-bottom: 20px;
    }
}

// 响应式设计
@media (max-width: 768px) {
    .enterprise-detail-page {
        padding: 20px 0;

        .content-container {
            padding: 0 15px;
        }
    }

    .enterprise-detail {
        padding: 20px;

        .enterprise-header {
            flex-direction: column;
            text-align: center;

            .enterprise-logo {
                margin-right: 0;
                margin-bottom: 15px;
                align-self: center;
            }

            .enterprise-basic {
                .enterprise-meta {
                    justify-content: center;
                }
            }
        }

        .cooperation-stats {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;

                .stat-item {
                    padding: 15px;

                    .stat-number {
                        font-size: 24px;
                    }
                }
            }
        }
    }
}
</style>