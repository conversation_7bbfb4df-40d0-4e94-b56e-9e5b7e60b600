<template>
  <div class="student-detail-page">
    <div class="content-container">

      <div class="student-detail" v-if="student.studentId" v-loading="loading">
        <!-- 学生基本信息 -->
        <div class="student-header">
          <div class="student-photo">
            <img :src="student.photo" :alt="student.name" />
            <div class="student-category" v-if="getCategoryName()">{{ getCategoryName() }}</div>
          </div>
          <div class="student-basic">
            <h2 class="student-name">{{ student.name }}</h2>
            <div class="student-meta">
              <div class="meta-row">
                <span class="meta-item" v-if="student.major">
                  <font-awesome-icon :icon="['fas', 'graduation-cap']" class="meta-icon" />
                  {{ student.major }}
                </span>
                <span class="meta-separator" v-if="student.major && student.graduationYear">·</span>
                <span class="meta-item" v-if="student.graduationYear">
                  <font-awesome-icon :icon="['fas', 'calendar-alt']" class="meta-icon" />
                  {{ student.graduationYear }}届毕业
                </span>
                <span class="meta-separator" v-if="(student.major || student.graduationYear) && student.company">·</span>
                <span class="meta-item" v-if="student.company">
                  <font-awesome-icon :icon="['fas', 'building']" class="meta-icon" />
                  {{ student.company }}
                </span>
                <span class="meta-separator" v-if="student.company && student.position">·</span>
                <span class="meta-item" v-if="student.position">
                  <font-awesome-icon :icon="['fas', 'briefcase']" class="meta-icon" />
                  {{ student.position }}
                </span>
              </div>
            </div>
            <div class="student-quote" v-if="student.quote">
              <font-awesome-icon :icon="['fas', 'quote-left']" class="quote-icon" />
              <span>{{ student.quote }}</span>
              <font-awesome-icon :icon="['fas', 'quote-right']" class="quote-icon" />
            </div>
            <div class="student-status" v-if="student.featured">
              <el-tag v-if="student.featured" type="warning" size="small">
                <font-awesome-icon :icon="['fas', 'star']" class="tag-icon" />
                推荐学生
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 学术表现 -->

        <!-- 获奖成就 -->
        <div class="achievements-section" v-if="student.achievements && student.achievements.length > 0">
          <div class="section-title">
            <font-awesome-icon :icon="['fas', 'trophy']" class="title-icon" />
            获奖成就
          </div>
          <div class="achievements-list">
            <div class="achievement-item" v-for="achievement in student.achievements" :key="achievement.id">
              <div class="achievement-header">
                <h4 class="achievement-title">{{ achievement.title }}</h4>
                <el-tag :type="getAchievementType(achievement.type)" size="small">
                  {{ getAchievementTypeName(achievement.type) }}
                </el-tag>
              </div>
              <p class="achievement-description">{{ achievement.description }}</p>
              <div class="achievement-meta">
                <span class="achievement-time">获得时间：{{ formatDate(achievement.time) }}</span>
                <span class="achievement-level" v-if="achievement.level">等级：{{ achievement.level }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 项目经历 -->
        <div class="projects-section" v-if="student.projects && student.projects.length > 0">
          <div class="section-title">
            <font-awesome-icon :icon="['fas', 'laptop-code']" class="title-icon" />
            项目经历
          </div>
          <div class="projects-list">
            <div class="project-item" v-for="project in student.projects" :key="project.id">
              <div class="project-header">
                <h4 class="project-name">{{ project.name }}</h4>
                <span class="project-role">{{ project.role }}</span>
              </div>
              <p class="project-description">{{ project.description }}</p>
              <div class="project-technologies" v-if="(project.tech || project.technologies) && (project.tech || project.technologies).length > 0">
                <span class="tech-label">技术栈：</span>
                <el-tag v-for="tech in (project.tech || project.technologies)" :key="tech" size="mini">{{ tech }}</el-tag>
              </div>
            </div>
          </div>
        </div>

        <!-- 个人故事 -->
        <div class="story-section" v-if="student.story">
          <div class="section-title">
            <font-awesome-icon :icon="['fas', 'book-open']" class="title-icon" />
            个人故事
          </div>
          <div class="story-content" v-html="student.story"></div>
        </div>

        <!-- 当前工作信息 -->
        <div class="work-info" v-if="student.company || student.position">
          <div class="section-title">
            <font-awesome-icon :icon="['fas', 'user-tie']" class="title-icon" />
            当前工作
          </div>
          <div class="work-detail">
            <div class="work-item" v-if="student.company">
              <span class="work-label">工作单位：</span>
              <span class="work-value">{{ student.company }}</span>
            </div>
            <div class="work-item" v-if="student.position">
              <span class="work-label">职位：</span>
              <span class="work-value">{{ student.position }}</span>
            </div>
          </div>
        </div>

        <!-- 技能特长 -->
        <div class="skills-section" v-if="student.skills && student.skills.length > 0">
          <div class="section-title">
            <font-awesome-icon :icon="['fas', 'cogs']" class="title-icon" />
            技能特长
          </div>
          <div class="skills-content">
            <!-- 如果是字符串数组格式 -->
            <div v-if="typeof student.skills[0] === 'string'" class="skills-tags">
              <el-tag v-for="skill in student.skills" :key="skill" size="medium" class="skill-tag">{{ skill }}</el-tag>
            </div>
            <!-- 如果是对象格式 -->
            <div v-else class="skills-detailed">
              <div class="skill-item" v-for="skill in student.skills" :key="skill.name">
                <div class="skill-header">
                  <span class="skill-name">{{ skill.name }}</span>
                  <span class="skill-level">{{ skill.level }}</span>
                </div>
                <div class="skill-bar">
                  <div class="skill-progress" :style="{width: skill.proficiency + '%'}"></div>
                </div>
              </div>
            </div>
          </div>
        </div>


      </div>

      <!-- 加载失败状态 -->
      <div class="error-state" v-else-if="!loading">
        <i class="el-icon-warning"></i>
        <p>学生信息不存在或加载失败</p>
      </div>
    </div>
  </div>
</template>

<script>
import {IndustryModel} from '@/model/IndustryModel'

export default {
  name: 'StudentDetailPage',
  data() {
    return {
      student: {},
      loading: false
    }
  },
  created() {
    this.fetchStudentDetail()
  },
  watch: {
    '$route.params.id'() {
      this.fetchStudentDetail()
    }
  },
  methods: {
    // 获取学生详情
    async fetchStudentDetail() {
      const studentId = this.$route.params.id
      if (!studentId) {
        this.$message.error('学生ID不存在')
        this.goBack()
        return
      }
      
      this.loading = true
      try {
        this.student = await IndustryModel.getStudentDetail(studentId)
        if (!this.student) {
          this.$message.error('学生信息不存在')
        }
      } catch (error) {
        this.$message.error('获取学生详情失败')
        console.error('获取学生详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    
    // 获取成就类型样式
    getAchievementType(type) {
      const typeMap = {
        'scholarship': 'warning',
        'competition': 'success',
        'honor': 'primary',
        'innovation': 'danger',
        'academic': 'info'
      }
      return typeMap[type] || 'info'
    },
    
    // 获取成就类型名称
    getAchievementTypeName(type) {
      const typeMap = {
        'scholarship': '奖学金',
        'competition': '竞赛',
        'honor': '荣誉',
        'innovation': '创新',
        'academic': '学术'
      }
      return typeMap[type] || '其他'
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return IndustryModel.formatDate(date, 'YYYY-MM-DD')
    },
    
    
    // 获取类别名称
    getCategoryName() {
      if (this.student.categoryName) {
        return this.student.categoryName
      }
      
      const categoryMap = {
        'outstanding': '优秀学生',
        'excellent': '优秀学生',
        'good': '良好学生',
        'normal': '普通学生'
      }
      
      return categoryMap[this.student.category] || ''
    }
  }
}
</script>

<style lang="less" scoped>


.student-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-button {
    margin-bottom: 20px;
    
    .el-button {
      .btn-icon {
        margin-right: 6px;
      }
    }
  }
}

.student-detail {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .student-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .student-photo {
      position: relative;
      width: 120px;
      height: 120px;
      margin-right: 25px;
      flex-shrink: 0;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
        border: 4px solid white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
      
      .student-category {
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        background: #4093f9;
        color: white;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
      }
    }
    
    .student-basic {
      flex: 1;
      
      .student-name {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 15px;
      }
      
      .student-meta {
        margin-bottom: 20px;
        
        .meta-row {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
          gap: 8px;
          font-size: 15px;
          
          .meta-item {
            color: #666;
            display: flex;
            align-items: center;
            gap: 6px;
            
            .meta-icon {
              color: #4093f9;
              width: 16px;
            }
          }
          
          .meta-separator {
            color: #999;
            margin: 0 4px;
          }
        }
      }
      
      .student-quote {
        color: #666;
        font-size: 15px;
        line-height: 1.6;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 8px;
        
        .quote-icon {
          color: #4093f9;
          font-size: 12px;
        }
        
        span {
          font-style: italic;
        }
      }
      
      .student-status {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        
        .el-tag {
          border-radius: 12px;
          
          .tag-icon {
            margin-right: 4px;
          }
        }
      }
    }
  }
  
  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    
    .title-icon {
      color: #4093f9 !important;
      font-size: 16px;
    }
  }
  
  .academic-performance {
    margin-bottom: 30px;
    
    .performance-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 15px;
      
      .performance-item {
        text-align: center;
        padding: 20px 15px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 147, 249, 0.15);
        }
        
        .performance-number {
          font-size: 24px;
          font-weight: bold;
          color: #4093f9;
          margin-bottom: 8px;
        }
        
        .performance-label {
          font-size: 13px;
          color: #666;
          font-weight: 500;
        }
      }
    }
  }
  
  .achievements-section, .projects-section, .story-section, .work-info {
    margin-bottom: 30px;
    
    .achievements-list, .projects-list {
      .achievement-item, .project-item {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 16px;
        border-left: 4px solid #4093f9;
        transition: all 0.3s ease;
        
        &:hover {
          background: #f1f3f4;
          transform: translateX(4px);
        }
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .achievement-header, .project-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;
          
          .achievement-title, .project-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin: 0;
          }
          
          .project-role {
            color: #4093f9;
            font-size: 14px;
            font-weight: 500;
            background: rgba(64, 147, 249, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
          }
        }
        
        .achievement-description, .project-description {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
          margin-bottom: 12px;
        }
        
        .project-technologies {
          
          .tech-label {
            font-size: 13px;
            color: #666;
            margin-right: 8px;
            font-weight: 500;
          }
          
          .el-tag {
            margin-right: 6px;
            margin-bottom: 6px;
            background: rgba(64, 147, 249, 0.1);
            color: #4093f9;
            border: 1px solid rgba(64, 147, 249, 0.2);
          }
        }
        
        .achievement-meta, .project-meta {
          font-size: 13px;
          color: #999;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
      }
    }
  }
  
  .story-section {
    .story-content {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 24px;
      font-size: 15px;
      line-height: 1.8;
      color: #555;
      border-left: 4px solid #4093f9;
    }
  }
  
  .work-info {
    .work-detail {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-radius: 12px;
      padding: 24px;
      border: 1px solid #e9ecef;
      
      .work-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .work-label {
          color: #666;
          min-width: 100px;
          font-weight: 500;
        }
        
        .work-value {
          color: #333;
          font-weight: 600;
        }
      }
    }
  }
  
  .skills-section {
    margin-bottom: 30px;
    
    .skills-content {
      .skills-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        justify-content: center;
        
        .skill-tag {
          background: linear-gradient(135deg, #4093f9 0%, #6bb6ff 100%);
          color: white;
          border: none;
          border-radius: 20px;
          padding: 8px 16px;
          font-weight: 500;
          transition: all 0.3s ease;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 1;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 147, 249, 0.3);
          }
        }
      }
      
      .skills-detailed {
        .skill-item {
          margin-bottom: 20px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .skill-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            
            .skill-name {
              font-size: 15px;
              color: #333;
              font-weight: 500;
            }
            
            .skill-level {
              font-size: 13px;
              color: #666;
            }
          }
          
          .skill-bar {
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            
            .skill-progress {
              height: 100%;
              background: linear-gradient(90deg, #4093f9 0%, #6bb6ff 100%);
              border-radius: 4px;
              transition: width 0.3s ease;
            }
          }
        }
      }
    }
  }
  
  .contact-info {
    margin-bottom: 30px;
    
    .contact-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      
      .contact-item {
        display: flex;
        align-items: center;
        padding: 16px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 12px;
        border: 1px solid #e9ecef;
        font-size: 14px;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .contact-label {
          color: #666;
          min-width: 80px;
          font-weight: 500;
        }
        
        .contact-value {
          color: #333;
          font-weight: 600;
        }
        
        .contact-link {
          color: #4093f9;
          text-decoration: none;
          font-weight: 600;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
  
  .action-buttons {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    
    .el-button {
      margin: 0 10px;
      
      .btn-icon {
        margin-right: 6px;
      }
    }
  }
}

.error-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  color: #999;
  
  i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #f56c6c;
  }
  
  p {
    font-size: 16px;
    margin-bottom: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .student-detail-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
  }
  
  .student-detail {
    padding: 20px;
    
    .student-header {
      flex-direction: column;
      text-align: center;
      
      .student-photo {
        margin-right: 0;
        margin-bottom: 20px;
        align-self: center;
      }
      
      .student-basic {
        .student-meta {
          .meta-row {
            justify-content: center;
            flex-direction: column;
            gap: 12px;
          }
        }
      }
    }
    
    
    .achievements-section, .projects-section, .story-section, .work-info {
      .achievements-list, .projects-list {
        .achievement-item, .project-item {
          padding: 20px 16px;
          
          .achievement-header, .project-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
          }
          
          .achievement-meta, .project-meta {
            flex-direction: column;
            align-items: flex-start;
            gap: 5px;
          }
        }
      }
      
      .story-content {
        padding: 20px 16px;
        font-size: 14px;
      }
      
      .work-detail {
        padding: 20px 16px;
        
        .work-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 4px;
          
          .work-label {
            min-width: auto;
            font-size: 13px;
          }
          
          .work-value {
            font-size: 14px;
          }
        }
      }
    }
    
    
    .skills-section {
      .skills-content {
        .skills-tags {
          gap: 8px;
          
          .skill-tag {
            padding: 6px 12px;
            font-size: 13px;
          }
        }
      }
    }
  }
}
</style>