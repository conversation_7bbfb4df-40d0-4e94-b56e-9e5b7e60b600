<template>
  <div class="recruitment-detail-page">
    <div class="content-container">

      <div class="recruitment-detail" v-if="job.jobId" v-loading="loading">
        <!-- 职位基本信息 -->
        <div class="job-header">
          <div class="job-basic">
            <div class="job-title-row">
              <h2 class="job-title">
                {{ job.title }}
                <el-tag v-if="job.urgent" type="danger" size="small">急招</el-tag>
                <el-tag v-if="job.hot" type="warning" size="small">热门</el-tag>
              </h2>
              <div class="job-salary">薪资范围：{{ job.salary }}</div>
            </div>
            <div class="job-meta">
              <span class="meta-item">
                <font-awesome-icon icon="building" />
                {{ job.companyName }}
              </span>
              <span class="meta-item">
                <font-awesome-icon icon="map-marker-alt" />
                {{ job.location }}
              </span>
              <span class="meta-item">
                <font-awesome-icon icon="clock" />
                {{ job.jobType }}
              </span>
            </div>
          </div>
        </div>

        <!-- 职位要求 -->
        <div class="job-requirements">
          <div class="section-title">
            <font-awesome-icon icon="list-alt" />
            职位要求
          </div>
          <div class="requirements-grid">
            <div class="requirement-item">
              <div class="requirement-icon">
                <font-awesome-icon icon="calendar-alt" />
              </div>
              <div class="requirement-content">
                <div class="requirement-label">工作经验</div>
                <div class="requirement-value">{{ job.experience }}</div>
              </div>
            </div>
            <div class="requirement-item">
              <div class="requirement-icon">
                <font-awesome-icon icon="graduation-cap" />
              </div>
              <div class="requirement-content">
                <div class="requirement-label">学历要求</div>
                <div class="requirement-value">{{ job.education }}</div>
              </div>
            </div>
            <div class="requirement-item">
              <div class="requirement-icon">
                <font-awesome-icon icon="briefcase" />
              </div>
              <div class="requirement-content">
                <div class="requirement-label">工作性质</div>
                <div class="requirement-value">{{ job.jobType }}</div>
              </div>
            </div>
            <div class="requirement-item" v-if="job.category">
              <div class="requirement-icon">
                <font-awesome-icon icon="tags" />
              </div>
              <div class="requirement-content">
                <div class="requirement-label">职位类别</div>
                <div class="requirement-value">{{ job.categoryName }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 职位描述 -->
        <div class="job-description">
          <div class="section-title">
            <font-awesome-icon icon="file-alt" />
            职位描述
          </div>
          <div class="description-content" v-html="job.fullDescription || job.description"></div>
        </div>

        <!-- 技能要求 -->
        <div class="job-skills" v-if="job.skills && job.skills.length > 0">
          <div class="section-title">
            <font-awesome-icon icon="code" />
            技能要求
          </div>
          <div class="skills-list">
            <el-tag v-for="skill in job.skills" :key="skill" size="medium" type="primary">
              <font-awesome-icon icon="code" style="margin-right: 5px;" />
              {{ skill }}
            </el-tag>
          </div>
        </div>

        <!-- 任职要求 -->
        <div class="job-requirements-detail" v-if="job.requirements && job.requirements.length > 0">
          <div class="section-title">
            <font-awesome-icon icon="check-circle" />
            任职要求
          </div>
          <ul class="requirements-detail-list">
            <li v-for="requirement in job.requirements" :key="requirement">{{ requirement }}</li>
          </ul>
        </div>


        <!-- 联系方式 -->
        <div class="contact-info" v-if="job.contactInfo">
          <div class="section-title">
            <font-awesome-icon icon="phone-alt" />
            联系方式
          </div>
          <div class="contact-grid">
            <div class="contact-item" v-if="job.contactInfo.contact">
              <font-awesome-icon icon="user" />
              <span class="contact-label">联系人：</span>
              <span class="contact-value">{{ job.contactInfo.contact }}</span>
            </div>
            <div class="contact-item" v-if="job.contactInfo.phone">
              <font-awesome-icon icon="phone" />
              <span class="contact-label">联系电话：</span>
              <span class="contact-value">{{ job.contactInfo.phone }}</span>
            </div>
            <div class="contact-item" v-if="job.contactInfo.email">
              <font-awesome-icon icon="envelope" />
              <span class="contact-label">邮箱：</span>
              <span class="contact-value">{{ job.contactInfo.email }}</span>
            </div>
            <div class="contact-item" v-if="job.contactInfo.address">
              <span class="contact-label">工作地址：</span>
              <span class="contact-value">{{ job.contactInfo.address }}</span>
            </div>
          </div>
        </div>

        <!-- 发布时间 -->
        <div class="publish-time">
          <font-awesome-icon icon="calendar-alt" />
          <span>发布时间：{{ formatDate(job.publishTime) }}</span>
        </div>

      </div>

      <!-- 加载失败状态 -->
      <div class="error-state" v-else-if="!loading">
        <i class="el-icon-warning"></i>
        <p>招聘信息不存在或加载失败</p>
      </div>
    </div>
  </div>
</template>

<script>
import {IndustryModel} from '@/model/IndustryModel'

export default {
  name: 'RecruitmentDetailPage',
  data() {
    return {
      job: {},
      loading: false
    }
  },
  created() {
    this.fetchJobDetail()
  },
  watch: {
    '$route.params.id'() {
      this.fetchJobDetail()
    }
  },
  methods: {
    // 获取招聘详情
    async fetchJobDetail() {
      const jobId = this.$route.params.id
      if (!jobId) {
        this.$message.error('招聘ID不存在')
        this.goBack()
        return
      }
      
      this.loading = true
      try {
        this.job = await IndustryModel.getJobDetail(jobId)
        if (!this.job) {
          this.$message.error('招聘信息不存在')
        }
      } catch (error) {
        this.$message.error('获取招聘详情失败')
        console.error('获取招聘详情失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    
    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      return IndustryModel.formatDate(date, 'YYYY-MM-DD')
    }
  }
}
</script>

<style lang="less" scoped>


.recruitment-detail-page {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding: 30px 0;
  
  .back-button {
    margin-bottom: 20px;
  }
}

.recruitment-detail {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .job-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
    
    .company-logo {
      width: 100px;
      height: 100px;
      margin-right: 25px;
      flex-shrink: 0;
      
      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 8px;
        border: 1px solid #f0f0f0;
      }
    }
    
    .job-basic {
      flex: 1;
      
      .job-title-row {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
        
        .job-title {
          font-size: 24px;
          font-weight: bold;
          color: #333;
          margin: 0;
          display: flex;
          align-items: center;
          gap: 10px;
          flex-wrap: wrap;
          flex: 1;
        }
        
        .job-salary {
          font-size: 28px;
          font-weight: bold;
          color: #f56c6c;
          flex-shrink: 0;
          margin-left: 20px;
        }
      }
      
      .job-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 30px;
        
        .meta-item {
          font-size: 15px;
          color: #666;
          display: flex;
          align-items: center;
          gap: 8px;
          
          svg {
            color: #4093f9;
            width: 16px;
            height: 16px;
          }
        }
      }
    }
  }
  
  .section-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    
    svg {
      color: #4093f9;
      width: 16px;
      height: 16px;
    }
  }
  
  .job-requirements {
    margin-bottom: 30px;
    
    .requirements-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
      
      .requirement-item {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        gap: 15px;
        
        .requirement-icon {
          width: 40px;
          height: 40px;
          background: #4093f9;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          
          svg {
            color: white;
            width: 18px;
            height: 18px;
          }
        }
        
        .requirement-content {
          flex: 1;
          
          .requirement-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 5px;
          }
          
          .requirement-value {
            font-size: 16px;
            font-weight: bold;
            color: #333;
          }
        }
      }
    }
  }
  
  .job-description {
    margin-bottom: 30px;
    
    .description-content {
      font-size: 15px;
      line-height: 1.8;
      color: #666;
      
      ::v-deep h3 {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin: 20px 0 10px 0;
      }
      
      ::v-deep p {
        margin-bottom: 15px;
      }
      
      ::v-deep ul {
        padding-left: 20px;
        margin-bottom: 15px;
        
        li {
          margin-bottom: 8px;
        }
      }
    }
  }
  
  .job-skills {
    margin-bottom: 30px;
    
    .skills-list {
      .el-tag {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
  }
  
  .job-requirements-detail {
    margin-bottom: 30px;
    
    .requirements-detail-list {
      list-style: none;
      padding: 0;
      
      li {
        position: relative;
        padding-left: 20px;
        margin-bottom: 10px;
        font-size: 15px;
        line-height: 1.6;
        color: #666;
        
        &::before {
          content: '•';
          position: absolute;
          left: 0;
          color: #4093f9;
          font-weight: bold;
        }
      }
    }
  }
  
  
  .contact-info {
    margin-bottom: 30px;
    
    .contact-grid {
      .contact-item {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        font-size: 14px;
        gap: 8px;
        
        svg {
          color: #4093f9;
          width: 14px;
          height: 14px;
        }
        
        .contact-label {
          color: #666;
          min-width: 80px;
        }
        
        .contact-value {
          color: #333;
        }
      }
    }
  }
  
  .publish-time {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #4093f9;
    
    svg {
      color: #4093f9;
      width: 14px;
      height: 14px;
    }
  }
  
  .action-buttons {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
    
    .el-button {
      margin: 0 10px;
    }
  }
}

.error-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  color: #999;
  
  i {
    font-size: 64px;
    margin-bottom: 16px;
    color: #f56c6c;
  }
  
  p {
    font-size: 16px;
    margin-bottom: 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .recruitment-detail-page {
    padding: 20px 0;
    
    .content-container {
      padding: 0 15px;
    }
  }
  
  .recruitment-detail {
    padding: 20px;
    
    .job-header {
      flex-direction: column;
      text-align: center;
      
      .company-logo {
        margin-right: 0;
        margin-bottom: 20px;
        align-self: center;
      }
      
      .job-basic {
        .job-title-row {
          flex-direction: column;
          align-items: center;
          text-align: center;
          
          .job-title {
            justify-content: center;
            font-size: 20px;
            margin-bottom: 10px;
          }
          
          .job-salary {
            font-size: 24px;
            margin-left: 0;
          }
        }
        
        .job-meta {
          flex-direction: column;
          align-items: flex-start;
          gap: 10px;
        }
      }
    }
    
    .job-requirements {
      .requirements-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        
        .requirement-item {
          padding: 15px;
          
          .requirement-icon {
            width: 35px;
            height: 35px;
            
            svg {
              width: 16px;
              height: 16px;
            }
          }
          
          .requirement-content {
            .requirement-value {
              font-size: 15px;
            }
          }
        }
      }
    }
    
  }
}
</style>