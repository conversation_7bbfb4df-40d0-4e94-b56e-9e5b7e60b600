/*初始化和通用css*/

* {
    /*所有元素采用盒模型*/
    box-sizing: border-box;
    font-family: "Segoe UI", "PingFangSC-Regular", "PingFang SC", "Microsoft YaHei";
}

img {
    vertical-align: middle;
    /*使用了viewport-units-buggyfill 安卓能正常显示图片 ios不能*/
    content: normal !important;
}

input {
    border: 0;
    outline: none;
    background-color: rgba(0, 0, 0, 0);
    -webkit-appearance: none;
}

textarea {
    outline: none;
    -webkit-appearance: none;
}

/*清除浮动*/
.clearFix:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
}

.fixed {
    position: fixed;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

/*flex*/
.flex {
    display: flex;
}

.flex-dr {
    flex-direction: column;
}

.flex-center {
    justify-content: center;
    align-items: center;
}

.flex-around {
    justify-content: space-around;
    align-items: center;
}

.flex-between {
    justify-content: space-between;
    align-items: center;
}

.flex-start {
    justify-content: flex-start;
    align-items: center;
}

.flex-end {
    justify-content: flex-end;
    align-items: center;
}

.flex-wrap {
    flex-wrap: wrap;
}

/*省略号  */
.ellipsis {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

/*英文数字等自动化换行 还需要设置width*/
.word-hr {
    word-wrap: break-word;
    white-space: pre-wrap;
    word-break: normal;
    text-align: center;
}
.white-space-wrap{
    white-space: pre-wrap;
}

.cursor-pointer {
    cursor: pointer;
}

/*页面容器*/
.content-container {
    width: 1280px;
    margin: 0 auto;
}

body {
    background-color: #f2f2f2;
}

/*html预览*/
.html-view img {
    max-width: 100%;
}

.html-view.limit-height {
    max-height: 500px;
    overflow-y: scroll;
}

.pagination-container {
    margin-top: 30px;
    text-align: right;
}
